# 🛍️ Enhanced Product List API Documentation

## Base URL
```
http://zayyrah.com/api/v1/inventory/products/
```

## Authentication
All endpoints require authentication. Include the authorization token in headers:
```
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
```

---

## 📋 API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/v1/inventory/products/` | Enhanced product list with advanced filtering |
| GET | `/api/v1/inventory/products/filter_options/` | Get available filter options |
| GET | `/api/v1/inventory/products/dashboard/` | Get product dashboard statistics |
| GET | `/api/v1/inventory/products/quick_search/` | Quick search for autocomplete |

---

## 1. 🛍️ ENHANCED PRODUCT LIST

**GET** `/api/v1/inventory/products/`

### Query Parameters

#### **Status Filter (Primary)**
- `status` (string) - Product status
  - `active` - Active products only (**default**)
  - `inactive` - Inactive products only
  - `all` - All products (active + inactive)

#### **Category Filters**
- `category` (int) - Exact category ID match
- `category_tree` (int) - Hierarchical search (includes subcategories)

#### **Brand & Supplier Filters**
- `brand` (int) - Brand ID
- `supplier` (int) - Supplier ID

#### **Location Filter**
- `location` (int) - Show products with stock in this location

#### **Stock Status Filter**
- `stock_status` (string) - Stock availability
  - `all` - All stock levels (**default**)
  - `in_stock` - Products with available stock
  - `low_stock` - Products with low stock warning
  - `out_of_stock` - Products out of stock

#### **Search & Text Filters**
- `search` (string) - Search in name, SKU, barcode, description, tags, category, brand, supplier
- `tags` (string) - Comma-separated tags filter

#### **Price Range Filters**
- `min_price` (decimal) - Minimum selling price
- `max_price` (decimal) - Maximum selling price

#### **Special Filters**
- `featured` (boolean) - `true` for featured products only

#### **Sorting & Pagination**
- `ordering` (string) - Sort order
  - `name` - Name A-Z (**default**)
  - `-name` - Name Z-A
  - `selling_price` - Price Low to High
  - `-selling_price` - Price High to Low
  - `-created_at` - Newest First
  - `created_at` - Oldest First
  - `-updated_at` - Recently Updated
  - `sku` - SKU A-Z
  - `-sku` - SKU Z-A
  - `reorder_level` - Reorder Level Low to High
  - `-reorder_level` - Reorder Level High to Low

- `page` (int) - Page number (default: 1)
- `page_size` (int) - Items per page (default: 20, max: 100)

### Example Requests

#### Basic Usage (Default Active Products)
```bash
GET /api/v1/inventory/products/
```

#### Filter by Category
```bash
GET /api/v1/inventory/products/?category=57&page_size=10
```

#### Hierarchical Category Search
```bash
GET /api/v1/inventory/products/?category_tree=1&stock_status=in_stock
```

#### Search with Multiple Filters
```bash
GET /api/v1/inventory/products/?search=milk&status=active&min_price=100&max_price=500&ordering=-selling_price
```

#### Low Stock Alert
```bash
GET /api/v1/inventory/products/?stock_status=low_stock&ordering=-updated_at
```

#### Location-Specific Inventory
```bash
GET /api/v1/inventory/products/?location=2&stock_status=in_stock&page_size=50
```

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
      {
        "id": 5,
        "name": "Fresh Milk",
        "sku": "MILK-001",
        "barcode": "123456789",
        "category": 57,
        "category_name": "Dairy Products",
        "brand": 12,
        "brand_name": "Farm Fresh",
        "supplier": 8,
        "supplier_name": "Local Dairy Co",
        "selling_price": "150.00",
        "purchase_price": "140.00",
        "unit": "l",
        "unit_label": "Liter",
        "current_stock": 56.234,
        "reorder_level": 5,
        "is_low_stock": false,
        "is_out_of_stock": false,
        "is_active": true,
        "is_featured": false,
        "tags": "dairy, fresh, organic",
        "description": "Fresh organic milk from local farms",
        "created_at": "2025-09-22T17:12:22.053807Z",
        "updated_at": "2025-09-23T05:55:11.421921Z"
      }
    ],
    "filters_applied": {
      "status": "active",
      "category": null,
      "category_tree": null,
      "brand": null,
      "supplier": null,
      "location": null,
      "stock_status": "all",
      "search": "",
      "min_price": null,
      "max_price": null,
      "featured": null,
      "tags": null,
      "ordering": "name"
    },
    "metadata": {
      "execution_time_seconds": 0.036,
      "total_products_in_system": 125,
      "filters_count": 0
    }
  }
}
```

### Error Response (400)
```json
{
  "success": false,
  "message": "Invalid status. Use: active, inactive, or all"
}
```

---

## 2. ⚙️ FILTER OPTIONS

**GET** `/api/v1/inventory/products/filter_options/`

Get all available filter options for building dynamic filters in frontend.

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "status": [
      {"value": "active", "label": "Active Products", "is_default": true},
      {"value": "inactive", "label": "Inactive Products", "is_default": false},
      {"value": "all", "label": "All Products", "is_default": false}
    ],
    "categories": [
      {
        "id": 1,
        "name": "Electronics",
        "level": 0,
        "parent_id": null,
        "full_path": "Electronics",
        "products_count": 25,
        "has_children": true
      },
      {
        "id": 2,
        "name": "Smartphones",
        "level": 1,
        "parent_id": 1,
        "full_path": "Electronics > Smartphones",
        "products_count": 12,
        "has_children": false
      }
    ],
    "brands": [
      {
        "id": 1,
        "name": "Samsung",
        "products_count": 15
      }
    ],
    "suppliers": [
      {
        "id": 1,
        "name": "Tech Wholesale Co",
        "products_count": 8
      }
    ],
    "locations": [
      {
        "id": 1,
        "name": "Main Store",
        "code": "MAIN",
        "location_type": "store",
        "products_with_stock": 42
      }
    ],
    "stock_status": [
      {"value": "all", "label": "All Stock Levels", "is_default": true},
      {"value": "in_stock", "label": "In Stock", "icon": "✅"},
      {"value": "low_stock", "label": "Low Stock", "icon": "⚠️"},
      {"value": "out_of_stock", "label": "Out of Stock", "icon": "❌"}
    ],
    "price_range": {
      "min_price": 0.0,
      "max_price": 5000.0,
      "step": 0.01
    },
    "popular_tags": [
      {"name": "organic", "count": 25},
      {"name": "premium", "count": 18}
    ],
    "ordering": [
      {"value": "name", "label": "Name (A-Z)", "is_default": true},
      {"value": "-name", "label": "Name (Z-A)"},
      {"value": "selling_price", "label": "Price (Low to High)"},
      {"value": "-selling_price", "label": "Price (High to Low)"},
      {"value": "-created_at", "label": "Newest First"},
      {"value": "created_at", "label": "Oldest First"},
      {"value": "-updated_at", "label": "Recently Updated"},
      {"value": "sku", "label": "SKU (A-Z)"},
      {"value": "-sku", "label": "SKU (Z-A)"},
      {"value": "reorder_level", "label": "Reorder Level (Low to High)"},
      {"value": "-reorder_level", "label": "Reorder Level (High to Low)"}
    ],
    "page_size": [
      {"value": 20, "label": "20 per page", "is_default": true},
      {"value": 50, "label": "50 per page"},
      {"value": 100, "label": "100 per page"}
    ],
    "defaults": {
      "status": "active",
      "stock_status": "all",
      "ordering": "name",
      "page_size": 20,
      "page": 1
    }
  },
  "cached": false,
  "cache_key": "product_filter_options_12345"
}
```

---

## 3. 📊 DASHBOARD STATISTICS

**GET** `/api/v1/inventory/products/dashboard/`

Get comprehensive product statistics for dashboard display.

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "overview": {
      "total_products": 125,
      "active_products": 120,
      "inactive_products": 5,
      "featured_products": 15,
      "recent_products_week": 8
    },
    "stock_status": {
      "in_stock": 95,
      "low_stock": 12,
      "out_of_stock": 13,
      "total_stock_value": 125000.50
    },
    "pricing": {
      "min_price": 10.0,
      "max_price": 5000.0,
      "avg_price": 285.75
    },
    "category_distribution": [
      {"category_name": "Electronics", "product_count": 45},
      {"category_name": "Clothing", "product_count": 32},
      {"category_name": "Food & Beverages", "product_count": 28},
      {"category_name": "Books", "product_count": 15}
    ],
    "brand_distribution": [
      {"brand_name": "Samsung", "product_count": 18},
      {"brand_name": "Apple", "product_count": 12},
      {"brand_name": "Nike", "product_count": 8}
    ],
    "alerts": {
      "low_stock_alert": true,
      "out_of_stock_alert": true,
      "no_stock_value": false,
      "uncategorized_products": 3
    }
  }
}
```

---

## 4. 🔍 QUICK SEARCH

**GET** `/api/v1/inventory/products/quick_search/`

Fast search for autocomplete functionality.

### Query Parameters
- `q` (string) - Search query (minimum 2 characters)
- `limit` (int) - Maximum results (default: 5, max: 20)

### Example Request
```bash
GET /api/v1/inventory/products/quick_search/?q=milk&limit=3
```

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "query": "milk",
    "results": [
      {
        "id": 5,
        "name": "Fresh Milk",
        "sku": "MILK-001",
        "selling_price": "150.00",
        "current_stock": 56.234,
        "category_name": "Dairy Products"
      },
      {
        "id": 12,
        "name": "Almond Milk",
        "sku": "ALM-001",
        "selling_price": "250.00",
        "current_stock": 25.5,
        "category_name": "Alternative Dairy"
      }
    ]
  }
}
```

---

## 🚨 HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 400 | Bad request (invalid parameters) |
| 401 | Unauthorized (missing/invalid token) |
| 404 | Endpoint not found |
| 500 | Internal server error |

---

## 📝 Advanced Usage Examples

### 1. Build Category Dropdown Filter
```javascript
// Get filter options first
fetch('/api/v1/inventory/products/filter_options/')
  .then(response => response.json())
  .then(data => {
    const categories = data.data.categories;
    // Build hierarchical dropdown
    categories.forEach(category => {
      console.log('─'.repeat(category.level * 2) + category.name + ` (${category.products_count})`);
    });
  });
```

### 2. Advanced Search Interface
```javascript
const searchParams = new URLSearchParams({
  status: 'active',
  search: 'smartphone',
  category_tree: 1,  // Electronics category
  min_price: 200,
  max_price: 1000,
  stock_status: 'in_stock',
  ordering: '-created_at',
  page_size: 25
});

fetch(`/api/v1/inventory/products/?${searchParams}`)
  .then(response => response.json())
  .then(data => {
    console.log(`Found ${data.data.count} products`);
    console.log(`Filters applied:`, data.data.filters_applied);
    console.log(`Execution time: ${data.data.metadata.execution_time_seconds}s`);
  });
```

### 3. Stock Alert Dashboard
```javascript
// Get dashboard stats
const dashboard = await fetch('/api/v1/inventory/products/dashboard/').then(r => r.json());

if (dashboard.data.alerts.low_stock_alert) {
  // Get low stock products
  const lowStockProducts = await fetch('/api/v1/inventory/products/?stock_status=low_stock&page_size=100')
    .then(r => r.json());

  console.log(`⚠️ ${lowStockProducts.data.count} products need restocking`);
}
```

### 4. Real-time Search with Debouncing
```javascript
let searchTimeout;
const searchInput = document.getElementById('search');

searchInput.addEventListener('input', (e) => {
  clearTimeout(searchTimeout);
  const query = e.target.value;

  if (query.length >= 2) {
    searchTimeout = setTimeout(() => {
      fetch(`/api/v1/inventory/products/quick_search/?q=${encodeURIComponent(query)}&limit=10`)
        .then(response => response.json())
        .then(data => {
          // Update suggestions dropdown
          displaySearchSuggestions(data.data.results);
        });
    }, 300); // 300ms debounce
  }
});
```

---

## 🔧 Filter Combinations

### Popular Filter Combinations

1. **Inventory Management View**
   ```
   ?status=active&stock_status=low_stock&ordering=-updated_at
   ```

2. **Category Browse with Price Filter**
   ```
   ?category_tree=1&min_price=100&max_price=500&ordering=selling_price
   ```

3. **Featured Products Showcase**
   ```
   ?featured=true&status=active&ordering=-created_at&page_size=12
   ```

4. **Location-Specific Stock Check**
   ```
   ?location=1&stock_status=in_stock&ordering=name
   ```

5. **Supplier Analysis**
   ```
   ?supplier=5&status=all&ordering=-selling_price
   ```

---

## ⚡ Performance Features

- **Execution Time Tracking** - Each response includes execution time
- **Intelligent Caching** - Filter options cached for 5 minutes
- **Optimized Queries** - Efficient database queries with minimal N+1 problems
- **Pagination Support** - Handle large datasets efficiently
- **Smart Filtering** - Filters applied in optimal order to reduce query complexity

---

## 🛡️ Security Features

- ✅ **Authentication Required** - All endpoints require valid auth token
- ✅ **User Isolation** - Users can only see their own products
- ✅ **Input Validation** - All query parameters validated
- ✅ **SQL Injection Protection** - Django ORM protects against SQL injection
- ✅ **Rate Limiting Ready** - Compatible with rate limiting middleware

---

## 🎯 Frontend Integration Tips

1. **Always load filter options first** to build dynamic filters
2. **Use dashboard stats** for overview cards and alerts
3. **Implement search debouncing** to avoid excessive API calls
4. **Cache filter options** on frontend for better UX
5. **Show loading states** during API calls
6. **Handle empty states** gracefully
7. **Display execution times** for performance monitoring
8. **Use pagination** for large product catalogs

---

**✨ Your Enhanced Product List API is ready for production use!**

## 📱 Ready for Frontend Integration

This API is optimized for:
- **React/Vue.js** single-page applications
- **Flutter** mobile applications
- **Traditional web** interfaces with AJAX
- **Third-party integrations** and webhooks