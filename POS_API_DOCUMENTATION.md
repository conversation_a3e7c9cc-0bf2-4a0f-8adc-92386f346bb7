# 🚀 Complete POS System API Documentation

## Base URL
**Production:** `http://*************:8002`
**API Base:** `http://*************:8002/inventory/pos/api/`

## Authentication
All API endpoints require authentication via session or token.

**Session Auth:** Send cookies with requests
**Headers Required:**
```json
{
  "Content-Type": "application/json",
  "X-CSRFToken": "your_csrf_token"
}
```

---

## 📋 1. Product Management APIs

### 1.1 Get POS Products
**Endpoint:** `GET /inventory/pos/api/products/`

**Description:** Retrieve all POS-enabled products with filtering options.

**Query Parameters:**
```json
{
  "category": "integer (optional) - Filter by category ID",
  "search": "string (optional) - Search in product name/SKU"
}
```

**Request Example:**
```bash
curl -X GET "http://*************:8002/inventory/pos/api/products/?category=1&search=milk" \
  -H "Content-Type: application/json" \
  -b "sessionid=your_session_id"
```

**Response Success (200):**
```json
{
  "success": true,
  "products": [
    {
      "id": 1,
      "name": "Fresh Milk (POS)",
      "sku": "MILK001",
      "barcode": "1234567890123",
      "price": 80.0,
      "original_price": 80.0,
      "current_stock": 75.0,
      "unit_label": "liters",
      "category_name": "Dairy Products",
      "allow_discount": true,
      "max_discount_percent": 10.0,
      "tax_rate": 0.0,
      "image_url": "/media/products/milk.jpg"
    }
  ],
  "total_count": 1
}
```

**Response Error (500):**
```json
{
  "success": false,
  "error": "Error message"
}
```

---

## 🛒 2. Transaction Management APIs

### 2.1 Create New Transaction
**Endpoint:** `POST /inventory/pos/api/transactions/`

**Description:** Create a new POS transaction.

**Request Body:**
```json
{
  "customer_id": 24,  // optional - null for walk-in customer
  "notes": "Special instructions"  // optional
}
```

**Request Example:**
```bash
curl -X POST "http://*************:8002/inventory/pos/api/transactions/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "customer_id": 24,
    "notes": "Customer requested extra packaging"
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "transaction": {
    "id": 15,
    "transaction_number": "TXN-1734854623-15",
    "status": "active",
    "total_amount": 0.0,
    "customer_name": "Walk-in Customer"
  }
}
```

### 2.2 Get Transaction Details
**Endpoint:** `GET /inventory/pos/api/transactions/{transaction_id}/`

**Description:** Retrieve complete transaction details including items.

**Request Example:**
```bash
curl -X GET "http://*************:8002/inventory/pos/api/transactions/15/" \
  -H "Content-Type: application/json" \
  -b "sessionid=your_session_id"
```

**Response Success (200):**
```json
{
  "success": true,
  "transaction": {
    "id": 15,
    "transaction_number": "TXN-1734854623-15",
    "status": "active",
    "subtotal": 240.0,
    "tax_total": 0.0,
    "discount_total": 0.0,
    "total_amount": 240.0,
    "payment_method": null,
    "payment_status": "pending",
    "amount_paid": 0.0,
    "customer_name": "Walk-in Customer",
    "items": [
      {
        "id": 12,
        "product_id": 1,
        "product_name": "Fresh Milk (POS)",
        "quantity": 3.0,
        "unit_price": 80.0,
        "discount_percent": 0.0,
        "discount_amount": 0.0,
        "line_total": 240.0,
        "tax_amount": 0.0,
        "unit_label": "liters"
      }
    ],
    "total_items": 3.0,
    "items_count": 1
  }
}
```

---

## 🛍️ 3. Transaction Items APIs

### 3.1 Add Item to Transaction
**Endpoint:** `POST /inventory/pos/api/transactions/{transaction_id}/items/`

**Description:** Add products to an active transaction.

**Request Body:**
```json
{
  "product_id": 1,
  "quantity": 2.5,
  "unit_price": 80.0,  // optional - uses product price if not provided
  "discount_percent": 10.0  // optional - default 0
}
```

**Request Example:**
```bash
curl -X POST "http://*************:8002/inventory/pos/api/transactions/15/items/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "product_id": 1,
    "quantity": 2.5,
    "discount_percent": 5.0
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Added 2.5 liters of Fresh Milk (POS)",
  "item": {
    "id": 13,
    "product_id": 1,
    "quantity": 2.5,
    "unit_price": 80.0,
    "line_total": 190.0,
    "discount_amount": 10.0
  },
  "transaction_total": 430.0
}
```

### 3.2 Update Transaction Item
**Endpoint:** `PUT /inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Description:** Update quantity or discount for existing item.

**Request Body:**
```json
{
  "quantity": 3.0,
  "discount_percent": 15.0
}
```

**Request Example:**
```bash
curl -X PUT "http://*************:8002/inventory/pos/api/transactions/15/items/13/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "quantity": 3.0,
    "discount_percent": 15.0
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Item updated successfully",
  "item": {
    "id": 13,
    "quantity": 3.0,
    "discount_percent": 15.0,
    "line_total": 204.0
  },
  "transaction_total": 444.0
}
```

### 3.3 Remove Item from Transaction
**Endpoint:** `DELETE /inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Request Example:**
```bash
curl -X DELETE "http://*************:8002/inventory/pos/api/transactions/15/items/13/" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id"
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Item removed successfully",
  "transaction_total": 240.0
}
```

---

## 💳 4. Payment APIs

### 4.1 Process Payment
**Endpoint:** `POST /inventory/pos/api/transactions/{transaction_id}/payment/`

**Description:** Process payment for a transaction.

**Request Body:**
```json
{
  "payment_method": "cash",  // cash, card, credit, mixed
  "amounts": {
    "cash": 250.0,
    "card": 0.0,
    "credit": 0.0
  },
  "customer_payment": 300.0,  // for cash payments to calculate change
  "discount_percent": 5.0  // optional - transaction-level discount
}
```

**Payment Methods:**
- `"cash"` - Cash payment
- `"card"` - Card payment
- `"credit"` - Customer credit
- `"mixed"` - Multiple payment methods

**Request Example:**
```bash
curl -X POST "http://*************:8002/inventory/pos/api/transactions/15/payment/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "payment_method": "cash",
    "amounts": {
      "cash": 240.0,
      "card": 0.0,
      "credit": 0.0
    },
    "customer_payment": 300.0
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "payment_details": {
    "total_amount": 240.0,
    "amount_paid": 240.0,
    "change_due": 60.0,
    "payment_method": "cash",
    "payment_status": "paid"
  },
  "transaction": {
    "id": 15,
    "status": "active",
    "payment_status": "paid"
  }
}
```

---

## ✅ 5. Transaction Completion APIs

### 5.1 Complete Transaction
**Endpoint:** `POST /inventory/pos/api/transactions/{transaction_id}/complete/`

**Description:** Complete and finalize a transaction (processes stock deduction).

**Request Body:**
```json
{
  "print_receipt": true  // optional - default false
}
```

**Request Example:**
```bash
curl -X POST "http://*************:8002/inventory/pos/api/transactions/15/complete/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "print_receipt": true
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Transaction completed successfully",
  "transaction": {
    "id": 15,
    "transaction_number": "TXN-1734854623-15",
    "status": "completed",
    "total_amount": 240.0,
    "payment_status": "paid",
    "completed_at": "2024-12-22T10:30:23.456Z"
  },
  "stock_deductions": [
    {
      "product_id": 1,
      "product_name": "Fresh Milk (POS)",
      "quantity_deducted": 3.0,
      "remaining_stock": 72.0
    }
  ]
}
```

---

## 👥 6. Customer APIs

### 6.1 Get POS Customers
**Endpoint:** `GET /inventory/pos/api/customers/`

**Description:** Get list of customers for POS selection.

**Query Parameters:**
```json
{
  "search": "string (optional) - Search customer name/mobile"
}
```

**Request Example:**
```bash
curl -X GET "http://*************:8002/inventory/pos/api/customers/?search=walk" \
  -H "Content-Type: application/json" \
  -b "sessionid=your_session_id"
```

**Response Success (200):**
```json
{
  "success": true,
  "customers": [
    {
      "id": 24,
      "name": "Walk-in Customer",
      "display_name": "Walk-in Customer",
      "mobile_number": null,
      "email": null,
      "credit_limit": 0.0,
      "current_balance": 0.0,
      "is_walk_in": true
    },
    {
      "id": 25,
      "name": "John Doe",
      "display_name": "John Doe",
      "mobile_number": "03001234567",
      "email": "<EMAIL>",
      "credit_limit": 5000.0,
      "current_balance": 1500.0,
      "is_walk_in": false
    }
  ]
}
```

---

## ✅ 7. Validation APIs

### 7.1 Validate Transaction Item
**Endpoint:** `POST /inventory/pos/api/validate/item/`

**Description:** Validate if item can be added to transaction (stock check).

**Request Body:**
```json
{
  "product_id": 1,
  "quantity": 5.0,
  "transaction_id": 15  // optional - for existing transaction items
}
```

**Request Example:**
```bash
curl -X POST "http://*************:8002/inventory/pos/api/validate/item/" \
  -H "Content-Type: application/json" \
  -H "X-CSRFToken: your_csrf_token" \
  -b "sessionid=your_session_id" \
  -d '{
    "product_id": 1,
    "quantity": 5.0
  }'
```

**Response Success (200):**
```json
{
  "success": true,
  "valid": true,
  "message": "Item can be added",
  "available_stock": 72.0,
  "requested_quantity": 5.0
}
```

**Response Warning (200):**
```json
{
  "success": true,
  "valid": false,
  "message": "Insufficient stock available",
  "available_stock": 2.0,
  "requested_quantity": 5.0
}
```

### 7.2 Validate Payment Amounts
**Endpoint:** `POST /inventory/pos/api/validate/payment/`

**Description:** Validate payment amounts and calculate change.

**Request Body:**
```json
{
  "transaction_id": 15,
  "payment_method": "mixed",
  "amounts": {
    "cash": 150.0,
    "card": 90.0,
    "credit": 0.0
  },
  "customer_payment": 200.0
}
```

**Response Success (200):**
```json
{
  "success": true,
  "valid": true,
  "validation": {
    "total_required": 240.0,
    "total_provided": 240.0,
    "change_due": 50.0,
    "payment_complete": true
  }
}
```

---

## 📊 8. Reports APIs

### 8.1 Get POS Reports
**Endpoint:** `GET /inventory/pos/api/reports/`

**Description:** Get sales reports and analytics.

**Query Parameters:**
```json
{
  "date_from": "2024-12-01",  // optional - YYYY-MM-DD format
  "date_to": "2024-12-31",    // optional - YYYY-MM-DD format
  "report_type": "daily"      // optional - daily, weekly, monthly
}
```

**Request Example:**
```bash
curl -X GET "http://*************:8002/inventory/pos/api/reports/?date_from=2024-12-01&report_type=daily" \
  -H "Content-Type: application/json" \
  -b "sessionid=your_session_id"
```

**Response Success (200):**
```json
{
  "success": true,
  "reports": {
    "summary": {
      "total_sales": 12450.0,
      "total_transactions": 25,
      "average_transaction": 498.0,
      "total_items_sold": 127.5
    },
    "daily_breakdown": [
      {
        "date": "2024-12-22",
        "sales": 2340.0,
        "transactions": 8,
        "items": 23.5
      }
    ],
    "top_products": [
      {
        "product_id": 1,
        "product_name": "Fresh Milk (POS)",
        "quantity_sold": 45.0,
        "revenue": 3600.0
      }
    ],
    "payment_methods": {
      "cash": 8500.0,
      "card": 3200.0,
      "credit": 750.0
    }
  }
}
```

---

## 🔄 9. Complete Workflow Example

### Step-by-Step POS Transaction Process:

```javascript
// 1. Get available products
const products = await fetch('/inventory/pos/api/products/')
  .then(r => r.json());

// 2. Create new transaction
const transaction = await fetch('/inventory/pos/api/transactions/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    customer_id: 24,
    notes: 'Regular customer'
  })
}).then(r => r.json());

const transactionId = transaction.transaction.id;

// 3. Add items to transaction
await fetch(`/inventory/pos/api/transactions/${transactionId}/items/`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    product_id: 1,
    quantity: 2.5,
    discount_percent: 5.0
  })
});

// 4. Process payment
await fetch(`/inventory/pos/api/transactions/${transactionId}/payment/`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    payment_method: 'cash',
    amounts: { cash: 190.0, card: 0.0, credit: 0.0 },
    customer_payment: 200.0
  })
});

// 5. Complete transaction
const completed = await fetch(`/inventory/pos/api/transactions/${transactionId}/complete/`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ print_receipt: true })
}).then(r => r.json());

console.log('Transaction completed:', completed.transaction.transaction_number);
```

---

## 🚨 Error Handling

### Common Error Responses:

**Authentication Error (401):**
```json
{
  "success": false,
  "error": "Authentication required"
}
```

**Validation Error (400):**
```json
{
  "success": false,
  "error": "Invalid request data",
  "details": {
    "quantity": ["This field is required"],
    "product_id": ["Product not found"]
  }
}
```

**Stock Error (400):**
```json
{
  "success": false,
  "error": "Insufficient stock available. Only 2.5 liters remaining."
}
```

**Server Error (500):**
```json
{
  "success": false,
  "error": "Internal server error message"
}
```

---

## 💡 Integration Tips

### 1. **CSRF Protection:**
Include CSRF token in all POST/PUT/DELETE requests:
```javascript
// Get CSRF token from cookie or meta tag
const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

fetch('/api/endpoint/', {
  method: 'POST',
  headers: {
    'X-CSRFToken': csrfToken,
    'Content-Type': 'application/json'
  }
});
```

### 2. **Error Handling:**
```javascript
const response = await fetch('/inventory/pos/api/products/');
const data = await response.json();

if (data.success) {
  // Handle success
  console.log('Products:', data.products);
} else {
  // Handle error
  console.error('Error:', data.error);
}
```

### 3. **Real-time Updates:**
For cart updates, call transaction detail endpoint after each item modification:
```javascript
const updateCart = async (transactionId) => {
  const response = await fetch(`/inventory/pos/api/transactions/${transactionId}/`);
  const data = await response.json();
  if (data.success) {
    updateCartDisplay(data.transaction);
  }
};
```

### 4. **Stock Validation:**
Always validate before adding items:
```javascript
const validateItem = async (productId, quantity) => {
  const response = await fetch('/inventory/pos/api/validate/item/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ product_id: productId, quantity })
  });
  const data = await response.json();
  return data.valid;
};
```

---

## 🎯 Summary

The POS API provides complete functionality for:
- ✅ Product management with real-time stock
- ✅ Transaction lifecycle management
- ✅ Flexible payment processing
- ✅ Customer integration
- ✅ Stock validation and deduction
- ✅ Comprehensive reporting
- ✅ Error handling and validation

**Base URL:** `http://*************:8002/inventory/pos/api/`
**Authentication:** Session-based with CSRF protection
**Format:** JSON request/response
**Status:** Production ready 🚀