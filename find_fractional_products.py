#!/usr/bin/env python3
"""
Find products that allow fractional quantities
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from inventory.models import EnhancedProduct
from accounts.models import User

# Get test user
user = User.objects.filter(mobile_number='**********').first()
products = EnhancedProduct.objects.filter(owner=user, allow_fractional_quantities=True)

print('Products with fractional quantities allowed:')
for p in products[:5]:
    print(f'- {p.name}: ${p.selling_price}/{p.unit_label}')

# Also check if Fresh Milk exists
fresh_milk = EnhancedProduct.objects.filter(owner=user, name__icontains='milk').first()
if fresh_milk:
    print(f'\nFresh Milk found: {fresh_milk.name} - ${fresh_milk.selling_price}/{fresh_milk.unit_label} - Fractional: {fresh_milk.allow_fractional_quantities}')
else:
    print('\nNo Fresh Milk product found')