# ChannabPOS API Test Examples

## Getting Started

All API endpoints are available at: `http://your-domain.com/api/v1/`

### Base URL Configuration
```
Development: http://localhost:8000/api/v1/
Production: https://your-domain.com/api/v1/
```

## Authentication Flow

### 1. Register a New User
```bash
curl -X POST http://localhost:8000/api/v1/accounts/register/ \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "**********",
    "password": "securepassword123",
    "password_confirm": "securepassword123",
    "account_type": "business",
    "shop_name": "My Test Shop"
  }'
```

**Expected Response:**
```json
{
  "success": true,
  "message": "Account created successfully",
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "My Test Shop",
    "is_active": true,
    "date_joined": "2024-01-22T10:30:00Z"
  },
  "tokens": {
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### 2. Login Existing User
```bash
curl -X POST http://localhost:8000/api/v1/accounts/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "**********",
    "password": "securepassword123"
  }'
```

### 3. Access Protected Endpoints
Use the access token in the Authorization header:

```bash
curl -X GET http://localhost:8000/api/v1/accounts/profile/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Customer Management Examples

### 1. Create a Customer
```bash
curl -X POST http://localhost:8000/api/v1/customers/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Doe",
    "mobile_number": "**********",
    "email": "<EMAIL>",
    "notes": "Regular customer"
  }'
```

### 2. List Customers with Search
```bash
# List all customers
curl -X GET http://localhost:8000/api/v1/customers/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Search customers
curl -X GET "http://localhost:8000/api/v1/customers/?search=john" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Paginated results
curl -X GET "http://localhost:8000/api/v1/customers/?page=2&page_size=10" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Get Customer Details
```bash
curl -X GET http://localhost:8000/api/v1/customers/1/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Update Customer
```bash
curl -X PUT http://localhost:8000/api/v1/customers/1/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "John Updated",
    "mobile_number": "**********",
    "email": "<EMAIL>",
    "notes": "VIP customer"
  }'
```

## Product Management Examples

### 1. Create Product Category
```bash
curl -X POST http://localhost:8000/api/v1/pos/categories/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Electronics"
  }'
```

### 2. Create Product
```bash
curl -X POST http://localhost:8000/api/v1/pos/products/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "iPhone 15",
    "sku": "IP15-128GB",
    "category_id": 1,
    "purchase_price": "800.00",
    "selling_price": "999.00",
    "stock_quantity": 25,
    "unit": "each",
    "tax_rate": "18.00"
  }'
```

### 3. List Products with Filters
```bash
# List all products
curl -X GET http://localhost:8000/api/v1/pos/products/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Search products
curl -X GET "http://localhost:8000/api/v1/pos/products/?search=iphone" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Filter by category
curl -X GET "http://localhost:8000/api/v1/pos/products/?category=1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Filter low stock products
curl -X GET "http://localhost:8000/api/v1/pos/products/?low_stock=true" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 4. Add Stock to Product
```bash
curl -X POST http://localhost:8000/api/v1/pos/products/1/stock/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "quantity_added": 20,
    "purchase_price": "795.00",
    "added_on": "2024-01-22",
    "expiry_date": "2025-12-31",
    "notes": "New shipment from supplier"
  }'
```

## Sales Management Examples

### 1. Create Sale
```bash
curl -X POST http://localhost:8000/api/v1/pos/sales/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "payment_method": "cash",
    "discount_total": "50.00",
    "notes": "Customer discount applied",
    "items": [
      {
        "product_id": 1,
        "quantity": 2,
        "price": "999.00"
      },
      {
        "product_id": 2,
        "quantity": 1,
        "price": "899.00"
      }
    ]
  }'
```

### 2. List Sales with Filters
```bash
# List all sales
curl -X GET http://localhost:8000/api/v1/pos/sales/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Filter by date range
curl -X GET "http://localhost:8000/api/v1/pos/sales/?date_from=2024-01-01&date_to=2024-01-31" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Filter by customer
curl -X GET "http://localhost:8000/api/v1/pos/sales/?customer=1" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Filter by payment method
curl -X GET "http://localhost:8000/api/v1/pos/sales/?payment_method=cash" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 3. Get Sale Details
```bash
curl -X GET http://localhost:8000/api/v1/pos/sales/1/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Dashboard Analytics

### Get Dashboard Data
```bash
# Today's data
curl -X GET http://localhost:8000/api/v1/pos/dashboard/ \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Weekly data
curl -X GET "http://localhost:8000/api/v1/pos/dashboard/?period=week" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Monthly data
curl -X GET "http://localhost:8000/api/v1/pos/dashboard/?period=month" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

## Error Handling Examples

### Validation Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "mobile_number": ["This field is required."],
    "password": ["This password is too short."]
  }
}
```

### Authentication Error Response
```json
{
  "success": false,
  "message": "Invalid credentials"
}
```

### Insufficient Stock Error
```json
{
  "success": false,
  "message": "Sale creation failed",
  "errors": {
    "items": ["Insufficient stock for iPhone 15. Available: 5"]
  }
}
```

## Token Refresh

### Refresh Access Token
```bash
curl -X POST http://localhost:8000/api/v1/accounts/token/refresh/ \
  -H "Content-Type: application/json" \
  -d '{
    "refresh": "YOUR_REFRESH_TOKEN"
  }'
```

**Response:**
```json
{
  "access": "NEW_ACCESS_TOKEN"
}
```

## Testing Your Implementation

### 1. Start Django Development Server
```bash
cd zayyrah
source ../.venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### 2. Test Registration Flow
1. Register a new user
2. Use the returned tokens for subsequent requests
3. Test all endpoints with the access token

### 3. Test Error Scenarios
- Try accessing protected endpoints without token
- Test with invalid credentials
- Test validation errors with incomplete data
- Test stock limitations when creating sales

### 4. Test Pagination
- Create multiple records
- Test pagination parameters
- Verify count and navigation links

## Flutter Integration Notes

### 1. HTTP Client Setup
```dart
import 'package:dio/dio.dart';

class ApiClient {
  static const String baseUrl = 'http://your-domain.com/api/v1';
  late Dio _dio;
  String? _accessToken;

  ApiClient() {
    _dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: Duration(seconds: 30),
      receiveTimeout: Duration(seconds: 30),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        handler.next(options);
      },
    ));
  }

  void setToken(String token) {
    _accessToken = token;
  }
}
```

### 2. Error Handling
```dart
class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final Map<String, dynamic>? errors;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.errors,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'],
      errors: json['errors'],
    );
  }
}
```

### 3. Model Classes
```dart
class User {
  final int id;
  final String mobileNumber;
  final String accountType;
  final String? shopName;
  final bool isActive;
  final DateTime dateJoined;

  User({
    required this.id,
    required this.mobileNumber,
    required this.accountType,
    this.shopName,
    required this.isActive,
    required this.dateJoined,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      mobileNumber: json['mobile_number'],
      accountType: json['account_type'],
      shopName: json['shop_name'],
      isActive: json['is_active'],
      dateJoined: DateTime.parse(json['date_joined']),
    );
  }
}
```

This documentation provides complete examples for testing and integrating with your ChannabPOS API!