# Complete API Endpoints Reference

**Base URL**: `http://54.166.200.11:8002`

**Authentication**: All endpoints require JWT token except login/register
```
Authorization: Bearer <jwt_token>
```

---

## 🔐 Authentication APIs

### Login
**POST** `/api/v1/accounts/login/`

**Request:**
```json
{
  "mobile_number": "**********",
  "password": "testpass123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "Test Shop"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### Register
**POST** `/api/v1/accounts/register/`

**Request:**
```json
{
  "mobile_number": "**********",
  "password": "password123",
  "password_confirm": "password123",
  "account_type": "business",
  "shop_name": "My Shop"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account created successfully",
  "data": {
    "id": 2,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "My Shop"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

### Token Refresh
**POST** `/api/v1/accounts/token/refresh/`

**Request:**
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response:**
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

---

## 🛒 Cart APIs (Recommended for Mobile)

### Get Current Cart
**GET** `/inventory/cart/api/`

**Response (Empty Cart):**
```json
{
  "success": true,
  "cart": null,
  "items": [],
  "totals": {
    "items_count": 0,
    "subtotal": 0.00,
    "tax_total": 0.00,
    "discount_total": 0.00,
    "total_amount": 0.00
  }
}
```

**Response (With Items):**
```json
{
  "success": true,
  "cart": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "customer_id": 45,
    "customer_name": "John Doe",
    "notes": ""
  },
  "items": [
    {
      "id": 1,
      "product_id": 67,
      "product_name": "Coca Cola 500ml",
      "sku": "CC500",
      "barcode": "**********",
      "quantity": 2.0,
      "unit_price": 25.00,
      "original_price": 25.00,
      "discount_percent": 0.00,
      "discount_amount": 0.00,
      "line_total": 50.00,
      "unit_label": "bottle",
      "image_url": "http://example.com/image.jpg"
    }
  ],
  "totals": {
    "items_count": 2,
    "subtotal": 50.00,
    "tax_total": 0.00,
    "discount_total": 0.00,
    "total_amount": 50.00
  }
}
```

### Create/Update Cart
**POST** `/inventory/cart/api/`

**Request:**
```json
{
  "customer_id": 45,
  "notes": "Special order"
}
```

**Response:**
```json
{
  "success": true,
  "cart": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "customer_id": 45,
    "customer_name": "John Doe"
  }
}
```

### Clear Cart
**DELETE** `/inventory/cart/api/`

**Response:**
```json
{
  "success": true,
  "message": "Cart cleared"
}
```

### Add Item to Cart
**POST** `/inventory/cart/api/items/`

**Request:**
```json
{
  "product_id": 67,
  "quantity": 2.0,
  "custom_price": 24.00,
  "discount_percent": 5.0,
  "discount_amount": 2.50
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item added to cart",
  "item": {
    "id": 1,
    "product_id": 67,
    "product_name": "Coca Cola 500ml",
    "quantity": 2.0,
    "unit_price": 24.00,
    "line_total": 45.50
  },
  "cart_total": 45.50
}
```

### Update Cart Item
**PUT** `/inventory/cart/api/items/`

**Request:**
```json
{
  "item_id": 1,
  "quantity": 3.0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item quantity updated",
  "item": {
    "id": 1,
    "quantity": 3.0,
    "line_total": 75.00
  },
  "cart_total": 75.00
}
```

### Remove Cart Item
**DELETE** `/inventory/cart/api/items/`

**Request:**
```json
{
  "item_id": 1
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item removed from cart",
  "cart_total": 0.00
}
```

### Quick Add to Cart
**POST** `/inventory/cart/api/quick-add/`

**Request (By Barcode):**
```json
{
  "barcode": "**********",
  "quantity": 1.0
}
```

**Request (By Product ID):**
```json
{
  "product_id": 67,
  "quantity": 2.0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Added Coca Cola 500ml to cart",
  "product": {
    "id": 67,
    "name": "Coca Cola 500ml",
    "price": 25.00
  },
  "cart_items_count": 1,
  "cart_total": 25.00
}
```

### Quick Checkout
**POST** `/inventory/cart/api/checkout/`

**Request:**
```json
{
  "cash_amount": 100.00,
  "credit_amount": 0.00,
  "card_amount": 0.00
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout completed successfully",
  "transaction": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "total_amount": 50.00,
    "payment_method": "cash",
    "change_amount": 50.00,
    "items_count": 2,
    "completed_at": "2025-09-21T16:30:00Z",
    "customer_name": "John Doe"
  }
}
```

---

## 🏪 POS APIs (Advanced Transaction Management)

### Get Products
**GET** `/inventory/pos/api/products/`

**Query Parameters:**
- `category` - Filter by category ID
- `search` - Search products by name/SKU

**Response:**
```json
{
  "success": true,
  "products": [
    {
      "id": 67,
      "name": "Coca Cola 500ml",
      "sku": "CC500",
      "barcode": "**********",
      "price": 25.00,
      "original_price": 25.00,
      "current_stock": 100.0,
      "unit_label": "bottle",
      "category_name": "Beverages",
      "allow_discount": true,
      "max_discount_percent": 10.00,
      "quick_sale_enabled": true,
      "pos_button_color": "#4CAF50",
      "image_url": "http://example.com/image.jpg"
    }
  ],
  "count": 1
}
```

### Get Customers
**GET** `/inventory/pos/api/customers/`

**Query Parameters:**
- `search` - Search by name or mobile number

**Response:**
```json
{
  "success": true,
  "customers": [
    {
      "id": 45,
      "name": "John Doe",
      "mobile_number": "**********",
      "credit_limit": 1000.00,
      "current_balance": 250.00,
      "available_credit": 750.00
    }
  ]
}
```

### Create Transaction
**POST** `/inventory/pos/api/transactions/`

**Request:**
```json
{
  "customer_id": 45,
  "notes": "Special order"
}
```

**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "status": "active",
    "total_amount": 0.00,
    "customer_name": "John Doe"
  }
}
```

### Get Transaction Details
**GET** `/inventory/pos/api/transactions/{transaction_id}/`

**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "status": "active",
    "subtotal": 50.00,
    "tax_total": 0.00,
    "discount_total": 0.00,
    "total_amount": 50.00,
    "payment_method": null,
    "payment_status": "pending",
    "amount_paid": 0.00,
    "customer_name": "John Doe",
    "items": [
      {
        "id": 1,
        "product_id": 67,
        "product_name": "Coca Cola 500ml",
        "quantity": 2.0,
        "unit_price": 25.00,
        "discount_percent": 0.00,
        "discount_amount": 0.00,
        "line_total": 50.00,
        "tax_amount": 0.00,
        "unit_label": "bottle"
      }
    ],
    "total_items": 2.0,
    "items_count": 1
  }
}
```

### Add Transaction Item
**POST** `/inventory/pos/api/transactions/{transaction_id}/items/`

**Request:**
```json
{
  "product_id": 67,
  "quantity": 2.0,
  "custom_price": 24.00,
  "discount_percent": 5.0,
  "discount_amount": 2.50
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item added to transaction",
  "item": {
    "id": 1,
    "product_name": "Coca Cola 500ml",
    "quantity": 2.0,
    "unit_price": 24.00,
    "line_total": 45.50
  },
  "transaction_total": 45.50
}
```

### Update Transaction Item
**PUT** `/inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Request:**
```json
{
  "quantity": 3.0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Item quantity updated",
  "transaction_total": 72.00
}
```

### Remove Transaction Item
**DELETE** `/inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Response:**
```json
{
  "success": true,
  "message": "Item removed from transaction",
  "transaction_total": 0.00
}
```

### Process Payment
**POST** `/inventory/pos/api/transactions/{transaction_id}/payment/`

**Request:**
```json
{
  "cash_amount": 100.00,
  "credit_amount": 0.00,
  "card_amount": 0.00
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "payment_info": {
    "payment_method": "cash",
    "total_paid": 100.00,
    "change_amount": 50.00,
    "remaining_balance": 0.00
  },
  "transaction": {
    "payment_status": "paid",
    "amount_paid": 100.00
  }
}
```

### Complete Transaction
**POST** `/inventory/pos/api/transactions/{transaction_id}/complete/`

**Response:**
```json
{
  "success": true,
  "message": "Transaction completed successfully",
  "result": {
    "transaction_number": "TXN-2025-001",
    "total_amount": 50.00,
    "payment_method": "cash",
    "change_amount": 50.00,
    "items_count": 2,
    "completed_at": "2025-09-21T16:30:00Z"
  }
}
```

---

## 📦 Inventory APIs

### Get Categories
**GET** `/api/v1/inventory/categories/`

**Response:**
```json
{
  "count": 2,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "Beverages",
      "parent": null,
      "level": 0,
      "is_active": true,
      "full_path": "Beverages",
      "children_count": 2
    },
    {
      "id": 2,
      "name": "Soft Drinks",
      "parent": 1,
      "level": 1,
      "is_active": true,
      "full_path": "Beverages > Soft Drinks",
      "children_count": 0
    }
  ]
}
```

### Create Category
**POST** `/api/v1/inventory/categories/`

**Request:**
```json
{
  "name": "Energy Drinks",
  "parent": 1,
  "description": "Energy and sports drinks"
}
```

**Response:**
```json
{
  "id": 3,
  "name": "Energy Drinks",
  "parent": 1,
  "level": 1,
  "is_active": true,
  "full_path": "Beverages > Energy Drinks",
  "description": "Energy and sports drinks",
  "created_at": "2025-09-21T16:30:00Z"
}
```

### Get Products
**GET** `/api/v1/inventory/products/`

**Query Parameters:**
- `category` - Filter by category ID
- `search` - Search by name/SKU/barcode
- `page` - Page number

**Response:**
```json
{
  "count": 1,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": 67,
      "name": "Coca Cola 500ml",
      "sku": "CC500",
      "barcode": "**********",
      "category": {
        "id": 2,
        "name": "Soft Drinks",
        "full_path": "Beverages > Soft Drinks"
      },
      "buying_price": 20.00,
      "selling_price": 25.00,
      "pos_price": 25.00,
      "current_stock": 100.0,
      "min_stock_level": 10.0,
      "unit_label": "bottle",
      "allow_discount": true,
      "max_discount_percent": 10.00,
      "quick_sale_enabled": true,
      "pos_button_color": "#4CAF50",
      "is_active": true,
      "created_at": "2025-09-21T16:30:00Z"
    }
  ]
}
```

### Create Product
**POST** `/api/v1/inventory/products/`

**Request:**
```json
{
  "name": "Red Bull 250ml",
  "sku": "RB250",
  "barcode": "9876543210",
  "category": 3,
  "buying_price": 80.00,
  "selling_price": 120.00,
  "pos_price": 120.00,
  "current_stock": 50.0,
  "min_stock_level": 5.0,
  "unit_label": "can",
  "allow_discount": false,
  "max_discount_percent": 0.00,
  "quick_sale_enabled": true,
  "pos_button_color": "#FF5722"
}
```

**Response:**
```json
{
  "id": 68,
  "name": "Red Bull 250ml",
  "sku": "RB250",
  "barcode": "9876543210",
  "category": {
    "id": 3,
    "name": "Energy Drinks",
    "full_path": "Beverages > Energy Drinks"
  },
  "buying_price": 80.00,
  "selling_price": 120.00,
  "pos_price": 120.00,
  "current_stock": 50.0,
  "min_stock_level": 5.0,
  "unit_label": "can",
  "allow_discount": false,
  "max_discount_percent": 0.00,
  "quick_sale_enabled": true,
  "pos_button_color": "#FF5722",
  "is_active": true,
  "created_at": "2025-09-21T16:30:00Z"
}
```

### Update Product
**PUT** `/api/v1/inventory/products/{product_id}/`

**Request:** (Same as create, all fields)

**PATCH** `/api/v1/inventory/products/{product_id}/`

**Request:** (Partial update)
```json
{
  "pos_price": 125.00,
  "current_stock": 75.0
}
```

**Response:** (Same as create response with updated values)

### Delete Product
**DELETE** `/api/v1/inventory/products/{product_id}/`

**Response:**
```json
{
  "detail": "Product deleted successfully"
}
```

---

## 📊 Reports APIs

### Daily Sales Report
**GET** `/inventory/pos/api/reports/?type=daily&date=2025-09-21`

**Response:**
```json
{
  "success": true,
  "report": {
    "date": "2025-09-21",
    "total_sales": 1250.00,
    "total_transactions": 25,
    "cash_sales": 800.00,
    "credit_sales": 300.00,
    "card_sales": 150.00
  }
}
```

### Low Stock Alerts
**GET** `/inventory/pos/api/reports/?type=low_stock&threshold=10`

**Response:**
```json
{
  "success": true,
  "alerts": [
    {
      "product_id": 67,
      "product_name": "Coca Cola 500ml",
      "current_stock": 8.0,
      "min_stock_level": 10.0,
      "stock_status": "low"
    }
  ]
}
```

---

## ⚠️ Error Responses

All APIs return consistent error format:

**400 Bad Request:**
```json
{
  "success": false,
  "error": "Validation error message"
}
```

**401 Unauthorized:**
```json
{
  "detail": "Authentication credentials were not provided."
}
```

**404 Not Found:**
```json
{
  "success": false,
  "error": "Product not found"
}
```

**500 Internal Server Error:**
```json
{
  "success": false,
  "error": "Internal server error description"
}
```

---

## 🔄 Common HTTP Status Codes

- **200** - Success
- **201** - Created
- **400** - Bad Request (validation errors)
- **401** - Unauthorized (missing/invalid token)
- **404** - Not Found
- **500** - Internal Server Error

---

## 📱 Recommended Mobile App Flow

1. **Authentication** → `POST /api/v1/accounts/login/`
2. **Get Products** → `GET /inventory/pos/api/products/`
3. **Get Cart** → `GET /inventory/cart/api/`
4. **Add Items** → `POST /inventory/cart/api/quick-add/`
5. **Checkout** → `POST /inventory/cart/api/checkout/`
6. **Repeat** → Next transaction automatically starts fresh