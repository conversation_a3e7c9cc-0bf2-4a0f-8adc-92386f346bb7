#!/usr/bin/env python3
"""
Comprehensive Pakistani Grocery & Department Store Category Population Script
Based on major Pakistani retail chains like Metro, Carrefour, Imtiaz, Al-Fatah, etc.
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import Category
from django.db import transaction

User = get_user_model()

# Comprehensive Pakistani Market Category Structure
PAKISTANI_CATEGORIES = {
    "🍽️ Food & Grocery": {
        "description": "Complete food and grocery items for daily needs",
        "sort_order": 1,
        "subcategories": {
            "🌾 Grains & Pulses": {
                "description": "Staple grains, rice, lentils and pulses",
                "sort_order": 1,
                "items": [
                    "Basmati Rice", "Sella Rice", "Steam Rice", "Brown Rice", "Biryani Rice",
                    "Wheat Flour", "Maida", "Suji/Semolina", "Besan", "Corn Flour",
                    "Masoor Dal", "Moong Dal", "Chana Dal", "Toor Dal", "Urad Dal",
                    "White Chana", "Black Chana", "Rajma", "Lobia", "Mash Dal"
                ]
            },
            "🥛 Dairy & Eggs": {
                "description": "Fresh dairy products and eggs",
                "sort_order": 2,
                "items": [
                    "Fresh Milk", "UHT Milk", "Cream", "Yogurt", "Lassi",
                    "Cheese Slices", "Cheddar Cheese", "Cottage Cheese", "Butter",
                    "Ghee", "Farm Eggs", "Brown Eggs", "Desi Eggs"
                ]
            },
            "🥩 Meat & Seafood": {
                "description": "Fresh and frozen meat and seafood",
                "sort_order": 3,
                "items": [
                    "Chicken Whole", "Chicken Pieces", "Mutton", "Beef", "Keema",
                    "Chicken Sausages", "Chicken Nuggets", "Fish Fresh", "Prawns",
                    "Crab", "Pomfret", "Rohu", "Tilapia", "Frozen Fish"
                ]
            },
            "🍎 Fresh Fruits": {
                "description": "Seasonal and imported fresh fruits",
                "sort_order": 4,
                "items": [
                    "Apples", "Bananas", "Oranges", "Mangoes", "Grapes",
                    "Pomegranate", "Guava", "Papaya", "Pineapple", "Watermelon",
                    "Melon", "Dates", "Strawberries", "Kiwi", "Avocado"
                ]
            },
            "🥬 Fresh Vegetables": {
                "description": "Daily fresh vegetables and herbs",
                "sort_order": 5,
                "items": [
                    "Onions", "Potatoes", "Tomatoes", "Garlic", "Ginger",
                    "Green Chilies", "Okra", "Spinach", "Cabbage", "Cauliflower",
                    "Carrots", "Peas", "Beans", "Brinjal", "Bitter Gourd",
                    "Bottle Gourd", "Cucumber", "Capsicum", "Mint", "Coriander"
                ]
            },
            "🌶️ Spices & Seasonings": {
                "description": "Traditional Pakistani spices and masalas",
                "sort_order": 6,
                "items": [
                    "Red Chili Powder", "Turmeric", "Cumin Seeds", "Coriander Seeds",
                    "Garam Masala", "Biryani Masala", "Chicken Masala", "Meat Masala",
                    "Black Pepper", "Cardamom", "Cinnamon", "Cloves", "Bay Leaves",
                    "Fenugreek Seeds", "Mustard Seeds", "Fennel Seeds", "Salt"
                ]
            },
            "🍞 Bakery & Bread": {
                "description": "Fresh bakery items and bread",
                "sort_order": 7,
                "items": [
                    "White Bread", "Brown Bread", "Naan", "Roti", "Kulcha",
                    "Biscuits", "Rusks", "Cake", "Pastries", "Donuts",
                    "Croissants", "Buns", "Pizza Base", "Pita Bread"
                ]
            },
            "🥫 Packaged Foods": {
                "description": "Canned, frozen and packaged food items",
                "sort_order": 8,
                "items": [
                    "Canned Tomatoes", "Tomato Paste", "Ketchup", "Mayonnaise",
                    "Pasta", "Noodles", "Rice Vermicelli", "Cornflakes",
                    "Oats", "Honey", "Jam", "Pickle", "Papad", "Frozen Vegetables"
                ]
            }
        }
    },
    "🧴 Personal Care & Health": {
        "description": "Personal hygiene, health and beauty products",
        "sort_order": 2,
        "subcategories": {
            "🧼 Bath & Body": {
                "description": "Soaps, body wash and personal hygiene",
                "sort_order": 1,
                "items": [
                    "Bath Soap", "Body Wash", "Shampoo", "Conditioner",
                    "Hair Oil", "Body Lotion", "Hand Wash", "Face Wash",
                    "Scrubs", "Antiseptic", "Dettol", "Savlon"
                ]
            },
            "🦷 Oral Care": {
                "description": "Dental hygiene and oral care products",
                "sort_order": 2,
                "items": [
                    "Toothpaste", "Toothbrush", "Mouthwash", "Dental Floss",
                    "Tongue Cleaner", "Denture Care", "Whitening Products"
                ]
            },
            "💄 Beauty & Cosmetics": {
                "description": "Makeup, skincare and beauty products",
                "sort_order": 3,
                "items": [
                    "Foundation", "Lipstick", "Mascara", "Eyeliner", "Kajal",
                    "Face Cream", "Sunscreen", "Moisturizer", "Face Mask",
                    "Nail Polish", "Perfume", "Deodorant", "Talcum Powder"
                ]
            },
            "💊 Health & Medicine": {
                "description": "Basic health and medical supplies",
                "sort_order": 4,
                "items": [
                    "Pain Relief", "Cough Syrup", "Vitamins", "First Aid",
                    "Bandages", "Thermometer", "Blood Pressure Monitor",
                    "Glucose Monitor", "Antiseptic Cream", "Ointments"
                ]
            }
        }
    },
    "🏠 Home & Kitchen": {
        "description": "Household items, kitchen appliances and home decor",
        "sort_order": 3,
        "subcategories": {
            "🍳 Cookware & Kitchenware": {
                "description": "Cooking pots, pans and kitchen utensils",
                "sort_order": 1,
                "items": [
                    "Pressure Cooker", "Non-stick Pans", "Steel Pots", "Tawa",
                    "Karahi", "Cooking Spoons", "Spatula", "Ladle", "Knives",
                    "Cutting Board", "Mixer Grinder", "Blender", "Food Processor"
                ]
            },
            "🍽️ Dinnerware & Glassware": {
                "description": "Plates, glasses and dining accessories",
                "sort_order": 2,
                "items": [
                    "Dinner Plates", "Bowls", "Cups", "Glasses", "Mugs",
                    "Serving Dishes", "Trays", "Cutlery Set", "Steel Plates",
                    "Melamine Ware", "Glass Set", "Tea Set"
                ]
            },
            "🧽 Cleaning Supplies": {
                "description": "Household cleaning and maintenance products",
                "sort_order": 3,
                "items": [
                    "Detergent Powder", "Liquid Detergent", "Fabric Softener",
                    "Dishwashing Liquid", "Floor Cleaner", "Glass Cleaner",
                    "Toilet Cleaner", "Air Freshener", "Naphthalene Balls",
                    "Sponges", "Scrubbers", "Brooms", "Mops"
                ]
            },
            "💡 Home Appliances": {
                "description": "Small and large home appliances",
                "sort_order": 4,
                "items": [
                    "Electric Kettle", "Rice Cooker", "Microwave", "Toaster",
                    "Iron", "Vacuum Cleaner", "Fan", "Heater", "AC",
                    "Refrigerator", "Washing Machine", "Water Dispenser"
                ]
            },
            "🛋️ Home Decor": {
                "description": "Decorative items and furniture accessories",
                "sort_order": 5,
                "items": [
                    "Cushions", "Curtains", "Bed Sheets", "Pillows", "Blankets",
                    "Rugs", "Carpets", "Wall Frames", "Vases", "Candles",
                    "Table Cloth", "Doormats", "Storage Boxes"
                ]
            }
        }
    },
    "👕 Clothing & Fashion": {
        "description": "Apparel and fashion accessories for all ages",
        "sort_order": 4,
        "subcategories": {
            "👨 Men's Clothing": {
                "description": "Men's apparel and accessories",
                "sort_order": 1,
                "items": [
                    "Shirts", "T-Shirts", "Pants", "Jeans", "Kurta", "Shalwar",
                    "Suits", "Ties", "Belts", "Socks", "Underwear", "Pajamas",
                    "Sherwanis", "Waistcoats", "Caps", "Handkerchiefs"
                ]
            },
            "👩 Women's Clothing": {
                "description": "Women's apparel and accessories",
                "sort_order": 2,
                "items": [
                    "Kameez", "Dupatta", "Shalwar", "Trousers", "Jeans", "Skirts",
                    "Tops", "Blouses", "Sarees", "Lehenga", "Suits", "Hijab",
                    "Scarves", "Undergarments", "Nightwear", "Formal Wear"
                ]
            },
            "👶 Kids Clothing": {
                "description": "Children's clothing and accessories",
                "sort_order": 3,
                "items": [
                    "Baby Clothes", "School Uniforms", "Casual Wear", "Party Wear",
                    "Shoes", "Socks", "Caps", "Bibs", "Diapers", "Baby Care",
                    "Toys", "School Bags", "Water Bottles"
                ]
            },
            "👟 Footwear": {
                "description": "Shoes, sandals and footwear accessories",
                "sort_order": 4,
                "items": [
                    "Formal Shoes", "Casual Shoes", "Sports Shoes", "Sandals",
                    "Slippers", "Boots", "Heels", "Flats", "Chappals",
                    "Khussa", "Kolhapuri", "Shoe Polish", "Shoe Laces"
                ]
            }
        }
    },
    "📱 Electronics & Tech": {
        "description": "Electronic devices, gadgets and accessories",
        "sort_order": 5,
        "subcategories": {
            "📱 Mobile & Accessories": {
                "description": "Mobile phones and related accessories",
                "sort_order": 1,
                "items": [
                    "Smartphones", "Feature Phones", "Mobile Covers", "Screen Guards",
                    "Chargers", "Power Banks", "Earphones", "Bluetooth Speakers",
                    "Memory Cards", "SIM Cards", "Mobile Stands"
                ]
            },
            "💻 Computing": {
                "description": "Computers, laptops and accessories",
                "sort_order": 2,
                "items": [
                    "Laptops", "Desktops", "Tablets", "Keyboards", "Mouse",
                    "Speakers", "Webcam", "USB Cables", "Hard Drives",
                    "Printers", "Scanners", "Software"
                ]
            },
            "📺 Entertainment": {
                "description": "TV, audio and entertainment devices",
                "sort_order": 3,
                "items": [
                    "LED TV", "Smart TV", "Home Theater", "Sound Systems",
                    "Gaming Consoles", "DVD Players", "Set-top Box",
                    "Streaming Devices", "Headphones", "Music Systems"
                ]
            }
        }
    },
    "📚 Books & Stationery": {
        "description": "Educational materials, books and office supplies",
        "sort_order": 6,
        "subcategories": {
            "📖 Books": {
                "description": "Educational and recreational books",
                "sort_order": 1,
                "items": [
                    "Textbooks", "Novels", "Islamic Books", "Children's Books",
                    "Dictionaries", "Reference Books", "Magazines", "Newspapers",
                    "Comics", "Poetry Books", "Cooking Books"
                ]
            },
            "✏️ Stationery": {
                "description": "Writing materials and office supplies",
                "sort_order": 2,
                "items": [
                    "Pens", "Pencils", "Notebooks", "Copy Books", "Files",
                    "Staplers", "Scissors", "Glue", "Tape", "Rulers",
                    "Erasers", "Sharpeners", "Markers", "Highlighters"
                ]
            }
        }
    },
    "🚗 Automotive": {
        "description": "Car care products and automotive accessories",
        "sort_order": 7,
        "subcategories": {
            "⛽ Car Care": {
                "description": "Vehicle maintenance and care products",
                "sort_order": 1,
                "items": [
                    "Engine Oil", "Brake Oil", "Car Shampoo", "Car Polish",
                    "Tire Cleaner", "Air Fresheners", "Car Covers", "Floor Mats",
                    "Seat Covers", "Steering Covers", "Car Accessories"
                ]
            }
        }
    },
    "🎉 Party & Gifts": {
        "description": "Party supplies, gifts and seasonal items",
        "sort_order": 8,
        "subcategories": {
            "🎈 Party Supplies": {
                "description": "Birthday and celebration party items",
                "sort_order": 1,
                "items": [
                    "Balloons", "Party Hats", "Banners", "Candles", "Cake Boxes",
                    "Disposable Plates", "Plastic Cups", "Napkins", "Streamers",
                    "Gift Wrapping", "Greeting Cards", "Party Games"
                ]
            },
            "🎁 Gifts & Souvenirs": {
                "description": "Gift items and memorable souvenirs",
                "sort_order": 2,
                "items": [
                    "Gift Sets", "Handicrafts", "Decorative Items", "Perfume Sets",
                    "Jewelry Boxes", "Photo Frames", "Clocks", "Plants",
                    "Artificial Flowers", "Traditional Crafts"
                ]
            }
        }
    },
    "🌱 Organic & Health Foods": {
        "description": "Organic, health and specialty food products",
        "sort_order": 9,
        "subcategories": {
            "🌿 Organic Products": {
                "description": "Certified organic and natural products",
                "sort_order": 1,
                "items": [
                    "Organic Rice", "Organic Vegetables", "Organic Fruits",
                    "Organic Spices", "Organic Oil", "Organic Flour",
                    "Organic Honey", "Organic Tea", "Natural Products"
                ]
            },
            "💪 Health Foods": {
                "description": "Nutritional and dietary supplements",
                "sort_order": 2,
                "items": [
                    "Protein Powder", "Energy Bars", "Dry Fruits", "Nuts",
                    "Seeds", "Quinoa", "Chia Seeds", "Flax Seeds",
                    "Herbal Teas", "Green Tea", "Supplements"
                ]
            }
        }
    },
    "🏪 Local Specialties": {
        "description": "Traditional Pakistani specialties and regional products",
        "sort_order": 10,
        "subcategories": {
            "🍰 Traditional Sweets": {
                "description": "Pakistani mithai and traditional sweets",
                "sort_order": 1,
                "items": [
                    "Gulab Jamun", "Rasgulla", "Jalebi", "Barfi", "Halwa",
                    "Laddu", "Kulfi", "Falooda", "Kheer", "Seviyan",
                    "Gajar Halwa", "Sohan", "Petha", "Milk Cake"
                ]
            },
            "🥘 Ready-to-Eat": {
                "description": "Traditional Pakistani ready meals",
                "sort_order": 2,
                "items": [
                    "Biryani", "Pulao", "Karahi", "Nihari", "Haleem",
                    "Qorma", "Kebabs", "Samosas", "Spring Rolls", "Pakoras",
                    "Chaat Items", "Traditional Curries"
                ]
            },
            "🌾 Regional Products": {
                "description": "Specialty products from different regions",
                "sort_order": 3,
                "items": [
                    "Multani Mitti", "Rock Salt", "Pink Salt", "Kashmiri Tea",
                    "Hunza Dry Fruits", "Swat Honey", "Skardu Apricots",
                    "Balochi Dates", "Sindhi Pickles", "Punjabi Masalas"
                ]
            }
        }
    }
}

def populate_categories():
    """Populate the database with comprehensive Pakistani market categories"""
    print("🇵🇰 Populating Pakistani Grocery & Department Store Categories...")

    # Get or create admin user
    user = None
    try:
        # Try to get the first superuser
        user = User.objects.filter(is_staff=True, is_superuser=True).first()
        if not user:
            # Get the first user
            user = User.objects.first()
            if not user:
                # Create a default user
                user = User.objects.create_user(
                    mobile_number='03001234567',
                    first_name='Admin',
                    last_name='User',
                    is_staff=True,
                    is_superuser=True
                )
                user.set_password('admin123')
                user.save()
                print(f"✅ Created admin user: {user.mobile_number}")
            else:
                print(f"✅ Using existing user: {user.mobile_number}")
        else:
            print(f"✅ Using admin user: {user.mobile_number}")
    except Exception as e:
        print(f"❌ Error with user setup: {e}")
        return

    created_count = 0
    updated_count = 0

    try:
        with transaction.atomic():
            print(f"\n📊 Processing {len(PAKISTANI_CATEGORIES)} main categories...")

            for main_cat_name, main_cat_data in PAKISTANI_CATEGORIES.items():
                # Create or get main category
                main_category, created = Category.objects.get_or_create(
                    owner=user,
                    name=main_cat_name,
                    parent=None,
                    defaults={
                        'description': main_cat_data['description'],
                        'sort_order': main_cat_data['sort_order'],
                        'is_active': True
                    }
                )

                if created:
                    created_count += 1
                    print(f"✅ Created main category: {main_cat_name}")
                else:
                    # Update description and sort order if changed
                    if (main_category.description != main_cat_data['description'] or
                        main_category.sort_order != main_cat_data['sort_order']):
                        main_category.description = main_cat_data['description']
                        main_category.sort_order = main_cat_data['sort_order']
                        main_category.save()
                        updated_count += 1
                    print(f"📝 Updated main category: {main_cat_name}")

                # Process subcategories
                if 'subcategories' in main_cat_data:
                    print(f"  📂 Processing {len(main_cat_data['subcategories'])} subcategories...")

                    for sub_cat_name, sub_cat_data in main_cat_data['subcategories'].items():
                        # Create or get subcategory
                        sub_category, created = Category.objects.get_or_create(
                            owner=user,
                            name=sub_cat_name,
                            parent=main_category,
                            defaults={
                                'description': sub_cat_data['description'],
                                'sort_order': sub_cat_data['sort_order'],
                                'is_active': True
                            }
                        )

                        if created:
                            created_count += 1
                            print(f"    ✅ Created subcategory: {sub_cat_name}")
                        else:
                            # Update if needed
                            if (sub_category.description != sub_cat_data['description'] or
                                sub_category.sort_order != sub_cat_data['sort_order']):
                                sub_category.description = sub_cat_data['description']
                                sub_category.sort_order = sub_cat_data['sort_order']
                                sub_category.save()
                                updated_count += 1
                            print(f"    📝 Updated subcategory: {sub_cat_name}")

                        # Process individual items as sub-subcategories if needed
                        if 'items' in sub_cat_data and len(sub_cat_data['items']) > 0:
                            print(f"      📄 Found {len(sub_cat_data['items'])} product categories")
                            # We won't create individual items as categories since they should be products
                            # But we'll note them for future product population

            print(f"\n🎉 Category population completed!")
            print(f"📊 Statistics:")
            print(f"   ✅ Created: {created_count} categories")
            print(f"   📝 Updated: {updated_count} categories")
            print(f"   📂 Total categories in system: {Category.objects.filter(owner=user).count()}")

            # Generate tree structure for verification
            print(f"\n🌳 Category Tree Structure:")
            tree_data = Category.get_tree_data(user, include_products_count=False)
            for root in tree_data:
                print(f"📁 {root['name']} (Level {root['level']})")
                for child in root['children']:
                    print(f"   📂 {child['name']} (Level {child['level']})")
                    for grandchild in child['children']:
                        print(f"      📄 {grandchild['name']} (Level {grandchild['level']})")

            print(f"\n✨ Pakistani market categories successfully populated!")
            print(f"🛒 Ready for grocery and department store operations!")

    except Exception as e:
        print(f"❌ Error populating categories: {e}")
        raise

if __name__ == "__main__":
    populate_categories()