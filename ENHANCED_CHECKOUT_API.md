# 🛒 Enhanced Checkout APIs for Mobile POS

## Overview
Complete checkout solution with **all POS details** for mobile receipt generation, customer information, itemized breakdown, taxes, discounts, and payment details.

**Base URL**: `http://*************:8002/inventory/cart/api/`

---

## 🔍 **Step 1: Checkout Preview** (Recommended First)

**GET** `/checkout/preview/`

Get complete checkout breakdown before finalizing payment.

### Response:
```json
{
  "success": true,
  "preview": {
    "transaction": {
      "id": 123,
      "transaction_number": "TXN-2025-001",
      "notes": "Special order"
    },
    "customer": {
      "id": 45,
      "name": "<PERSON>",
      "mobile_number": "1234567890",
      "available_credit": 750.00
    },
    "items": [
      {
        "id": 1,
        "product_id": 67,
        "product_name": "Coca Cola 500ml",
        "sku": "CC500",
        "quantity": 2.0,
        "unit_label": "bottle",
        "unit_price": 25.00,
        "original_price": 25.00,
        "discount_percent": 0.00,
        "discount_amount": 0.00,
        "tax_rate": 0.00,
        "tax_amount": 0.00,
        "line_total": 50.00
      },
      {
        "id": 2,
        "product_id": 68,
        "product_name": "Lays Chips 50g",
        "sku": "LY50",
        "quantity": 3.0,
        "unit_label": "pack",
        "unit_price": 15.00,
        "original_price": 15.00,
        "discount_percent": 10.00,
        "discount_amount": 4.50,
        "tax_rate": 5.00,
        "tax_amount": 2.03,
        "line_total": 42.53
      }
    ],
    "totals": {
      "items_count": 2,
      "total_quantity": 5.0,
      "subtotal": 95.00,
      "discount_total": 4.50,
      "tax_total": 2.03,
      "total_amount": 92.53
    },
    "payment_options": {
      "can_use_cash": true,
      "can_use_credit": true,
      "can_use_card": true,
      "required_amount": 92.53,
      "available_credit": 750.00
    }
  }
}
```

---

## 💳 **Step 2: Complete Checkout** (Final Payment)

**POST** `/checkout/`

Complete checkout with payment and get full receipt details.

### Request:
```json
{
  "cash_amount": 100.00,
  "credit_amount": 0.00,
  "card_amount": 0.00
}
```

### Response (Complete Receipt):
```json
{
  "success": true,
  "message": "Checkout completed successfully",
  "receipt": {
    "transaction": {
      "id": 123,
      "transaction_number": "TXN-2025-001",
      "status": "completed",
      "completed_at": "2025-09-21T16:30:00Z",
      "notes": "Special order"
    },
    "business": {
      "shop_name": "Channab Milk Point",
      "mobile_number": "***********",
      "business_type": "business"
    },
    "customer": {
      "id": 45,
      "name": "John Doe",
      "mobile_number": "1234567890",
      "credit_limit": 1000.00,
      "current_balance": 250.00,
      "available_credit": 750.00
    },
    "items": [
      {
        "id": 1,
        "product_id": 67,
        "product_name": "Coca Cola 500ml",
        "sku": "CC500",
        "barcode": "1234567890",
        "quantity": 2.0,
        "unit_label": "bottle",
        "unit_price": 25.00,
        "original_price": 25.00,
        "discount_percent": 0.00,
        "discount_amount": 0.00,
        "tax_rate": 0.00,
        "tax_amount": 0.00,
        "line_total": 50.00,
        "line_subtotal": 50.00
      },
      {
        "id": 2,
        "product_id": 68,
        "product_name": "Lays Chips 50g",
        "sku": "LY50",
        "barcode": "9876543210",
        "quantity": 3.0,
        "unit_label": "pack",
        "unit_price": 15.00,
        "original_price": 15.00,
        "discount_percent": 10.00,
        "discount_amount": 4.50,
        "tax_rate": 5.00,
        "tax_amount": 2.03,
        "line_total": 42.53,
        "line_subtotal": 45.00
      }
    ],
    "totals": {
      "items_count": 2,
      "total_quantity": 5.0,
      "subtotal": 95.00,
      "discount_total": 4.50,
      "tax_total": 2.03,
      "total_amount": 92.53
    },
    "payment": {
      "cash_amount": 100.00,
      "credit_amount": 0.00,
      "card_amount": 0.00,
      "total_paid": 100.00,
      "change_amount": 7.47,
      "payment_method": "cash"
    },
    "receipt_footer": {
      "thank_you_message": "Thank you for your business!",
      "return_policy": "Items can be returned within 7 days with receipt.",
      "generated_at": "2025-09-21T16:30:00Z"
    }
  }
}
```

---

## 📱 **Mobile App Checkout Flow**

### 1. **Cart Ready** → Show Checkout Button
```dart
// User has items in cart, show "Proceed to Checkout"
```

### 2. **Checkout Preview** → Show Payment Screen
```dart
GET /inventory/cart/api/checkout/preview/

// Display:
// - Itemized list with prices, discounts, taxes
// - Customer info (if selected)
// - Total breakdown
// - Payment options (cash/credit/card availability)
```

### 3. **Payment Selection** → User Chooses Payment
```dart
// User selects:
// - Cash: Enter amount
// - Credit: Check available credit
// - Card: Enter amount
// - Mixed: Multiple payment methods
```

### 4. **Final Checkout** → Complete Transaction
```dart
POST /inventory/cart/api/checkout/
{
  "cash_amount": 100.00,
  "credit_amount": 0.00,
  "card_amount": 0.00
}

// Get complete receipt for:
// - Display confirmation screen
// - Print receipt (if printer connected)
// - Send SMS/Email receipt
// - Store transaction locally
```

### 5. **Post-Checkout** → Fresh Start
```dart
// Cart automatically cleared
// Ready for next customer
GET /inventory/cart/api/ // Will return empty cart
```

---

## 🧾 **Receipt Generation Data**

The checkout response provides **everything needed** for mobile receipt:

### **Receipt Header**
- Business name, mobile, type
- Transaction number and date

### **Customer Section**
- Customer name and mobile (if applicable)
- Credit balance info

### **Itemized List**
- Product names, SKUs, barcodes
- Quantities with unit labels
- Individual prices and discounts
- Tax breakdown per item
- Line totals

### **Payment Section**
- Payment method breakdown
- Change amount
- Total paid vs total due

### **Receipt Footer**
- Thank you message
- Return policy
- Generated timestamp

---

## 🎯 **Key Benefits**

1. **Complete POS Data**: All details needed for professional receipts
2. **Two-Step Process**: Preview before final checkout
3. **Payment Validation**: Check credit limits and payment options
4. **Mobile Optimized**: Perfect for Flutter receipt screens
5. **Multi-Payment**: Support cash, credit, card, and mixed payments
6. **Auto Cart Clear**: Ready for next transaction immediately

---

## 💡 **Flutter Implementation Tips**

### **Receipt Screen Layout**
```dart
// Use the receipt response to build:
Widget buildReceiptScreen(Map<String, dynamic> receipt) {
  return Column(
    children: [
      // Business header
      buildBusinessHeader(receipt['business']),

      // Transaction info
      buildTransactionInfo(receipt['transaction']),

      // Customer info (if exists)
      if (receipt['customer'] != null)
        buildCustomerInfo(receipt['customer']),

      // Items list
      buildItemsList(receipt['items']),

      // Totals breakdown
      buildTotalsSection(receipt['totals']),

      // Payment info
      buildPaymentSection(receipt['payment']),

      // Footer
      buildReceiptFooter(receipt['receipt_footer']),
    ],
  );
}
```

### **Error Handling**
```dart
try {
  final response = await checkoutAPI();
  if (response['success']) {
    showReceiptScreen(response['receipt']);
  } else {
    showError(response['error']);
  }
} catch (e) {
  showError('Checkout failed: $e');
}
```

---

## ⚡ **Quick Reference**

| Endpoint | Purpose | When to Use |
|----------|---------|-------------|
| `GET /checkout/preview/` | Show checkout details | Before payment screen |
| `POST /checkout/` | Complete transaction | After payment confirmation |

**Both APIs provide complete POS data for professional mobile receipts!**