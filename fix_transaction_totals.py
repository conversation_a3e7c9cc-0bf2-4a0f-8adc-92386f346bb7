#!/usr/bin/env python3
"""
Fix transaction total calculation discrepancies
"""

import os
import sys
import django
from decimal import Decimal

# Set up Django environment
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import POSTransaction
from django.db import transaction

def fix_transaction_totals():
    print("🔧 Fixing Transaction Total Calculation Discrepancies")
    print("=" * 55)

    User = get_user_model()

    try:
        user = User.objects.get(mobile_number='03478181583')
        print(f"✓ Found user: {user.mobile_number}")
    except User.DoesNotExist:
        print("✗ User not found")
        return

    # Get all transactions for this user
    transactions = POSTransaction.objects.filter(owner=user).order_by('id')

    print(f"\n📊 Analyzing {transactions.count()} transactions...")

    problematic_transactions = []
    fixed_count = 0

    for pos_transaction in transactions:
        print(f"\n🔍 Checking Transaction {pos_transaction.id}: {pos_transaction.transaction_number}")

        # Calculate correct totals from items
        items = pos_transaction.items.all()

        if items.count() == 0:
            print(f"  ⚠️  No items found - skipping")
            continue

        # Calculate item-based totals
        calculated_subtotal = Decimal('0.00')
        total_discount = Decimal('0.00')

        for item in items:
            line_total = item.line_total
            calculated_subtotal += line_total
            total_discount += item.discount_amount
            print(f"    Item: {item.product.pos_name} - Qty: {item.quantity} - Line Total: Rs. {line_total}")

        # Calculate tax (17% on subtotal after discount)
        taxable_amount = calculated_subtotal - total_discount
        calculated_tax = (taxable_amount * Decimal('0.17')).quantize(Decimal('0.01'))
        calculated_total = calculated_subtotal - total_discount + calculated_tax

        print(f"  📋 Calculated Values:")
        print(f"    • Subtotal (from items): Rs. {calculated_subtotal}")
        print(f"    • Discount: Rs. {total_discount}")
        print(f"    • Taxable amount: Rs. {taxable_amount}")
        print(f"    • Tax (17%): Rs. {calculated_tax}")
        print(f"    • Final Total: Rs. {calculated_total}")

        print(f"  📋 Current Transaction Values:")
        print(f"    • Stored Subtotal: Rs. {pos_transaction.subtotal}")
        print(f"    • Stored Tax: Rs. {pos_transaction.tax_total}")
        print(f"    • Stored Total: Rs. {pos_transaction.total_amount}")

        # Check for discrepancies
        subtotal_diff = abs(calculated_subtotal - pos_transaction.subtotal)
        tax_diff = abs(calculated_tax - pos_transaction.tax_total)
        total_diff = abs(calculated_total - pos_transaction.total_amount)

        if subtotal_diff > Decimal('0.01') or tax_diff > Decimal('0.01') or total_diff > Decimal('0.01'):
            print(f"  ❌ DISCREPANCY FOUND!")
            print(f"    • Subtotal diff: Rs. {subtotal_diff}")
            print(f"    • Tax diff: Rs. {tax_diff}")
            print(f"    • Total diff: Rs. {total_diff}")

            problematic_transactions.append({
                'transaction': pos_transaction,
                'calculated_subtotal': calculated_subtotal,
                'calculated_tax': calculated_tax,
                'calculated_total': calculated_total,
                'current_subtotal': pos_transaction.subtotal,
                'current_tax': pos_transaction.tax_total,
                'current_total': pos_transaction.total_amount
            })
        else:
            print(f"  ✅ Calculations are correct")

    print(f"\n📊 Analysis Results:")
    print(f"  • Total transactions analyzed: {transactions.count()}")
    print(f"  • Problematic transactions found: {len(problematic_transactions)}")

    if problematic_transactions:
        print(f"\n🔧 Fixing problematic transactions...")

        with transaction.atomic():
            for problem in problematic_transactions:
                pos_trans = problem['transaction']

                # Update transaction with correct values
                pos_trans.subtotal = problem['calculated_subtotal']
                pos_trans.tax_total = problem['calculated_tax']
                pos_trans.discount_total = Decimal('0.00')  # Already included in line totals
                pos_trans.total_amount = problem['calculated_total']
                pos_trans.save()

                fixed_count += 1
                print(f"  ✅ Fixed transaction {pos_trans.id}: {pos_trans.transaction_number}")
                print(f"    Updated total: Rs. {pos_trans.total_amount}")

        print(f"\n🎉 Successfully fixed {fixed_count} transactions!")

        # Verify fixes
        print(f"\n🔍 Verifying fixes...")
        for problem in problematic_transactions:
            pos_trans = problem['transaction']
            pos_trans.refresh_from_db()
            print(f"  ✓ Transaction {pos_trans.id}: Rs. {pos_trans.total_amount} (was Rs. {problem['current_total']})")

    else:
        print(f"\n✅ All transaction calculations are correct!")

    print(f"\n🎯 Price Discrepancy Fix Complete!")

if __name__ == '__main__':
    fix_transaction_totals()