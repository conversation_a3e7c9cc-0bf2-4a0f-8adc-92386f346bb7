# Cloudflare Request Logging Configuration

## Overview
Enhanced logging system to capture all requests coming through Cloudflare with detailed headers and origin information for both Nginx and Gunicorn/Django.

## ✅ **What's Been Configured**

### **1. Nginx Enhanced Logging**
- **Custom log format** with Cloudflare-specific headers
- **Separate access log**: `/home/<USER>/ChannabPOS/nginx_access.log`
- **Error log**: `/home/<USER>/ChannabPOS/nginx_error.log`

### **2. Gunicorn Enhanced Logging**
- **Enhanced access log format** with Cloudflare headers
- **Combined logging**: All logs to `/home/<USER>/ChannabPOS/gunicorn_logs.log`

### **3. Django Middleware Enhancement**
- **Cloudflare detection** in custom middleware
- **Enhanced request/response logging** with CF headers
- **Real client IP detection** from Cloudflare headers

## 📊 **Log Formats & Examples**

### **Nginx Access Log Format:**
```
$remote_addr - $remote_user [$time_local] "$request" $status $body_bytes_sent
"$http_referer" "$http_user_agent"
cf_ray="$http_cf_ray"
cf_ipcountry="$http_cf_ipcountry"
cf_visitor="$http_cf_visitor"
x_forwarded_for="$http_x_forwarded_for"
x_real_ip="$http_x_real_ip"
cf_connecting_ip="$http_cf_connecting_ip"
request_time=$request_time
upstream_response_time=$upstream_response_time
```

### **Example Nginx Log Entry:**
```
************* - - [19/Sep/2025:19:36:27 +0500] "POST /api/v1/accounts/login/ HTTP/1.1" 200 704 "-" "curl/7.81.0" cf_ray="12345-TEST" cf_ipcountry="US" cf_visitor="-" x_forwarded_for="-" x_real_ip="-" cf_connecting_ip="*******" request_time=1.150 upstream_response_time=1.151
```

### **Gunicorn Access Log Format:**
```
%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s %(L)s %(p)s
cf_ray="%({CF-RAY}i)s"
cf_ipcountry="%({CF-IPCOUNTRY}i)s"
cf_visitor="%({CF-VISITOR}i)s"
cf_connecting_ip="%({CF-CONNECTING-IP}i)s"
x_forwarded_for="%({X-FORWARDED-FOR}i)s"
x_real_ip="%({X-REAL-IP}i)s"
```

### **Example Gunicorn Log Entry:**
```
- - [19/Sep/2025:14:36:27 +0000] "POST /api/v1/accounts/login/ HTTP/1.0" 200 704 "-" "curl/7.81.0" 1148016 1.148016 <172606> cf_ray="12345-TEST" cf_ipcountry="US" cf_visitor="-" cf_connecting_ip="*******" x_forwarded_for="*************" x_real_ip="*************"
```

### **Django Middleware Log Format:**
```
[2025-09-19 14:36:26,682] INFO API-REQUEST: POST /api/v1/accounts/login/ | IP: ******* | User: Anonymous | User-Agent: curl/7.81.0 | Body: {"mobile_number": "***********", "password": "***MASKED***"} | CLOUDFLARE: cf_ray=12345-TEST | cf_country=US | cf_connecting_ip=*******

[2025-09-19 14:36:27,818] INFO API-REQUEST: RESPONSE POST /api/v1/accounts/login/ | Status: 200 | Duration: 1136.83ms | IP: ******* | User: Anonymous | Response: {"success": true, "message": "Login successful", ...} | CLOUDFLARE: cf_ray=12345-TEST | cf_country=US | cf_connecting_ip=*******
```

## 🔍 **Cloudflare Headers Captured**

### **Nginx & Gunicorn:**
- `CF-Ray` - Unique request identifier
- `CF-IPCountry` - Country code of the client
- `CF-Visitor` - Protocol scheme (HTTP/HTTPS)
- `CF-Connecting-IP` - Original client IP address
- `X-Forwarded-For` - Forwarded IP chain
- `X-Real-IP` - Real client IP

### **Django Middleware:**
- `cf_ray` - Cloudflare Ray ID
- `cf_country` - Client country
- `cf_visitor` - Visitor protocol info
- `cf_connecting_ip` - Original client IP

## 📁 **Log File Locations**

```bash
# Nginx logs
/home/<USER>/ChannabPOS/nginx_access.log    # Cloudflare-enhanced access logs
/home/<USER>/ChannabPOS/nginx_error.log     # Nginx error logs

# Gunicorn/Django logs
/home/<USER>/ChannabPOS/gunicorn_logs.log   # Combined app + Cloudflare logs
```

## 🔧 **Features**

### **✅ Cloudflare Detection:**
- Automatic detection of requests coming through Cloudflare
- Enhanced logging for CF requests with additional headers
- Real client IP extraction from Cloudflare headers

### **✅ Enhanced Security:**
- Original client IP tracking
- Country-based request logging
- Request fingerprinting via CF-Ray
- Sensitive data masking in Django logs

### **✅ Performance Monitoring:**
- Request timing at Nginx level
- Upstream response time tracking
- End-to-end request duration in Django

## 📈 **Log Analysis Commands**

### **View Real-time Cloudflare Requests:**
```bash
# Watch all logs for Cloudflare requests
tail -f /home/<USER>/ChannabPOS/nginx_access.log | grep cf_ray

# Watch Django logs for Cloudflare
tail -f /home/<USER>/ChannabPOS/gunicorn_logs.log | grep CLOUDFLARE
```

### **Analyze Cloudflare Traffic:**
```bash
# Count requests by country
grep cf_ipcountry /home/<USER>/ChannabPOS/nginx_access.log | cut -d'"' -f8 | sort | uniq -c

# Find requests by CF-Ray ID
grep "cf_ray=\"12345-TEST\"" /home/<USER>/ChannabPOS/*.log

# Check request timing for performance
grep upstream_response_time /home/<USER>/ChannabPOS/nginx_access.log
```

### **Monitor API Usage:**
```bash
# API requests through Cloudflare
grep "api/" /home/<USER>/ChannabPOS/nginx_access.log | grep cf_ray

# Failed API requests through Cloudflare
grep "api/" /home/<USER>/ChannabPOS/gunicorn_logs.log | grep "Status: [45]" | grep CLOUDFLARE
```

## 🔄 **Service Management**

### **Restart Services to Apply Changes:**
```bash
sudo systemctl restart nginx
sudo systemctl restart gunicorn
```

### **Test Configuration:**
```bash
# Test Nginx config
sudo nginx -t

# Check service status
sudo systemctl status nginx
sudo systemctl status gunicorn
```

## 🧪 **Testing Cloudflare Logging**

### **Simulate Cloudflare Request:**
```bash
curl -H "Host: zayyrah.com" \
     -H "CF-Ray: 12345-TEST" \
     -H "CF-IPCountry: US" \
     -H "CF-Connecting-IP: *******" \
     -X POST http://*************/api/v1/accounts/login/ \
     -H "Content-Type: application/json" \
     -d '{"mobile_number": "***********", "password": "testpass"}'
```

### **Verify Logging:**
```bash
# Check all log files for the test
grep "12345-TEST" /home/<USER>/ChannabPOS/*.log
```

## 🚨 **Monitoring & Alerts**

### **Key Metrics to Monitor:**
- **CF-Ray frequency** - Request volume through Cloudflare
- **Geographic distribution** - Country-based traffic analysis
- **Response times** - Performance impact of Cloudflare
- **Error rates** - Failed requests through CF

### **Security Monitoring:**
- **Suspicious countries** - Unexpected geographic sources
- **Missing CF headers** - Direct server access attempts
- **High request volumes** - Potential DDoS through Cloudflare

## ✅ **Status: Fully Configured & Tested**

All requests coming through Cloudflare will now be logged with comprehensive details across Nginx, Gunicorn, and Django layers, providing complete visibility into traffic patterns and origins.