#!/bin/bash
# Log rotation script for gunicorn_logs.log

LOG_FILE="/home/<USER>/ChannabPOS/gunicorn_logs.log"
MAX_SIZE=100M  # Maximum log file size

# Check if log file exists
if [ ! -f "$LOG_FILE" ]; then
    echo "Log file does not exist: $LOG_FILE"
    exit 1
fi

# Get file size in bytes
FILE_SIZE=$(stat -c%s "$LOG_FILE")
MAX_SIZE_BYTES=$((100 * 1024 * 1024))  # 100MB in bytes

# Check if file size exceeds maximum
if [ $FILE_SIZE -gt $MAX_SIZE_BYTES ]; then
    echo "Log file size ($FILE_SIZE bytes) exceeds maximum ($MAX_SIZE_BYTES bytes)"

    # Create backup with timestamp
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="${LOG_FILE}.${TIMESTAMP}"

    # Move current log to backup
    mv "$LOG_FILE" "$BACKUP_FILE"

    # Create new empty log file with correct permissions
    touch "$LOG_FILE"
    chown ubuntu:www-data "$LOG_FILE"
    chmod 664 "$LOG_FILE"

    # Restart Gunicorn to start writing to new log file
    systemctl restart gunicorn

    # Compress backup file
    gzip "$BACKUP_FILE"

    echo "Log rotated successfully. Backup created: ${BACKUP_FILE}.gz"

    # Optional: Remove backups older than 30 days
    find /home/<USER>/ChannabPOS -name "gunicorn_logs.log.*.gz" -mtime +30 -delete

else
    echo "Log file size ($FILE_SIZE bytes) is within limits"
fi