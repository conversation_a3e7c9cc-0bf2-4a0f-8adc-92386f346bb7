# 📦 ChannabPOS Product Detail APIs - Complete Documentation

## 🎯 Overview

This is the **complete documentation** for all Product Detail APIs in the ChannabPOS system. These APIs provide comprehensive product information, stock management, and sales analytics matching the web interface functionality.

**🌐 Base URL**: `http://*************:8003/api/v1/inventory/products/`
**🔐 Authentication**: Required (<PERSON><PERSON>)
**📅 Last Updated**: September 25, 2025

---

## 🚀 Quick Start

### **NEW! Comprehensive Product Detail API** (Recommended)
```
GET /api/v1/inventory/products/{product_id}/comprehensive_detail/
```

**✅ This single endpoint provides everything you need:**
- Complete product information
- Real-time stock management data
- Sales analytics and history
- Stock valuation and location data
- Related API endpoints

**🎯 Perfect for**: Product detail pages, dashboards, mobile apps

---

## 📋 Complete API Reference

### 1. **Product Information APIs**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/{id}/` | GET | Basic product details |
| `/{id}/comprehensive_detail/` | GET | **🆕 Complete product detail** |

### 2. **Stock Management APIs**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/{id}/stock_summary/` | GET | Stock levels & batch info |
| `/{id}/stock_by_location/` | GET | Stock across locations |
| `/{id}/stock_valuation/` | GET | FIFO/LIFO valuation |
| `/{id}/adjust_stock/` | POST | Manual stock adjustments |
| `/{id}/transfer_stock/` | POST | Transfer between locations |
| `/{id}/reserve_stock/` | POST | Reserve for orders |

### 3. **Sales & Analytics APIs**

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/{id}/sales_history/` | GET | Sales history & analytics |

---

## 🎯 Key Features

### ✅ **What These APIs Provide**

**📊 Real-time Data:**
- Current stock levels across all locations
- Live inventory valuations (FIFO/LIFO/Weighted Average)
- Real-time sales analytics

**📈 Sales Intelligence:**
- Lifetime sales statistics
- Period analysis (today/week/month)
- Recent transaction history
- Top customers for the product

**📦 Stock Management:**
- Batch-level stock tracking
- Multi-location inventory
- Stock movement history
- Expiry date tracking

**🔧 Stock Operations:**
- Manual stock adjustments
- Inter-location transfers
- Stock reservations
- Automated FIFO/LIFO allocation

---

## 🖥️ Frontend Integration Examples

### **React/Next.js Example**
```javascript
import { useState, useEffect } from 'react';

function ProductDetail({ productId }) {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProductDetail();
  }, [productId]);

  const fetchProductDetail = async () => {
    try {
      const response = await fetch(
        `http://*************:8003/api/v1/inventory/products/${productId}/comprehensive_detail/`,
        {
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();

      if (data.success) {
        setProduct(data.data);
      }
    } catch (error) {
      console.error('Error fetching product:', error);
    } finally {
      setLoading(false);
    }
  };

  const adjustStock = async (adjustmentData) => {
    try {
      const response = await fetch(
        `http://*************:8003/api/v1/inventory/products/${productId}/adjust_stock/`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(adjustmentData)
        }
      );

      const result = await response.json();

      if (result.success) {
        // Refresh product data
        fetchProductDetail();
        alert('Stock adjusted successfully!');
      } else {
        alert(`Error: ${result.message}`);
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
    }
  };

  if (loading) return <div>Loading...</div>;

  return (
    <div className="product-detail">
      {/* Product Information */}
      <div className="product-info">
        <h1>{product.product.name}</h1>
        <p>Price: ${product.product.selling_price}</p>
        <p>Category: {product.product.category_name}</p>
        <p>Current Stock: {product.stock_management.summary.current_stock}</p>
        <p>Stock Value: ${product.stock_management.summary.total_stock_value}</p>
      </div>

      {/* Stock Management */}
      <div className="stock-management">
        <h2>Stock Management</h2>
        <div className="stock-entries">
          {product.stock_management.stock_entries.map(entry => (
            <div key={entry.id} className="stock-entry">
              <span>Batch: {entry.batch_number}</span>
              <span>Available: {entry.quantity_available}</span>
              <span>Price: ${entry.purchase_price}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Sales Analytics */}
      <div className="sales-analytics">
        <h2>Sales Analytics</h2>
        <div className="analytics-summary">
          <p>Total Sold: {product.sales_analytics.lifetime.total_quantity_sold}</p>
          <p>Total Revenue: ${product.sales_analytics.lifetime.total_revenue}</p>
          <p>Total Transactions: {product.sales_analytics.lifetime.total_transactions}</p>
        </div>

        <div className="period-analysis">
          <h3>Period Analysis</h3>
          <p>Today: {product.sales_analytics.period_analysis.today.quantity} units</p>
          <p>This Week: {product.sales_analytics.period_analysis.this_week.quantity} units</p>
          <p>This Month: {product.sales_analytics.period_analysis.this_month.quantity} units</p>
        </div>
      </div>

      {/* Stock Actions */}
      <div className="stock-actions">
        <button onClick={() => adjustStock({
          location: 3,
          adjustment_type: 'add',
          quantity: 10,
          reason: 'Stock replenishment'
        })}>
          Add Stock
        </button>
      </div>
    </div>
  );
}

export default ProductDetail;
```

### **Vue.js Example**
```javascript
<template>
  <div class="product-detail" v-if="product">
    <!-- Product Info -->
    <div class="product-header">
      <h1>{{ product.product.name }}</h1>
      <div class="price">${{ product.product.selling_price }}</div>
      <div class="category">{{ product.product.category_name }}</div>
    </div>

    <!-- Stock Summary -->
    <div class="stock-summary">
      <h2>Stock Summary</h2>
      <div class="metrics">
        <div class="metric">
          <label>Current Stock</label>
          <value>{{ product.stock_management.summary.current_stock }}</value>
        </div>
        <div class="metric">
          <label>Stock Value</label>
          <value>${{ product.stock_management.summary.total_stock_value }}</value>
        </div>
        <div class="metric">
          <label>Total Batches</label>
          <value>{{ product.stock_management.summary.total_batches }}</value>
        </div>
      </div>
    </div>

    <!-- Stock Entries -->
    <div class="stock-entries">
      <h3>Stock Batches</h3>
      <div v-for="entry in product.stock_management.stock_entries" :key="entry.id" class="stock-entry">
        <div class="batch-info">
          <span class="batch-number">{{ entry.batch_number }}</span>
          <span class="quantity">{{ entry.quantity_available }} available</span>
          <span class="price">${{ entry.purchase_price }}</span>
        </div>
      </div>
    </div>

    <!-- Sales Analytics -->
    <div class="sales-analytics">
      <h2>Sales Performance</h2>
      <div class="lifetime-stats">
        <div class="stat">
          <label>Total Sold</label>
          <value>{{ product.sales_analytics.lifetime.total_quantity_sold }}</value>
        </div>
        <div class="stat">
          <label>Total Revenue</label>
          <value>${{ product.sales_analytics.lifetime.total_revenue }}</value>
        </div>
        <div class="stat">
          <label>Transactions</label>
          <value>{{ product.sales_analytics.lifetime.total_transactions }}</value>
        </div>
      </div>
    </div>

    <!-- Actions -->
    <div class="actions">
      <button @click="showAdjustStockModal" class="btn btn-primary">Adjust Stock</button>
      <button @click="showTransferModal" class="btn btn-secondary">Transfer Stock</button>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue';

export default {
  name: 'ProductDetail',
  props: {
    productId: {
      type: Number,
      required: true
    }
  },
  setup(props) {
    const product = ref(null);
    const loading = ref(true);

    const fetchProductDetail = async () => {
      try {
        const response = await fetch(
          `http://*************:8003/api/v1/inventory/products/${props.productId}/comprehensive_detail/`,
          {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
              'Content-Type': 'application/json'
            }
          }
        );

        const data = await response.json();

        if (data.success) {
          product.value = data.data;
        } else {
          console.error('API Error:', data.message);
        }
      } catch (error) {
        console.error('Network Error:', error);
      } finally {
        loading.value = false;
      }
    };

    const adjustStock = async (adjustmentData) => {
      try {
        const response = await fetch(
          `http://*************:8003/api/v1/inventory/products/${props.productId}/adjust_stock/`,
          {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(adjustmentData)
          }
        );

        const result = await response.json();

        if (result.success) {
          // Refresh product data
          await fetchProductDetail();
          alert('Stock adjusted successfully!');
        } else {
          alert(`Error: ${result.message}`);
        }
      } catch (error) {
        console.error('Error adjusting stock:', error);
      }
    };

    const showAdjustStockModal = () => {
      // Implementation for showing adjust stock modal
      adjustStock({
        location: 3,
        adjustment_type: 'add',
        quantity: 10,
        reason: 'Manual adjustment'
      });
    };

    const showTransferModal = () => {
      // Implementation for showing transfer modal
    };

    onMounted(() => {
      fetchProductDetail();
    });

    return {
      product,
      loading,
      showAdjustStockModal,
      showTransferModal
    };
  }
};
</script>
```

### **Angular Example**
```typescript
import { Component, OnInit, Input } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';

interface ProductDetailResponse {
  success: boolean;
  data: {
    product: any;
    stock_management: any;
    sales_analytics: any;
    metadata: any;
    related_endpoints: any;
  };
}

@Component({
  selector: 'app-product-detail',
  template: `
    <div class="product-detail" *ngIf="product">
      <!-- Product Information -->
      <div class="product-header">
        <h1>{{ product.product.name }}</h1>
        <div class="product-meta">
          <span class="price">₹{{ product.product.selling_price }}</span>
          <span class="category">{{ product.product.category_name }}</span>
        </div>
      </div>

      <!-- Stock Management -->
      <div class="stock-section">
        <h2>Stock Management</h2>
        <div class="stock-metrics">
          <div class="metric">
            <label>Current Stock</label>
            <span class="value">{{ product.stock_management.summary.current_stock }}</span>
          </div>
          <div class="metric">
            <label>Stock Value</label>
            <span class="value">₹{{ product.stock_management.summary.total_stock_value }}</span>
          </div>
          <div class="metric">
            <label>Low Stock</label>
            <span [class]="product.stock_management.summary.is_low_stock ? 'status danger' : 'status success'">
              {{ product.stock_management.summary.is_low_stock ? 'Yes' : 'No' }}
            </span>
          </div>
        </div>

        <!-- Stock Entries -->
        <div class="stock-entries">
          <h3>Stock Batches</h3>
          <div class="batch-list">
            <div *ngFor="let entry of product.stock_management.stock_entries" class="batch-item">
              <div class="batch-header">
                <span class="batch-number">{{ entry.batch_number }}</span>
                <span class="batch-price">₹{{ entry.purchase_price }}</span>
              </div>
              <div class="batch-details">
                <span>Available: {{ entry.quantity_available }}</span>
                <span>Received: {{ entry.received_date | date }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sales Analytics -->
      <div class="sales-section">
        <h2>Sales Analytics</h2>
        <div class="analytics-grid">
          <div class="analytics-card">
            <h4>Lifetime Performance</h4>
            <div class="stats">
              <div class="stat">
                <label>Total Sold</label>
                <span>{{ product.sales_analytics.lifetime.total_quantity_sold }}</span>
              </div>
              <div class="stat">
                <label>Total Revenue</label>
                <span>₹{{ product.sales_analytics.lifetime.total_revenue }}</span>
              </div>
              <div class="stat">
                <label>Transactions</label>
                <span>{{ product.sales_analytics.lifetime.total_transactions }}</span>
              </div>
            </div>
          </div>

          <div class="analytics-card">
            <h4>Period Analysis</h4>
            <div class="period-stats">
              <div class="period">
                <label>Today</label>
                <span>{{ product.sales_analytics.period_analysis.today.quantity || 0 }} units</span>
              </div>
              <div class="period">
                <label>This Week</label>
                <span>{{ product.sales_analytics.period_analysis.this_week.quantity || 0 }} units</span>
              </div>
              <div class="period">
                <label>This Month</label>
                <span>{{ product.sales_analytics.period_analysis.this_month.quantity || 0 }} units</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="actions">
        <button (click)="openAdjustStockModal()" class="btn btn-primary">
          Adjust Stock
        </button>
        <button (click)="openTransferModal()" class="btn btn-secondary">
          Transfer Stock
        </button>
        <button (click)="refreshData()" class="btn btn-outline">
          Refresh
        </button>
      </div>
    </div>

    <div *ngIf="loading" class="loading">
      Loading product details...
    </div>
  `,
  styleUrls: ['./product-detail.component.css']
})
export class ProductDetailComponent implements OnInit {
  @Input() productId!: number;

  product: any = null;
  loading = true;

  private readonly baseUrl = 'http://*************:8003/api/v1/inventory/products';
  private readonly authToken = localStorage.getItem('auth_token') || '';

  constructor(private http: HttpClient) {}

  ngOnInit(): void {
    this.fetchProductDetail();
  }

  private getHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Authorization': `Bearer ${this.authToken}`,
      'Content-Type': 'application/json'
    });
  }

  async fetchProductDetail(): Promise<void> {
    this.loading = true;

    try {
      const response = await this.http.get<ProductDetailResponse>(
        `${this.baseUrl}/${this.productId}/comprehensive_detail/`,
        { headers: this.getHeaders() }
      ).toPromise();

      if (response?.success) {
        this.product = response.data;
      } else {
        console.error('API Error:', response);
      }
    } catch (error) {
      console.error('Error fetching product detail:', error);
    } finally {
      this.loading = false;
    }
  }

  async adjustStock(adjustmentData: any): Promise<void> {
    try {
      const response = await this.http.post(
        `${this.baseUrl}/${this.productId}/adjust_stock/`,
        adjustmentData,
        { headers: this.getHeaders() }
      ).toPromise();

      if ((response as any).success) {
        // Refresh product data
        await this.fetchProductDetail();
        alert('Stock adjusted successfully!');
      } else {
        alert(`Error: ${(response as any).message}`);
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
      alert('Network error occurred');
    }
  }

  openAdjustStockModal(): void {
    // For demo purposes, directly calling adjust stock
    this.adjustStock({
      location: 3,
      adjustment_type: 'add',
      quantity: 10,
      reason: 'Manual adjustment from UI'
    });
  }

  openTransferModal(): void {
    // Implementation for transfer modal
    console.log('Transfer modal would open here');
  }

  refreshData(): void {
    this.fetchProductDetail();
  }
}
```

---

## 📱 Mobile App Examples

### **React Native Example**
```javascript
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, Alert } from 'react-native';

const ProductDetailScreen = ({ route }) => {
  const { productId } = route.params;
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchProductDetail();
  }, [productId]);

  const fetchProductDetail = async () => {
    try {
      setLoading(true);
      const response = await fetch(
        `http://*************:8003/api/v1/inventory/products/${productId}/comprehensive_detail/`,
        {
          headers: {
            'Authorization': `Bearer ${await getAuthToken()}`,
            'Content-Type': 'application/json'
          }
        }
      );

      const data = await response.json();

      if (data.success) {
        setProduct(data.data);
      } else {
        Alert.alert('Error', data.message || 'Failed to fetch product details');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      Alert.alert('Error', 'Network error occurred');
    } finally {
      setLoading(false);
    }
  };

  const adjustStock = async (adjustmentData) => {
    try {
      const response = await fetch(
        `http://*************:8003/api/v1/inventory/products/${productId}/adjust_stock/`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${await getAuthToken()}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(adjustmentData)
        }
      );

      const result = await response.json();

      if (result.success) {
        Alert.alert('Success', 'Stock adjusted successfully!');
        fetchProductDetail(); // Refresh data
      } else {
        Alert.alert('Error', result.message || 'Failed to adjust stock');
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
      Alert.alert('Error', 'Network error occurred');
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Loading product details...</Text>
      </View>
    );
  }

  if (!product) {
    return (
      <View style={styles.errorContainer}>
        <Text>Failed to load product details</Text>
        <TouchableOpacity onPress={fetchProductDetail} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container}>
      {/* Product Header */}
      <View style={styles.productHeader}>
        <Text style={styles.productName}>{product.product.name}</Text>
        <Text style={styles.productPrice}>₹{product.product.selling_price}</Text>
        <Text style={styles.productCategory}>{product.product.category_name}</Text>
      </View>

      {/* Stock Summary */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Stock Summary</Text>
        <View style={styles.metricsRow}>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Current Stock</Text>
            <Text style={styles.metricValue}>{product.stock_management.summary.current_stock}</Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Stock Value</Text>
            <Text style={styles.metricValue}>₹{product.stock_management.summary.total_stock_value}</Text>
          </View>
          <View style={styles.metric}>
            <Text style={styles.metricLabel}>Batches</Text>
            <Text style={styles.metricValue}>{product.stock_management.summary.total_batches}</Text>
          </View>
        </View>

        {product.stock_management.summary.is_low_stock && (
          <View style={styles.lowStockAlert}>
            <Text style={styles.alertText}>⚠️ Low Stock Alert</Text>
          </View>
        )}
      </View>

      {/* Stock Entries */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Stock Batches</Text>
        {product.stock_management.stock_entries.map(entry => (
          <View key={entry.id} style={styles.batchItem}>
            <View style={styles.batchHeader}>
              <Text style={styles.batchNumber}>{entry.batch_number}</Text>
              <Text style={styles.batchPrice}>₹{entry.purchase_price}</Text>
            </View>
            <View style={styles.batchDetails}>
              <Text style={styles.batchDetail}>Available: {entry.quantity_available}</Text>
              <Text style={styles.batchDetail}>Received: {entry.received_date}</Text>
            </View>
          </View>
        ))}
      </View>

      {/* Sales Analytics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Sales Performance</Text>
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsCard}>
            <Text style={styles.cardTitle}>Lifetime</Text>
            <Text style={styles.cardValue}>
              {product.sales_analytics.lifetime.total_quantity_sold} sold
            </Text>
            <Text style={styles.cardSubtitle}>
              ₹{product.sales_analytics.lifetime.total_revenue} revenue
            </Text>
          </View>
          <View style={styles.analyticsCard}>
            <Text style={styles.cardTitle}>This Month</Text>
            <Text style={styles.cardValue}>
              {product.sales_analytics.period_analysis.this_month.quantity || 0} sold
            </Text>
            <Text style={styles.cardSubtitle}>
              ₹{product.sales_analytics.period_analysis.this_month.revenue || 0} revenue
            </Text>
          </View>
        </View>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionsSection}>
        <TouchableOpacity
          style={[styles.actionButton, styles.primaryButton]}
          onPress={() => adjustStock({
            location: 3,
            adjustment_type: 'add',
            quantity: 10,
            reason: 'Mobile app adjustment'
          })}
        >
          <Text style={styles.buttonText}>Add Stock (+10)</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.secondaryButton]}
          onPress={fetchProductDetail}
        >
          <Text style={styles.buttonTextSecondary}>Refresh Data</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  productHeader: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  productName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  productPrice: {
    fontSize: 20,
    color: '#0da487',
    fontWeight: '600',
    marginTop: 5,
  },
  productCategory: {
    fontSize: 16,
    color: '#666',
    marginTop: 5,
  },
  section: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  metricsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  metric: {
    alignItems: 'center',
    flex: 1,
  },
  metricLabel: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0da487',
    marginTop: 5,
    textAlign: 'center',
  },
  lowStockAlert: {
    backgroundColor: '#fff3cd',
    padding: 10,
    borderRadius: 5,
    marginTop: 15,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  alertText: {
    color: '#856404',
    fontWeight: '600',
  },
  batchItem: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  batchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 5,
  },
  batchNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  batchPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#0da487',
  },
  batchDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  batchDetail: {
    fontSize: 14,
    color: '#666',
  },
  analyticsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  analyticsCard: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 5,
    alignItems: 'center',
  },
  cardTitle: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  cardValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  cardSubtitle: {
    fontSize: 12,
    color: '#0da487',
    marginTop: 5,
  },
  actionsSection: {
    backgroundColor: 'white',
    padding: 20,
    marginBottom: 20,
  },
  actionButton: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  primaryButton: {
    backgroundColor: '#0da487',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#0da487',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonTextSecondary: {
    color: '#0da487',
    fontSize: 16,
    fontWeight: '600',
  },
  retryButton: {
    backgroundColor: '#0da487',
    padding: 10,
    borderRadius: 5,
    marginTop: 10,
  },
  retryButtonText: {
    color: 'white',
    fontWeight: '600',
  },
});

export default ProductDetailScreen;
```

### **Flutter Example**
```dart
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class ProductDetailScreen extends StatefulWidget {
  final int productId;

  const ProductDetailScreen({Key? key, required this.productId}) : super(key: key);

  @override
  State<ProductDetailScreen> createState() => _ProductDetailScreenState();
}

class _ProductDetailScreenState extends State<ProductDetailScreen> {
  Map<String, dynamic>? product;
  bool loading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    fetchProductDetail();
  }

  Future<void> fetchProductDetail() async {
    setState(() {
      loading = true;
      error = null;
    });

    try {
      final response = await http.get(
        Uri.parse('http://*************:8003/api/v1/inventory/products/${widget.productId}/comprehensive_detail/'),
        headers: {
          'Authorization': 'Bearer ${await getAuthToken()}',
          'Content-Type': 'application/json',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['success']) {
          setState(() {
            product = data['data'];
            loading = false;
          });
        } else {
          setState(() {
            error = data['message'] ?? 'Unknown error occurred';
            loading = false;
          });
        }
      } else {
        setState(() {
          error = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
          loading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Network error: $e';
        loading = false;
      });
    }
  }

  Future<void> adjustStock(Map<String, dynamic> adjustmentData) async {
    try {
      final response = await http.post(
        Uri.parse('http://*************:8003/api/v1/inventory/products/${widget.productId}/adjust_stock/'),
        headers: {
          'Authorization': 'Bearer ${await getAuthToken()}',
          'Content-Type': 'application/json',
        },
        body: json.encode(adjustmentData),
      );

      final result = json.decode(response.body);

      if (result['success']) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Stock adjusted successfully!'),
            backgroundColor: Colors.green,
          ),
        );
        // Refresh data
        fetchProductDetail();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${result['message']}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Network error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<String> getAuthToken() async {
    // Implementation to get auth token from secure storage
    return 'your_auth_token_here';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(product?['product']?['name'] ?? 'Product Detail'),
        backgroundColor: const Color(0xFF0da487),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: fetchProductDetail,
          ),
        ],
      ),
      body: loading
          ? const Center(child: CircularProgressIndicator())
          : error != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: $error',
                        style: const TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: fetchProductDetail,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : product == null
                  ? const Center(child: Text('No data available'))
                  : SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Product Header
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    product!['product']['name'],
                                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    '₹${product!['product']['selling_price']}',
                                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                          color: const Color(0xFF0da487),
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    product!['product']['category_name'],
                                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                          color: Colors.grey[600],
                                        ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Stock Summary
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Stock Summary',
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                                    children: [
                                      _buildMetricColumn(
                                        'Current Stock',
                                        '${product!['stock_management']['summary']['current_stock']}',
                                      ),
                                      _buildMetricColumn(
                                        'Stock Value',
                                        '₹${product!['stock_management']['summary']['total_stock_value']}',
                                      ),
                                      _buildMetricColumn(
                                        'Batches',
                                        '${product!['stock_management']['summary']['total_batches']}',
                                      ),
                                    ],
                                  ),

                                  if (product!['stock_management']['summary']['is_low_stock'])
                                    Container(
                                      margin: const EdgeInsets.only(top: 16),
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.orange[50],
                                        border: Border(
                                          left: BorderSide(
                                            color: Colors.orange,
                                            width: 4,
                                          ),
                                        ),
                                      ),
                                      child: Row(
                                        children: const [
                                          Icon(Icons.warning, color: Colors.orange),
                                          SizedBox(width: 8),
                                          Text(
                                            'Low Stock Alert',
                                            style: TextStyle(
                                              color: Colors.orange,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Stock Batches
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Stock Batches',
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(height: 16),
                                  ...product!['stock_management']['stock_entries'].map<Widget>((entry) {
                                    return Container(
                                      margin: const EdgeInsets.only(bottom: 12),
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[50],
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                entry['batch_number'],
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                '₹${entry['purchase_price']}',
                                                style: const TextStyle(
                                                  fontWeight: FontWeight.bold,
                                                  color: Color(0xFF0da487),
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text('Available: ${entry['quantity_available']}'),
                                              Text('Received: ${entry['received_date']}'),
                                            ],
                                          ),
                                        ],
                                      ),
                                    );
                                  }).toList(),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 16),

                          // Sales Analytics
                          Card(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Sales Analytics',
                                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: _buildAnalyticsCard(
                                          'Lifetime Sales',
                                          '${product!['sales_analytics']['lifetime']['total_quantity_sold']} sold',
                                          '₹${product!['sales_analytics']['lifetime']['total_revenue']} revenue',
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: _buildAnalyticsCard(
                                          'This Month',
                                          '${product!['sales_analytics']['period_analysis']['this_month']['quantity'] ?? 0} sold',
                                          '₹${product!['sales_analytics']['period_analysis']['this_month']['revenue'] ?? 0} revenue',
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Action Buttons
                          Column(
                            children: [
                              SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: () => adjustStock({
                                    'location': 3,
                                    'adjustment_type': 'add',
                                    'quantity': 10.0,
                                    'reason': 'Manual adjustment from Flutter app',
                                  }),
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add Stock (+10)'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF0da487),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.all(16),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 12),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: fetchProductDetail,
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('Refresh Data'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: const Color(0xFF0da487),
                                    side: const BorderSide(color: Color(0xFF0da487)),
                                    padding: const EdgeInsets.all(16),
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),
                        ],
                      ),
                    ),
    );
  }

  Widget _buildMetricColumn(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF0da487),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildAnalyticsCard(String title, String primaryValue, String secondaryValue) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            primaryValue,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            secondaryValue,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF0da487),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
```

---

## 🔗 Related Documentation Files

1. **Comprehensive Detail**: `/Documentation/APIs/product_detail_apis.md`
2. **Quick Reference**: `/Documentation/APIs/product_apis_quick_reference.md`
3. **Existing Inventory APIs**: `/Documentation/APIs/inventory_apis.md`
4. **Customer APIs**: `/Documentation/APIs/customers_apis.md`
5. **Manual Sales APIs**: `/Documentation/APIs/manual_sales_apis.md`

---

## 📞 Support & Contact

For questions about these APIs:
- **Technical Support**: Check error messages and response formats
- **Integration Help**: Use the provided frontend examples
- **API Updates**: Monitor the documentation for changes

**✅ All APIs are now live and ready for production use!**

---

**Last Updated**: September 25, 2025
**API Version**: v1.0
**Documentation Version**: 1.0