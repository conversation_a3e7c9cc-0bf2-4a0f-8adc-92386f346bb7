#!/usr/bin/env python3
"""
Migration script to move POS products to Inventory system
This script will:
1. Copy all POS products to EnhancedProduct
2. Update any references
3. Prepare for removal of POS product system
"""

import os
import django
from django.conf import settings

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from pos.models import Product as POSProduct, Sale, SaleItem
from inventory.models import EnhancedProduct
from decimal import Decimal

def migrate_pos_products():
    """Migrate POS products to inventory system"""
    print("=== POS to Inventory Migration ===")

    # Get all POS products
    pos_products = POSProduct.objects.all()
    print(f"Found {pos_products.count()} POS products to migrate")

    migrated_products = []

    for pos_product in pos_products:
        print(f"\nMigrating: {pos_product.name} (ID: {pos_product.id})")

        # Check if already exists in inventory
        existing = EnhancedProduct.objects.filter(
            owner=pos_product.owner,
            name=pos_product.name,
            sku=pos_product.sku if pos_product.sku else ''
        ).first()

        if existing:
            print(f"  ⚠️ Product already exists in inventory: {existing.name} (ID: {existing.id})")
            migrated_products.append({
                'pos_id': pos_product.id,
                'inventory_id': existing.id,
                'action': 'existing'
            })
            continue

        # Create enhanced product
        enhanced_product = EnhancedProduct.objects.create(
            owner=pos_product.owner,
            category=pos_product.category,
            name=pos_product.name,
            sku=pos_product.sku or '',
            purchase_price=pos_product.purchase_price,
            selling_price=pos_product.selling_price,
            unit=pos_product.unit,
            unit_custom_label=pos_product.unit_custom_label or '',
            tax_rate=pos_product.tax_rate,

            # Enhanced fields with sensible defaults
            description=f"Migrated from POS system - {pos_product.name}",
            track_stock=True,  # Enable stock tracking
            allow_negative_stock=False,
            reorder_level=10,  # Default reorder level
            is_active=True,
            is_featured=False,

            # Copy creation date if needed
            # created_at=pos_product.created_at,  # Uncomment if you want to preserve dates
        )

        print(f"  ✅ Created EnhancedProduct: {enhanced_product.name} (ID: {enhanced_product.id})")

        # Create initial stock batch if POS product had stock
        if pos_product.stock_quantity > 0:
            from inventory.models import StockBatch, StockMovement, Location

            # Get the default location for this owner
            location = Location.objects.filter(owner=pos_product.owner).first()
            if not location:
                # Create a default location if none exists
                location = Location.objects.create(
                    owner=pos_product.owner,
                    name="Main Store",
                    location_type="store",
                    is_active=True
                )

            # Create stock batch
            batch = StockBatch.objects.create(
                owner=pos_product.owner,
                product=enhanced_product,
                location=location,
                batch_number=f"MIGRATED-{pos_product.id}",
                quantity_received=pos_product.stock_quantity,
                quantity_available=pos_product.stock_quantity,
                purchase_price=pos_product.purchase_price,
                notes=f"Initial stock migrated from POS product {pos_product.id}"
            )

            # Create stock movement
            StockMovement.objects.create(
                owner=pos_product.owner,
                product=enhanced_product,
                batch=batch,
                movement_type='in',
                quantity=pos_product.stock_quantity,
                created_by=pos_product.owner,
                notes=f"Migration from POS product {pos_product.id}"
            )

            print(f"  📦 Created stock batch with {pos_product.stock_quantity} units")

        migrated_products.append({
            'pos_id': pos_product.id,
            'inventory_id': enhanced_product.id,
            'action': 'created'
        })

    return migrated_products

def check_sales_dependencies():
    """Check if any sales are using POS products"""
    print("\n=== Checking Sales Dependencies ===")

    pos_products = POSProduct.objects.all()
    for pos_product in pos_products:
        sale_items = SaleItem.objects.filter(product=pos_product)
        if sale_items.exists():
            print(f"⚠️ Product '{pos_product.name}' is used in {sale_items.count()} sale items")
            for item in sale_items:
                print(f"   - Sale {item.sale.id} on {item.sale.created_at}")
        else:
            print(f"✅ Product '{pos_product.name}' has no sale dependencies")

def main():
    print("Starting POS to Inventory migration...")

    # Check dependencies first
    check_sales_dependencies()

    # Migrate products
    migrated = migrate_pos_products()

    print(f"\n=== Migration Summary ===")
    print(f"Total products processed: {len(migrated)}")
    created = [p for p in migrated if p['action'] == 'created']
    existing = [p for p in migrated if p['action'] == 'existing']
    print(f"New products created: {len(created)}")
    print(f"Existing products found: {len(existing)}")

    print(f"\n=== Migration Mapping ===")
    for product in migrated:
        print(f"POS ID {product['pos_id']} → Inventory ID {product['inventory_id']} ({product['action']})")

    print(f"\n✅ Migration completed successfully!")
    print(f"Next steps:")
    print(f"1. Test the migrated products in inventory system")
    print(f"2. Update URLs and views to use inventory system")
    print(f"3. Remove POS product system when ready")

if __name__ == '__main__':
    main()