from django.urls import path
from . import views

app_name = 'employees'

urlpatterns = [
    # Dashboard
    path('', views.employee_dashboard, name='dashboard'),

    # Employee Management
    path('list/', views.employee_list, name='employee-list'),
    path('add/', views.employee_add, name='employee-add'),
    path('<int:pk>/', views.employee_detail, name='employee-detail'),
    path('<int:pk>/edit/', views.employee_edit, name='employee-edit'),
    path('<int:pk>/delete/', views.employee_delete, name='employee-delete'),

    # Attendance Management
    path('attendance/', views.attendance_dashboard, name='attendance-dashboard'),
    path('attendance/list/', views.attendance_list, name='attendance-list'),
    path('attendance/mark/<int:employee_id>/', views.mark_attendance, name='mark-attendance'),
    path('attendance/bulk/', views.bulk_attendance, name='bulk-attendance'),
    path('attendance/report/', views.attendance_report, name='attendance-report'),

    # Leave Management
    path('leaves/', views.leave_dashboard, name='leave-dashboard'),
    path('leaves/request/', views.leave_request, name='leave-request'),
    path('leaves/approve/<int:pk>/', views.approve_leave, name='approve-leave'),
    path('leaves/types/', views.leave_types, name='leave-types'),

    # Payroll Management
    path('payroll/', views.payroll_dashboard, name='payroll-dashboard'),
    path('payroll/process/', views.process_payroll, name='process-payroll'),
    path('payroll/transactions/', views.payroll_transactions, name='payroll-transactions'),
    path('payroll/periods/', views.payroll_periods, name='payroll-periods'),

    # Salary Management
    path('salary/<int:employee_id>/', views.salary_management, name='salary-management'),
    path('salary/<int:employee_id>/increment/', views.salary_increment, name='salary-increment'),
    path('salary/components/', views.salary_components, name='salary-components'),

    # Reports
    path('reports/', views.reports_dashboard, name='reports-dashboard'),
    path('reports/attendance/', views.attendance_reports, name='attendance-reports'),
    path('reports/payroll/', views.payroll_reports, name='payroll-reports'),
    path('reports/employees/', views.employee_reports, name='employee-reports'),

    # Department and Position Management
    path('departments/', views.department_list, name='department-list'),
    path('positions/', views.position_list, name='position-list'),

    # Performance Management
    path('performance/', views.performance_dashboard, name='performance-dashboard'),
    path('performance/<int:employee_id>/review/', views.performance_review, name='performance-review'),

    # Documents
    path('documents/', views.document_list, name='document-list'),
    path('documents/<int:employee_id>/upload/', views.document_upload, name='document-upload'),
]