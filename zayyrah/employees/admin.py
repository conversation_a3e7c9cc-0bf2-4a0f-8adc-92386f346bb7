from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Department, Position, Employee, SalaryComponent, SalaryStructure,
    SalaryStructureComponent, Attendance, LeaveType, LeaveRequest,
    PayrollPeriod, PayrollTransaction, EmployeeDocument, PerformanceReview
)


class SalaryStructureComponentInline(admin.TabularInline):
    model = SalaryStructureComponent
    extra = 1


@admin.register(Department)
class DepartmentAdmin(admin.ModelAdmin):
    list_display = ['name', 'manager', 'employee_count', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'description']
    ordering = ['name']

    def employee_count(self, obj):
        return obj.employees.count()
    employee_count.short_description = 'Employees'


@admin.register(Position)
class PositionAdmin(admin.ModelAdmin):
    list_display = ['title', 'department', 'base_salary', 'employee_count', 'created_at']
    list_filter = ['department', 'created_at']
    search_fields = ['title', 'description']
    ordering = ['department__name', 'title']

    def employee_count(self, obj):
        return obj.employees.count()
    employee_count.short_description = 'Employees'


@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = ['employee_id', 'full_name', 'department', 'position', 'employment_status', 'hire_date', 'current_salary_display']
    list_filter = ['employment_status', 'department', 'position', 'gender', 'hire_date']
    search_fields = ['employee_id', 'first_name', 'last_name', 'email']
    ordering = ['employee_id']

    fieldsets = (
        ('Personal Information', {
            'fields': ('employee_id', 'first_name', 'last_name', 'email', 'phone',
                      'date_of_birth', 'gender', 'marital_status', 'address')
        }),
        ('Emergency Contact', {
            'fields': ('emergency_contact_name', 'emergency_contact_phone')
        }),
        ('Employment Information', {
            'fields': ('department', 'position', 'hire_date', 'employment_status', 'termination_date')
        }),
        ('Banking Information', {
            'fields': ('bank_name', 'account_number', 'routing_number')
        }),
        ('System', {
            'fields': ('user',),
            'classes': ('collapse',)
        })
    )

    def current_salary_display(self, obj):
        salary = obj.current_salary
        if salary > 0:
            return format_html('<span style="color: green;">${:,.2f}</span>', salary)
        return format_html('<span style="color: red;">Not Set</span>')
    current_salary_display.short_description = 'Current Salary'


@admin.register(SalaryComponent)
class SalaryComponentAdmin(admin.ModelAdmin):
    list_display = ['name', 'component_type', 'default_frequency', 'is_taxable', 'is_mandatory']
    list_filter = ['component_type', 'is_taxable', 'is_mandatory', 'default_frequency']
    search_fields = ['name', 'description']
    ordering = ['component_type', 'name']


@admin.register(SalaryStructure)
class SalaryStructureAdmin(admin.ModelAdmin):
    list_display = ['employee', 'effective_date', 'total_salary_display', 'is_active', 'created_by']
    list_filter = ['is_active', 'effective_date', 'created_by']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering = ['-effective_date']
    inlines = [SalaryStructureComponentInline]

    def total_salary_display(self, obj):
        return format_html('${:,.2f}', obj.total_salary)
    total_salary_display.short_description = 'Total Salary'


@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ['employee', 'date', 'status', 'check_in', 'check_out', 'hours_worked', 'overtime_hours']
    list_filter = ['status', 'date', 'employee__department']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering = ['-date', 'employee__employee_id']
    date_hierarchy = 'date'


@admin.register(LeaveType)
class LeaveTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'days_allowed_per_year', 'is_paid', 'requires_approval', 'max_consecutive_days']
    list_filter = ['is_paid', 'requires_approval']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(LeaveRequest)
class LeaveRequestAdmin(admin.ModelAdmin):
    list_display = ['employee', 'leave_type', 'start_date', 'end_date', 'days_requested', 'status', 'approved_by']
    list_filter = ['status', 'leave_type', 'start_date', 'approved_by']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'reason']
    ordering = ['-created_at']
    date_hierarchy = 'start_date'

    def get_readonly_fields(self, request, obj=None):
        if obj and obj.status in ['approved', 'rejected']:
            return ['employee', 'leave_type', 'start_date', 'end_date', 'reason']
        return []


@admin.register(PayrollPeriod)
class PayrollPeriodAdmin(admin.ModelAdmin):
    list_display = ['name', 'period_type', 'start_date', 'end_date', 'pay_date', 'is_processed']
    list_filter = ['period_type', 'is_processed', 'start_date']
    search_fields = ['name']
    ordering = ['-start_date']
    date_hierarchy = 'start_date'


@admin.register(PayrollTransaction)
class PayrollTransactionAdmin(admin.ModelAdmin):
    list_display = ['employee', 'transaction_type', 'amount_display', 'net_amount_display', 'payment_status', 'payment_date']
    list_filter = ['transaction_type', 'payment_status', 'payroll_period', 'created_at']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'reference_number']
    ordering = ['-created_at']
    date_hierarchy = 'created_at'

    def amount_display(self, obj):
        return format_html('${:,.2f}', obj.amount)
    amount_display.short_description = 'Amount'

    def net_amount_display(self, obj):
        return format_html('${:,.2f}', obj.net_amount)
    net_amount_display.short_description = 'Net Amount'


@admin.register(EmployeeDocument)
class EmployeeDocumentAdmin(admin.ModelAdmin):
    list_display = ['employee', 'document_type', 'title', 'uploaded_by', 'uploaded_at']
    list_filter = ['document_type', 'uploaded_at', 'uploaded_by']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'title']
    ordering = ['-uploaded_at']


@admin.register(PerformanceReview)
class PerformanceReviewAdmin(admin.ModelAdmin):
    list_display = ['employee', 'review_type', 'review_date', 'overall_rating', 'reviewer']
    list_filter = ['review_type', 'review_date', 'reviewer']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering = ['-review_date']
    date_hierarchy = 'review_date'
