from rest_framework import generics, status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q, Count, Sum, Avg
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from .models import (
    Department, Position, Employee, SalaryComponent, SalaryStructure,
    SalaryStructureComponent, Attendance, LeaveType, LeaveRequest,
    PayrollPeriod, PayrollTransaction, EmployeeDocument, PerformanceReview
)

from .serializers import (
    DepartmentSerializer, PositionSerializer, EmployeeListSerializer,
    EmployeeDetailSerializer, EmployeeCreateUpdateSerializer, SalaryComponentSerializer,
    SalaryStructureSerializer, AttendanceSerializer, BulkAttendanceSerializer,
    LeaveTypeSerializer, LeaveRequestSerializer, PayrollPeriodSerializer,
    PayrollTransactionSerializer, EmployeeDocumentSerializer, PerformanceReviewSerializer,
    SalaryIncrementSerializer, AttendanceSummarySerializer
)


# Department Views
class DepartmentListCreateAPIView(generics.ListCreateAPIView):
    queryset = Department.objects.annotate(employee_count=Count('employees'))
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']


class DepartmentDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Department.objects.annotate(employee_count=Count('employees'))
    serializer_class = DepartmentSerializer
    permission_classes = [permissions.IsAuthenticated]


# Position Views
class PositionListCreateAPIView(generics.ListCreateAPIView):
    queryset = Position.objects.select_related('department').annotate(employee_count=Count('employees'))
    serializer_class = PositionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['department']
    search_fields = ['title', 'description']
    ordering_fields = ['title', 'department__name', 'base_salary']
    ordering = ['department__name', 'title']


class PositionDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Position.objects.select_related('department').annotate(employee_count=Count('employees'))
    serializer_class = PositionSerializer
    permission_classes = [permissions.IsAuthenticated]


# Employee Views
class EmployeeListCreateAPIView(generics.ListCreateAPIView):
    queryset = Employee.objects.select_related('department', 'position')
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['department', 'position', 'employment_status', 'gender']
    search_fields = ['employee_id', 'first_name', 'last_name', 'email']
    ordering_fields = ['employee_id', 'first_name', 'last_name', 'hire_date']
    ordering = ['employee_id']

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return EmployeeCreateUpdateSerializer
        return EmployeeListSerializer


class EmployeeDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Employee.objects.select_related('department', 'position').prefetch_related('salary_structures__components__salary_component')
    permission_classes = [permissions.IsAuthenticated]

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return EmployeeCreateUpdateSerializer
        return EmployeeDetailSerializer


# Salary Component Views
class SalaryComponentListCreateAPIView(generics.ListCreateAPIView):
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['component_type', 'is_taxable', 'is_mandatory', 'default_frequency']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'component_type']
    ordering = ['component_type', 'name']


class SalaryComponentDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = SalaryComponent.objects.all()
    serializer_class = SalaryComponentSerializer
    permission_classes = [permissions.IsAuthenticated]


# Salary Structure Views
class SalaryStructureListCreateAPIView(generics.ListCreateAPIView):
    queryset = SalaryStructure.objects.select_related('employee', 'created_by').prefetch_related('components__salary_component')
    serializer_class = SalaryStructureSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'is_active']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering_fields = ['effective_date', 'created_at']
    ordering = ['-effective_date']


class SalaryStructureDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = SalaryStructure.objects.select_related('employee', 'created_by').prefetch_related('components__salary_component')
    serializer_class = SalaryStructureSerializer
    permission_classes = [permissions.IsAuthenticated]


# Attendance Views
class AttendanceListCreateAPIView(generics.ListCreateAPIView):
    queryset = Attendance.objects.select_related('employee__department')
    serializer_class = AttendanceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'status', 'date']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering_fields = ['date', 'employee__employee_id']
    ordering = ['-date', 'employee__employee_id']

    def get_queryset(self):
        queryset = super().get_queryset()
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(date__gte=date_from)
        if date_to:
            queryset = queryset.filter(date__lte=date_to)

        return queryset


class AttendanceDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Attendance.objects.select_related('employee__department')
    serializer_class = AttendanceSerializer
    permission_classes = [permissions.IsAuthenticated]


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def bulk_attendance_create(request):
    """Create attendance records for multiple employees"""
    serializer = BulkAttendanceSerializer(data=request.data)
    if serializer.is_valid():
        records = serializer.save()
        return Response({
            'message': f'Successfully created/updated {len(records)} attendance records',
            'records_count': len(records)
        }, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Leave Management Views
class LeaveTypeListCreateAPIView(generics.ListCreateAPIView):
    queryset = LeaveType.objects.all()
    serializer_class = LeaveTypeSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [SearchFilter, OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'days_allowed_per_year']
    ordering = ['name']


class LeaveTypeDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = LeaveType.objects.all()
    serializer_class = LeaveTypeSerializer
    permission_classes = [permissions.IsAuthenticated]


class LeaveRequestListCreateAPIView(generics.ListCreateAPIView):
    queryset = LeaveRequest.objects.select_related('employee', 'leave_type', 'approved_by')
    serializer_class = LeaveRequestSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'leave_type', 'status']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'reason']
    ordering_fields = ['start_date', 'created_at']
    ordering = ['-created_at']


class LeaveRequestDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = LeaveRequest.objects.select_related('employee', 'leave_type', 'approved_by')
    serializer_class = LeaveRequestSerializer
    permission_classes = [permissions.IsAuthenticated]


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def approve_leave_request(request, pk):
    """Approve or reject a leave request"""
    try:
        leave_request = LeaveRequest.objects.get(pk=pk)
    except LeaveRequest.DoesNotExist:
        return Response({'error': 'Leave request not found'}, status=status.HTTP_404_NOT_FOUND)

    action = request.data.get('action')  # 'approve' or 'reject'
    rejection_reason = request.data.get('rejection_reason', '')

    if action == 'approve':
        leave_request.status = 'approved'
        leave_request.approved_by = request.user
        leave_request.approval_date = timezone.now()
    elif action == 'reject':
        leave_request.status = 'rejected'
        leave_request.approved_by = request.user
        leave_request.approval_date = timezone.now()
        leave_request.rejection_reason = rejection_reason
    else:
        return Response({'error': 'Invalid action'}, status=status.HTTP_400_BAD_REQUEST)

    leave_request.save()

    # Create attendance records for approved leave
    if action == 'approve':
        create_leave_attendance_records(leave_request)

    serializer = LeaveRequestSerializer(leave_request)
    return Response(serializer.data)


def create_leave_attendance_records(leave_request):
    """Create attendance records for approved leave period"""
    current_date = leave_request.start_date
    while current_date <= leave_request.end_date:
        Attendance.objects.get_or_create(
            employee=leave_request.employee,
            date=current_date,
            defaults={
                'status': 'on_leave',
                'remarks': f'On {leave_request.leave_type.name} leave'
            }
        )
        current_date += timedelta(days=1)


# Payroll Views
class PayrollPeriodListCreateAPIView(generics.ListCreateAPIView):
    queryset = PayrollPeriod.objects.annotate(
        total_transactions=Count('transactions'),
        total_amount=Sum('transactions__net_amount')
    )
    serializer_class = PayrollPeriodSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, OrderingFilter]
    filterset_fields = ['period_type', 'is_processed']
    ordering_fields = ['start_date', 'pay_date']
    ordering = ['-start_date']


class PayrollPeriodDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = PayrollPeriod.objects.annotate(
        total_transactions=Count('transactions'),
        total_amount=Sum('transactions__net_amount')
    )
    serializer_class = PayrollPeriodSerializer
    permission_classes = [permissions.IsAuthenticated]


class PayrollTransactionListCreateAPIView(generics.ListCreateAPIView):
    queryset = PayrollTransaction.objects.select_related('employee', 'payroll_period', 'component', 'created_by')
    serializer_class = PayrollTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'payroll_period', 'transaction_type', 'payment_status']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'reference_number']
    ordering_fields = ['created_at', 'payment_date', 'amount']
    ordering = ['-created_at']


class PayrollTransactionDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = PayrollTransaction.objects.select_related('employee', 'payroll_period', 'component', 'created_by')
    serializer_class = PayrollTransactionSerializer
    permission_classes = [permissions.IsAuthenticated]


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def process_payroll(request, period_id):
    """Process payroll for a specific period"""
    try:
        payroll_period = PayrollPeriod.objects.get(pk=period_id)
    except PayrollPeriod.DoesNotExist:
        return Response({'error': 'Payroll period not found'}, status=status.HTTP_404_NOT_FOUND)

    if payroll_period.is_processed:
        return Response({'error': 'Payroll already processed'}, status=status.HTTP_400_BAD_REQUEST)

    # Get all active employees
    employees = Employee.objects.filter(employment_status='active')
    processed_count = 0

    for employee in employees:
        # Get current salary structure
        salary_structure = employee.salary_structures.filter(
            effective_date__lte=payroll_period.end_date,
            is_active=True
        ).order_by('-effective_date').first()

        if salary_structure:
            # Create payroll transactions for each salary component
            for component in salary_structure.components.filter(is_active=True):
                # Calculate amount based on frequency and period
                amount = calculate_payroll_amount(component, payroll_period)

                if amount > 0:
                    PayrollTransaction.objects.create(
                        employee=employee,
                        payroll_period=payroll_period,
                        transaction_type='salary',
                        component=component.salary_component,
                        amount=amount,
                        tax_amount=calculate_tax_amount(amount, component.salary_component.is_taxable),
                        net_amount=amount - calculate_tax_amount(amount, component.salary_component.is_taxable),
                        created_by=request.user
                    )

            processed_count += 1

    # Mark payroll as processed
    payroll_period.is_processed = True
    payroll_period.save()

    return Response({
        'message': f'Payroll processed successfully for {processed_count} employees',
        'employees_processed': processed_count
    })


def calculate_payroll_amount(component, payroll_period):
    """Calculate payroll amount based on component frequency and period type"""
    base_amount = component.amount
    frequency = component.frequency
    period_type = payroll_period.period_type

    # Convert everything to monthly equivalent for calculation
    monthly_multipliers = {
        'daily': 30,
        'weekly': 4.33,
        'bi_weekly': 2.17,
        'monthly': 1,
        'quarterly': 0.33,
        'yearly': 0.083
    }

    period_multipliers = {
        'weekly': 0.23,
        'bi_weekly': 0.46,
        'monthly': 1
    }

    monthly_amount = base_amount * monthly_multipliers.get(frequency, 1)
    period_amount = monthly_amount * period_multipliers.get(period_type, 1)

    return period_amount


def calculate_tax_amount(amount, is_taxable):
    """Calculate tax amount (simplified calculation)"""
    if not is_taxable:
        return Decimal('0.00')

    # Simple tax calculation - 10% for demonstration
    return amount * Decimal('0.10')


# Document Views
class EmployeeDocumentListCreateAPIView(generics.ListCreateAPIView):
    queryset = EmployeeDocument.objects.select_related('employee', 'uploaded_by')
    serializer_class = EmployeeDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'document_type']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name', 'title']
    ordering_fields = ['uploaded_at', 'title']
    ordering = ['-uploaded_at']


class EmployeeDocumentDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = EmployeeDocument.objects.select_related('employee', 'uploaded_by')
    serializer_class = EmployeeDocumentSerializer
    permission_classes = [permissions.IsAuthenticated]


# Performance Review Views
class PerformanceReviewListCreateAPIView(generics.ListCreateAPIView):
    queryset = PerformanceReview.objects.select_related('employee', 'reviewer')
    serializer_class = PerformanceReviewSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['employee', 'review_type', 'reviewer']
    search_fields = ['employee__employee_id', 'employee__first_name', 'employee__last_name']
    ordering_fields = ['review_date', 'overall_rating']
    ordering = ['-review_date']


class PerformanceReviewDetailAPIView(generics.RetrieveUpdateDestroyAPIView):
    queryset = PerformanceReview.objects.select_related('employee', 'reviewer')
    serializer_class = PerformanceReviewSerializer
    permission_classes = [permissions.IsAuthenticated]


# Salary Increment/Decrement
@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def salary_increment(request):
    """Apply salary increment or decrement to an employee"""
    serializer = SalaryIncrementSerializer(data=request.data)
    if serializer.is_valid():
        employee_id = serializer.validated_data['employee_id']
        increment_type = serializer.validated_data['increment_type']
        increment_value = serializer.validated_data['increment_value']
        effective_date = serializer.validated_data['effective_date']
        remarks = serializer.validated_data.get('remarks', '')

        try:
            employee = Employee.objects.get(id=employee_id)
        except Employee.DoesNotExist:
            return Response({'error': 'Employee not found'}, status=status.HTTP_404_NOT_FOUND)

        # Get current salary structure
        current_structure = employee.salary_structures.filter(
            effective_date__lte=timezone.now().date(),
            is_active=True
        ).order_by('-effective_date').first()

        if not current_structure:
            return Response({'error': 'No active salary structure found'}, status=status.HTTP_400_BAD_REQUEST)

        # Deactivate current structure if effective date is today or in the future
        if effective_date >= timezone.now().date():
            current_structure.is_active = False
            current_structure.end_date = effective_date - timedelta(days=1)
            current_structure.save()

        # Create new salary structure
        new_structure = SalaryStructure.objects.create(
            employee=employee,
            effective_date=effective_date,
            is_active=True,
            remarks=remarks,
            created_by=request.user
        )

        # Copy components with increment/decrement
        for component in current_structure.components.all():
            new_amount = component.amount

            if increment_type == 'percentage':
                new_amount = component.amount * (1 + increment_value / 100)
            else:  # amount
                new_amount = component.amount + increment_value

            SalaryStructureComponent.objects.create(
                salary_structure=new_structure,
                salary_component=component.salary_component,
                amount=max(new_amount, 0),  # Ensure non-negative
                frequency=component.frequency,
                is_active=component.is_active
            )

        # Return updated employee data
        serializer = EmployeeDetailSerializer(employee)
        return Response({
            'message': 'Salary updated successfully',
            'employee': serializer.data
        })

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# Analytics and Reports
@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def attendance_summary(request):
    """Get attendance summary for employees"""
    date_from = request.query_params.get('date_from')
    date_to = request.query_params.get('date_to')
    employee_id = request.query_params.get('employee_id')

    if not date_from or not date_to:
        return Response({'error': 'date_from and date_to are required'}, status=status.HTTP_400_BAD_REQUEST)

    # Base queryset
    employees = Employee.objects.filter(employment_status='active')
    if employee_id:
        employees = employees.filter(id=employee_id)

    summary_data = []

    for employee in employees:
        attendances = Attendance.objects.filter(
            employee=employee,
            date__range=[date_from, date_to]
        )

        total_days = attendances.count()
        present_days = attendances.filter(status='present').count()
        absent_days = attendances.filter(status='absent').count()
        late_days = attendances.filter(status='late').count()
        leave_days = attendances.filter(status='on_leave').count()

        total_hours = attendances.aggregate(total=Sum('hours_worked'))['total'] or 0
        overtime_hours = attendances.aggregate(total=Sum('overtime_hours'))['total'] or 0

        attendance_percentage = (present_days / total_days * 100) if total_days > 0 else 0

        summary_data.append({
            'employee': employee,
            'total_days': total_days,
            'present_days': present_days,
            'absent_days': absent_days,
            'late_days': late_days,
            'leave_days': leave_days,
            'total_hours': total_hours,
            'overtime_hours': overtime_hours,
            'attendance_percentage': attendance_percentage
        })

    serializer = AttendanceSummarySerializer(summary_data, many=True)
    return Response(serializer.data)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def dashboard_stats(request):
    """Get dashboard statistics"""
    today = timezone.now().date()

    # Employee stats
    total_employees = Employee.objects.filter(employment_status='active').count()

    # Today's attendance stats
    today_attendance = Attendance.objects.filter(date=today)
    present_today = today_attendance.filter(status='present').count()
    absent_today = today_attendance.filter(status='absent').count()
    late_today = today_attendance.filter(status='late').count()

    # Pending leave requests
    pending_leaves = LeaveRequest.objects.filter(status='pending').count()

    # Department-wise attendance
    departments = Department.objects.annotate(
        total_employees=Count('employees', filter=Q(employees__employment_status='active'))
    )

    department_attendance = []
    for dept in departments:
        dept_attendance = today_attendance.filter(employee__department=dept)
        present = dept_attendance.filter(status='present').count()
        absent = dept_attendance.filter(status='absent').count()
        late = dept_attendance.filter(status='late').count()
        on_leave = dept_attendance.filter(status='on_leave').count()

        department_attendance.append({
            'department': DepartmentSerializer(dept).data,
            'total_employees': dept.total_employees,
            'present': present,
            'absent': absent,
            'late': late,
            'on_leave': on_leave,
            'present_percentage': (present / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'absent_percentage': (absent / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'late_percentage': (late / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'leave_percentage': (on_leave / dept.total_employees * 100) if dept.total_employees > 0 else 0
        })

    # Recent activities (simplified)
    recent_activities = []

    return Response({
        'total_employees': total_employees,
        'present_today': present_today,
        'absent_today': absent_today,
        'late_today': late_today,
        'pending_leaves': pending_leaves,
        'present_percentage': (present_today / total_employees * 100) if total_employees > 0 else 0,
        'absent_percentage': (absent_today / total_employees * 100) if total_employees > 0 else 0,
        'late_percentage': (late_today / total_employees * 100) if total_employees > 0 else 0,
        'department_attendance': department_attendance,
        'recent_activities': recent_activities
    })