from django import forms
from django.core.exceptions import ValidationError
from .models import Employee, Department, Position


class EmployeeForm(forms.ModelForm):
    """Form for creating and editing employees"""

    class Meta:
        model = Employee
        fields = [
            'first_name', 'last_name', 'email', 'phone',
            'date_of_birth', 'gender', 'marital_status', 'address',
            'employee_id', 'department', 'position', 'hire_date',
            'employment_status', 'emergency_contact_name', 'emergency_contact_phone',
            'bank_name', 'account_number', 'routing_number'
        ]

        widgets = {
            'first_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter first name'
            }),
            'last_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter last name'
            }),
            'email': forms.EmailInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter email address'
            }),
            'phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter phone number'
            }),
            'date_of_birth': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'gender': forms.Select(attrs={
                'class': 'form-control'
            }),
            'marital_status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'address': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Enter full address'
            }),
            'employee_id': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Employee ID (auto-generated if empty)'
            }),
            'department': forms.Select(attrs={
                'class': 'form-control'
            }),
            'position': forms.Select(attrs={
                'class': 'form-control'
            }),
            'hire_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'employment_status': forms.Select(attrs={
                'class': 'form-control'
            }),
            'emergency_contact_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter emergency contact name'
            }),
            'emergency_contact_phone': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter emergency contact phone'
            }),
            'bank_name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter bank name'
            }),
            'account_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter account number'
            }),
            'routing_number': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter routing number'
            }),
        }

        labels = {
            'first_name': 'First Name',
            'last_name': 'Last Name',
            'email': 'Email Address',
            'phone': 'Phone Number',
            'date_of_birth': 'Date of Birth',
            'gender': 'Gender',
            'marital_status': 'Marital Status',
            'address': 'Address',
            'employee_id': 'Employee ID',
            'department': 'Department',
            'position': 'Position',
            'hire_date': 'Date of Joining',
            'employment_status': 'Employment Status',
            'emergency_contact_name': 'Emergency Contact Name',
            'emergency_contact_phone': 'Emergency Contact Phone',
            'bank_name': 'Bank Name',
            'account_number': 'Account Number',
            'routing_number': 'Routing Number',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make certain fields required
        self.fields['first_name'].required = True
        self.fields['last_name'].required = True
        self.fields['phone'].required = True
        self.fields['employee_id'].required = True
        self.fields['department'].required = True
        self.fields['position'].required = True
        self.fields['hire_date'].required = True
        self.fields['employment_status'].required = True

        # Add empty option for optional fields
        self.fields['department'].empty_label = "Select Department"
        self.fields['position'].empty_label = "Select Position"

    def clean_employee_id(self):
        """Validate employee ID uniqueness"""
        employee_id = self.cleaned_data.get('employee_id')
        if employee_id:
            # Check if employee_id already exists (excluding current instance for edits)
            existing = Employee.objects.filter(employee_id=employee_id)
            if self.instance and self.instance.pk:
                existing = existing.exclude(pk=self.instance.pk)

            if existing.exists():
                raise ValidationError("Employee ID already exists. Please choose a different ID.")

        return employee_id

    def clean_phone(self):
        """Validate phone number format"""
        phone = self.cleaned_data.get('phone')
        if phone:
            # Basic phone number validation (adjust pattern as needed)
            import re
            if not re.match(r'^\+?[\d\s\-\(\)]{10,15}$', phone):
                raise ValidationError("Please enter a valid phone number.")

        return phone

    def clean_email(self):
        """Validate email uniqueness if provided"""
        email = self.cleaned_data.get('email')
        if email:
            # Check if email already exists (excluding current instance for edits)
            from accounts.models import User
            existing = User.objects.filter(email=email)
            if self.instance and self.instance.pk and self.instance.user:
                existing = existing.exclude(pk=self.instance.user.pk)

            if existing.exists():
                raise ValidationError("Email address already exists. Please choose a different email.")

        return email

    def clean(self):
        """Additional validation"""
        cleaned_data = super().clean()
        date_of_birth = cleaned_data.get('date_of_birth')
        hire_date = cleaned_data.get('hire_date')

        # Validate that hire date is not before date of birth
        if date_of_birth and hire_date:
            from datetime import date
            if hire_date < date_of_birth:
                raise ValidationError("Date of joining cannot be before date of birth.")

            # Validate minimum age (e.g., 16 years)
            age = hire_date.year - date_of_birth.year
            if hire_date.month < date_of_birth.month or \
               (hire_date.month == date_of_birth.month and hire_date.day < date_of_birth.day):
                age -= 1

            if age < 16:
                raise ValidationError("Employee must be at least 16 years old at the time of joining.")

        return cleaned_data