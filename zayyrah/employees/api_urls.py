from django.urls import path
from . import api_views

app_name = 'employees_api'

urlpatterns = [
    # Department URLs
    path('departments/', api_views.DepartmentListCreateAPIView.as_view(), name='department-list-create'),
    path('departments/<int:pk>/', api_views.DepartmentDetailAPIView.as_view(), name='department-detail'),

    # Position URLs
    path('positions/', api_views.PositionListCreateAPIView.as_view(), name='position-list-create'),
    path('positions/<int:pk>/', api_views.PositionDetailAPIView.as_view(), name='position-detail'),

    # Employee URLs
    path('employees/', api_views.EmployeeListCreateAPIView.as_view(), name='employee-list-create'),
    path('employees/<int:pk>/', api_views.EmployeeDetailAPIView.as_view(), name='employee-detail'),

    # Salary Component URLs
    path('salary-components/', api_views.SalaryComponentListCreateAPIView.as_view(), name='salary-component-list-create'),
    path('salary-components/<int:pk>/', api_views.SalaryComponentDetailAPIView.as_view(), name='salary-component-detail'),

    # Salary Structure URLs
    path('salary-structures/', api_views.SalaryStructureListCreateAPIView.as_view(), name='salary-structure-list-create'),
    path('salary-structures/<int:pk>/', api_views.SalaryStructureDetailAPIView.as_view(), name='salary-structure-detail'),

    # Attendance URLs
    path('attendance/', api_views.AttendanceListCreateAPIView.as_view(), name='attendance-list-create'),
    path('attendance/<int:pk>/', api_views.AttendanceDetailAPIView.as_view(), name='attendance-detail'),
    path('attendance/bulk/', api_views.bulk_attendance_create, name='bulk-attendance-create'),
    path('attendance/summary/', api_views.attendance_summary, name='attendance-summary'),

    # Leave Management URLs
    path('leave-types/', api_views.LeaveTypeListCreateAPIView.as_view(), name='leave-type-list-create'),
    path('leave-types/<int:pk>/', api_views.LeaveTypeDetailAPIView.as_view(), name='leave-type-detail'),
    path('leave-requests/', api_views.LeaveRequestListCreateAPIView.as_view(), name='leave-request-list-create'),
    path('leave-requests/<int:pk>/', api_views.LeaveRequestDetailAPIView.as_view(), name='leave-request-detail'),
    path('leave-requests/<int:pk>/approve/', api_views.approve_leave_request, name='approve-leave-request'),

    # Payroll URLs
    path('payroll-periods/', api_views.PayrollPeriodListCreateAPIView.as_view(), name='payroll-period-list-create'),
    path('payroll-periods/<int:pk>/', api_views.PayrollPeriodDetailAPIView.as_view(), name='payroll-period-detail'),
    path('payroll-periods/<int:period_id>/process/', api_views.process_payroll, name='process-payroll'),
    path('payroll-transactions/', api_views.PayrollTransactionListCreateAPIView.as_view(), name='payroll-transaction-list-create'),
    path('payroll-transactions/<int:pk>/', api_views.PayrollTransactionDetailAPIView.as_view(), name='payroll-transaction-detail'),

    # Document URLs
    path('documents/', api_views.EmployeeDocumentListCreateAPIView.as_view(), name='document-list-create'),
    path('documents/<int:pk>/', api_views.EmployeeDocumentDetailAPIView.as_view(), name='document-detail'),

    # Performance Review URLs
    path('performance-reviews/', api_views.PerformanceReviewListCreateAPIView.as_view(), name='performance-review-list-create'),
    path('performance-reviews/<int:pk>/', api_views.PerformanceReviewDetailAPIView.as_view(), name='performance-review-detail'),

    # Salary Management URLs
    path('salary/increment/', api_views.salary_increment, name='salary-increment'),

    # Dashboard and Analytics URLs
    path('dashboard/stats/', api_views.dashboard_stats, name='dashboard-stats'),
]