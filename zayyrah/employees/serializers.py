from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import (
    Department, Position, Employee, SalaryComponent, SalaryStructure,
    SalaryStructureComponent, Attendance, LeaveType, LeaveRequest,
    PayrollPeriod, PayrollTransaction, EmployeeDocument, PerformanceReview
)

User = get_user_model()


class DepartmentSerializer(serializers.ModelSerializer):
    employee_count = serializers.IntegerField(read_only=True)
    manager_name = serializers.CharField(source='manager.full_name', read_only=True)

    class Meta:
        model = Department
        fields = ['id', 'name', 'description', 'manager', 'manager_name', 'employee_count', 'created_at', 'updated_at']


class PositionSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    employee_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Position
        fields = ['id', 'title', 'department', 'department_name', 'description', 'base_salary', 'employee_count', 'created_at', 'updated_at']


class SalaryComponentSerializer(serializers.ModelSerializer):
    class Meta:
        model = SalaryComponent
        fields = ['id', 'name', 'component_type', 'description', 'is_taxable', 'is_mandatory', 'default_frequency', 'created_at', 'updated_at']


class SalaryStructureComponentSerializer(serializers.ModelSerializer):
    component_name = serializers.CharField(source='salary_component.name', read_only=True)
    component_type = serializers.CharField(source='salary_component.component_type', read_only=True)

    class Meta:
        model = SalaryStructureComponent
        fields = ['id', 'salary_component', 'component_name', 'component_type', 'amount', 'frequency', 'is_active']


class SalaryStructureSerializer(serializers.ModelSerializer):
    components = SalaryStructureComponentSerializer(many=True, read_only=True)
    total_salary = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = SalaryStructure
        fields = ['id', 'employee', 'effective_date', 'end_date', 'is_active', 'remarks', 'components', 'total_salary', 'created_by', 'created_by_name', 'created_at', 'updated_at']


class EmployeeListSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    position_title = serializers.CharField(source='position.title', read_only=True)
    current_salary = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)

    class Meta:
        model = Employee
        fields = [
            'id', 'employee_id', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'department', 'department_name', 'position', 'position_title', 'hire_date',
            'employment_status', 'current_salary', 'created_at', 'updated_at'
        ]


class EmployeeDetailSerializer(serializers.ModelSerializer):
    department_name = serializers.CharField(source='department.name', read_only=True)
    position_title = serializers.CharField(source='position.title', read_only=True)
    current_salary = serializers.DecimalField(max_digits=12, decimal_places=2, read_only=True)
    salary_structures = SalaryStructureSerializer(many=True, read_only=True)

    class Meta:
        model = Employee
        fields = [
            'id', 'employee_id', 'user', 'first_name', 'last_name', 'full_name', 'email', 'phone',
            'date_of_birth', 'gender', 'marital_status', 'address', 'emergency_contact_name',
            'emergency_contact_phone', 'department', 'department_name', 'position', 'position_title',
            'hire_date', 'employment_status', 'termination_date', 'bank_name', 'account_number',
            'routing_number', 'current_salary', 'salary_structures', 'created_at', 'updated_at'
        ]


class EmployeeCreateUpdateSerializer(serializers.ModelSerializer):
    salary_components = serializers.ListField(write_only=True, required=False)

    class Meta:
        model = Employee
        fields = [
            'employee_id', 'first_name', 'last_name', 'email', 'phone', 'date_of_birth',
            'gender', 'marital_status', 'address', 'emergency_contact_name', 'emergency_contact_phone',
            'department', 'position', 'hire_date', 'employment_status', 'termination_date',
            'bank_name', 'account_number', 'routing_number', 'salary_components'
        ]

    def create(self, validated_data):
        salary_components_data = validated_data.pop('salary_components', [])
        employee = Employee.objects.create(**validated_data)

        # Create initial salary structure if components provided
        if salary_components_data:
            self._create_salary_structure(employee, salary_components_data)

        return employee

    def update(self, instance, validated_data):
        salary_components_data = validated_data.pop('salary_components', None)

        # Update employee fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update salary structure if provided
        if salary_components_data is not None:
            self._create_salary_structure(instance, salary_components_data)

        return instance

    def _create_salary_structure(self, employee, components_data):
        from django.utils import timezone

        # Deactivate previous salary structures
        employee.salary_structures.filter(is_active=True).update(is_active=False)

        # Create new salary structure
        salary_structure = SalaryStructure.objects.create(
            employee=employee,
            effective_date=timezone.now().date(),
            is_active=True,
            created_by=self.context['request'].user
        )

        # Create salary components
        for component_data in components_data:
            component = SalaryComponent.objects.get(name=component_data['component'])
            SalaryStructureComponent.objects.create(
                salary_structure=salary_structure,
                salary_component=component,
                amount=component_data['amount'],
                frequency=component_data.get('frequency', component.default_frequency)
            )


class AttendanceSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    employee_id_display = serializers.CharField(source='employee.employee_id', read_only=True)
    department_name = serializers.CharField(source='employee.department.name', read_only=True)

    class Meta:
        model = Attendance
        fields = [
            'id', 'employee', 'employee_name', 'employee_id_display', 'department_name',
            'date', 'check_in', 'check_out', 'status', 'hours_worked', 'overtime_hours',
            'remarks', 'created_at', 'updated_at'
        ]


class BulkAttendanceSerializer(serializers.Serializer):
    date = serializers.DateField()
    attendance_records = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        )
    )

    def create(self, validated_data):
        from django.utils import timezone
        from datetime import datetime, timedelta

        date = validated_data['date']
        records = validated_data['attendance_records']
        created_records = []

        for record in records:
            employee_id = record.get('employee_id')
            if not employee_id:
                continue

            try:
                employee = Employee.objects.get(id=employee_id)
            except Employee.DoesNotExist:
                continue

            # Check if attendance already exists for this date
            attendance, created = Attendance.objects.get_or_create(
                employee=employee,
                date=date,
                defaults={
                    'status': record.get('status', 'present'),
                    'check_in': record.get('check_in') or None,
                    'check_out': record.get('check_out') or None,
                    'remarks': record.get('remarks', '')
                }
            )

            if not created:
                # Update existing record
                attendance.status = record.get('status', attendance.status)
                if record.get('check_in'):
                    attendance.check_in = record.get('check_in')
                if record.get('check_out'):
                    attendance.check_out = record.get('check_out')
                attendance.remarks = record.get('remarks', attendance.remarks)
                attendance.save()

            created_records.append(attendance)

        return created_records


class LeaveTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = LeaveType
        fields = ['id', 'name', 'description', 'days_allowed_per_year', 'is_paid', 'requires_approval', 'max_consecutive_days', 'created_at', 'updated_at']


class LeaveRequestSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    employee_id_display = serializers.CharField(source='employee.employee_id', read_only=True)
    leave_type_name = serializers.CharField(source='leave_type.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.username', read_only=True)

    class Meta:
        model = LeaveRequest
        fields = [
            'id', 'employee', 'employee_name', 'employee_id_display', 'leave_type',
            'leave_type_name', 'start_date', 'end_date', 'days_requested', 'reason',
            'status', 'approved_by', 'approved_by_name', 'approval_date', 'rejection_reason',
            'created_at', 'updated_at'
        ]


class PayrollPeriodSerializer(serializers.ModelSerializer):
    total_transactions = serializers.IntegerField(read_only=True)
    total_amount = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)

    class Meta:
        model = PayrollPeriod
        fields = ['id', 'name', 'period_type', 'start_date', 'end_date', 'pay_date', 'is_processed', 'total_transactions', 'total_amount', 'created_at', 'updated_at']


class PayrollTransactionSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    employee_id_display = serializers.CharField(source='employee.employee_id', read_only=True)
    component_name = serializers.CharField(source='component.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = PayrollTransaction
        fields = [
            'id', 'employee', 'employee_name', 'employee_id_display', 'payroll_period',
            'transaction_type', 'component', 'component_name', 'amount', 'tax_amount',
            'net_amount', 'payment_status', 'payment_date', 'reference_number', 'remarks',
            'created_by', 'created_by_name', 'created_at', 'updated_at'
        ]


class EmployeeDocumentSerializer(serializers.ModelSerializer):
    uploaded_by_name = serializers.CharField(source='uploaded_by.username', read_only=True)

    class Meta:
        model = EmployeeDocument
        fields = ['id', 'employee', 'document_type', 'title', 'file', 'description', 'uploaded_by', 'uploaded_by_name', 'uploaded_at']


class PerformanceReviewSerializer(serializers.ModelSerializer):
    employee_name = serializers.CharField(source='employee.full_name', read_only=True)
    employee_id_display = serializers.CharField(source='employee.employee_id', read_only=True)
    reviewer_name = serializers.CharField(source='reviewer.username', read_only=True)

    class Meta:
        model = PerformanceReview
        fields = [
            'id', 'employee', 'employee_name', 'employee_id_display', 'review_type',
            'review_period_start', 'review_period_end', 'overall_rating', 'goals_achieved',
            'areas_of_improvement', 'future_goals', 'reviewer', 'reviewer_name', 'review_date',
            'created_at', 'updated_at'
        ]


class SalaryIncrementSerializer(serializers.Serializer):
    employee_id = serializers.IntegerField()
    increment_type = serializers.ChoiceField(choices=['percentage', 'amount'])
    increment_value = serializers.DecimalField(max_digits=10, decimal_places=2)
    effective_date = serializers.DateField()
    remarks = serializers.CharField(max_length=500, required=False, allow_blank=True)

    def validate_employee_id(self, value):
        try:
            Employee.objects.get(id=value)
        except Employee.DoesNotExist:
            raise serializers.ValidationError("Employee not found")
        return value

    def validate_increment_value(self, value):
        if value <= 0:
            raise serializers.ValidationError("Increment value must be positive")
        return value


class AttendanceSummarySerializer(serializers.Serializer):
    employee = EmployeeListSerializer()
    total_days = serializers.IntegerField()
    present_days = serializers.IntegerField()
    absent_days = serializers.IntegerField()
    late_days = serializers.IntegerField()
    leave_days = serializers.IntegerField()
    total_hours = serializers.DecimalField(max_digits=8, decimal_places=2)
    overtime_hours = serializers.DecimalField(max_digits=8, decimal_places=2)
    attendance_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)