from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.db.models import Count, Sum, Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.http import JsonResponse
from datetime import datetime, timedelta

from .models import (
    Department, Position, Employee, SalaryComponent, SalaryStructure,
    Attendance, LeaveType, LeaveRequest, PayrollPeriod, PayrollTransaction,
    EmployeeDocument, PerformanceReview
)


@login_required
def employee_dashboard(request):
    """Main employee management dashboard"""
    today = timezone.now().date()

    # Basic stats
    total_employees = Employee.objects.filter(employment_status='active').count()

    # Today's attendance
    today_attendance = Attendance.objects.filter(date=today)
    present_today = today_attendance.filter(status='present').count()
    absent_today = today_attendance.filter(status='absent').count()
    late_today = today_attendance.filter(status='late').count()

    # Pending leave requests
    pending_leaves = LeaveRequest.objects.filter(status='pending').count()

    # Department-wise attendance
    departments = Department.objects.annotate(
        total_employees=Count('employees', filter=Q(employees__employment_status='active'))
    )

    department_attendance = []
    for dept in departments:
        dept_attendance = today_attendance.filter(employee__department=dept)
        present = dept_attendance.filter(status='present').count()
        absent = dept_attendance.filter(status='absent').count()
        late = dept_attendance.filter(status='late').count()
        on_leave = dept_attendance.filter(status='on_leave').count()

        department_attendance.append({
            'department': dept,
            'total_employees': dept.total_employees,
            'present': present,
            'absent': absent,
            'late': late,
            'on_leave': on_leave,
            'present_percentage': (present / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'absent_percentage': (absent / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'late_percentage': (late / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'leave_percentage': (on_leave / dept.total_employees * 100) if dept.total_employees > 0 else 0
        })

    # Recent activities (simplified for now)
    recent_activities = []

    context = {
        'total_employees': total_employees,
        'present_today': present_today,
        'absent_today': absent_today,
        'late_today': late_today,
        'pending_leaves': pending_leaves,
        'present_percentage': (present_today / total_employees * 100) if total_employees > 0 else 0,
        'absent_percentage': (absent_today / total_employees * 100) if total_employees > 0 else 0,
        'late_percentage': (late_today / total_employees * 100) if total_employees > 0 else 0,
        'department_attendance': department_attendance,
        'recent_activities': recent_activities,
        'departments': departments,
        'positions': Position.objects.all(),
        'employees': Employee.objects.filter(employment_status='active')[:20],  # Limit for modal
        'today': today
    }

    return render(request, 'employees/employee_dashboard_new.html', context)


@login_required
def employee_list(request):
    """Employee list view with filtering and pagination"""
    employees = Employee.objects.select_related('department', 'position').order_by('employee_id')

    # Filter by department
    department_id = request.GET.get('department')
    if department_id:
        employees = employees.filter(department_id=department_id)

    # Filter by status
    status = request.GET.get('status')
    if status:
        employees = employees.filter(employment_status=status)

    # Search
    search = request.GET.get('search')
    if search:
        employees = employees.filter(
            Q(employee_id__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(email__icontains=search)
        )

    # Pagination
    paginator = Paginator(employees, 12)  # 12 employees per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'employees': page_obj,
        'departments': Department.objects.all(),
        'positions': Position.objects.all(),
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages(),
        'today': timezone.now().date()
    }

    return render(request, 'employees/employee_list.html', context)


@login_required
def employee_detail(request, pk):
    """Employee detail view"""
    employee = get_object_or_404(Employee, pk=pk)

    # Get recent attendance
    recent_attendance = Attendance.objects.filter(
        employee=employee
    ).order_by('-date')[:30]

    # Get leave requests
    leave_requests = LeaveRequest.objects.filter(
        employee=employee
    ).order_by('-created_at')[:10]

    # Get salary history
    salary_structures = SalaryStructure.objects.filter(
        employee=employee
    ).order_by('-effective_date')[:5]

    # Get documents
    documents = EmployeeDocument.objects.filter(
        employee=employee
    ).order_by('-uploaded_at')[:10]

    context = {
        'employee': employee,
        'recent_attendance': recent_attendance,
        'leave_requests': leave_requests,
        'salary_structures': salary_structures,
        'documents': documents
    }

    return render(request, 'employees/employee_detail.html', context)


@login_required
def employee_add(request):
    """Add new employee"""
    from .forms import EmployeeForm
    from accounts.models import User
    from django.db import transaction
    import uuid

    if request.method == 'POST':
        form = EmployeeForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create user account first
                    user_data = {
                        'mobile_number': form.cleaned_data['phone'],
                        'first_name': form.cleaned_data['first_name'],
                        'last_name': form.cleaned_data['last_name'],
                    }
                    if form.cleaned_data.get('email'):
                        user_data['email'] = form.cleaned_data['email']

                    # Generate a temporary password
                    temp_password = str(uuid.uuid4())[:8]
                    user = User.objects.create_user(password=temp_password, **user_data)

                    # Create employee record
                    employee = form.save(commit=False)
                    employee.user = user
                    employee.save()

                    messages.success(request, f'Employee added successfully! Temporary password: {temp_password}')
                    return redirect('employees:employee-detail', pk=employee.pk)

            except Exception as e:
                messages.error(request, f'Error creating employee: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = EmployeeForm()

    context = {
        'form': form,
        'departments': Department.objects.all(),
        'positions': Position.objects.all(),
        'salary_components': SalaryComponent.objects.all()
    }

    return render(request, 'employees/employee_form.html', context)


@login_required
def employee_edit(request, pk):
    """Edit employee"""
    from .forms import EmployeeForm
    from django.db import transaction

    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        form = EmployeeForm(request.POST, request.FILES, instance=employee)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Update user account if exists
                    if employee.user:
                        user = employee.user
                        user.mobile_number = form.cleaned_data['phone']
                        user.first_name = form.cleaned_data['first_name']
                        user.last_name = form.cleaned_data['last_name']
                        if form.cleaned_data.get('email'):
                            user.email = form.cleaned_data['email']
                        user.save()

                    # Update employee record
                    employee = form.save()

                    messages.success(request, 'Employee updated successfully!')
                    return redirect('employees:employee-detail', pk=pk)

            except Exception as e:
                messages.error(request, f'Error updating employee: {str(e)}')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = EmployeeForm(instance=employee)

    context = {
        'form': form,
        'employee': employee,
        'departments': Department.objects.all(),
        'positions': Position.objects.all(),
        'salary_components': SalaryComponent.objects.all()
    }

    return render(request, 'employees/employee_form.html', context)


@login_required
def employee_delete(request, pk):
    """Delete employee"""
    employee = get_object_or_404(Employee, pk=pk)

    if request.method == 'POST':
        employee.employment_status = 'terminated'
        employee.termination_date = timezone.now().date()
        employee.save()
        messages.success(request, f'Employee {employee.full_name} has been terminated.')
        return redirect('employees:employee-list')

    return render(request, 'employees/employee_confirm_delete.html', {'employee': employee})


@login_required
def attendance_dashboard(request):
    """Attendance dashboard"""
    today = timezone.now().date()

    # Today's attendance summary
    today_attendance = Attendance.objects.filter(date=today)
    present_count = today_attendance.filter(status='present').count()
    absent_count = today_attendance.filter(status='absent').count()
    late_count = today_attendance.filter(status='late').count()
    on_leave_count = today_attendance.filter(status='on_leave').count()

    total_employees = Employee.objects.filter(employment_status='active').count()

    # Calculate percentages
    present_percentage = (present_count / total_employees * 100) if total_employees > 0 else 0
    absent_percentage = (absent_count / total_employees * 100) if total_employees > 0 else 0
    late_percentage = (late_count / total_employees * 100) if total_employees > 0 else 0
    leave_percentage = (on_leave_count / total_employees * 100) if total_employees > 0 else 0

    # Today's attendance records
    todays_attendance = Attendance.objects.filter(date=today).select_related(
        'employee__department'
    ).order_by('employee__employee_id')

    # Department-wise attendance
    departments = Department.objects.annotate(
        total_employees=Count('employees', filter=Q(employees__employment_status='active'))
    )

    department_attendance = []
    for dept in departments:
        dept_attendance = today_attendance.filter(employee__department=dept)
        present = dept_attendance.filter(status='present').count()
        absent = dept_attendance.filter(status='absent').count()
        late = dept_attendance.filter(status='late').count()
        on_leave = dept_attendance.filter(status='on_leave').count()

        department_attendance.append({
            'department': dept,
            'total_employees': dept.total_employees,
            'present': present,
            'absent': absent,
            'late': late,
            'on_leave': on_leave,
            'present_percentage': (present / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'absent_percentage': (absent / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'late_percentage': (late / dept.total_employees * 100) if dept.total_employees > 0 else 0,
            'leave_percentage': (on_leave / dept.total_employees * 100) if dept.total_employees > 0 else 0
        })

    # Attendance trend data for charts (last 90 days)
    start_date = today - timedelta(days=89)
    attendance_history = Attendance.objects.filter(
        date__range=[start_date, today]
    ).values('date').annotate(
        present=Count('id', filter=Q(status='present')),
        absent=Count('id', filter=Q(status='absent')),
        late=Count('id', filter=Q(status='late')),
        on_leave=Count('id', filter=Q(status='on_leave'))
    )

    history_map = {item['date']: item for item in attendance_history}
    attendance_trend = []
    current_date = start_date
    while current_date <= today:
        record = history_map.get(current_date, {})
        attendance_trend.append({
            'date': current_date.isoformat(),
            'present': record.get('present', 0),
            'absent': record.get('absent', 0),
            'late': record.get('late', 0),
            'on_leave': record.get('on_leave', 0),
        })
        current_date += timedelta(days=1)

    modal_employee_limit = 50
    employees_for_modal = list(
        Employee.objects.filter(employment_status='active')
        .order_by('employee_id')[:modal_employee_limit]
    )

    context = {
        'present_count': present_count,
        'absent_count': absent_count,
        'late_count': late_count,
        'on_leave_count': on_leave_count,
        'present_percentage': present_percentage,
        'absent_percentage': absent_percentage,
        'late_percentage': late_percentage,
        'leave_percentage': leave_percentage,
        'todays_attendance': todays_attendance,
        'department_attendance': department_attendance,
        'attendance_trend': attendance_trend,
        'total_employees': total_employees,
        'employees': employees_for_modal,
        'has_more_employees': total_employees > len(employees_for_modal),
        'modal_employee_limit': modal_employee_limit,
        'today': today
    }

    return render(request, 'employees/attendance_dashboard.html', context)


@login_required
def attendance_list(request):
    """Attendance list view"""
    # Get date range from query params
    date_from = request.GET.get('date_from', timezone.now().date().strftime('%Y-%m-%d'))
    date_to = request.GET.get('date_to', timezone.now().date().strftime('%Y-%m-%d'))

    attendance = Attendance.objects.filter(
        date__range=[date_from, date_to]
    ).select_related('employee__department').order_by('-date', 'employee__employee_id')

    # Filter by employee
    employee_id = request.GET.get('employee')
    if employee_id:
        attendance = attendance.filter(employee_id=employee_id)

    # Filter by status
    status = request.GET.get('status')
    if status:
        attendance = attendance.filter(status=status)

    # Pagination
    paginator = Paginator(attendance, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'attendance': page_obj,
        'employees': Employee.objects.filter(employment_status='active'),
        'date_from': date_from,
        'date_to': date_to,
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages()
    }

    return render(request, 'employees/attendance_list.html', context)


@login_required
def mark_attendance(request, employee_id):
    """Mark attendance for a specific employee"""
    employee = get_object_or_404(Employee, pk=employee_id)

    if request.method == 'POST':
        # Handle attendance marking (simplified for now)
        messages.success(request, f'Attendance marked for {employee.full_name}')
        return redirect('employees:attendance-dashboard')

    context = {
        'employee': employee,
        'today': timezone.now().date()
    }

    return render(request, 'employees/mark_attendance.html', context)


@login_required
def bulk_attendance(request):
    """Bulk attendance entry"""
    if request.method == 'POST':
        # Handle bulk attendance (simplified for now)
        messages.success(request, 'Bulk attendance saved successfully!')
        return redirect('employees:attendance-dashboard')

    employees = Employee.objects.filter(employment_status='active').order_by('employee_id')

    context = {
        'employees': employees,
        'today': timezone.now().date()
    }

    return render(request, 'employees/bulk_attendance.html', context)


@login_required
def attendance_report(request):
    """Attendance reports"""
    context = {}
    return render(request, 'employees/attendance_report.html', context)


@login_required
def leave_dashboard(request):
    """Leave management dashboard"""
    # Pending leave requests
    pending_requests = LeaveRequest.objects.filter(status='pending').select_related(
        'employee', 'leave_type'
    ).order_by('-created_at')

    # Recent leave requests
    recent_requests = LeaveRequest.objects.select_related(
        'employee', 'leave_type', 'approved_by'
    ).order_by('-created_at')[:20]

    # Leave types
    leave_types = LeaveType.objects.all()

    context = {
        'pending_requests': pending_requests,
        'recent_requests': recent_requests,
        'leave_types': leave_types
    }

    return render(request, 'employees/leave_dashboard.html', context)


@login_required
def leave_request(request):
    """Create leave request"""
    if request.method == 'POST':
        # Handle leave request creation (simplified for now)
        messages.success(request, 'Leave request submitted successfully!')
        return redirect('employees:leave-dashboard')

    context = {
        'employees': Employee.objects.filter(employment_status='active'),
        'leave_types': LeaveType.objects.all()
    }

    return render(request, 'employees/leave_request_form.html', context)


@login_required
def approve_leave(request, pk):
    """Approve/reject leave request"""
    leave_request = get_object_or_404(LeaveRequest, pk=pk)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action == 'approve':
            leave_request.status = 'approved'
            leave_request.approved_by = request.user
            leave_request.approval_date = timezone.now()
            messages.success(request, f'Leave request approved for {leave_request.employee.full_name}')
        elif action == 'reject':
            leave_request.status = 'rejected'
            leave_request.approved_by = request.user
            leave_request.approval_date = timezone.now()
            leave_request.rejection_reason = request.POST.get('rejection_reason', '')
            messages.success(request, f'Leave request rejected for {leave_request.employee.full_name}')

        leave_request.save()
        return redirect('employees:leave-dashboard')

    context = {
        'leave_request': leave_request
    }

    return render(request, 'employees/approve_leave.html', context)


@login_required
def leave_types(request):
    """Manage leave types"""
    leave_types = LeaveType.objects.all().order_by('name')

    context = {
        'leave_types': leave_types
    }

    return render(request, 'employees/leave_types.html', context)


@login_required
def payroll_dashboard(request):
    """Payroll dashboard"""
    # Current month payroll period
    current_month = timezone.now().date().replace(day=1)
    current_period = PayrollPeriod.objects.filter(
        start_date__lte=current_month,
        end_date__gte=current_month
    ).first()

    # Recent payroll periods
    recent_periods = PayrollPeriod.objects.order_by('-start_date')[:10]

    # Recent transactions
    recent_transactions = PayrollTransaction.objects.select_related(
        'employee', 'payroll_period'
    ).order_by('-created_at')[:20]

    context = {
        'current_period': current_period,
        'recent_periods': recent_periods,
        'recent_transactions': recent_transactions
    }

    return render(request, 'employees/payroll_dashboard.html', context)


@login_required
def process_payroll(request):
    """Process payroll"""
    if request.method == 'POST':
        # Handle payroll processing (simplified for now)
        messages.success(request, 'Payroll processed successfully!')
        return redirect('employees:payroll-dashboard')

    # Get unprocessed periods
    unprocessed_periods = PayrollPeriod.objects.filter(is_processed=False)

    context = {
        'unprocessed_periods': unprocessed_periods
    }

    return render(request, 'employees/process_payroll.html', context)


@login_required
def payroll_transactions(request):
    """Payroll transactions list"""
    transactions = PayrollTransaction.objects.select_related(
        'employee', 'payroll_period', 'component'
    ).order_by('-created_at')

    # Pagination
    paginator = Paginator(transactions, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'transactions': page_obj,
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages()
    }

    return render(request, 'employees/payroll_transactions.html', context)


@login_required
def payroll_periods(request):
    """Payroll periods management"""
    periods = PayrollPeriod.objects.order_by('-start_date')

    context = {
        'periods': periods
    }

    return render(request, 'employees/payroll_periods.html', context)


@login_required
def salary_management(request, employee_id):
    """Salary management for employee"""
    employee = get_object_or_404(Employee, pk=employee_id)

    # Get salary structures
    salary_structures = SalaryStructure.objects.filter(
        employee=employee
    ).order_by('-effective_date')

    context = {
        'employee': employee,
        'salary_structures': salary_structures
    }

    return render(request, 'employees/salary_management.html', context)


@login_required
def salary_increment(request, employee_id):
    """Apply salary increment"""
    employee = get_object_or_404(Employee, pk=employee_id)

    if request.method == 'POST':
        # Handle salary increment (simplified for now)
        messages.success(request, f'Salary updated for {employee.full_name}')
        return redirect('employees:salary-management', employee_id=employee_id)

    context = {
        'employee': employee
    }

    return render(request, 'employees/salary_increment_form.html', context)


@login_required
def salary_components(request):
    """Manage salary components"""
    components = SalaryComponent.objects.order_by('component_type', 'name')

    context = {
        'components': components
    }

    return render(request, 'employees/salary_components.html', context)


@login_required
def reports_dashboard(request):
    """Reports dashboard"""
    context = {}
    return render(request, 'employees/reports_dashboard.html', context)


@login_required
def attendance_reports(request):
    """Attendance reports"""
    context = {}
    return render(request, 'employees/attendance_reports.html', context)


@login_required
def payroll_reports(request):
    """Payroll reports"""
    context = {}
    return render(request, 'employees/payroll_reports.html', context)


@login_required
def employee_reports(request):
    """Employee reports"""
    context = {}
    return render(request, 'employees/employee_reports.html', context)


@login_required
def department_list(request):
    """Department management"""
    departments = Department.objects.annotate(
        employee_count=Count('employees')
    ).order_by('name')

    context = {
        'departments': departments
    }

    return render(request, 'employees/department_list.html', context)


@login_required
def position_list(request):
    """Position management"""
    positions = Position.objects.select_related('department').annotate(
        employee_count=Count('employees')
    ).order_by('department__name', 'title')

    context = {
        'positions': positions,
        'departments': Department.objects.all()
    }

    return render(request, 'employees/position_list.html', context)


@login_required
def performance_dashboard(request):
    """Performance management dashboard"""
    # Recent reviews
    recent_reviews = PerformanceReview.objects.select_related(
        'employee', 'reviewer'
    ).order_by('-review_date')[:20]

    # Due reviews (simplified)
    due_reviews = Employee.objects.filter(employment_status='active')[:10]

    context = {
        'recent_reviews': recent_reviews,
        'due_reviews': due_reviews
    }

    return render(request, 'employees/performance_dashboard.html', context)


@login_required
def performance_review(request, employee_id):
    """Create/edit performance review"""
    employee = get_object_or_404(Employee, pk=employee_id)

    if request.method == 'POST':
        # Handle performance review creation (simplified for now)
        messages.success(request, f'Performance review saved for {employee.full_name}')
        return redirect('employees:performance-dashboard')

    context = {
        'employee': employee
    }

    return render(request, 'employees/performance_review_form.html', context)


@login_required
def document_list(request):
    """Employee documents management"""
    documents = EmployeeDocument.objects.select_related(
        'employee', 'uploaded_by'
    ).order_by('-uploaded_at')

    # Pagination
    paginator = Paginator(documents, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'documents': page_obj,
        'page_obj': page_obj,
        'is_paginated': page_obj.has_other_pages()
    }

    return render(request, 'employees/document_list.html', context)


@login_required
def document_upload(request, employee_id):
    """Upload employee document"""
    employee = get_object_or_404(Employee, pk=employee_id)

    if request.method == 'POST':
        # Handle document upload (simplified for now)
        messages.success(request, f'Document uploaded for {employee.full_name}')
        return redirect('employees:document-list')

    context = {
        'employee': employee
    }

    return render(request, 'employees/document_upload_form.html', context)
