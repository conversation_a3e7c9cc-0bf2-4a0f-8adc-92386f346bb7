# Generated by Django 5.2.6 on 2025-09-20 14:25

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0002_otptoken_passwordresettoken'),
    ]

    operations = [
        migrations.CreateModel(
            name='Business',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('business_name', models.CharField(max_length=255)),
                ('business_type', models.CharField(choices=[('retail', 'Retail Store'), ('restaurant', 'Restaurant'), ('grocery', 'Grocery Store'), ('pharmacy', 'Pharmacy'), ('electronics', 'Electronics Store'), ('clothing', 'Clothing Store'), ('other', 'Other')], default='retail', max_length=50)),
                ('owner_name', models.Char<PERSON>ield(max_length=255)),
                ('mobile_number', models.CharField(max_length=15, validators=[django.core.validators.RegexValidator('^\\d{6,15}$', 'Enter a valid mobile number (6 to 15 digits).')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address', models.TextField()),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('postal_code', models.CharField(max_length=20)),
                ('country', models.CharField(default='Pakistan', max_length=100)),
                ('registration_number', models.CharField(blank=True, max_length=100, null=True)),
                ('tax_number', models.CharField(blank=True, max_length=100, null=True)),
                ('license_number', models.CharField(blank=True, max_length=100, null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Approval'), ('approved', 'Approved'), ('suspended', 'Suspended'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('subscription_plan', models.CharField(default='basic', max_length=50)),
                ('monthly_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('max_employees', models.IntegerField(default=5)),
                ('max_products', models.IntegerField(default=1000)),
                ('max_transactions_per_month', models.IntegerField(default=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('last_activity', models.DateTimeField(blank=True, null=True)),
                ('total_revenue', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('monthly_revenue', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('total_transactions', models.IntegerField(default=0)),
                ('admin_user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='owned_business', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business',
                'verbose_name_plural': 'Businesses',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BusinessSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plan_name', models.CharField(max_length=50)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('start_date', models.DateTimeField()),
                ('end_date', models.DateTimeField()),
                ('status', models.CharField(choices=[('active', 'Active'), ('expired', 'Expired'), ('cancelled', 'Cancelled'), ('pending_payment', 'Pending Payment')], default='active', max_length=20)),
                ('payment_method', models.CharField(blank=True, max_length=50)),
                ('transaction_id', models.CharField(blank=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to='accounts.business')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='BusinessAdmin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('can_manage_inventory', models.BooleanField(default=True)),
                ('can_manage_employees', models.BooleanField(default=False)),
                ('can_view_reports', models.BooleanField(default=True)),
                ('can_manage_customers', models.BooleanField(default=True)),
                ('can_process_sales', models.BooleanField(default=True)),
                ('can_manage_settings', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_active', models.BooleanField(default=True)),
                ('business', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='administrators', to='accounts.business')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='administered_businesses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Business Administrator',
                'verbose_name_plural': 'Business Administrators',
                'unique_together': {('business', 'user')},
            },
        ),
    ]
