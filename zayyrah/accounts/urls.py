from django.contrib.auth.views import LoginView
from django.urls import path

from .forms import MobileAuthenticationForm
from .views import DashboardView, LogoutRedirectView, SignupView

app_name = 'accounts'

login_view = LoginView.as_view(
    template_name='registration/login.html',
    authentication_form=MobileAuthenticationForm,
    redirect_authenticated_user=True,
)

urlpatterns = [
    path('', login_view, name='login'),
    path('login/', login_view),
    path('logout/', LogoutRedirectView.as_view(), name='logout'),
    path('signup/', SignupView.as_view(), name='signup'),
    path('dashboard/', DashboardView.as_view(), name='dashboard'),
]
