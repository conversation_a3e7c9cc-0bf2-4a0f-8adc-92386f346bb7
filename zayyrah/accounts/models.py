from django.contrib.auth.base_user import Abs<PERSON><PERSON><PERSON><PERSON><PERSON>, BaseUserManager
from django.contrib.auth.models import PermissionsMixin
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.db import models
from django.utils import timezone
import secrets
import string
from datetime import timedel<PERSON>
from decimal import Decimal


class UserManager(BaseUserManager):
    def _create_user(self, mobile_number, password, **extra_fields):
        if not mobile_number:
            raise ValueError('The mobile number must be set')
        mobile_number = str(mobile_number).strip()
        if not mobile_number:
            raise ValueError('The mobile number must be set')
        user = self.model(mobile_number=mobile_number, **extra_fields)
        user.set_password(password)
        user.full_clean()
        user.save(using=self._db)
        return user

    def create_user(self, mobile_number, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        if password is None:
            raise ValueError('Users must have a password')
        return self._create_user(mobile_number, password, **extra_fields)

    def create_superuser(self, mobile_number, password, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')

        return self._create_user(mobile_number, password, **extra_fields)


class User(AbstractBaseUser, PermissionsMixin):
    class AccountType(models.TextChoices):
        CUSTOMER = 'customer', 'Customer'
        BUSINESS = 'business', 'Business'

    mobile_number = models.CharField(
        max_length=15,
        unique=True,
        validators=[
            RegexValidator(r'^\d{6,15}$', 'Enter a valid mobile number (6 to 15 digits).'),
        ],
    )
    account_type = models.CharField(
        max_length=20,
        choices=AccountType.choices,
        default=AccountType.CUSTOMER,
    )
    shop_name = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)
    is_staff = models.BooleanField(default=False)
    date_joined = models.DateTimeField(default=timezone.now)

    objects = UserManager()

    USERNAME_FIELD = 'mobile_number'
    REQUIRED_FIELDS = ['account_type']

    def clean(self):
        super().clean()
        if self.account_type == self.AccountType.BUSINESS and not self.shop_name:
            raise ValidationError({'shop_name': 'Shop name is required for business accounts.'})

    def __str__(self):
        return self.mobile_number


class PasswordResetToken(models.Model):
    """Model to handle password reset tokens"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='password_reset_tokens')
    token = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)

    def save(self, *args, **kwargs):
        if not self.token:
            self.token = self.generate_token()
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=15)  # 15 minutes expiry
        super().save(*args, **kwargs)

    @staticmethod
    def generate_token():
        """Generate a secure random token"""
        return secrets.token_urlsafe(32)

    def is_valid(self):
        """Check if token is still valid"""
        return not self.is_used and timezone.now() < self.expires_at

    def use_token(self):
        """Mark token as used"""
        self.is_used = True
        self.save()

    def __str__(self):
        return f"Reset token for {self.user.mobile_number}"

    class Meta:
        ordering = ['-created_at']


class OTPToken(models.Model):
    """Model to handle OTP tokens"""
    mobile_number = models.CharField(max_length=15)
    otp_code = models.CharField(max_length=6)
    purpose = models.CharField(max_length=50, choices=[
        ('password_reset', 'Password Reset'),
        ('account_verification', 'Account Verification'),
    ])
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    attempts = models.IntegerField(default=0)
    max_attempts = models.IntegerField(default=3)

    def save(self, *args, **kwargs):
        if not self.otp_code:
            self.otp_code = self.generate_otp()
        if not self.expires_at:
            self.expires_at = timezone.now() + timedelta(minutes=10)  # 10 minutes expiry
        super().save(*args, **kwargs)

    @staticmethod
    def generate_otp():
        """Generate a 6-digit OTP"""
        return ''.join(secrets.choice(string.digits) for _ in range(6))

    def is_valid(self):
        """Check if OTP is still valid"""
        return (not self.is_used and
                timezone.now() < self.expires_at and
                self.attempts < self.max_attempts)

    def verify_otp(self, provided_otp):
        """Verify the provided OTP"""
        self.attempts += 1
        self.save()

        if not self.is_valid():
            return False

        if self.otp_code == provided_otp:
            self.is_used = True
            self.save()
            return True
        return False

    def display_to_terminal(self):
        """Display OTP to terminal for development/testing"""
        print(f"\n{'='*50}")
        print(f"OTP for {self.mobile_number}")
        print(f"Purpose: {self.get_purpose_display()}")
        print(f"Code: {self.otp_code}")
        print(f"Expires at: {self.expires_at.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*50}\n")

    def __str__(self):
        return f"OTP for {self.mobile_number} - {self.purpose}"

    class Meta:
        ordering = ['-created_at']


class Business(models.Model):
    """Model to represent registered businesses"""
    class BusinessStatus(models.TextChoices):
        PENDING = 'pending', 'Pending Approval'
        APPROVED = 'approved', 'Approved'
        SUSPENDED = 'suspended', 'Suspended'
        REJECTED = 'rejected', 'Rejected'

    class BusinessType(models.TextChoices):
        RETAIL = 'retail', 'Retail Store'
        RESTAURANT = 'restaurant', 'Restaurant'
        GROCERY = 'grocery', 'Grocery Store'
        PHARMACY = 'pharmacy', 'Pharmacy'
        ELECTRONICS = 'electronics', 'Electronics Store'
        CLOTHING = 'clothing', 'Clothing Store'
        OTHER = 'other', 'Other'

    business_name = models.CharField(max_length=255)
    business_type = models.CharField(max_length=50, choices=BusinessType.choices, default=BusinessType.RETAIL)
    owner_name = models.CharField(max_length=255)
    mobile_number = models.CharField(
        max_length=15,
        validators=[RegexValidator(r'^\d{6,15}$', 'Enter a valid mobile number (6 to 15 digits).')]
    )
    email = models.EmailField(blank=True, null=True)
    address = models.TextField()
    city = models.CharField(max_length=100)
    state = models.CharField(max_length=100)
    postal_code = models.CharField(max_length=20)
    country = models.CharField(max_length=100, default='Pakistan')

    # Business details
    registration_number = models.CharField(max_length=100, blank=True, null=True)
    tax_number = models.CharField(max_length=100, blank=True, null=True)
    license_number = models.CharField(max_length=100, blank=True, null=True)

    # Admin and status
    admin_user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='owned_business')
    status = models.CharField(max_length=20, choices=BusinessStatus.choices, default=BusinessStatus.PENDING)

    # Subscription and limits
    subscription_plan = models.CharField(max_length=50, default='basic')
    monthly_fee = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    max_employees = models.IntegerField(default=5)
    max_products = models.IntegerField(default=1000)
    max_transactions_per_month = models.IntegerField(default=500)

    # Dates
    created_at = models.DateTimeField(auto_now_add=True)
    approved_at = models.DateTimeField(blank=True, null=True)
    last_activity = models.DateTimeField(blank=True, null=True)

    # Financial tracking
    total_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    monthly_revenue = models.DecimalField(max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_transactions = models.IntegerField(default=0)

    def __str__(self):
        return f"{self.business_name} ({self.owner_name})"

    def get_status_display_color(self):
        colors = {
            'pending': 'orange',
            'approved': 'green',
            'suspended': 'red',
            'rejected': 'gray'
        }
        return colors.get(self.status, 'gray')

    def get_current_month_stats(self):
        from django.db.models import Sum, Count
        from django.utils import timezone
        try:
            from pos.models import Sale
            current_month = timezone.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

            sales = Sale.objects.filter(
                business=self,
                created_at__gte=current_month
            )

            return {
                'transactions': sales.count(),
                'revenue': sales.aggregate(total=Sum('total_amount'))['total'] or Decimal('0.00'),
                'avg_transaction': sales.aggregate(avg=models.Avg('total_amount'))['avg'] or Decimal('0.00')
            }
        except ImportError:
            return {
                'transactions': 0,
                'revenue': Decimal('0.00'),
                'avg_transaction': Decimal('0.00')
            }

    class Meta:
        verbose_name = 'Business'
        verbose_name_plural = 'Businesses'
        ordering = ['-created_at']


class BusinessAdmin(models.Model):
    """Model to track business administrators and their permissions"""
    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='administrators')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='administered_businesses')

    # Permission levels
    can_manage_inventory = models.BooleanField(default=True)
    can_manage_employees = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=True)
    can_manage_customers = models.BooleanField(default=True)
    can_process_sales = models.BooleanField(default=True)
    can_manage_settings = models.BooleanField(default=False)

    created_at = models.DateTimeField(auto_now_add=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.user.mobile_number} - {self.business.business_name}"

    class Meta:
        unique_together = ('business', 'user')
        verbose_name = 'Business Administrator'
        verbose_name_plural = 'Business Administrators'


class BusinessSubscription(models.Model):
    """Model to track business subscription history"""
    class SubscriptionStatus(models.TextChoices):
        ACTIVE = 'active', 'Active'
        EXPIRED = 'expired', 'Expired'
        CANCELLED = 'cancelled', 'Cancelled'
        PENDING_PAYMENT = 'pending_payment', 'Pending Payment'

    business = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='subscriptions')
    plan_name = models.CharField(max_length=50)
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    status = models.CharField(max_length=20, choices=SubscriptionStatus.choices, default=SubscriptionStatus.ACTIVE)
    payment_method = models.CharField(max_length=50, blank=True)
    transaction_id = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.business.business_name} - {self.plan_name} ({self.status})"

    def is_active(self):
        return self.status == self.SubscriptionStatus.ACTIVE and self.end_date > timezone.now()

    class Meta:
        ordering = ['-created_at']
