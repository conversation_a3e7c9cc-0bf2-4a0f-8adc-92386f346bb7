from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import User, Business, BusinessAdmin


class Command(BaseCommand):
    help = 'Migrate existing business users to new Business model structure'

    def handle(self, *args, **options):
        self.stdout.write('🔍 Checking for business users without Business records...')

        # Find all business users
        business_users = User.objects.filter(account_type=User.AccountType.BUSINESS)
        migrated_count = 0

        for user in business_users:
            # Check if this user already has a Business record
            try:
                existing_business = user.owned_business
                self.stdout.write(f'✅ {user.mobile_number} already has Business record: {existing_business.business_name}')
            except Business.DoesNotExist:
                # Create Business record for this user
                business_name = user.shop_name or f"Business of {user.mobile_number}"

                business = Business.objects.create(
                    business_name=business_name,
                    business_type=Business.BusinessType.RETAIL,  # Default type
                    owner_name=f"Owner of {business_name}",
                    mobile_number=user.mobile_number,
                    email='',
                    address='Address not provided',
                    city='Not specified',
                    state='Not specified',
                    postal_code='00000',
                    admin_user=user,
                    status=Business.BusinessStatus.PENDING,  # Default to pending
                    created_at=user.date_joined  # Use original registration date
                )

                # Create BusinessAdmin record
                BusinessAdmin.objects.create(
                    business=business,
                    user=user,
                    can_manage_inventory=True,
                    can_view_reports=True,
                    can_manage_customers=True,
                    can_process_sales=True,
                    can_manage_employees=False,
                    can_manage_settings=False
                )

                migrated_count += 1
                self.stdout.write(
                    self.style.SUCCESS(f'✅ Created Business record for: {user.mobile_number} - {business_name}')
                )

        if migrated_count == 0:
            self.stdout.write(self.style.SUCCESS('🎉 All business users already have Business records!'))
        else:
            self.stdout.write(
                self.style.SUCCESS(f'🎉 Successfully migrated {migrated_count} business users!')
            )