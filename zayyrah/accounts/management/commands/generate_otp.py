from django.core.management.base import BaseCommand
from accounts.models import OTPToken, User


class Command(BaseCommand):
    help = 'Generate OTP for testing purposes'

    def add_arguments(self, parser):
        parser.add_argument('mobile_number', type=str, help='Mobile number to generate OTP for')
        parser.add_argument(
            '--purpose',
            type=str,
            default='password_reset',
            choices=['password_reset', 'account_verification'],
            help='Purpose of the OTP'
        )

    def handle(self, *args, **options):
        mobile_number = options['mobile_number']
        purpose = options['purpose']

        # Check if user exists
        try:
            user = User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'User with mobile number {mobile_number} does not exist')
            )
            return

        # Clear any existing unused OTPs
        OTPToken.objects.filter(
            mobile_number=mobile_number,
            purpose=purpose,
            is_used=False
        ).update(is_used=True)

        # Create new OTP
        otp_token = OTPToken.objects.create(
            mobile_number=mobile_number,
            purpose=purpose
        )

        # Display OTP
        otp_token.display_to_terminal()

        self.stdout.write(
            self.style.SUCCESS(f'OTP generated successfully for {mobile_number}')
        )