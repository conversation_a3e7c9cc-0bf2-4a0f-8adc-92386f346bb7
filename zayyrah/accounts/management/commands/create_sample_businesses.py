from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
import random

from accounts.models import User, Business, BusinessAdmin, BusinessSubscription


class Command(BaseCommand):
    help = 'Create sample businesses for testing the admin dashboard'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=10,
            help='Number of sample businesses to create',
        )

    def handle(self, *args, **options):
        count = options['count']

        business_names = [
            'Green Valley Grocers', 'Urban Fashion Store', 'TechHub Electronics',
            'Spice Garden Restaurant', 'City Pharmacy Plus', 'BookWorm Cafe',
            'Golden Jewelry House', 'Fresh Mart Express', 'Digital Solutions Store',
            'Comfort Home Furniture', 'Peak Performance Gym', 'Sunrise Bakery',
            'Metro Mobile Repairs', 'Elegant Beauty Salon', 'Fast Track Logistics'
        ]

        cities = ['Karachi', 'Lahore', 'Islamabad', 'Rawalpindi', 'Faisalabad', 'Multan']
        states = ['Sindh', 'Punjab', 'Islamabad Capital Territory', 'Punjab', 'Punjab', 'Punjab']

        business_types = [
            Business.BusinessType.GROCERY,
            Business.BusinessType.CLOTHING,
            Business.BusinessType.ELECTRONICS,
            Business.BusinessType.RESTAURANT,
            Business.BusinessType.PHARMACY,
            Business.BusinessType.RETAIL
        ]

        statuses = [
            Business.BusinessStatus.PENDING,
            Business.BusinessStatus.APPROVED,
            Business.BusinessStatus.APPROVED,
            Business.BusinessStatus.APPROVED,
            Business.BusinessStatus.SUSPENDED
        ]

        self.stdout.write(f'Creating {count} sample businesses...')

        for i in range(count):
            # Create business owner user
            mobile_number = f'03{random.randint(*********, *********)}'

            try:
                user = User.objects.create_user(
                    mobile_number=mobile_number,
                    password='password123',
                    account_type=User.AccountType.BUSINESS,
                    shop_name=business_names[i % len(business_names)]
                )

                # Create business
                business_name = business_names[i % len(business_names)]
                if i >= len(business_names):
                    business_name += f' #{i + 1}'

                city_index = i % len(cities)
                business = Business.objects.create(
                    business_name=business_name,
                    business_type=random.choice(business_types),
                    owner_name=f'Owner {i + 1}',
                    mobile_number=mobile_number,
                    email=f'owner{i + 1}@{business_name.lower().replace(" ", "")}.com',
                    address=f'{random.randint(1, 999)} Main Street, Block {chr(65 + i % 26)}',
                    city=cities[city_index],
                    state=states[city_index],
                    postal_code=f'{random.randint(10000, 99999)}',
                    country='Pakistan',
                    registration_number=f'REG{random.randint(100000, 999999)}',
                    tax_number=f'TAX{random.randint(100000, 999999)}',
                    admin_user=user,
                    status=random.choice(statuses),
                    subscription_plan=random.choice(['basic', 'premium', 'enterprise']),
                    monthly_fee=Decimal(random.choice(['99.99', '199.99', '499.99'])),
                    max_employees=random.randint(5, 50),
                    max_products=random.randint(500, 5000),
                    max_transactions_per_month=random.randint(100, 1000),
                    total_revenue=Decimal(random.randint(5000, 100000)),
                    monthly_revenue=Decimal(random.randint(500, 10000)),
                    total_transactions=random.randint(50, 500)
                )

                # Set approved_at for approved businesses
                if business.status == Business.BusinessStatus.APPROVED:
                    business.approved_at = timezone.now() - timezone.timedelta(days=random.randint(1, 90))
                    business.save()

                # Create business admin
                BusinessAdmin.objects.create(
                    business=business,
                    user=user,
                    can_manage_inventory=True,
                    can_manage_employees=random.choice([True, False]),
                    can_view_reports=True,
                    can_manage_customers=True,
                    can_process_sales=True,
                    can_manage_settings=random.choice([True, False])
                )

                # Create subscription for approved businesses
                if business.status == Business.BusinessStatus.APPROVED:
                    BusinessSubscription.objects.create(
                        business=business,
                        plan_name=business.subscription_plan,
                        amount=business.monthly_fee,
                        start_date=timezone.now() - timezone.timedelta(days=random.randint(1, 365)),
                        end_date=timezone.now() + timezone.timedelta(days=30),
                        status=BusinessSubscription.SubscriptionStatus.ACTIVE,
                        payment_method='Credit Card',
                        transaction_id=f'TXN{random.randint(1000000, 9999999)}'
                    )

                self.stdout.write(
                    self.style.SUCCESS(f'Created business: {business.business_name}')
                )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error creating business {i + 1}: {str(e)}')
                )

        self.stdout.write(
            self.style.SUCCESS(f'Successfully created {count} sample businesses!')
        )