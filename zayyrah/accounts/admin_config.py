from django.contrib import admin
from django.urls import path, reverse
from django.utils.html import format_html
from django.shortcuts import redirect

from . import admin_views


class BusinessAdminSite(admin.AdminSite):
    """
    Custom Admin Site for Business Management
    """
    site_header = 'Zayyrah POS - Site Administration'
    site_title = 'Zayyrah POS Admin'
    index_title = 'Business Management Dashboard'

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path('business-dashboard/', admin_views.business_dashboard, name='business_dashboard'),
            path('approve-business/<int:business_id>/', admin_views.approve_business, name='approve_business'),
            path('reject-business/<int:business_id>/', admin_views.reject_business, name='reject_business'),
            path('suspend-business/<int:business_id>/', admin_views.suspend_business, name='suspend_business'),
            path('analytics-api/', admin_views.business_analytics_api, name='business_analytics_api'),
            path('export-business-data/', admin_views.export_business_data, name='export_business_data'),
        ]
        return custom_urls + urls

    def index(self, request, extra_context=None):
        """
        Override the default admin index to redirect to our custom dashboard
        if user is a superuser
        """
        if request.user.is_superuser:
            return redirect('admin:business_dashboard')
        return super().index(request, extra_context)

    def each_context(self, request):
        """
        Add custom context for all admin templates
        """
        context = super().each_context(request)

        # Add dashboard link to context
        context.update({
            'dashboard_url': reverse('admin:business_dashboard'),
            'show_dashboard_link': request.user.is_superuser,
        })

        return context


# Create custom admin site instance
business_admin_site = BusinessAdminSite(name='business_admin')