:root {
    --primary: #004b23;
    --primary-dark: #003017;
    --primary-light: #e8f4ec;
    --accent: #8ce6b1;
    --text: #101c16;
    --muted: #5c7265;
    --error: #d64545;
    --radius-lg: 24px;
    --radius-md: 16px;
    --radius-sm: 12px;
    --shadow-soft: 0 20px 45px -30px rgba(0, 75, 35, 0.55);
    --shadow-card: 0 20px 40px -20px rgba(0, 68, 31, 0.35);
    --transition: 160ms ease;
    font-family: "Inter", "Segoe UI", -apple-system, BlinkMacSystemFont, sans-serif;
}

* {
    box-sizing: border-box;
}

body.site-body {
    min-height: 100vh;
    margin: 0;
    background: radial-gradient(circle at 10% 20%, rgba(0, 75, 35, 0.1), transparent 55%),
        radial-gradient(circle at 90% 10%, rgba(140, 230, 177, 0.18), transparent 35%),
        linear-gradient(135deg, #f2fbf5, #d9f1e2 40%, #f7fffa);
    display: flex;
    justify-content: center;
    color: var(--text);
    padding: clamp(16px, 4vw, 48px);
}

body.site-body::before {
    content: "";
    position: fixed;
    inset: 0;
    pointer-events: none;
    background: radial-gradient(circle at 50% 10%, rgba(0, 75, 35, 0.08), transparent 70%);
    opacity: 0.65;
}

.site-wrapper {
    position: relative;
    width: min(1100px, 100%);
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: clamp(24px, 5vw, 48px);
}

.mobile-nav {
    display: none;
}

.mobile-nav ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: grid;
    grid-auto-flow: column;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.mobile-nav-link {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 10px 12px;
    border-radius: 999px;
    text-decoration: none;
    color: var(--muted);
    font-weight: 600;
    transition: var(--transition);
}

.mobile-nav-link.active,
.mobile-nav-link:hover {
    color: var(--primary);
    background: rgba(0, 75, 35, 0.12);
}

.site-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(12px);
    border-radius: var(--radius-md);
    padding: clamp(16px, 3vw, 24px);
    box-shadow: var(--shadow-soft);
}

.brand {
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 700;
    font-size: clamp(1.2rem, 2.4vw, 1.6rem);
    color: var(--primary);
    text-decoration: none;
    letter-spacing: 0.02em;
}

.brand-mark {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    box-shadow: 0 8px 16px -8px rgba(0, 75, 35, 0.55);
}

.primary-nav {
    display: flex;
    align-items: center;
    gap: clamp(12px, 3vw, 20px);
}

.primary-nav a {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    padding: 10px 16px;
    border-radius: var(--radius-sm);
    transition: var(--transition);
}

.primary-nav a:hover {
    background: rgba(0, 75, 35, 0.08);
}

.primary-nav .cta {
    background: var(--primary);
    color: white;
    box-shadow: 0 10px 18px -12px rgba(0, 75, 35, 0.6);
}

.primary-nav .cta:hover {
    background: var(--primary-dark);
}

.flash-region {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.flash-message {
    padding: 14px 18px;
    border-radius: var(--radius-sm);
    background: rgba(0, 75, 35, 0.08);
    color: var(--primary-dark);
    font-weight: 500;
}

.flash-message.error {
    background: rgba(214, 69, 69, 0.12);
    color: var(--error);
}

.page-shell {
    display: flex;
    justify-content: center;
    width: 100%;
}

.auth-layout {
    width: 100%;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: clamp(24px, 4vw, 40px);
    align-items: stretch;
}

.auth-illustration {
    position: relative;
    background: linear-gradient(140deg, var(--primary), rgba(0, 75, 35, 0.82));
    border-radius: var(--radius-lg);
    padding: clamp(24px, 5vw, 40px);
    color: white;
    overflow: hidden;
    box-shadow: var(--shadow-card);
}

.auth-illustration::after {
    content: "";
    position: absolute;
    inset: 0;
    background: radial-gradient(circle at 20% 20%, rgba(140, 230, 177, 0.35), transparent 55%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.16), transparent 55%);
    mix-blend-mode: screen;
    pointer-events: none;
}

.auth-illustration h2 {
    font-size: clamp(1.8rem, 3vw, 2.2rem);
    margin-bottom: 16px;
    font-weight: 600;
}

.auth-illustration p {
    margin: 0 0 18px 0;
    line-height: 1.55;
    font-size: 1rem;
}

.highlight-pill {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    border-radius: 999px;
    background: rgba(255, 255, 255, 0.12);
    font-weight: 500;
    margin-bottom: 18px;
}

.feature-list {
    display: grid;
    gap: 12px;
    margin-top: 24px;
    padding: 0;
    list-style: none;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: 12px;
}

.feature-icon {
    width: 28px;
    height: 28px;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.14);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 0.85rem;
}

.auth-card {
    background: rgba(255, 255, 255, 0.98);
    border-radius: var(--radius-lg);
    padding: clamp(32px, 5vw, 44px);
    box-shadow: var(--shadow-card);
    display: flex;
    flex-direction: column;
    gap: clamp(16px, 3vw, 28px);
}

.auth-card header {
    display: flex;
    flex-direction: column;
    gap: 8px;
    text-align: left;
}

.auth-card header h2 {
    margin: 0;
    font-size: clamp(1.6rem, 3vw, 2rem);
    color: var(--primary);
}

.auth-card header p {
    margin: 0;
    color: var(--muted);
    line-height: 1.5;
    font-size: 1rem;
}

.auth-form {
    display: grid;
    gap: 18px;
}

.form-group {
    display: grid;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--muted);
}

.form-group input,
.form-group select,
.auth-form input[type="password"],
.auth-form input[type="text"],
.auth-form input[type="tel"],
.auth-form select {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid rgba(0, 75, 35, 0.16);
    border-radius: var(--radius-sm);
    background: rgba(255, 255, 255, 0.96);
    font-size: 1rem;
    transition: border-color var(--transition), box-shadow var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 75, 35, 0.15);
}

.errorlist {
    list-style: none;
    margin: 0;
    padding: 0;
    color: var(--error);
    font-size: 0.88rem;
}

.field-error {
    color: var(--error);
    font-size: 0.88rem;
}

.non-field-errors {
    padding: 14px 16px;
    border-radius: var(--radius-sm);
    background: rgba(214, 69, 69, 0.12);
    color: var(--error);
    font-weight: 500;
}

.primary-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-weight: 600;
    font-size: 1rem;
    padding: 14px;
    border-radius: var(--radius-sm);
    background: var(--primary);
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 18px 28px -18px rgba(0, 75, 35, 0.7);
}

.primary-button:hover {
    background: var(--primary-dark);
}

.account-switch {
    font-size: 0.95rem;
    text-align: center;
    color: var(--muted);
}

.account-switch a {
    color: var(--primary);
    font-weight: 600;
    text-decoration: none;
}

.account-switch a:hover {
    text-decoration: underline;
}

.password-hint {
    font-size: 0.85rem;
    color: rgba(16, 28, 22, 0.65);
}

.form-footer {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.shop-name-group {
    transition: var(--transition);
}

.shop-name-group[data-hidden="true"] {
    display: none;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.96);
    border-radius: var(--radius-md);
    padding: clamp(24px, 4vw, 36px);
    box-shadow: var(--shadow-card);
}

@media (max-width: 768px) {
    body.site-body {
        padding: clamp(16px, 6vw, 32px);
        padding-bottom: clamp(96px, 10vh, 120px);
    }

    .site-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }

    .primary-nav {
        display: none;
    }

    .mobile-nav {
        display: block;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 14px clamp(16px, 6vw, 28px);
        padding-bottom: calc(14px + env(safe-area-inset-bottom));
        background: rgba(255, 255, 255, 0.92);
        backdrop-filter: blur(16px);
        border-top: 1px solid rgba(0, 75, 35, 0.16);
        box-shadow: 0 -18px 40px -28px rgba(0, 68, 31, 0.45);
        z-index: 999;
    }

    .mobile-nav ul {
        grid-auto-flow: row;
        grid-template-columns: repeat(auto-fit, minmax(96px, 1fr));
    }

    .auth-layout {
        grid-template-columns: 1fr;
    }

    .auth-illustration {
        order: 2;
        text-align: center;
    }

    .feature-list {
        text-align: left;
    }

    .auth-card {
        order: 1;
    }
}

@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
