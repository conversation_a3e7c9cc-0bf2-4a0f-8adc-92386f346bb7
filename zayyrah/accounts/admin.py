from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.urls import reverse, path
from django.db.models import Sum, Count, Avg
from django.utils import timezone
from django.shortcuts import redirect
from django.contrib import messages
from django.http import JsonResponse
from datetime import datetime, timedelta

from .forms import AdminUserChangeForm, AdminUserCreationForm
from .models import User, PasswordResetToken, OTPToken, Business, BusinessAdmin, BusinessSubscription
from . import admin_views


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    add_form = AdminUserCreationForm
    form = AdminUserChangeForm
    model = User
    list_display = ('mobile_number', 'account_type', 'shop_name', 'is_staff', 'is_active')
    list_filter = ('account_type', 'is_staff', 'is_active')
    readonly_fields = ('date_joined',)
    ordering = ('mobile_number',)
    search_fields = ('mobile_number', 'shop_name')
    fieldsets = (
        (None, {'fields': ('mobile_number', 'password')}),
        (_('Profile'), {'fields': ('account_type', 'shop_name')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    add_fieldsets = (
        (
            None,
            {
                'classes': ('wide',),
                'fields': (
                    'mobile_number',
                    'account_type',
                    'shop_name',
                    'password1',
                    'password2',
                    'is_staff',
                    'is_active',
                ),
            },
        ),
    )
    filter_horizontal = ('groups', 'user_permissions')


@admin.register(PasswordResetToken)
class PasswordResetTokenAdmin(admin.ModelAdmin):
    list_display = ('user', 'token', 'created_at', 'expires_at', 'is_used')
    list_filter = ('is_used', 'created_at', 'expires_at')
    search_fields = ('user__mobile_number', 'token')
    readonly_fields = ('token', 'created_at', 'expires_at')
    ordering = ('-created_at',)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


@admin.register(OTPToken)
class OTPTokenAdmin(admin.ModelAdmin):
    list_display = ('mobile_number', 'otp_code', 'purpose', 'created_at', 'expires_at', 'is_used', 'attempts')
    list_filter = ('purpose', 'is_used', 'created_at', 'expires_at')
    search_fields = ('mobile_number', 'otp_code')
    readonly_fields = ('otp_code', 'created_at', 'expires_at')
    ordering = ('-created_at',)

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False


class BusinessAdminInline(admin.TabularInline):
    model = BusinessAdmin
    extra = 0
    fields = ('user', 'can_manage_inventory', 'can_manage_employees', 'can_view_reports', 'is_active')
    readonly_fields = ('created_at',)


class BusinessSubscriptionInline(admin.TabularInline):
    model = BusinessSubscription
    extra = 0
    fields = ('plan_name', 'amount', 'start_date', 'end_date', 'status')
    readonly_fields = ('created_at',)


@admin.register(Business)
class BusinessDashboardAdmin(admin.ModelAdmin):
    list_display = (
        'business_name', 'owner_name', 'business_type', 'status_badge',
        'subscription_plan', 'monthly_revenue', 'total_transactions',
        'created_at', 'actions_column'
    )
    list_filter = (
        'status', 'business_type', 'subscription_plan', 'city', 'state', 'created_at'
    )
    search_fields = (
        'business_name', 'owner_name', 'mobile_number', 'email',
        'admin_user__mobile_number', 'registration_number'
    )
    readonly_fields = (
        'created_at', 'approved_at', 'total_revenue', 'monthly_revenue',
        'total_transactions', 'business_stats', 'last_activity'
    )

    fieldsets = (
        (_('Business Information'), {
            'fields': (
                'business_name', 'business_type', 'owner_name',
                'mobile_number', 'email'
            )
        }),
        (_('Address'), {
            'fields': ('address', 'city', 'state', 'postal_code', 'country'),
            'classes': ('collapse',)
        }),
        (_('Legal Information'), {
            'fields': ('registration_number', 'tax_number', 'license_number'),
            'classes': ('collapse',)
        }),
        (_('Administration'), {
            'fields': ('admin_user', 'status', 'approved_at')
        }),
        (_('Subscription & Limits'), {
            'fields': (
                'subscription_plan', 'monthly_fee', 'max_employees',
                'max_products', 'max_transactions_per_month'
            )
        }),
        (_('Statistics'), {
            'fields': (
                'business_stats', 'total_revenue', 'monthly_revenue',
                'total_transactions', 'last_activity'
            ),
            'classes': ('collapse',)
        }),
        (_('Important Dates'), {
            'fields': ('created_at',)
        }),
    )

    inlines = [BusinessAdminInline, BusinessSubscriptionInline]
    actions = ['approve_businesses', 'suspend_businesses', 'reject_businesses', 'send_notifications']

    def status_badge(self, obj):
        color = obj.get_status_display_color()
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def actions_column(self, obj):
        buttons = []

        if obj.status == Business.BusinessStatus.PENDING:
            buttons.append(
                f'<button onclick="approveRejectBusiness({obj.pk}, \'approve\')" '
                f'class="button" style="background: green; color: white; padding: 5px; margin: 2px;">Approve</button>'
            )
            buttons.append(
                f'<button onclick="approveRejectBusiness({obj.pk}, \'reject\')" '
                f'class="button" style="background: red; color: white; padding: 5px; margin: 2px;">Reject</button>'
            )

        if obj.status == Business.BusinessStatus.APPROVED:
            buttons.append(
                f'<button onclick="approveRejectBusiness({obj.pk}, \'suspend\')" '
                f'class="button" style="background: orange; color: white; padding: 5px; margin: 2px;">Suspend</button>'
            )

        return format_html(' '.join(buttons))
    actions_column.short_description = 'Actions'

    def business_stats(self, obj):
        stats = obj.get_current_month_stats()
        return format_html(
            '<strong>This Month:</strong><br>'
            'Transactions: {}<br>'
            'Revenue: ${:,.2f}<br>'
            'Avg Transaction: ${:,.2f}',
            stats['transactions'],
            float(stats['revenue']),
            float(stats['avg_transaction'])
        )
    business_stats.short_description = 'Current Month Stats'

    def approve_businesses(self, request, queryset):
        updated = queryset.filter(status=Business.BusinessStatus.PENDING).update(
            status=Business.BusinessStatus.APPROVED,
            approved_at=timezone.now()
        )
        self.message_user(request, f'{updated} businesses approved successfully.', messages.SUCCESS)
    approve_businesses.short_description = "Approve selected businesses"

    def suspend_businesses(self, request, queryset):
        updated = queryset.filter(status=Business.BusinessStatus.APPROVED).update(
            status=Business.BusinessStatus.SUSPENDED
        )
        self.message_user(request, f'{updated} businesses suspended.', messages.WARNING)
    suspend_businesses.short_description = "Suspend selected businesses"

    def reject_businesses(self, request, queryset):
        updated = queryset.filter(status=Business.BusinessStatus.PENDING).update(
            status=Business.BusinessStatus.REJECTED
        )
        self.message_user(request, f'{updated} businesses rejected.', messages.ERROR)
    reject_businesses.short_description = "Reject selected businesses"

    def send_notifications(self, request, queryset):
        count = queryset.count()
        self.message_user(request, f'Notifications sent to {count} businesses.', messages.INFO)
    send_notifications.short_description = "Send notifications to selected businesses"

    def changelist_view(self, request, extra_context=None):
        extra_context = extra_context or {}

        # Business summary statistics
        total_businesses = Business.objects.count()
        pending_businesses = Business.objects.filter(status=Business.BusinessStatus.PENDING).count()
        approved_businesses = Business.objects.filter(status=Business.BusinessStatus.APPROVED).count()
        suspended_businesses = Business.objects.filter(status=Business.BusinessStatus.SUSPENDED).count()

        # Revenue statistics
        total_revenue = Business.objects.aggregate(total=Sum('total_revenue'))['total'] or 0
        monthly_revenue = Business.objects.aggregate(total=Sum('monthly_revenue'))['total'] or 0

        # Recent registrations
        recent_businesses = Business.objects.filter(
            created_at__gte=timezone.now() - timedelta(days=7)
        ).count()

        extra_context.update({
            'total_businesses': total_businesses,
            'pending_businesses': pending_businesses,
            'approved_businesses': approved_businesses,
            'suspended_businesses': suspended_businesses,
            'total_revenue': total_revenue,
            'monthly_revenue': monthly_revenue,
            'recent_businesses': recent_businesses,
        })

        return super().changelist_view(request, extra_context=extra_context)


@admin.register(BusinessAdmin)
class BusinessUserAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'business', 'permissions_summary', 'is_active', 'created_at'
    )
    list_filter = (
        'is_active', 'can_manage_inventory', 'can_manage_employees',
        'can_view_reports', 'created_at'
    )
    search_fields = (
        'user__mobile_number', 'business__business_name', 'business__owner_name'
    )
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Administrator'), {
            'fields': ('business', 'user', 'is_active')
        }),
        (_('Permissions'), {
            'fields': (
                'can_manage_inventory', 'can_manage_employees', 'can_view_reports',
                'can_manage_customers', 'can_process_sales', 'can_manage_settings'
            )
        }),
        (_('Dates'), {
            'fields': ('created_at',)
        }),
    )

    def permissions_summary(self, obj):
        permissions = []
        if obj.can_manage_inventory:
            permissions.append('Inventory')
        if obj.can_manage_employees:
            permissions.append('Employees')
        if obj.can_view_reports:
            permissions.append('Reports')
        if obj.can_manage_customers:
            permissions.append('Customers')
        if obj.can_process_sales:
            permissions.append('Sales')
        if obj.can_manage_settings:
            permissions.append('Settings')

        return ', '.join(permissions) if permissions else 'No permissions'
    permissions_summary.short_description = 'Permissions'


@admin.register(BusinessSubscription)
class BusinessSubscriptionAdmin(admin.ModelAdmin):
    list_display = (
        'business', 'plan_name', 'amount', 'status_badge',
        'start_date', 'end_date', 'is_active_badge'
    )
    list_filter = ('status', 'plan_name', 'start_date', 'end_date')
    search_fields = ('business__business_name', 'plan_name', 'transaction_id')
    readonly_fields = ('created_at',)

    fieldsets = (
        (_('Subscription Details'), {
            'fields': ('business', 'plan_name', 'amount', 'status')
        }),
        (_('Duration'), {
            'fields': ('start_date', 'end_date')
        }),
        (_('Payment Information'), {
            'fields': ('payment_method', 'transaction_id')
        }),
        (_('Dates'), {
            'fields': ('created_at',)
        }),
    )

    def status_badge(self, obj):
        colors = {
            'active': 'green',
            'expired': 'red',
            'cancelled': 'gray',
            'pending_payment': 'orange'
        }
        color = colors.get(obj.status, 'gray')
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            obj.get_status_display()
        )
    status_badge.short_description = 'Status'

    def is_active_badge(self, obj):
        is_active = obj.is_active()
        color = 'green' if is_active else 'red'
        text = 'Yes' if is_active else 'No'
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            text
        )
    is_active_badge.short_description = 'Active'


# Add custom URLs to admin site
def get_admin_urls():
    from django.urls import path
    return [
        path('business-dashboard/', admin_views.business_dashboard, name='business_dashboard'),
        path('approve-business/<int:business_id>/', admin_views.approve_business, name='approve_business'),
        path('reject-business/<int:business_id>/', admin_views.reject_business, name='reject_business'),
        path('suspend-business/<int:business_id>/', admin_views.suspend_business, name='suspend_business'),
        path('analytics-api/', admin_views.business_analytics_api, name='business_analytics_api'),
        path('export-business-data/', admin_views.export_business_data, name='export_business_data'),
    ]

# Monkey patch the admin site to include our custom URLs
original_get_urls = admin.site.get_urls

def custom_get_urls():
    urls = original_get_urls()
    custom_urls = get_admin_urls()
    return custom_urls + urls

admin.site.get_urls = custom_get_urls
