from rest_framework import status, generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import login
from .serializers import (
    UserRegistrationSerializer, UserLoginSerializer,
    UserProfileSerializer, UserProfileUpdateSerializer,
    PasswordResetRequestSerializer, OTPVerificationSerializer,
    PasswordResetConfirmSerializer
)
from .models import User, PasswordResetToken, OTPToken


def get_tokens_for_user(user):
    """Generate JWT tokens for user"""
    refresh = RefreshToken.for_user(user)
    return {
        'refresh': str(refresh),
        'access': str(refresh.access_token),
    }


@api_view(['POST'])
@permission_classes([AllowAny])
def register_view(request):
    """User registration endpoint"""
    serializer = UserRegistrationSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.save()
        tokens = get_tokens_for_user(user)

        return Response({
            'success': True,
            'message': 'Account created successfully',
            'data': UserProfileSerializer(user).data,
            'tokens': tokens
        }, status=status.HTTP_201_CREATED)

    return Response({
        'success': False,
        'message': 'Validation failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """User login endpoint"""
    import logging
    import json

    logger = logging.getLogger(__name__)

    # Log incoming request details
    client_ip = request.META.get('HTTP_X_FORWARDED_FOR', request.META.get('REMOTE_ADDR', 'Unknown'))
    user_agent = request.META.get('HTTP_USER_AGENT', 'Unknown')

    # Log request data (safely, without passwords in production)
    request_data = request.data.copy()
    received_mobile = request_data.get('mobile_number', 'NOT_PROVIDED')
    received_password_present = bool(request_data.get('password'))

    logger.warning(f"LOGIN ATTEMPT - IP: {client_ip}, User-Agent: {user_agent}")
    logger.warning(f"LOGIN DATA - Mobile: '{received_mobile}', Password Present: {received_password_present}")
    logger.warning(f"LOGIN RAW DATA KEYS: {list(request_data.keys())}")

    # Log data types and lengths for debugging
    if 'mobile_number' in request_data:
        mobile_val = request_data['mobile_number']
        logger.warning(f"MOBILE DETAILS - Value: '{mobile_val}', Type: {type(mobile_val)}, Length: {len(str(mobile_val))}")

    if 'password' in request_data:
        password_val = request_data['password']
        logger.warning(f"PASSWORD DETAILS - Present: True, Type: {type(password_val)}, Length: {len(str(password_val))}")

    # Check if user exists before validation
    if received_mobile and received_mobile != 'NOT_PROVIDED':
        from .models import User
        try:
            user_exists = User.objects.filter(mobile_number=received_mobile).exists()
            logger.warning(f"USER EXISTS CHECK - Mobile '{received_mobile}': {user_exists}")

            if user_exists:
                user = User.objects.get(mobile_number=received_mobile)
                logger.warning(f"USER DETAILS - ID: {user.id}, Active: {user.is_active}, Account Type: {user.account_type}")
        except Exception as e:
            logger.warning(f"USER LOOKUP ERROR: {e}")

    serializer = UserLoginSerializer(data=request.data, context={'request': request})
    if serializer.is_valid():
        user = serializer.validated_data['user']
        tokens = get_tokens_for_user(user)

        logger.warning(f"LOGIN SUCCESS - User: {user.mobile_number}, ID: {user.id}")

        return Response({
            'success': True,
            'message': 'Login successful',
            'data': UserProfileSerializer(user).data,
            'tokens': tokens
        }, status=status.HTTP_200_OK)

    # Log validation errors in detail
    logger.warning(f"LOGIN VALIDATION FAILED - Errors: {serializer.errors}")

    return Response({
        'success': False,
        'message': 'Invalid credentials',
        'errors': serializer.errors
    }, status=status.HTTP_401_UNAUTHORIZED)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """User logout endpoint - blacklist the refresh token"""
    try:
        refresh_token = request.data.get('refresh_token')
        if refresh_token:
            token = RefreshToken(refresh_token)
            token.blacklist()

        return Response({
            'success': True,
            'message': 'Logged out successfully'
        }, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({
            'success': False,
            'message': 'Error during logout'
        }, status=status.HTTP_400_BAD_REQUEST)


class ProfileView(generics.RetrieveAPIView):
    """Get user profile"""
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })


class ProfileUpdateView(generics.UpdateAPIView):
    """Update user profile"""
    serializer_class = UserProfileUpdateSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        return self.request.user

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Profile updated successfully',
                'data': UserProfileSerializer(instance).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_request_view(request):
    """Request password reset - generates and sends OTP"""
    serializer = PasswordResetRequestSerializer(data=request.data)
    if serializer.is_valid():
        mobile_number = serializer.validated_data['mobile_number']

        # Clear any existing unused OTPs for this mobile number
        OTPToken.objects.filter(
            mobile_number=mobile_number,
            purpose='password_reset',
            is_used=False
        ).update(is_used=True)

        # Create new OTP
        otp_token = OTPToken.objects.create(
            mobile_number=mobile_number,
            purpose='password_reset'
        )

        # Display OTP to terminal for development
        otp_token.display_to_terminal()

        return Response({
            'success': True,
            'message': 'OTP sent successfully. Check your terminal for the OTP code.',
            'data': {
                'mobile_number': mobile_number,
                'expires_at': otp_token.expires_at.isoformat()
            }
        }, status=status.HTTP_200_OK)

    return Response({
        'success': False,
        'message': 'Validation failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def verify_otp_view(request):
    """Verify OTP code"""
    serializer = OTPVerificationSerializer(data=request.data)
    if serializer.is_valid():
        otp_token = serializer.validated_data['otp_token']
        otp_code = serializer.validated_data['otp_code']

        if otp_token.verify_otp(otp_code):
            return Response({
                'success': True,
                'message': 'OTP verified successfully',
                'data': {
                    'mobile_number': otp_token.mobile_number,
                    'purpose': otp_token.purpose
                }
            }, status=status.HTTP_200_OK)
        else:
            return Response({
                'success': False,
                'message': 'Invalid or expired OTP'
            }, status=status.HTTP_400_BAD_REQUEST)

    return Response({
        'success': False,
        'message': 'Validation failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([AllowAny])
def password_reset_confirm_view(request):
    """Confirm password reset with OTP and set new password"""
    serializer = PasswordResetConfirmSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        new_password = serializer.validated_data['new_password']

        # Set new password
        user.set_password(new_password)
        user.save()

        # Clear any remaining password reset OTPs for this user
        OTPToken.objects.filter(
            mobile_number=user.mobile_number,
            purpose='password_reset',
            is_used=False
        ).update(is_used=True)

        # Generate new tokens for immediate login
        tokens = get_tokens_for_user(user)

        return Response({
            'success': True,
            'message': 'Password reset successful',
            'data': UserProfileSerializer(user).data,
            'tokens': tokens
        }, status=status.HTTP_200_OK)

    return Response({
        'success': False,
        'message': 'Validation failed',
        'errors': serializer.errors
    }, status=status.HTTP_400_BAD_REQUEST)