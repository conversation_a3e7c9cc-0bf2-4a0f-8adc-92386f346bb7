from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.http import JsonResponse
from django.db.models import Sum, Count, Avg, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from .models import Business, BusinessAdmin, BusinessSubscription, User


@staff_member_required
def business_dashboard(request):
    """
    Main site admin dashboard showing business analytics and management overview
    """
    # Get date ranges
    today = timezone.now().date()
    current_month_start = today.replace(day=1)
    last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
    last_month_end = current_month_start - timedelta(days=1)

    # Business statistics
    business_stats = {
        'total': Business.objects.count(),
        'pending': Business.objects.filter(status=Business.BusinessStatus.PENDING).count(),
        'approved': Business.objects.filter(status=Business.BusinessStatus.APPROVED).count(),
        'suspended': Business.objects.filter(status=Business.BusinessStatus.SUSPENDED).count(),
        'rejected': Business.objects.filter(status=Business.BusinessStatus.REJECTED).count(),
    }

    # Recent registrations (last 7 days)
    recent_businesses = Business.objects.filter(
        created_at__gte=timezone.now() - timedelta(days=7)
    ).count()

    # Monthly registrations comparison
    this_month_registrations = Business.objects.filter(
        created_at__gte=current_month_start
    ).count()

    last_month_registrations = Business.objects.filter(
        created_at__gte=last_month_start,
        created_at__lte=last_month_end
    ).count()

    # Revenue statistics
    revenue_stats = Business.objects.aggregate(
        total_revenue=Sum('total_revenue'),
        monthly_revenue_sum=Sum('monthly_revenue'),
        avg_monthly_revenue=Avg('monthly_revenue')
    )

    # Subscription statistics
    subscription_stats = BusinessSubscription.objects.values('plan_name').annotate(
        count=Count('id'),
        total_revenue=Sum('amount')
    ).order_by('-count')

    # Business type distribution
    business_type_stats = Business.objects.values('business_type').annotate(
        count=Count('id')
    ).order_by('-count')

    # Recent pending businesses
    pending_businesses = Business.objects.filter(
        status=Business.BusinessStatus.PENDING
    ).order_by('-created_at')[:10]

    # Top performing businesses
    top_businesses = Business.objects.filter(
        status=Business.BusinessStatus.APPROVED
    ).order_by('-monthly_revenue')[:10]

    # Active subscriptions
    active_subscriptions = BusinessSubscription.objects.filter(
        status=BusinessSubscription.SubscriptionStatus.ACTIVE,
        end_date__gte=timezone.now()
    ).count()

    # Users statistics
    user_stats = {
        'total_users': User.objects.count(),
        'business_users': User.objects.filter(account_type=User.AccountType.BUSINESS).count(),
        'customer_users': User.objects.filter(account_type=User.AccountType.CUSTOMER).count(),
        'recent_users': User.objects.filter(
            date_joined__gte=timezone.now() - timedelta(days=7)
        ).count()
    }

    context = {
        'business_stats': business_stats,
        'recent_businesses': recent_businesses,
        'this_month_registrations': this_month_registrations,
        'last_month_registrations': last_month_registrations,
        'revenue_stats': revenue_stats,
        'subscription_stats': subscription_stats,
        'business_type_stats': business_type_stats,
        'pending_businesses': pending_businesses,
        'top_businesses': top_businesses,
        'active_subscriptions': active_subscriptions,
        'user_stats': user_stats,
        'title': 'Site Admin Dashboard',
    }

    return render(request, 'admin/accounts/business_dashboard.html', context)


@staff_member_required
def approve_business(request, business_id):
    """Approve a pending business"""
    business = get_object_or_404(Business, id=business_id)

    if business.status == Business.BusinessStatus.PENDING:
        business.status = Business.BusinessStatus.APPROVED
        business.approved_at = timezone.now()
        business.save()

        messages.success(request, f'Business "{business.business_name}" has been approved successfully.')
    else:
        messages.error(request, 'Only pending businesses can be approved.')

    return redirect('admin:accounts_business_changelist')


@staff_member_required
def reject_business(request, business_id):
    """Reject a pending business"""
    business = get_object_or_404(Business, id=business_id)

    if business.status == Business.BusinessStatus.PENDING:
        business.status = Business.BusinessStatus.REJECTED
        business.save()

        messages.warning(request, f'Business "{business.business_name}" has been rejected.')
    else:
        messages.error(request, 'Only pending businesses can be rejected.')

    return redirect('admin:accounts_business_changelist')


@staff_member_required
def suspend_business(request, business_id):
    """Suspend an approved business"""
    business = get_object_or_404(Business, id=business_id)

    if business.status == Business.BusinessStatus.APPROVED:
        business.status = Business.BusinessStatus.SUSPENDED
        business.save()

        messages.warning(request, f'Business "{business.business_name}" has been suspended.')
    else:
        messages.error(request, 'Only approved businesses can be suspended.')

    return redirect('admin:accounts_business_changelist')


@staff_member_required
def business_analytics_api(request):
    """API endpoint for dashboard analytics data"""

    # Get timeframe from request
    timeframe = request.GET.get('timeframe', '7')  # Default 7 days
    days = int(timeframe)

    start_date = timezone.now() - timedelta(days=days)

    # Registration trends
    registrations = []
    revenues = []
    dates = []

    for i in range(days):
        date = start_date + timedelta(days=i)
        date_str = date.strftime('%Y-%m-%d')
        dates.append(date_str)

        # Count registrations for this day
        day_registrations = Business.objects.filter(
            created_at__date=date.date()
        ).count()
        registrations.append(day_registrations)

        # Sum revenue for this day
        day_revenue = Business.objects.filter(
            created_at__date=date.date()
        ).aggregate(total=Sum('monthly_revenue'))['total'] or 0
        revenues.append(float(day_revenue))

    # Business status distribution
    status_data = []
    for status, label in Business.BusinessStatus.choices:
        count = Business.objects.filter(status=status).count()
        status_data.append({
            'label': label,
            'value': count,
            'color': Business().get_status_display_color() if hasattr(Business(), 'get_status_display_color') else '#007cba'
        })

    # Subscription plan distribution
    subscription_data = []
    plans = BusinessSubscription.objects.values('plan_name').annotate(
        count=Count('id')
    ).order_by('-count')

    for plan in plans:
        subscription_data.append({
            'label': plan['plan_name'].title(),
            'value': plan['count']
        })

    return JsonResponse({
        'registrations': {
            'labels': dates,
            'data': registrations
        },
        'revenues': {
            'labels': dates,
            'data': revenues
        },
        'status_distribution': status_data,
        'subscription_distribution': subscription_data
    })


@staff_member_required
def export_business_data(request):
    """Export business data to CSV"""
    import csv
    from django.http import HttpResponse

    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="businesses.csv"'

    writer = csv.writer(response)
    writer.writerow([
        'Business Name', 'Owner Name', 'Business Type', 'Status',
        'Mobile Number', 'Email', 'City', 'State',
        'Registration Date', 'Approved Date', 'Subscription Plan',
        'Monthly Fee', 'Total Revenue', 'Monthly Revenue', 'Total Transactions'
    ])

    businesses = Business.objects.all().order_by('-created_at')

    for business in businesses:
        writer.writerow([
            business.business_name,
            business.owner_name,
            business.get_business_type_display(),
            business.get_status_display(),
            business.mobile_number,
            business.email or '',
            business.city,
            business.state,
            business.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            business.approved_at.strftime('%Y-%m-%d %H:%M:%S') if business.approved_at else '',
            business.subscription_plan,
            float(business.monthly_fee),
            float(business.total_revenue),
            float(business.monthly_revenue),
            business.total_transactions
        ])

    return response