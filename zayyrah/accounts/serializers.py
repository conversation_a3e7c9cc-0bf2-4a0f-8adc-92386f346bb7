from rest_framework import serializers
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from .models import User, PasswordResetToken, OTPToken


class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, validators=[validate_password])
    password_confirm = serializers.CharField(write_only=True)

    class Meta:
        model = User
        fields = ('mobile_number', 'password', 'password_confirm', 'account_type', 'shop_name')

    def validate(self, attrs):
        if attrs['password'] != attrs['password_confirm']:
            raise serializers.ValidationError("Passwords don't match.")

        # Check if business account has shop_name
        if attrs['account_type'] == User.AccountType.BUSINESS and not attrs.get('shop_name'):
            raise serializers.ValidationError("Shop name is required for business accounts.")

        return attrs

    def create(self, validated_data):
        validated_data.pop('password_confirm')
        password = validated_data.pop('password')
        user = User.objects.create_user(password=password, **validated_data)
        return user


class UserLoginSerializer(serializers.Serializer):
    mobile_number = serializers.CharField()
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        import logging
        logger = logging.getLogger(__name__)

        mobile_number = attrs.get('mobile_number')
        password = attrs.get('password')

        logger.warning(f"SERIALIZER VALIDATE - Mobile: '{mobile_number}', Password Present: {bool(password)}")

        if mobile_number and password:
            logger.warning(f"ATTEMPTING AUTHENTICATION - Mobile: '{mobile_number}'")

            # Check if user exists before authenticate
            from .models import User
            try:
                user_obj = User.objects.get(mobile_number=mobile_number)
                logger.warning(f"USER FOUND - ID: {user_obj.id}, Active: {user_obj.is_active}")

                # Test password manually
                password_correct = user_obj.check_password(password)
                logger.warning(f"PASSWORD CHECK - Correct: {password_correct}")
            except User.DoesNotExist:
                logger.warning(f"USER NOT FOUND - Mobile: '{mobile_number}'")
            except Exception as e:
                logger.warning(f"USER LOOKUP ERROR in serializer: {e}")

            user = authenticate(
                request=self.context.get('request'),
                username=mobile_number,
                password=password
            )

            logger.warning(f"AUTHENTICATE RESULT - User: {user}")

            if not user:
                logger.warning(f"AUTHENTICATION FAILED - No user returned")
                raise serializers.ValidationError('Invalid credentials.')
            if not user.is_active:
                logger.warning(f"USER INACTIVE - Mobile: '{mobile_number}'")
                raise serializers.ValidationError('User account is disabled.')
            attrs['user'] = user
            return attrs
        else:
            logger.warning(f"MISSING CREDENTIALS - Mobile: '{mobile_number}', Password: {bool(password)}")
            raise serializers.ValidationError('Must include mobile_number and password.')


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'mobile_number', 'account_type', 'shop_name', 'is_active', 'date_joined')
        read_only_fields = ('id', 'mobile_number', 'date_joined')


class UserProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('account_type', 'shop_name')

    def validate(self, attrs):
        # Check if business account has shop_name
        if attrs.get('account_type') == User.AccountType.BUSINESS and not attrs.get('shop_name'):
            raise serializers.ValidationError("Shop name is required for business accounts.")
        return attrs


class PasswordResetRequestSerializer(serializers.Serializer):
    """Serializer for requesting password reset"""
    mobile_number = serializers.CharField(max_length=15)

    def validate_mobile_number(self, value):
        try:
            user = User.objects.get(mobile_number=value)
            if not user.is_active:
                raise serializers.ValidationError("User account is disabled.")
        except User.DoesNotExist:
            raise serializers.ValidationError("User with this mobile number does not exist.")
        return value


class OTPVerificationSerializer(serializers.Serializer):
    """Serializer for OTP verification"""
    mobile_number = serializers.CharField(max_length=15)
    otp_code = serializers.CharField(max_length=6)
    purpose = serializers.ChoiceField(choices=[
        ('password_reset', 'Password Reset'),
        ('account_verification', 'Account Verification'),
    ])

    def validate(self, attrs):
        mobile_number = attrs['mobile_number']
        otp_code = attrs['otp_code']
        purpose = attrs['purpose']

        try:
            otp_token = OTPToken.objects.filter(
                mobile_number=mobile_number,
                purpose=purpose,
                is_used=False
            ).latest('created_at')
        except OTPToken.DoesNotExist:
            raise serializers.ValidationError("No valid OTP found for this mobile number.")

        if not otp_token.is_valid():
            raise serializers.ValidationError("OTP has expired or exceeded maximum attempts.")

        attrs['otp_token'] = otp_token
        return attrs


class PasswordResetConfirmSerializer(serializers.Serializer):
    """Serializer for confirming password reset"""
    mobile_number = serializers.CharField(max_length=15)
    otp_code = serializers.CharField(max_length=6)
    new_password = serializers.CharField(write_only=True, validators=[validate_password])
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['confirm_password']:
            raise serializers.ValidationError("Passwords don't match.")

        mobile_number = attrs['mobile_number']
        otp_code = attrs['otp_code']

        try:
            user = User.objects.get(mobile_number=mobile_number)
        except User.DoesNotExist:
            raise serializers.ValidationError("User does not exist.")

        try:
            otp_token = OTPToken.objects.filter(
                mobile_number=mobile_number,
                purpose='password_reset',
                is_used=False
            ).latest('created_at')
        except OTPToken.DoesNotExist:
            raise serializers.ValidationError("No valid OTP found.")

        if not otp_token.verify_otp(otp_code):
            raise serializers.ValidationError("Invalid or expired OTP.")

        attrs['user'] = user
        return attrs