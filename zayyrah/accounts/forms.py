from django import forms
from django.contrib.auth.forms import Authentication<PERSON>orm, ReadOnlyPasswordHashField

from .models import User


class UserRegistrationForm(forms.ModelForm):
    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={'placeholder': 'Minimum 8 characters', 'autocomplete': 'new-password'}),
    )
    password2 = forms.CharField(
        label='Confirm password',
        widget=forms.PasswordInput(attrs={'placeholder': 'Re-enter your password', 'autocomplete': 'new-password'}),
    )

    class Meta:
        model = User
        fields = ('mobile_number', 'account_type', 'shop_name')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['mobile_number'].widget.attrs.update(
            {
                'placeholder': 'e.g. ***********',
                'autocomplete': 'tel',
                'inputmode': 'numeric',
            }
        )
        self.fields['account_type'].widget.attrs.update({'class': 'account-type-select'})
        self.fields['shop_name'].widget.attrs.update({'placeholder': 'e.g. Emerald Boutique'})

    def clean_shop_name(self):
        shop_name = self.cleaned_data.get('shop_name', '').strip()
        account_type = self.cleaned_data.get('account_type')
        if account_type == User.AccountType.BUSINESS and not shop_name:
            raise forms.ValidationError('Shop name is required for business accounts.')
        return shop_name

    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError('Passwords do not match.')
        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data['password1'])
        if commit:
            user.save()
        return user


class MobileAuthenticationForm(AuthenticationForm):
    username = forms.CharField(
        label='Mobile number',
        widget=forms.TextInput(
            attrs={
                'autofocus': True,
                'placeholder': 'e.g. ***********',
                'autocomplete': 'tel',
                'inputmode': 'numeric',
            }
        ),
        max_length=15,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['password'].widget.attrs.update(
            {
                'placeholder': 'Enter your password',
                'autocomplete': 'current-password',
            }
        )


class AdminUserCreationForm(forms.ModelForm):
    password1 = forms.CharField(label='Password', widget=forms.PasswordInput)
    password2 = forms.CharField(label='Confirm password', widget=forms.PasswordInput)

    class Meta:
        model = User
        fields = ('mobile_number', 'account_type', 'shop_name', 'is_staff', 'is_active')

    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')
        if password1 and password2 and password1 != password2:
            raise forms.ValidationError('Passwords do not match.')
        return cleaned_data

    def save(self, commit=True):
        user = super().save(commit=False)
        user.set_password(self.cleaned_data['password1'])
        if commit:
            user.save()
        return user


class AdminUserChangeForm(forms.ModelForm):
    password = ReadOnlyPasswordHashField(label='Password')

    class Meta:
        model = User
        fields = (
            'mobile_number',
            'password',
            'account_type',
            'shop_name',
            'is_active',
            'is_staff',
            'is_superuser',
            'groups',
            'user_permissions',
        )

    def clean_password(self):
        return self.initial.get('password')
