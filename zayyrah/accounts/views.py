from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect, render
from django.utils.decorators import method_decorator
from django.views import View

from .forms import UserRegistrationForm


@method_decorator(login_required, name='dispatch')
class DashboardView(View):
    template_name = 'accounts/dashboard.html'

    def get(self, request):
        # If user is a site admin (superuser), redirect to admin dashboard
        if request.user.is_superuser:
            return redirect('/admin/business-dashboard/')

        from employees.models import Employee, Department
        from customers.models import Customer

        # Get dashboard statistics
        context = {
            'total_employees': Employee.objects.filter(employment_status='active').count(),
            'departments_count': Department.objects.count(),
            'total_customers': Customer.objects.count(),
            'monthly_revenue': 0,  # TODO: Calculate from sales
            'sales_count': 0,  # TODO: Get from POS sales
            'inventory_count': 0,  # TODO: Get from inventory
            'low_stock_items': 0,  # TODO: Get low stock count
        }

        return render(request, self.template_name, context)


class SignupView(View):
    template_name = 'accounts/signup.html'

    def get(self, request):
        if request.user.is_authenticated:
            return redirect('accounts:dashboard')
        form = UserRegistrationForm()
        return render(request, self.template_name, {'form': form})

    def post(self, request):
        if request.user.is_authenticated:
            return redirect('accounts:dashboard')
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            return redirect('accounts:dashboard')
        return render(request, self.template_name, {'form': form})


class LogoutRedirectView(View):
    def get(self, request):
        logout(request)
        return redirect('accounts:login')

    def post(self, request):
        logout(request)
        return redirect('accounts:login')
