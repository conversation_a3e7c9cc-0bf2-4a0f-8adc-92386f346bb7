from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .api_views import (
    register_view, login_view, logout_view,
    ProfileView, ProfileUpdateView,
    password_reset_request_view, verify_otp_view,
    password_reset_confirm_view
)

app_name = 'accounts_api'

urlpatterns = [
    # Authentication endpoints
    path('register/', register_view, name='register'),
    path('login/', login_view, name='login'),
    path('logout/', logout_view, name='logout'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    # Profile endpoints
    path('profile/', ProfileView.as_view(), name='profile'),
    path('profile/update/', ProfileUpdateView.as_view(), name='profile_update'),

    # Password reset endpoints
    path('password-reset/request/', password_reset_request_view, name='password_reset_request'),
    path('verify-otp/', verify_otp_view, name='verify_otp'),
    path('password-reset/confirm/', password_reset_confirm_view, name='password_reset_confirm'),
]