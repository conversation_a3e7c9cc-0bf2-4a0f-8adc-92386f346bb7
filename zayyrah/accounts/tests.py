from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.urls import reverse
from django.test import TestCase


class UserModelTests(TestCase):
    def setUp(self):
        self.User = get_user_model()

    def test_business_account_requires_shop_name(self):
        user = self.User(
            mobile_number='**********',
            account_type=self.User.AccountType.BUSINESS,
        )
        user.set_password('safepass123')
        with self.assertRaises(ValidationError):
            user.full_clean()

    def test_customer_account_does_not_require_shop_name(self):
        user = self.User(
            mobile_number='**********',
            account_type=self.User.AccountType.CUSTOMER,
        )
        user.set_password('safepass123')
        # Should not raise
        user.full_clean()


class LogoutFlowTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.password = 'safepass123'
        self.user = self.User.objects.create_user(
            mobile_number='**********',
            password=self.password,
            account_type=self.User.AccountType.CUSTOMER,
        )

    def test_logout_redirects_to_login(self):
        login_successful = self.client.login(
            mobile_number=self.user.mobile_number,
            password=self.password,
        )
        self.assertTrue(login_successful)
        response = self.client.get(reverse('accounts:logout'))
        self.assertRedirects(response, reverse('accounts:login'))
