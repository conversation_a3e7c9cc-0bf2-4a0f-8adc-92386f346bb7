"""
Advanced Inventory Allocation Service

This service implements sophisticated inventory allocation algorithms:
- FIFO (First In First Out) - for general inventory
- LIFO (Last In First Out) - for specific business needs
- FEFO (First Expire First Out) - for perishable goods
- Weighted allocation based on quality scores
- Location-aware allocation with preferences
"""
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional, Tuple
from django.db import transaction
from django.db.models import Q, Sum, F
from django.utils import timezone

from ..models import EnhancedProduct, StockBatch, Location, StockMovement

logger = logging.getLogger(__name__)


class AllocationResult:
    """Container for allocation results"""

    def __init__(self):
        self.allocations: List[Dict[str, Any]] = []
        self.total_allocated: Decimal = Decimal('0.00')
        self.total_requested: Decimal = Decimal('0.00')
        self.success: bool = False
        self.message: str = ""
        self.warnings: List[str] = []

    def add_allocation(self, batch, quantity, reason=""):
        """Add a batch allocation to the result"""
        allocation = {
            'batch': batch,
            'batch_id': batch.id,
            'batch_number': batch.batch_number,
            'quantity': quantity,
            'purchase_price': batch.purchase_price,
            'quality_score': batch.quality_score,
            'expiry_date': batch.expiry_date,
            'location': batch.location,
            'reason': reason
        }
        self.allocations.append(allocation)
        self.total_allocated += quantity

    def is_fully_allocated(self) -> bool:
        """Check if the full requested quantity was allocated"""
        return self.total_allocated >= self.total_requested

    def get_allocation_summary(self) -> Dict[str, Any]:
        """Get summary of allocation results"""
        return {
            'total_requested': float(self.total_requested),
            'total_allocated': float(self.total_allocated),
            'allocation_percentage': float((self.total_allocated / self.total_requested) * 100) if self.total_requested > 0 else 0,
            'batches_used': len(self.allocations),
            'success': self.success,
            'message': self.message,
            'warnings': self.warnings,
            'allocations': [{
                'batch_id': alloc['batch_id'],
                'batch_number': alloc['batch_number'],
                'quantity': float(alloc['quantity']),
                'quality_score': alloc['quality_score'],
                'expiry_date': alloc['expiry_date'].isoformat() if alloc['expiry_date'] else None,
                'location_name': alloc['location'].name,
                'reason': alloc['reason']
            } for alloc in self.allocations]
        }


class InventoryAllocationService:
    """Advanced inventory allocation algorithms"""

    @staticmethod
    def get_available_batches(product: EnhancedProduct, location: Location = None,
                            min_quality_score: int = 0, exclude_expired: bool = True) -> List[StockBatch]:
        """Get available batches for allocation"""
        queryset = StockBatch.objects.filter(
            product=product,
            status='active',
            quantity_available__gt=0,
            quality_score__gte=min_quality_score
        ).select_related('location', 'product')

        if location:
            queryset = queryset.filter(location=location)

        if exclude_expired:
            queryset = queryset.filter(
                Q(expiry_date__isnull=True) | Q(expiry_date__gt=timezone.now().date())
            )

        return list(queryset)

    @staticmethod
    def allocate_fifo(product: EnhancedProduct, quantity: Decimal, location: Location = None,
                     min_quality_score: int = 0, max_batches: int = None) -> AllocationResult:
        """
        First In First Out allocation
        Allocates from oldest batches first based on received_date
        """
        result = AllocationResult()
        result.total_requested = quantity

        try:
            # Get available batches ordered by received date (oldest first)
            available_batches = InventoryAllocationService.get_available_batches(
                product, location, min_quality_score
            )

            # Sort by received date (FIFO)
            batches = sorted(available_batches, key=lambda b: (b.received_date, b.created_at))

            if max_batches:
                batches = batches[:max_batches]

            remaining_quantity = quantity

            for batch in batches:
                if remaining_quantity <= 0:
                    break

                available_in_batch = batch.quantity_available
                allocated_quantity = min(remaining_quantity, available_in_batch)

                result.add_allocation(
                    batch=batch,
                    quantity=allocated_quantity,
                    reason=f"FIFO allocation (received: {batch.received_date})"
                )

                remaining_quantity -= allocated_quantity

            result.success = True
            result.message = f"FIFO allocation completed. Allocated {result.total_allocated} of {quantity} requested."

            if remaining_quantity > 0:
                result.warnings.append(f"Could not allocate {remaining_quantity} units due to insufficient stock")

        except Exception as e:
            result.success = False
            result.message = f"FIFO allocation failed: {str(e)}"
            logger.error(f"FIFO allocation error for product {product.id}: {str(e)}")

        return result

    @staticmethod
    def allocate_lifo(product: EnhancedProduct, quantity: Decimal, location: Location = None,
                     min_quality_score: int = 0, max_batches: int = None) -> AllocationResult:
        """
        Last In First Out allocation
        Allocates from newest batches first based on received_date
        """
        result = AllocationResult()
        result.total_requested = quantity

        try:
            # Get available batches ordered by received date (newest first)
            available_batches = InventoryAllocationService.get_available_batches(
                product, location, min_quality_score
            )

            # Sort by received date (LIFO)
            batches = sorted(available_batches, key=lambda b: (b.received_date, b.created_at), reverse=True)

            if max_batches:
                batches = batches[:max_batches]

            remaining_quantity = quantity

            for batch in batches:
                if remaining_quantity <= 0:
                    break

                available_in_batch = batch.quantity_available
                allocated_quantity = min(remaining_quantity, available_in_batch)

                result.add_allocation(
                    batch=batch,
                    quantity=allocated_quantity,
                    reason=f"LIFO allocation (received: {batch.received_date})"
                )

                remaining_quantity -= allocated_quantity

            result.success = True
            result.message = f"LIFO allocation completed. Allocated {result.total_allocated} of {quantity} requested."

            if remaining_quantity > 0:
                result.warnings.append(f"Could not allocate {remaining_quantity} units due to insufficient stock")

        except Exception as e:
            result.success = False
            result.message = f"LIFO allocation failed: {str(e)}"
            logger.error(f"LIFO allocation error for product {product.id}: {str(e)}")

        return result

    @staticmethod
    def allocate_fefo(product: EnhancedProduct, quantity: Decimal, location: Location = None,
                     min_quality_score: int = 0, max_batches: int = None,
                     expiry_buffer_days: int = 0) -> AllocationResult:
        """
        First Expire First Out allocation
        Allocates from batches with earliest expiry dates first (for perishables)
        """
        result = AllocationResult()
        result.total_requested = quantity

        try:
            # Check if product tracks expiry
            if not product.track_expiry:
                result.warnings.append("Product does not track expiry dates. Consider using FIFO instead.")

            # Get available batches with expiry dates
            available_batches = InventoryAllocationService.get_available_batches(
                product, location, min_quality_score, exclude_expired=True
            )

            # Filter out batches expiring within buffer period
            if expiry_buffer_days > 0:
                buffer_date = timezone.now().date() + timedelta(days=expiry_buffer_days)
                available_batches = [
                    batch for batch in available_batches
                    if not batch.expiry_date or batch.expiry_date > buffer_date
                ]

            # Sort by expiry date (earliest first), then by received date
            def sort_key(batch):
                # Batches without expiry dates go last
                expiry_sort = batch.expiry_date if batch.expiry_date else datetime.max.date()
                return (expiry_sort, batch.received_date, batch.created_at)

            batches = sorted(available_batches, key=sort_key)

            if max_batches:
                batches = batches[:max_batches]

            remaining_quantity = quantity

            for batch in batches:
                if remaining_quantity <= 0:
                    break

                available_in_batch = batch.quantity_available
                allocated_quantity = min(remaining_quantity, available_in_batch)

                expiry_info = f"expires: {batch.expiry_date}" if batch.expiry_date else "no expiry"
                result.add_allocation(
                    batch=batch,
                    quantity=allocated_quantity,
                    reason=f"FEFO allocation ({expiry_info})"
                )

                remaining_quantity -= allocated_quantity

                # Add warning for batches expiring soon
                if batch.expiry_date:
                    days_until_expiry = (batch.expiry_date - timezone.now().date()).days
                    if days_until_expiry <= 7:
                        result.warnings.append(
                            f"Batch {batch.batch_number} expires in {days_until_expiry} days"
                        )

            result.success = True
            result.message = f"FEFO allocation completed. Allocated {result.total_allocated} of {quantity} requested."

            if remaining_quantity > 0:
                result.warnings.append(f"Could not allocate {remaining_quantity} units due to insufficient stock")

        except Exception as e:
            result.success = False
            result.message = f"FEFO allocation failed: {str(e)}"
            logger.error(f"FEFO allocation error for product {product.id}: {str(e)}")

        return result

    @staticmethod
    def allocate_weighted(product: EnhancedProduct, quantity: Decimal, location: Location = None,
                         quality_preference: str = 'highest', min_quality_score: int = 0,
                         max_batches: int = None, expiry_weight: float = 0.3) -> AllocationResult:
        """
        Weighted allocation based on quality scores and expiry dates

        Args:
            quality_preference: 'highest' or 'lowest' quality first
            expiry_weight: Weight given to expiry date in scoring (0.0 to 1.0)
        """
        result = AllocationResult()
        result.total_requested = quantity

        try:
            available_batches = InventoryAllocationService.get_available_batches(
                product, location, min_quality_score
            )

            # Calculate weighted scores for each batch
            scored_batches = []
            current_date = timezone.now().date()

            for batch in available_batches:
                quality_score = batch.quality_score / 100.0  # Normalize to 0-1

                # Calculate expiry score (closer to expiry = lower score)
                if batch.expiry_date and product.track_expiry:
                    days_until_expiry = (batch.expiry_date - current_date).days
                    # Normalize expiry score (longer time = higher score)
                    expiry_score = min(1.0, max(0.0, days_until_expiry / 365.0))
                else:
                    expiry_score = 1.0  # No expiry = full score

                # Combine scores
                weighted_score = (1 - expiry_weight) * quality_score + expiry_weight * expiry_score

                scored_batches.append({
                    'batch': batch,
                    'quality_score': quality_score,
                    'expiry_score': expiry_score,
                    'weighted_score': weighted_score
                })

            # Sort by weighted score
            reverse_sort = quality_preference == 'highest'
            scored_batches.sort(key=lambda x: x['weighted_score'], reverse=reverse_sort)

            if max_batches:
                scored_batches = scored_batches[:max_batches]

            remaining_quantity = quantity

            for scored_batch in scored_batches:
                if remaining_quantity <= 0:
                    break

                batch = scored_batch['batch']
                available_in_batch = batch.quantity_available
                allocated_quantity = min(remaining_quantity, available_in_batch)

                result.add_allocation(
                    batch=batch,
                    quantity=allocated_quantity,
                    reason=f"Weighted allocation (score: {scored_batch['weighted_score']:.3f})"
                )

                remaining_quantity -= allocated_quantity

            result.success = True
            result.message = f"Weighted allocation completed. Allocated {result.total_allocated} of {quantity} requested."

            if remaining_quantity > 0:
                result.warnings.append(f"Could not allocate {remaining_quantity} units due to insufficient stock")

        except Exception as e:
            result.success = False
            result.message = f"Weighted allocation failed: {str(e)}"
            logger.error(f"Weighted allocation error for product {product.id}: {str(e)}")

        return result

    @staticmethod
    def allocate_location_aware(product: EnhancedProduct, quantity: Decimal,
                              preferred_locations: List[Location], fallback_to_other: bool = True,
                              allocation_method: str = 'FIFO', min_quality_score: int = 0,
                              max_batches: int = None) -> AllocationResult:
        """
        Location-aware allocation with preferences

        Args:
            preferred_locations: List of locations in order of preference
            fallback_to_other: Whether to use other locations if preferred ones don't have enough stock
            allocation_method: FIFO, LIFO, or FEFO for allocation within each location
        """
        result = AllocationResult()
        result.total_requested = quantity

        try:
            remaining_quantity = quantity

            # Try preferred locations first
            for location in preferred_locations:
                if remaining_quantity <= 0:
                    break

                # Allocate from this location using specified method
                if allocation_method == 'FIFO':
                    location_result = InventoryAllocationService.allocate_fifo(
                        product, remaining_quantity, location, min_quality_score, max_batches
                    )
                elif allocation_method == 'LIFO':
                    location_result = InventoryAllocationService.allocate_lifo(
                        product, remaining_quantity, location, min_quality_score, max_batches
                    )
                elif allocation_method == 'FEFO':
                    location_result = InventoryAllocationService.allocate_fefo(
                        product, remaining_quantity, location, min_quality_score, max_batches
                    )
                else:
                    raise ValueError(f"Unknown allocation method: {allocation_method}")

                # Add allocations from this location
                for allocation in location_result.allocations:
                    result.add_allocation(
                        batch=allocation['batch'],
                        quantity=allocation['quantity'],
                        reason=f"Location-aware {allocation_method} from {location.name}"
                    )

                remaining_quantity -= location_result.total_allocated
                result.warnings.extend(location_result.warnings)

            # If not fully allocated and fallback is enabled, try other locations
            if remaining_quantity > 0 and fallback_to_other:
                # Get all other locations
                used_location_ids = [loc.id for loc in preferred_locations]
                fallback_result = None

                if allocation_method == 'FIFO':
                    fallback_result = InventoryAllocationService.allocate_fifo(
                        product, remaining_quantity, None, min_quality_score, max_batches
                    )
                elif allocation_method == 'LIFO':
                    fallback_result = InventoryAllocationService.allocate_lifo(
                        product, remaining_quantity, None, min_quality_score, max_batches
                    )
                elif allocation_method == 'FEFO':
                    fallback_result = InventoryAllocationService.allocate_fefo(
                        product, remaining_quantity, None, min_quality_score, max_batches
                    )

                if fallback_result:
                    # Filter out allocations from already used locations
                    for allocation in fallback_result.allocations:
                        if allocation['batch'].location.id not in used_location_ids:
                            result.add_allocation(
                                batch=allocation['batch'],
                                quantity=allocation['quantity'],
                                reason=f"Fallback {allocation_method} from {allocation['batch'].location.name}"
                            )
                            remaining_quantity -= allocation['quantity']

                            if remaining_quantity <= 0:
                                break

            result.success = True
            result.message = f"Location-aware allocation completed. Allocated {result.total_allocated} of {quantity} requested."

            if remaining_quantity > 0:
                result.warnings.append(f"Could not allocate {remaining_quantity} units due to insufficient stock in preferred locations")

        except Exception as e:
            result.success = False
            result.message = f"Location-aware allocation failed: {str(e)}"
            logger.error(f"Location-aware allocation error for product {product.id}: {str(e)}")

        return result

    @staticmethod
    @transaction.atomic
    def execute_allocation(allocation_result: AllocationResult, created_by=None,
                          reference_type: str = 'sale', reference_number: str = '',
                          notes: str = '') -> bool:
        """
        Execute the allocation by updating batch quantities and creating movement records
        """
        try:
            movements = []

            for allocation in allocation_result.allocations:
                batch = allocation['batch']
                quantity = allocation['quantity']

                # Update batch quantity
                batch.quantity_available -= quantity
                batch.save()

                # Create movement record
                movement = StockMovement.objects.create(
                    owner=batch.owner,
                    product=batch.product,
                    batch=batch,
                    location=batch.location,
                    movement_type='sale',
                    quantity=-quantity,  # Negative for outgoing
                    reference_type=reference_type,
                    reference_number=reference_number,
                    notes=f"{notes} | {allocation['reason']}",
                    created_by=created_by
                )
                movements.append(movement)

            logger.info(f"Successfully executed allocation: {len(movements)} movements created")
            return True

        except Exception as e:
            logger.error(f"Failed to execute allocation: {str(e)}")
            return False

    @staticmethod
    def get_allocation_recommendations(product: EnhancedProduct, quantity: Decimal,
                                     location: Location = None) -> Dict[str, AllocationResult]:
        """
        Get allocation recommendations using different algorithms
        """
        recommendations = {}

        try:
            # Try different allocation methods
            recommendations['FIFO'] = InventoryAllocationService.allocate_fifo(
                product, quantity, location
            )

            recommendations['LIFO'] = InventoryAllocationService.allocate_lifo(
                product, quantity, location
            )

            if product.track_expiry:
                recommendations['FEFO'] = InventoryAllocationService.allocate_fefo(
                    product, quantity, location
                )

            recommendations['WEIGHTED_HIGH'] = InventoryAllocationService.allocate_weighted(
                product, quantity, location, quality_preference='highest'
            )

            recommendations['WEIGHTED_LOW'] = InventoryAllocationService.allocate_weighted(
                product, quantity, location, quality_preference='lowest'
            )

        except Exception as e:
            logger.error(f"Error generating allocation recommendations: {str(e)}")

        return recommendations