"""
Expiry Management Service

This service provides comprehensive expiry date management:
- FEFO (First Expire First Out) algorithm implementation
- Expiry alerts and notifications
- Quality degradation tracking over time
- Automated expiry batch processing
- Shelf life management
"""
import logging
from datetime import datetime, timedelta, date
from decimal import Decimal
from typing import List, Dict, Any, Optional, Tuple
from django.db import transaction
from django.db.models import Q, F, Count, Sum
from django.utils import timezone

from ..models import (
    StockBatch, ExpiryAlert, EnhancedProduct, StockAnomalyDetection
)

logger = logging.getLogger(__name__)


class ExpiryManagementService:
    """FEFO implementation and expiry tracking"""

    @staticmethod
    def generate_expiry_alerts(owner, days_ahead: int = 30,
                             force_regenerate: bool = False) -> Dict[str, Any]:
        """
        Generate alerts for expiring products

        Args:
            owner: User/business owner
            days_ahead: Number of days to look ahead for expiry
            force_regenerate: Whether to regenerate existing alerts
        """
        results = {
            'alerts_created': 0,
            'alerts_updated': 0,
            'batches_processed': 0,
            'critical_alerts': 0,
            'warnings': []
        }

        try:
            # Calculate date range
            today = timezone.now().date()
            alert_date = today + timedelta(days=days_ahead)

            # Get batches that expire within the specified period
            expiring_batches = StockBatch.objects.filter(
                owner=owner,
                status='active',
                quantity_available__gt=0,
                expiry_date__isnull=False,
                expiry_date__lte=alert_date
            ).select_related('product')

            results['batches_processed'] = expiring_batches.count()

            for batch in expiring_batches:
                days_until_expiry = (batch.expiry_date - today).days

                # Skip if already expired and alert exists
                if days_until_expiry < 0:
                    alert_type = 'expired'
                    priority = 'critical'
                elif days_until_expiry <= 3:
                    alert_type = 'approaching_expiry'
                    priority = 'critical'
                elif days_until_expiry <= 7:
                    alert_type = 'approaching_expiry'
                    priority = 'high'
                elif days_until_expiry <= 14:
                    alert_type = 'approaching_expiry'
                    priority = 'medium'
                else:
                    alert_type = 'approaching_expiry'
                    priority = 'low'

                # Check if alert already exists
                existing_alert = ExpiryAlert.objects.filter(
                    batch=batch,
                    alert_type=alert_type,
                    status__in=['active', 'acknowledged']
                ).first()

                if existing_alert and not force_regenerate:
                    # Update existing alert if needed
                    if existing_alert.days_until_expiry != days_until_expiry:
                        existing_alert.days_until_expiry = days_until_expiry
                        existing_alert.priority = priority
                        existing_alert.save()
                        results['alerts_updated'] += 1
                    continue

                # Generate alert message
                if days_until_expiry < 0:
                    message = f"Batch {batch.batch_number} expired {abs(days_until_expiry)} days ago"
                elif days_until_expiry == 0:
                    message = f"Batch {batch.batch_number} expires today!"
                elif days_until_expiry == 1:
                    message = f"Batch {batch.batch_number} expires tomorrow"
                else:
                    message = f"Batch {batch.batch_number} expires in {days_until_expiry} days"

                # Create alert
                alert = ExpiryAlert.objects.create(
                    owner=owner,
                    batch=batch,
                    alert_type=alert_type,
                    priority=priority,
                    expiry_date=batch.expiry_date,
                    days_until_expiry=days_until_expiry,
                    message=message,
                    alert_metadata={
                        'product_name': batch.product.name,
                        'quantity_available': float(batch.quantity_available),
                        'quality_score': batch.quality_score,
                        'batch_value': float(batch.quantity_available * batch.purchase_price),
                        'location': batch.location.name if batch.location else None
                    }
                )

                results['alerts_created'] += 1

                if priority == 'critical':
                    results['critical_alerts'] += 1

                logger.info(f"Created {alert_type} alert for batch {batch.batch_number}")

        except Exception as e:
            logger.error(f"Error generating expiry alerts: {str(e)}")
            results['warnings'].append(f"Error generating alerts: {str(e)}")

        return results

    @staticmethod
    def calculate_quality_degradation(batch: StockBatch) -> Dict[str, Any]:
        """
        Calculate quality degradation over time based on expiry and time elapsed
        """
        try:
            current_date = timezone.now().date()
            degradation_info = {
                'original_quality': batch.quality_score,
                'current_estimated_quality': batch.quality_score,
                'degradation_percentage': 0,
                'degradation_factor': 0,
                'shelf_life_percentage_used': 0,
                'is_degraded': False,
                'recommendations': []
            }

            # If no expiry date, minimal degradation based on time only
            if not batch.expiry_date:
                days_since_received = (current_date - batch.received_date).days
                if days_since_received > 365:  # After 1 year
                    time_degradation = min(20, days_since_received / 365 * 10)  # Max 20% degradation
                    degradation_info['current_estimated_quality'] = max(0, batch.quality_score - time_degradation)
                    degradation_info['degradation_percentage'] = time_degradation
                    degradation_info['is_degraded'] = time_degradation > 5
                return degradation_info

            # Calculate shelf life usage
            if batch.manufacture_date:
                total_shelf_life = (batch.expiry_date - batch.manufacture_date).days
                days_since_manufacture = (current_date - batch.manufacture_date).days
            else:
                total_shelf_life = (batch.expiry_date - batch.received_date).days
                days_since_manufacture = (current_date - batch.received_date).days

            if total_shelf_life <= 0:
                return degradation_info

            shelf_life_percentage = min(100, (days_since_manufacture / total_shelf_life) * 100)
            degradation_info['shelf_life_percentage_used'] = shelf_life_percentage

            # Calculate quality degradation based on shelf life usage
            if shelf_life_percentage > 100:  # Expired
                degradation_factor = 50 + (shelf_life_percentage - 100) * 2  # Accelerated degradation after expiry
            elif shelf_life_percentage > 80:  # Approaching expiry
                degradation_factor = 10 + (shelf_life_percentage - 80) * 2  # Faster degradation near expiry
            elif shelf_life_percentage > 50:  # Mid-life
                degradation_factor = 5 + (shelf_life_percentage - 50) * 0.15  # Slow degradation
            else:  # Fresh
                degradation_factor = shelf_life_percentage * 0.1  # Minimal degradation

            # Apply product-specific factors
            if batch.product.perishable:
                degradation_factor *= 1.5  # Perishable items degrade faster

            if batch.temperature_controlled and not batch.batch_metadata.get('temperature_maintained', True):
                degradation_factor *= 2  # Temperature breach accelerates degradation

            # Calculate final quality
            degradation_percentage = min(100, degradation_factor)
            current_quality = max(0, batch.quality_score - degradation_percentage)

            degradation_info.update({
                'current_estimated_quality': int(current_quality),
                'degradation_percentage': degradation_percentage,
                'degradation_factor': degradation_factor,
                'is_degraded': degradation_percentage > 10
            })

            # Generate recommendations
            if shelf_life_percentage > 90:
                degradation_info['recommendations'].append("Critical: Use immediately or dispose")
            elif shelf_life_percentage > 80:
                degradation_info['recommendations'].append("High priority: Use within 2-3 days")
            elif shelf_life_percentage > 60:
                degradation_info['recommendations'].append("Medium priority: Use within 1 week")
            elif degradation_percentage > 20:
                degradation_info['recommendations'].append("Quality declining: Consider price reduction")

            return degradation_info

        except Exception as e:
            logger.error(f"Error calculating quality degradation: {str(e)}")
            return {
                'original_quality': batch.quality_score,
                'current_estimated_quality': batch.quality_score,
                'degradation_percentage': 0,
                'error': str(e)
            }

    @staticmethod
    def get_fefo_allocation_order(product: EnhancedProduct, location=None) -> List[Dict[str, Any]]:
        """
        Get batch allocation order for FEFO (First Expire First Out)
        Returns batches sorted by expiry date and quality degradation
        """
        try:
            # Get available batches
            queryset = StockBatch.objects.filter(
                product=product,
                status='active',
                quantity_available__gt=0
            ).select_related('location', 'product')

            if location:
                queryset = queryset.filter(location=location)

            batches = list(queryset)

            # Calculate FEFO scoring for each batch
            fefo_batches = []
            current_date = timezone.now().date()

            for batch in batches:
                # Calculate basic FEFO score
                if batch.expiry_date:
                    days_until_expiry = (batch.expiry_date - current_date).days

                    # Priority scoring (lower is better for FEFO)
                    if days_until_expiry < 0:  # Expired
                        fefo_score = -1000 + days_until_expiry  # Most urgent (very negative)
                    elif days_until_expiry <= 3:  # Critical
                        fefo_score = -100 + days_until_expiry
                    elif days_until_expiry <= 7:  # High priority
                        fefo_score = -50 + days_until_expiry
                    else:  # Normal priority
                        fefo_score = days_until_expiry
                else:
                    # No expiry date - lowest priority
                    fefo_score = 9999

                # Apply quality factor (prefer higher quality if expiry dates are similar)
                quality_bonus = (batch.quality_score / 100) * 0.5
                fefo_score -= quality_bonus

                # Apply quantity factor (prefer larger batches for efficiency)
                quantity_bonus = min(1.0, float(batch.quantity_available) / 100) * 0.1
                fefo_score -= quantity_bonus

                # Calculate degradation info
                degradation_info = ExpiryManagementService.calculate_quality_degradation(batch)

                fefo_batches.append({
                    'batch': batch,
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'quantity_available': float(batch.quantity_available),
                    'expiry_date': batch.expiry_date,
                    'days_until_expiry': days_until_expiry if batch.expiry_date else None,
                    'quality_score': batch.quality_score,
                    'estimated_current_quality': degradation_info['current_estimated_quality'],
                    'degradation_percentage': degradation_info['degradation_percentage'],
                    'fefo_score': fefo_score,
                    'priority_level': (
                        'expired' if fefo_score <= -1000 else
                        'critical' if fefo_score <= -100 else
                        'high' if fefo_score <= -50 else
                        'medium' if fefo_score <= 30 else
                        'low'
                    ),
                    'location_name': batch.location.name if batch.location else 'Unknown',
                    'recommendations': degradation_info.get('recommendations', [])
                })

            # Sort by FEFO score (lowest first - most urgent)
            fefo_batches.sort(key=lambda x: x['fefo_score'])

            return fefo_batches

        except Exception as e:
            logger.error(f"Error getting FEFO allocation order: {str(e)}")
            return []

    @staticmethod
    @transaction.atomic
    def handle_expired_batches(owner, action: str = 'mark_expired') -> Dict[str, Any]:
        """
        Process expired batches

        Args:
            owner: User/business owner
            action: Action to take ('mark_expired', 'create_alerts', 'analyze_only')
        """
        results = {
            'expired_batches_found': 0,
            'batches_processed': 0,
            'alerts_created': 0,
            'anomalies_detected': 0,
            'total_value_affected': Decimal('0.00'),
            'expired_batches': [],
            'warnings': []
        }

        try:
            today = timezone.now().date()

            # Find expired batches
            expired_batches = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date__lt=today,
                quantity_available__gt=0
            ).select_related('product', 'location')

            results['expired_batches_found'] = expired_batches.count()

            for batch in expired_batches:
                days_expired = (today - batch.expiry_date).days
                batch_value = batch.quantity_available * batch.purchase_price
                results['total_value_affected'] += batch_value

                batch_info = {
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'product_name': batch.product.name,
                    'quantity': float(batch.quantity_available),
                    'value': float(batch_value),
                    'days_expired': days_expired,
                    'location': batch.location.name if batch.location else 'Unknown'
                }

                if action == 'mark_expired':
                    # Mark batch as expired
                    batch.status = 'expired'
                    batch.notes = f"{batch.notes} | Marked expired on {today} ({days_expired} days overdue)"
                    batch.save()

                    results['batches_processed'] += 1

                    # Create expiry alert if not exists
                    alert, created = ExpiryAlert.objects.get_or_create(
                        batch=batch,
                        alert_type='expired',
                        defaults={
                            'owner': owner,
                            'priority': 'critical',
                            'expiry_date': batch.expiry_date,
                            'days_until_expiry': -days_expired,
                            'message': f"Batch {batch.batch_number} expired {days_expired} days ago",
                            'alert_metadata': {
                                'product_name': batch.product.name,
                                'quantity_affected': float(batch.quantity_available),
                                'value_affected': float(batch_value),
                                'auto_processed': True
                            }
                        }
                    )

                    if created:
                        results['alerts_created'] += 1

                    # Create anomaly for significant expired value
                    if batch_value > 1000:  # Threshold for anomaly detection
                        anomaly = StockAnomalyDetection.create_anomaly(
                            owner=owner,
                            anomaly_type='pattern_deviation',
                            severity='high',
                            batch=batch,
                            product=batch.product,
                            anomaly_data={
                                'reason': 'High value expired batch',
                                'expired_value': float(batch_value),
                                'days_expired': days_expired,
                                'suggested_action': 'Review expiry management processes'
                            },
                            severity_score=min(100, 50 + days_expired * 2)
                        )
                        results['anomalies_detected'] += 1

                elif action == 'create_alerts':
                    # Only create alerts without changing batch status
                    alert, created = ExpiryAlert.objects.get_or_create(
                        batch=batch,
                        alert_type='expired',
                        defaults={
                            'owner': owner,
                            'priority': 'critical',
                            'expiry_date': batch.expiry_date,
                            'days_until_expiry': -days_expired,
                            'message': f"Batch {batch.batch_number} expired {days_expired} days ago",
                        }
                    )

                    if created:
                        results['alerts_created'] += 1

                results['expired_batches'].append(batch_info)

        except Exception as e:
            logger.error(f"Error handling expired batches: {str(e)}")
            results['warnings'].append(f"Error processing expired batches: {str(e)}")

        return results

    @staticmethod
    def get_expiry_dashboard_data(owner, days_ahead: int = 30) -> Dict[str, Any]:
        """
        Get comprehensive expiry dashboard data
        """
        try:
            today = timezone.now().date()
            future_date = today + timedelta(days=days_ahead)

            # Get expiry statistics
            expiry_stats = {
                'expired_batches': 0,
                'expiring_today': 0,
                'expiring_this_week': 0,
                'expiring_this_month': 0,
                'total_value_at_risk': Decimal('0.00'),
                'active_alerts': 0,
                'critical_alerts': 0
            }

            # Count expired batches
            expired_batches = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date__lt=today,
                quantity_available__gt=0
            )
            expiry_stats['expired_batches'] = expired_batches.count()

            # Count expiring batches
            expiring_today = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date=today,
                quantity_available__gt=0
            )
            expiry_stats['expiring_today'] = expiring_today.count()

            expiring_week = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date__range=[today, today + timedelta(days=7)],
                quantity_available__gt=0
            )
            expiry_stats['expiring_this_week'] = expiring_week.count()

            expiring_month = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date__range=[today, today + timedelta(days=30)],
                quantity_available__gt=0
            )
            expiry_stats['expiring_this_month'] = expiring_month.count()

            # Calculate value at risk
            at_risk_batches = StockBatch.objects.filter(
                owner=owner,
                status='active',
                expiry_date__lte=future_date,
                quantity_available__gt=0
            )

            for batch in at_risk_batches:
                batch_value = batch.quantity_available * batch.purchase_price
                expiry_stats['total_value_at_risk'] += batch_value

            # Count alerts
            active_alerts = ExpiryAlert.objects.filter(
                owner=owner,
                status__in=['active', 'acknowledged']
            )
            expiry_stats['active_alerts'] = active_alerts.count()
            expiry_stats['critical_alerts'] = active_alerts.filter(priority='critical').count()

            # Get product categories most affected
            affected_products = EnhancedProduct.objects.filter(
                owner=owner,
                stock_batches__expiry_date__lte=future_date,
                stock_batches__status='active',
                stock_batches__quantity_available__gt=0
            ).distinct().values('category__name').annotate(
                batch_count=Count('stock_batches'),
                total_value=Sum(F('stock_batches__quantity_available') * F('stock_batches__purchase_price'))
            ).order_by('-total_value')[:10]

            return {
                'statistics': expiry_stats,
                'affected_categories': list(affected_products),
                'generated_at': timezone.now(),
                'period_analyzed': f"{today} to {future_date}"
            }

        except Exception as e:
            logger.error(f"Error generating expiry dashboard data: {str(e)}")
            return {
                'statistics': {},
                'error': str(e)
            }

    @staticmethod
    def suggest_expiry_actions(owner, batch: StockBatch) -> List[Dict[str, Any]]:
        """
        Suggest actions for batches approaching expiry
        """
        suggestions = []

        try:
            if not batch.expiry_date:
                return suggestions

            current_date = timezone.now().date()
            days_until_expiry = (batch.expiry_date - current_date).days
            degradation_info = ExpiryManagementService.calculate_quality_degradation(batch)

            # Expired batches
            if days_until_expiry < 0:
                suggestions.extend([
                    {
                        'action': 'dispose',
                        'priority': 'critical',
                        'reason': f'Batch expired {abs(days_until_expiry)} days ago',
                        'estimated_cost': float(batch.quantity_available * batch.purchase_price)
                    },
                    {
                        'action': 'quality_check',
                        'priority': 'high',
                        'reason': 'Verify if product is still usable',
                        'estimated_time': '15-30 minutes'
                    }
                ])

            # Critical expiry (0-3 days)
            elif days_until_expiry <= 3:
                suggestions.extend([
                    {
                        'action': 'urgent_sale',
                        'priority': 'critical',
                        'reason': f'Expires in {days_until_expiry} days',
                        'suggested_discount': '50-70%'
                    },
                    {
                        'action': 'staff_consumption',
                        'priority': 'medium',
                        'reason': 'Consider for staff use if appropriate',
                    },
                    {
                        'action': 'donation',
                        'priority': 'medium',
                        'reason': 'Donate if still good quality',
                    }
                ])

            # High priority (4-7 days)
            elif days_until_expiry <= 7:
                suggestions.extend([
                    {
                        'action': 'promotion',
                        'priority': 'high',
                        'reason': f'Create promotion for {days_until_expiry} days',
                        'suggested_discount': '20-40%'
                    },
                    {
                        'action': 'bundle_sale',
                        'priority': 'medium',
                        'reason': 'Include in product bundles',
                    }
                ])

            # Medium priority (8-14 days)
            elif days_until_expiry <= 14:
                suggestions.extend([
                    {
                        'action': 'feature_promotion',
                        'priority': 'medium',
                        'reason': 'Feature prominently in store',
                    },
                    {
                        'action': 'customer_notification',
                        'priority': 'low',
                        'reason': 'Notify regular customers',
                    }
                ])

            # Quality-based suggestions
            if degradation_info['is_degraded']:
                suggestions.append({
                    'action': 'quality_discount',
                    'priority': 'medium',
                    'reason': f'Quality degraded by {degradation_info["degradation_percentage"]:.1f}%',
                    'suggested_discount': f'{min(50, degradation_info["degradation_percentage"])}%'
                })

            # High-value batch suggestions
            batch_value = batch.quantity_available * batch.purchase_price
            if batch_value > 500:
                suggestions.append({
                    'action': 'management_review',
                    'priority': 'high',
                    'reason': f'High value batch (${batch_value:.2f}) needs attention',
                })

        except Exception as e:
            logger.error(f"Error generating expiry suggestions: {str(e)}")

        return suggestions