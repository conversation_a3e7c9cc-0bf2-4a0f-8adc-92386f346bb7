"""
Stock Anomaly Detection Service

This service provides comprehensive anomaly detection and monitoring:
- Detect unusual stock movement patterns
- Monitor quality score trends
- Detect temperature breaches
- Analyze comprehensive stock patterns
- Generate automated alerts for anomalies
"""
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from statistics import mean, stdev
from typing import List, Dict, Any, Optional, Tuple
from django.db import transaction
from django.db.models import Q, F, Count, Sum, Avg, Min, Max
from django.utils import timezone

from ..models import (
    StockBatch, StockMovement, EnhancedProduct, StockAnomalyDetection,
    ExpiryAlert
)

logger = logging.getLogger(__name__)


class AnomalyDetectionService:
    """Stock anomaly detection and monitoring"""

    @staticmethod
    def detect_unusual_movements(owner, analysis_period_days: int = 30,
                               sensitivity: str = 'medium') -> List[Dict[str, Any]]:
        """
        Detect unusual stock movement patterns

        Args:
            owner: User/business owner
            analysis_period_days: Period to analyze for patterns
            sensitivity: Detection sensitivity ('low', 'medium', 'high')
        """
        anomalies = []

        try:
            # Set sensitivity thresholds
            thresholds = {
                'low': {'quantity_multiplier': 5, 'frequency_threshold': 15},
                'medium': {'quantity_multiplier': 3, 'frequency_threshold': 10},
                'high': {'quantity_multiplier': 2, 'frequency_threshold': 7}
            }

            threshold = thresholds.get(sensitivity, thresholds['medium'])

            # Analyze recent movements
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=analysis_period_days)

            recent_movements = StockMovement.objects.filter(
                owner=owner,
                created_at__date__range=[start_date, end_date]
            ).select_related('product', 'batch', 'location')

            if not recent_movements.exists():
                return anomalies

            # Detect unusual quantity movements
            quantity_anomalies = AnomalyDetectionService._detect_quantity_anomalies(
                recent_movements, threshold['quantity_multiplier']
            )
            anomalies.extend(quantity_anomalies)

            # Detect rapid consecutive movements
            frequency_anomalies = AnomalyDetectionService._detect_frequency_anomalies(
                recent_movements, threshold['frequency_threshold']
            )
            anomalies.extend(frequency_anomalies)

            # Detect unauthorized movements (movements outside business hours)
            unauthorized_anomalies = AnomalyDetectionService._detect_unauthorized_movements(
                recent_movements
            )
            anomalies.extend(unauthorized_anomalies)

            # Detect negative stock warnings
            negative_stock_anomalies = AnomalyDetectionService._detect_negative_stock_risks(
                owner
            )
            anomalies.extend(negative_stock_anomalies)

            # Create anomaly records for significant findings
            for anomaly in anomalies:
                if anomaly.get('severity_score', 0) >= 60:  # Only create records for significant anomalies
                    AnomalyDetectionService._create_anomaly_record(owner, anomaly)

        except Exception as e:
            logger.error(f"Error detecting unusual movements: {str(e)}")
            anomalies.append({
                'type': 'system_error',
                'description': f'Error in anomaly detection: {str(e)}',
                'severity_score': 30
            })

        return anomalies

    @staticmethod
    def _detect_quantity_anomalies(movements, multiplier: float) -> List[Dict[str, Any]]:
        """Detect movements with unusually large quantities"""
        anomalies = []

        try:
            # Group movements by product and calculate statistics
            product_movements = {}
            for movement in movements:
                product_id = movement.product.id
                if product_id not in product_movements:
                    product_movements[product_id] = {
                        'movements': [],
                        'product': movement.product
                    }
                product_movements[product_id]['movements'].append(movement)

            for product_id, data in product_movements.items():
                movements_list = data['movements']
                if len(movements_list) < 3:  # Need at least 3 movements for pattern analysis
                    continue

                # Calculate quantity statistics
                quantities = [abs(movement.quantity) for movement in movements_list]
                avg_quantity = mean(quantities)

                if len(quantities) > 1:
                    std_quantity = stdev(quantities)
                else:
                    std_quantity = 0

                # Find outliers
                for movement in movements_list:
                    abs_quantity = abs(movement.quantity)

                    # Check if quantity is significantly larger than average
                    if abs_quantity > avg_quantity * multiplier and abs_quantity > avg_quantity + (2 * std_quantity):
                        severity_score = min(100, 50 + (abs_quantity / avg_quantity - multiplier) * 10)

                        anomalies.append({
                            'type': 'unusual_quantity',
                            'movement_id': movement.id,
                            'product_id': movement.product.id,
                            'product_name': movement.product.name,
                            'description': f'Movement quantity {abs_quantity} is {abs_quantity/avg_quantity:.1f}x larger than average ({avg_quantity:.1f})',
                            'severity_score': int(severity_score),
                            'movement_data': {
                                'quantity': float(movement.quantity),
                                'average_quantity': avg_quantity,
                                'multiplier': abs_quantity / avg_quantity,
                                'movement_type': movement.movement_type,
                                'date': movement.created_at.isoformat()
                            }
                        })

        except Exception as e:
            logger.error(f"Error detecting quantity anomalies: {str(e)}")

        return anomalies

    @staticmethod
    def _detect_frequency_anomalies(movements, threshold: int) -> List[Dict[str, Any]]:
        """Detect rapid consecutive movements"""
        anomalies = []

        try:
            # Group by product and check frequency
            product_movements = {}
            for movement in movements:
                product_id = movement.product.id
                if product_id not in product_movements:
                    product_movements[product_id] = []
                product_movements[product_id].append(movement)

            for product_id, product_movements_list in product_movements.items():
                if len(product_movements_list) >= threshold:
                    # Check if movements are clustered in time
                    sorted_movements = sorted(product_movements_list, key=lambda x: x.created_at)

                    # Calculate time gaps between movements
                    time_gaps = []
                    for i in range(1, len(sorted_movements)):
                        gap = (sorted_movements[i].created_at - sorted_movements[i-1].created_at).total_seconds() / 3600  # Hours
                        time_gaps.append(gap)

                    if time_gaps:
                        avg_gap = mean(time_gaps)

                        # If average gap is less than 4 hours, it's suspicious
                        if avg_gap < 4:
                            severity_score = min(100, 60 + (threshold - len(product_movements_list)) * -2)

                            anomalies.append({
                                'type': 'rapid_movements',
                                'product_id': product_id,
                                'product_name': sorted_movements[0].product.name,
                                'description': f'{len(product_movements_list)} movements in short timeframe (avg {avg_gap:.1f}h apart)',
                                'severity_score': max(50, int(severity_score)),
                                'movement_data': {
                                    'movement_count': len(product_movements_list),
                                    'average_gap_hours': avg_gap,
                                    'time_span_hours': (sorted_movements[-1].created_at - sorted_movements[0].created_at).total_seconds() / 3600
                                }
                            })

        except Exception as e:
            logger.error(f"Error detecting frequency anomalies: {str(e)}")

        return anomalies

    @staticmethod
    def _detect_unauthorized_movements(movements) -> List[Dict[str, Any]]:
        """Detect movements outside normal business hours"""
        anomalies = []

        try:
            # Define business hours (can be made configurable)
            business_start = 8  # 8 AM
            business_end = 20   # 8 PM

            for movement in movements:
                movement_hour = movement.created_at.hour

                # Check if movement is outside business hours
                if movement_hour < business_start or movement_hour >= business_end:
                    # Check if it's a weekend
                    is_weekend = movement.created_at.weekday() >= 5  # Saturday = 5, Sunday = 6

                    severity_score = 70 if is_weekend else 60
                    severity_score += 10 if abs(movement.quantity) > 100 else 0  # Higher severity for large quantities

                    time_description = "weekend" if is_weekend else "after hours"

                    anomalies.append({
                        'type': 'unauthorized_movement',
                        'movement_id': movement.id,
                        'product_id': movement.product.id,
                        'description': f'Stock movement during {time_description} at {movement.created_at.strftime("%H:%M")}',
                        'severity_score': min(100, int(severity_score)),
                        'movement_data': {
                            'movement_time': movement.created_at.isoformat(),
                            'is_weekend': is_weekend,
                            'hour': movement_hour,
                            'quantity': float(movement.quantity),
                            'movement_type': movement.movement_type
                        }
                    })

        except Exception as e:
            logger.error(f"Error detecting unauthorized movements: {str(e)}")

        return anomalies

    @staticmethod
    def _detect_negative_stock_risks(owner) -> List[Dict[str, Any]]:
        """Detect products at risk of negative stock"""
        anomalies = []

        try:
            # Find products with low stock that don't allow negative stock
            risky_products = EnhancedProduct.objects.filter(
                owner=owner,
                is_active=True,
                track_stock=True,
                allow_negative_stock=False
            ).select_related()

            for product in risky_products:
                current_stock = product.current_stock
                reorder_level = product.reorder_level

                if current_stock <= reorder_level:
                    # Calculate severity based on how close to zero
                    if current_stock <= 0:
                        severity_score = 95
                        description = f'Product {product.name} has zero stock'
                    elif current_stock <= reorder_level * 0.5:
                        severity_score = 80
                        description = f'Product {product.name} is critically low ({current_stock} units, reorder at {reorder_level})'
                    else:
                        severity_score = 60
                        description = f'Product {product.name} is below reorder level ({current_stock} units)'

                    anomalies.append({
                        'type': 'negative_stock_warning',
                        'product_id': product.id,
                        'product_name': product.name,
                        'description': description,
                        'severity_score': severity_score,
                        'stock_data': {
                            'current_stock': float(current_stock),
                            'reorder_level': reorder_level,
                            'stock_percentage': (current_stock / reorder_level * 100) if reorder_level > 0 else 0
                        }
                    })

        except Exception as e:
            logger.error(f"Error detecting negative stock risks: {str(e)}")

        return anomalies

    @staticmethod
    def monitor_quality_trends(owner, product: EnhancedProduct = None,
                             analysis_period_days: int = 90) -> List[Dict[str, Any]]:
        """
        Monitor quality score trends across batches

        Args:
            owner: User/business owner
            product: Specific product to analyze (optional)
            analysis_period_days: Period to analyze for trends
        """
        quality_anomalies = []

        try:
            # Get batches for analysis
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=analysis_period_days)

            queryset = StockBatch.objects.filter(
                owner=owner,
                created_at__date__range=[start_date, end_date]
            ).order_by('created_at')

            if product:
                queryset = queryset.filter(product=product)

            batches = list(queryset.select_related('product'))

            if len(batches) < 5:  # Need sufficient data for trend analysis
                return quality_anomalies

            # Group by product for trend analysis
            product_batches = {}
            for batch in batches:
                product_id = batch.product.id
                if product_id not in product_batches:
                    product_batches[product_id] = {
                        'product': batch.product,
                        'batches': []
                    }
                product_batches[product_id]['batches'].append(batch)

            for product_id, data in product_batches.items():
                product_batches_list = data['batches']
                product_obj = data['product']

                if len(product_batches_list) < 3:
                    continue

                # Analyze quality trend
                quality_scores = [batch.quality_score for batch in product_batches_list]

                # Calculate trend
                recent_scores = quality_scores[-3:]  # Last 3 batches
                older_scores = quality_scores[:-3] if len(quality_scores) > 3 else quality_scores[:3]

                recent_avg = mean(recent_scores)
                older_avg = mean(older_scores)

                quality_change = recent_avg - older_avg

                # Detect significant quality drops
                if quality_change <= -15:  # 15+ point drop
                    severity_score = min(100, 70 + abs(quality_change))

                    quality_anomalies.append({
                        'type': 'quality_drop',
                        'product_id': product_id,
                        'product_name': product_obj.name,
                        'description': f'Quality dropped by {abs(quality_change):.1f} points (from {older_avg:.1f} to {recent_avg:.1f})',
                        'severity_score': int(severity_score),
                        'quality_data': {
                            'older_average': older_avg,
                            'recent_average': recent_avg,
                            'quality_change': quality_change,
                            'batches_analyzed': len(product_batches_list),
                            'min_quality': min(quality_scores),
                            'max_quality': max(quality_scores)
                        }
                    })

                    # Create anomaly record
                    AnomalyDetectionService._create_anomaly_record(
                        owner, quality_anomalies[-1], product=product_obj
                    )

                # Detect quality variance (inconsistent quality)
                if len(quality_scores) > 5:
                    quality_std = stdev(quality_scores)
                    if quality_std > 20:  # High variance
                        quality_anomalies.append({
                            'type': 'quality_variance',
                            'product_id': product_id,
                            'product_name': product_obj.name,
                            'description': f'Inconsistent quality (std dev: {quality_std:.1f})',
                            'severity_score': min(100, int(40 + quality_std)),
                            'quality_data': {
                                'standard_deviation': quality_std,
                                'quality_range': max(quality_scores) - min(quality_scores),
                                'average_quality': mean(quality_scores)
                            }
                        })

        except Exception as e:
            logger.error(f"Error monitoring quality trends: {str(e)}")

        return quality_anomalies

    @staticmethod
    def detect_temperature_breaches(owner) -> List[Dict[str, Any]]:
        """
        Detect temperature-controlled product issues
        """
        temperature_anomalies = []

        try:
            # Find temperature-controlled batches
            temp_controlled_batches = StockBatch.objects.filter(
                owner=owner,
                temperature_controlled=True,
                status='active'
            ).select_related('product')

            for batch in temp_controlled_batches:
                # Check for temperature breach indicators in metadata
                metadata = batch.batch_metadata or {}

                # Look for temperature breach data
                if metadata.get('temperature_breach'):
                    breach_data = metadata['temperature_breach']

                    severity_score = 80  # High severity for temperature breaches
                    if breach_data.get('duration_hours', 0) > 4:
                        severity_score = 95

                    temperature_anomalies.append({
                        'type': 'temperature_breach',
                        'batch_id': batch.id,
                        'product_id': batch.product.id,
                        'description': f'Temperature breach detected for batch {batch.batch_number}',
                        'severity_score': severity_score,
                        'temperature_data': breach_data
                    })

                    # Create anomaly record
                    AnomalyDetectionService._create_anomaly_record(
                        owner, temperature_anomalies[-1], batch=batch, product=batch.product
                    )

                # Check if required temperature is not set but product is temperature controlled
                elif not batch.storage_temperature:
                    temperature_anomalies.append({
                        'type': 'missing_temperature_requirement',
                        'batch_id': batch.id,
                        'product_id': batch.product.id,
                        'description': f'Temperature-controlled batch {batch.batch_number} missing storage temperature requirement',
                        'severity_score': 60,
                        'temperature_data': {
                            'issue': 'missing_storage_temperature',
                            'batch_number': batch.batch_number
                        }
                    })

        except Exception as e:
            logger.error(f"Error detecting temperature breaches: {str(e)}")

        return temperature_anomalies

    @staticmethod
    def _create_anomaly_record(owner, anomaly_data: Dict[str, Any],
                             product: EnhancedProduct = None,
                             batch: StockBatch = None,
                             movement: StockMovement = None) -> Optional[StockAnomalyDetection]:
        """
        Create an anomaly detection record
        """
        try:
            # Map anomaly type to severity
            severity_mapping = {
                'unusual_quantity': 'medium',
                'rapid_movements': 'medium',
                'quality_drop': 'high',
                'temperature_breach': 'critical',
                'unauthorized_movement': 'high',
                'negative_stock_warning': 'medium'
            }

            anomaly_type = anomaly_data.get('type', 'pattern_deviation')
            severity = severity_mapping.get(anomaly_type, 'medium')

            # Check if similar anomaly already exists (avoid duplicates)
            existing = StockAnomalyDetection.objects.filter(
                owner=owner,
                anomaly_type=anomaly_type,
                is_resolved=False,
                detected_at__gte=timezone.now() - timedelta(hours=24)
            )

            if product:
                existing = existing.filter(product=product)
            if batch:
                existing = existing.filter(batch=batch)

            if existing.exists():
                return None  # Don't create duplicate

            anomaly = StockAnomalyDetection.objects.create(
                owner=owner,
                anomaly_type=anomaly_type,
                severity=severity,
                product=product,
                batch=batch,
                movement=movement,
                anomaly_data=anomaly_data,
                severity_score=anomaly_data.get('severity_score', 50)
            )

            logger.info(f"Created anomaly record: {anomaly_type} for {anomaly.related_object_name}")
            return anomaly

        except Exception as e:
            logger.error(f"Error creating anomaly record: {str(e)}")
            return None

    @staticmethod
    def analyze_stock_patterns(owner, product: EnhancedProduct = None,
                             analysis_period_days: int = 60) -> Dict[str, Any]:
        """
        Comprehensive pattern analysis for stock movements
        """
        try:
            end_date = timezone.now().date()
            start_date = end_date - timedelta(days=analysis_period_days)

            # Get movements for analysis
            movements_query = StockMovement.objects.filter(
                owner=owner,
                created_at__date__range=[start_date, end_date]
            )

            if product:
                movements_query = movements_query.filter(product=product)

            movements = list(movements_query.select_related('product', 'batch', 'location'))

            analysis_results = {
                'analysis_period': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'days_analyzed': analysis_period_days
                },
                'movement_statistics': {},
                'patterns_detected': [],
                'recommendations': [],
                'anomaly_summary': {
                    'total_anomalies': 0,
                    'critical_anomalies': 0,
                    'resolved_anomalies': 0
                }
            }

            if not movements:
                analysis_results['recommendations'].append('No movements found for analysis period')
                return analysis_results

            # Calculate movement statistics
            total_movements = len(movements)
            inbound_movements = [m for m in movements if m.quantity > 0]
            outbound_movements = [m for m in movements if m.quantity < 0]

            analysis_results['movement_statistics'] = {
                'total_movements': total_movements,
                'inbound_movements': len(inbound_movements),
                'outbound_movements': len(outbound_movements),
                'average_daily_movements': total_movements / analysis_period_days,
                'total_inbound_quantity': sum(m.quantity for m in inbound_movements),
                'total_outbound_quantity': abs(sum(m.quantity for m in outbound_movements)),
                'movement_types': {}
            }

            # Analyze movement types
            movement_type_counts = {}
            for movement in movements:
                movement_type = movement.movement_type
                if movement_type not in movement_type_counts:
                    movement_type_counts[movement_type] = 0
                movement_type_counts[movement_type] += 1

            analysis_results['movement_statistics']['movement_types'] = movement_type_counts

            # Detect patterns
            patterns = []

            # Pattern 1: Seasonal trends (if enough data)
            if analysis_period_days >= 28:
                weekly_movements = {}
                for movement in movements:
                    week = movement.created_at.isocalendar()[1]
                    if week not in weekly_movements:
                        weekly_movements[week] = 0
                    weekly_movements[week] += abs(movement.quantity)

                if len(weekly_movements) > 2:
                    movement_values = list(weekly_movements.values())
                    if max(movement_values) > min(movement_values) * 2:
                        patterns.append({
                            'type': 'seasonal_variation',
                            'description': 'Significant weekly variation in movement volumes detected',
                            'data': weekly_movements
                        })

            # Pattern 2: Batch utilization patterns
            batch_usage = {}
            for movement in movements:
                if movement.batch:
                    batch_id = movement.batch.id
                    if batch_id not in batch_usage:
                        batch_usage[batch_id] = {
                            'movements': 0,
                            'total_quantity': 0,
                            'batch_number': movement.batch.batch_number
                        }
                    batch_usage[batch_id]['movements'] += 1
                    batch_usage[batch_id]['total_quantity'] += abs(movement.quantity)

            # Find batches with unusual usage patterns
            for batch_id, usage_data in batch_usage.items():
                if usage_data['movements'] > total_movements * 0.3:  # One batch accounts for >30% of movements
                    patterns.append({
                        'type': 'batch_concentration',
                        'description': f'Batch {usage_data["batch_number"]} accounts for {usage_data["movements"]/total_movements*100:.1f}% of movements',
                        'data': usage_data
                    })

            analysis_results['patterns_detected'] = patterns

            # Generate recommendations
            recommendations = []

            if len(inbound_movements) == 0:
                recommendations.append('No inbound movements detected - consider restocking')
            elif len(outbound_movements) == 0:
                recommendations.append('No outbound movements detected - review sales performance')

            if len(patterns) > 0:
                recommendations.append(f'Detected {len(patterns)} movement patterns - review for optimization opportunities')

            # Check for anomalies
            anomaly_count = StockAnomalyDetection.objects.filter(
                owner=owner,
                detected_at__date__range=[start_date, end_date]
            ).count()

            critical_anomaly_count = StockAnomalyDetection.objects.filter(
                owner=owner,
                detected_at__date__range=[start_date, end_date],
                severity__in=['high', 'critical']
            ).count()

            resolved_anomaly_count = StockAnomalyDetection.objects.filter(
                owner=owner,
                detected_at__date__range=[start_date, end_date],
                is_resolved=True
            ).count()

            analysis_results['anomaly_summary'] = {
                'total_anomalies': anomaly_count,
                'critical_anomalies': critical_anomaly_count,
                'resolved_anomalies': resolved_anomaly_count
            }

            if critical_anomaly_count > 0:
                recommendations.append(f'{critical_anomaly_count} critical anomalies detected - immediate attention required')

            analysis_results['recommendations'] = recommendations

            return analysis_results

        except Exception as e:
            logger.error(f"Error analyzing stock patterns: {str(e)}")
            return {
                'error': str(e),
                'analysis_period': {'start_date': start_date, 'end_date': end_date}
            }

    @staticmethod
    def get_anomaly_dashboard(owner) -> Dict[str, Any]:
        """
        Get comprehensive anomaly dashboard data
        """
        try:
            today = timezone.now().date()
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)

            dashboard_data = {
                'summary': {
                    'total_anomalies': 0,
                    'active_anomalies': 0,
                    'critical_anomalies': 0,
                    'resolved_this_week': 0,
                    'false_positives': 0
                },
                'anomaly_types': {},
                'severity_distribution': {},
                'recent_anomalies': [],
                'trending_products': [],
                'resolution_stats': {}
            }

            # Get anomaly counts
            all_anomalies = StockAnomalyDetection.objects.filter(owner=owner)
            dashboard_data['summary']['total_anomalies'] = all_anomalies.count()

            active_anomalies = all_anomalies.filter(is_resolved=False, is_false_positive=False)
            dashboard_data['summary']['active_anomalies'] = active_anomalies.count()

            critical_anomalies = active_anomalies.filter(severity__in=['high', 'critical'])
            dashboard_data['summary']['critical_anomalies'] = critical_anomalies.count()

            resolved_this_week = all_anomalies.filter(
                is_resolved=True,
                reviewed_at__date__gte=week_ago
            ).count()
            dashboard_data['summary']['resolved_this_week'] = resolved_this_week

            false_positives = all_anomalies.filter(is_false_positive=True).count()
            dashboard_data['summary']['false_positives'] = false_positives

            # Anomaly types breakdown
            anomaly_types = all_anomalies.values('anomaly_type').annotate(
                count=Count('id')
            ).order_by('-count')
            dashboard_data['anomaly_types'] = {item['anomaly_type']: item['count'] for item in anomaly_types}

            # Severity distribution
            severity_dist = active_anomalies.values('severity').annotate(
                count=Count('id')
            )
            dashboard_data['severity_distribution'] = {item['severity']: item['count'] for item in severity_dist}

            # Recent anomalies (last 7 days)
            recent = active_anomalies.filter(
                detected_at__date__gte=week_ago
            ).order_by('-detected_at')[:10]

            dashboard_data['recent_anomalies'] = [{
                'id': anomaly.id,
                'type': anomaly.get_anomaly_type_display(),
                'severity': anomaly.severity,
                'severity_score': anomaly.severity_score,
                'related_object': anomaly.related_object_name,
                'detected_at': anomaly.detected_at,
                'description': anomaly.anomaly_data.get('description', 'No description available')
            } for anomaly in recent]

            # Products with most anomalies
            trending_products = all_anomalies.filter(
                product__isnull=False,
                detected_at__date__gte=month_ago
            ).values('product__name', 'product__id').annotate(
                anomaly_count=Count('id')
            ).order_by('-anomaly_count')[:5]

            dashboard_data['trending_products'] = list(trending_products)

            # Resolution statistics
            total_resolved = all_anomalies.filter(is_resolved=True).count()
            if dashboard_data['summary']['total_anomalies'] > 0:
                resolution_rate = (total_resolved / dashboard_data['summary']['total_anomalies']) * 100
            else:
                resolution_rate = 0

            dashboard_data['resolution_stats'] = {
                'total_resolved': total_resolved,
                'resolution_rate_percentage': round(resolution_rate, 1),
                'average_resolution_time_days': 0  # Can be calculated if needed
            }

            return dashboard_data

        except Exception as e:
            logger.error(f"Error generating anomaly dashboard: {str(e)}")
            return {'error': str(e)}