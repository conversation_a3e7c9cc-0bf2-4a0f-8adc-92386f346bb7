"""
Enhanced Unit Conversion Service

This service provides comprehensive unit conversion capabilities with:
- Bidirectional unit conversions (kg ↔ g, liters ↔ ml, etc.)
- Context-aware conversions based on product types
- Conversion path finding for complex conversions
- Validation and precision handling
- Support for custom units per business
"""
import logging
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from typing import List, Dict, Any, Optional, Tuple
from django.db import transaction
from django.core.cache import cache
from django.db.models import Q

from ..models import UnitConversion, EnhancedProduct

logger = logging.getLogger(__name__)


class ConversionResult:
    """Container for conversion results"""

    def __init__(self):
        self.success: bool = False
        self.converted_quantity: Optional[Decimal] = None
        self.from_unit: str = ""
        self.to_unit: str = ""
        self.original_quantity: Optional[Decimal] = None
        self.conversion_factor: Optional[Decimal] = None
        self.conversion_path: List[str] = []
        self.precision_used: int = 2
        self.message: str = ""
        self.warnings: List[str] = []

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for API responses"""
        return {
            'success': self.success,
            'converted_quantity': float(self.converted_quantity) if self.converted_quantity else None,
            'from_unit': self.from_unit,
            'to_unit': self.to_unit,
            'original_quantity': float(self.original_quantity) if self.original_quantity else None,
            'conversion_factor': float(self.conversion_factor) if self.conversion_factor else None,
            'conversion_path': self.conversion_path,
            'precision_used': self.precision_used,
            'message': self.message,
            'warnings': self.warnings
        }


class UnitConversionService:
    """Enhanced unit conversion with bidirectional support"""

    # Standard system-defined conversions
    SYSTEM_CONVERSIONS = {
        # Weight conversions
        ('kg', 'g'): Decimal('1000'),
        ('g', 'kg'): Decimal('0.001'),
        ('kg', 'oz'): Decimal('35.274'),
        ('oz', 'kg'): Decimal('0.028349523125'),
        ('g', 'oz'): Decimal('0.035274'),
        ('oz', 'g'): Decimal('28.349523125'),
        ('lb', 'kg'): Decimal('0.45359237'),
        ('kg', 'lb'): Decimal('2.20462262185'),
        ('lb', 'g'): Decimal('453.59237'),
        ('g', 'lb'): Decimal('0.00220462262185'),

        # Volume conversions
        ('l', 'ml'): Decimal('1000'),
        ('ml', 'l'): Decimal('0.001'),
        ('l', 'fl_oz'): Decimal('33.814'),
        ('fl_oz', 'l'): Decimal('0.0295735'),
        ('ml', 'fl_oz'): Decimal('0.033814'),
        ('fl_oz', 'ml'): Decimal('29.5735'),
        ('gal', 'l'): Decimal('3.78541'),
        ('l', 'gal'): Decimal('0.264172'),

        # Length conversions
        ('m', 'cm'): Decimal('100'),
        ('cm', 'm'): Decimal('0.01'),
        ('m', 'mm'): Decimal('1000'),
        ('mm', 'm'): Decimal('0.001'),
        ('cm', 'mm'): Decimal('10'),
        ('mm', 'cm'): Decimal('0.1'),
        ('m', 'ft'): Decimal('3.28084'),
        ('ft', 'm'): Decimal('0.3048'),
        ('ft', 'in'): Decimal('12'),
        ('in', 'ft'): Decimal('0.0833333333'),

        # Area conversions
        ('m2', 'cm2'): Decimal('10000'),
        ('cm2', 'm2'): Decimal('0.0001'),
        ('m2', 'ft2'): Decimal('10.7639'),
        ('ft2', 'm2'): Decimal('0.092903'),

        # Temperature conversions (special handling needed)
        # Note: Temperature conversions require different formulas, not simple multiplication
    }

    @classmethod
    def _get_cache_key(cls, owner_id: int, from_unit: str, to_unit: str, context_type: str = None) -> str:
        """Generate cache key for conversion lookups"""
        context_part = f"_{context_type}" if context_type else ""
        return f"unit_conversion_{owner_id}_{from_unit}_{to_unit}{context_part}"

    @classmethod
    def _initialize_system_conversions(cls, owner) -> None:
        """Initialize system-defined conversions for a user"""
        try:
            for (from_unit, to_unit), factor in cls.SYSTEM_CONVERSIONS.items():
                UnitConversion.objects.get_or_create(
                    owner=owner,
                    from_unit=from_unit,
                    to_unit=to_unit,
                    defaults={
                        'conversion_factor': factor,
                        'is_system_defined': True,
                        'precision_level': 6
                    }
                )
        except Exception as e:
            logger.error(f"Error initializing system conversions for user {owner.id}: {str(e)}")

    @classmethod
    def get_conversion_factor(cls, owner, from_unit: str, to_unit: str,
                            context_type: str = None) -> Optional[Tuple[Decimal, List[str]]]:
        """
        Get conversion factor between units, including through conversion paths
        Returns: (conversion_factor, conversion_path) or None if no conversion exists
        """
        if from_unit == to_unit:
            return Decimal('1'), [from_unit]

        # Check cache first
        cache_key = cls._get_cache_key(owner.id, from_unit, to_unit, context_type)
        cached_result = cache.get(cache_key)
        if cached_result:
            return cached_result

        try:
            # Try direct conversion first
            direct_conversion = UnitConversion.objects.filter(
                owner=owner,
                from_unit=from_unit,
                to_unit=to_unit,
                is_active=True
            ).first()

            if context_type:
                # Try context-specific conversion first
                context_conversion = UnitConversion.objects.filter(
                    owner=owner,
                    from_unit=from_unit,
                    to_unit=to_unit,
                    context_type=context_type,
                    is_active=True
                ).first()
                if context_conversion:
                    direct_conversion = context_conversion

            if direct_conversion:
                result = (direct_conversion.conversion_factor, [from_unit, to_unit])
                cache.set(cache_key, result, 3600)  # Cache for 1 hour
                return result

            # Try to find conversion path through intermediate units
            conversion_path = cls._find_conversion_path(owner, from_unit, to_unit, context_type)
            if conversion_path:
                # Calculate total conversion factor
                total_factor = Decimal('1')
                for i in range(len(conversion_path) - 1):
                    step_conversion = UnitConversion.objects.filter(
                        owner=owner,
                        from_unit=conversion_path[i],
                        to_unit=conversion_path[i + 1],
                        is_active=True
                    ).first()

                    if step_conversion:
                        total_factor *= step_conversion.conversion_factor
                    else:
                        return None

                result = (total_factor, conversion_path)
                cache.set(cache_key, result, 3600)
                return result

            # If no user-defined conversion, try to initialize system conversions
            cls._initialize_system_conversions(owner)

            # Try again after initialization
            direct_conversion = UnitConversion.objects.filter(
                owner=owner,
                from_unit=from_unit,
                to_unit=to_unit,
                is_active=True
            ).first()

            if direct_conversion:
                result = (direct_conversion.conversion_factor, [from_unit, to_unit])
                cache.set(cache_key, result, 3600)
                return result

        except Exception as e:
            logger.error(f"Error getting conversion factor: {str(e)}")

        return None

    @classmethod
    def _find_conversion_path(cls, owner, from_unit: str, to_unit: str,
                            context_type: str = None, max_depth: int = 3) -> Optional[List[str]]:
        """
        Find conversion path between units using breadth-first search
        """
        if from_unit == to_unit:
            return [from_unit]

        visited = set()
        queue = [(from_unit, [from_unit])]

        while queue and len(queue[0][1]) <= max_depth:
            current_unit, path = queue.pop(0)

            if current_unit in visited:
                continue

            visited.add(current_unit)

            # Get all possible next units from current unit
            next_conversions = UnitConversion.objects.filter(
                owner=owner,
                from_unit=current_unit,
                is_active=True
            )

            if context_type:
                # Prefer context-specific conversions
                context_conversions = next_conversions.filter(context_type=context_type)
                if context_conversions.exists():
                    next_conversions = context_conversions

            for conversion in next_conversions:
                next_unit = conversion.to_unit
                new_path = path + [next_unit]

                if next_unit == to_unit:
                    return new_path

                if next_unit not in visited:
                    queue.append((next_unit, new_path))

        return None

    @classmethod
    def convert_with_precision(cls, owner, quantity: Decimal, from_unit: str, to_unit: str,
                              precision: int = None, context_type: str = None) -> ConversionResult:
        """
        Convert quantity with configurable precision
        """
        result = ConversionResult()
        result.original_quantity = quantity
        result.from_unit = from_unit
        result.to_unit = to_unit

        try:
            # Validate inputs
            if not isinstance(quantity, Decimal):
                try:
                    quantity = Decimal(str(quantity))
                except (InvalidOperation, ValueError):
                    result.message = "Invalid quantity provided"
                    return result

            if quantity < 0:
                result.warnings.append("Negative quantity provided")

            # Get conversion factor and path
            conversion_data = cls.get_conversion_factor(owner, from_unit, to_unit, context_type)
            if not conversion_data:
                result.message = f"No conversion available from {from_unit} to {to_unit}"
                return result

            conversion_factor, conversion_path = conversion_data
            result.conversion_factor = conversion_factor
            result.conversion_path = conversion_path

            # Perform conversion
            converted_quantity = quantity * conversion_factor

            # Apply precision
            if precision is None:
                # Try to get precision from product or use default
                try:
                    product = EnhancedProduct.objects.filter(
                        owner=owner,
                        Q(unit=to_unit) | Q(base_unit=to_unit)
                    ).first()
                    if product and product.unit_precision:
                        precision = product.unit_precision
                    else:
                        precision = 2  # Default precision
                except Exception:
                    precision = 2

            result.precision_used = precision

            # Round to specified precision
            decimal_places = Decimal('0.1') ** precision
            result.converted_quantity = converted_quantity.quantize(decimal_places, rounding=ROUND_HALF_UP)

            result.success = True
            result.message = f"Successfully converted {quantity} {from_unit} to {result.converted_quantity} {to_unit}"

            # Add warnings for precision loss
            if len(conversion_path) > 2:
                result.warnings.append(f"Conversion used intermediate units: {' → '.join(conversion_path)}")

            if abs(converted_quantity - result.converted_quantity) > Decimal('0.001'):
                result.warnings.append(f"Precision loss due to rounding to {precision} decimal places")

        except Exception as e:
            result.success = False
            result.message = f"Conversion failed: {str(e)}"
            logger.error(f"Conversion error from {from_unit} to {to_unit}: {str(e)}")

        return result

    @classmethod
    def validate_conversion(cls, owner, from_unit: str, to_unit: str,
                          context_type: str = None) -> Dict[str, Any]:
        """
        Validate if conversion is possible and get conversion info
        """
        validation_result = {
            'is_valid': False,
            'conversion_factor': None,
            'conversion_path': [],
            'is_direct': False,
            'message': '',
            'warnings': []
        }

        try:
            conversion_data = cls.get_conversion_factor(owner, from_unit, to_unit, context_type)

            if conversion_data:
                conversion_factor, conversion_path = conversion_data
                validation_result.update({
                    'is_valid': True,
                    'conversion_factor': float(conversion_factor),
                    'conversion_path': conversion_path,
                    'is_direct': len(conversion_path) == 2,
                    'message': f"Conversion available from {from_unit} to {to_unit}"
                })

                if len(conversion_path) > 2:
                    validation_result['warnings'].append(
                        f"Indirect conversion through: {' → '.join(conversion_path)}"
                    )
            else:
                validation_result['message'] = f"No conversion available from {from_unit} to {to_unit}"

        except Exception as e:
            validation_result['message'] = f"Validation error: {str(e)}"
            logger.error(f"Conversion validation error: {str(e)}")

        return validation_result

    @classmethod
    def get_available_conversions(cls, owner, unit: str, context_type: str = None) -> List[Dict[str, Any]]:
        """
        Get all possible conversions from a given unit
        """
        conversions = []

        try:
            # Get direct conversions from this unit
            direct_conversions = UnitConversion.objects.filter(
                owner=owner,
                from_unit=unit,
                is_active=True
            ).select_related()

            if context_type:
                # Filter by context if specified
                context_conversions = direct_conversions.filter(context_type=context_type)
                if context_conversions.exists():
                    direct_conversions = context_conversions

            for conversion in direct_conversions:
                conversions.append({
                    'to_unit': conversion.to_unit,
                    'conversion_factor': float(conversion.conversion_factor),
                    'is_direct': True,
                    'context_type': conversion.context_type,
                    'precision_level': conversion.precision_level,
                    'is_system_defined': conversion.is_system_defined
                })

            # Also find indirect conversions (limited depth to avoid performance issues)
            all_units = set(UnitConversion.objects.filter(
                owner=owner,
                is_active=True
            ).values_list('to_unit', flat=True))

            for target_unit in all_units:
                if target_unit != unit:
                    # Check if we already have this as a direct conversion
                    if not any(c['to_unit'] == target_unit for c in conversions):
                        validation = cls.validate_conversion(owner, unit, target_unit, context_type)
                        if validation['is_valid'] and not validation['is_direct']:
                            conversions.append({
                                'to_unit': target_unit,
                                'conversion_factor': validation['conversion_factor'],
                                'is_direct': False,
                                'conversion_path': validation['conversion_path'],
                                'context_type': context_type,
                                'is_system_defined': False
                            })

        except Exception as e:
            logger.error(f"Error getting available conversions for {unit}: {str(e)}")

        return conversions

    @classmethod
    @transaction.atomic
    def create_conversion(cls, owner, from_unit: str, to_unit: str, conversion_factor: Decimal,
                         context_type: str = None, precision_level: int = 6,
                         create_reverse: bool = True) -> Tuple[bool, str]:
        """
        Create a new unit conversion (and optionally its reverse)
        """
        try:
            # Validate inputs
            if from_unit == to_unit:
                return False, "Cannot create conversion from unit to itself"

            if conversion_factor <= 0:
                return False, "Conversion factor must be positive"

            # Check if conversion already exists
            existing = UnitConversion.objects.filter(
                owner=owner,
                from_unit=from_unit,
                to_unit=to_unit
            ).first()

            if existing:
                return False, f"Conversion from {from_unit} to {to_unit} already exists"

            # Create the conversion
            conversion = UnitConversion.objects.create(
                owner=owner,
                from_unit=from_unit,
                to_unit=to_unit,
                conversion_factor=conversion_factor,
                context_type=context_type,
                precision_level=precision_level,
                is_system_defined=False
            )

            # Create reverse conversion if requested
            if create_reverse:
                reverse_factor = Decimal('1') / conversion_factor

                reverse_existing = UnitConversion.objects.filter(
                    owner=owner,
                    from_unit=to_unit,
                    to_unit=from_unit
                ).first()

                if not reverse_existing:
                    UnitConversion.objects.create(
                        owner=owner,
                        from_unit=to_unit,
                        to_unit=from_unit,
                        conversion_factor=reverse_factor,
                        context_type=context_type,
                        precision_level=precision_level,
                        is_system_defined=False
                    )

            # Clear cache for this conversion
            cache_key = cls._get_cache_key(owner.id, from_unit, to_unit, context_type)
            cache.delete(cache_key)

            if create_reverse:
                reverse_cache_key = cls._get_cache_key(owner.id, to_unit, from_unit, context_type)
                cache.delete(reverse_cache_key)

            return True, f"Successfully created conversion from {from_unit} to {to_unit}"

        except Exception as e:
            logger.error(f"Error creating conversion: {str(e)}")
            return False, f"Failed to create conversion: {str(e)}"

    @classmethod
    def bulk_convert(cls, owner, conversions: List[Dict[str, Any]]) -> List[ConversionResult]:
        """
        Perform multiple conversions in batch

        Args:
            conversions: List of dicts with keys: quantity, from_unit, to_unit, precision, context_type
        """
        results = []

        for conversion_data in conversions:
            try:
                result = cls.convert_with_precision(
                    owner=owner,
                    quantity=conversion_data.get('quantity'),
                    from_unit=conversion_data.get('from_unit'),
                    to_unit=conversion_data.get('to_unit'),
                    precision=conversion_data.get('precision'),
                    context_type=conversion_data.get('context_type')
                )
                results.append(result)
            except Exception as e:
                error_result = ConversionResult()
                error_result.success = False
                error_result.message = f"Bulk conversion error: {str(e)}"
                results.append(error_result)

        return results