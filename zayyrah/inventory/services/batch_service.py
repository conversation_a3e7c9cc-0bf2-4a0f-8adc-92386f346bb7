"""
Batch Management Service

This service provides comprehensive batch management capabilities:
- Batch splitting (divide one batch into multiple)
- Batch merging (combine compatible batches)
- Quality management and updates
- Parent-child batch relationships
- Batch operation auditing
"""
import logging
from decimal import Decimal, ROUND_HALF_UP
from typing import List, Dict, Any, Optional, Tuple
from django.db import transaction
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.utils import timezone

from ..models import StockBatch, BatchOperation, EnhancedProduct, Location, Supplier

logger = logging.getLogger(__name__)


class BatchOperationResult:
    """Container for batch operation results"""

    def __init__(self):
        self.success: bool = False
        self.message: str = ""
        self.warnings: List[str] = []
        self.created_batches: List[StockBatch] = []
        self.updated_batches: List[StockBatch] = []
        self.operation_record: Optional[BatchOperation] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary for API responses"""
        return {
            'success': self.success,
            'message': self.message,
            'warnings': self.warnings,
            'created_batches': [{
                'id': batch.id,
                'batch_number': batch.batch_number,
                'quantity_available': float(batch.quantity_available),
                'quality_score': batch.quality_score,
                'status': batch.status
            } for batch in self.created_batches],
            'updated_batches': [{
                'id': batch.id,
                'batch_number': batch.batch_number,
                'quantity_available': float(batch.quantity_available),
                'quality_score': batch.quality_score,
                'status': batch.status
            } for batch in self.updated_batches],
            'operation_id': self.operation_record.id if self.operation_record else None
        }


class BatchManagementService:
    """Batch splitting, merging, and quality management"""

    @staticmethod
    def validate_batch_compatibility(batches: List[StockBatch]) -> Tuple[bool, str]:
        """
        Validate that batches can be merged together
        Returns: (is_compatible, error_message)
        """
        if len(batches) < 2:
            return False, "At least 2 batches required for merging"

        # Check same owner
        owners = set(batch.owner.id for batch in batches)
        if len(owners) > 1:
            return False, "All batches must belong to the same owner"

        # Check same product
        products = set(batch.product.id for batch in batches)
        if len(products) > 1:
            return False, "All batches must be for the same product"

        # Check same supplier (optional but recommended)
        suppliers = set(batch.supplier.id if batch.supplier else None for batch in batches)
        if len(suppliers) > 1:
            return True, "Warning: Batches have different suppliers"

        # Check status compatibility
        statuses = set(batch.status for batch in batches)
        incompatible_statuses = {'expired', 'damaged', 'sold_out'}
        if any(status in incompatible_statuses for status in statuses):
            return False, "Cannot merge expired, damaged, or sold out batches"

        # Check expiry dates compatibility
        expiry_dates = [batch.expiry_date for batch in batches if batch.expiry_date]
        if expiry_dates:
            date_range = max(expiry_dates) - min(expiry_dates)
            if date_range.days > 30:  # More than 30 days difference
                return True, "Warning: Batches have significant expiry date differences"

        return True, ""

    @staticmethod
    @transaction.atomic
    def split_batch(source_batch: StockBatch, split_quantities: List[Decimal],
                   reasons: List[str] = None, performed_by=None,
                   inherit_quality: bool = True, new_batch_numbers: List[str] = None) -> BatchOperationResult:
        """
        Split batch into multiple smaller batches

        Args:
            source_batch: The batch to split
            split_quantities: List of quantities for each new batch
            reasons: Optional reasons for each split
            performed_by: User performing the operation
            inherit_quality: Whether new batches inherit quality from source
            new_batch_numbers: Optional custom batch numbers for new batches
        """
        result = BatchOperationResult()

        try:
            # Validate inputs
            if not split_quantities:
                result.message = "Split quantities are required"
                return result

            total_split_quantity = sum(split_quantities)
            if total_split_quantity > source_batch.quantity_available:
                result.message = f"Total split quantity ({total_split_quantity}) exceeds available quantity ({source_batch.quantity_available})"
                return result

            if any(qty <= 0 for qty in split_quantities):
                result.message = "All split quantities must be positive"
                return result

            # Prepare operation metadata
            operation_data = {
                'source_batch_id': source_batch.id,
                'split_quantities': [float(qty) for qty in split_quantities],
                'inherit_quality': inherit_quality,
                'total_split_quantity': float(total_split_quantity)
            }

            if reasons:
                operation_data['reasons'] = reasons

            # Generate batch numbers if not provided
            if not new_batch_numbers:
                new_batch_numbers = []
                for i, qty in enumerate(split_quantities):
                    new_batch_numbers.append(f"{source_batch.batch_number}-S{i+1}")

            if len(new_batch_numbers) != len(split_quantities):
                result.message = "Number of batch numbers must match number of split quantities"
                return result

            # Create new batches
            created_batches = []
            for i, (quantity, batch_number) in enumerate(zip(split_quantities, new_batch_numbers)):
                # Calculate quality score for new batch
                if inherit_quality:
                    new_quality_score = source_batch.quality_score
                else:
                    # Default to slightly lower quality for splits
                    new_quality_score = max(50, source_batch.quality_score - 5)

                # Create new batch
                new_batch = StockBatch.objects.create(
                    owner=source_batch.owner,
                    product=source_batch.product,
                    location=source_batch.location,
                    supplier=source_batch.supplier,
                    batch_number=batch_number,
                    purchase_order_number=source_batch.purchase_order_number,
                    quantity_received=quantity,
                    quantity_available=quantity,
                    purchase_price=source_batch.purchase_price,
                    received_date=source_batch.received_date,
                    manufacture_date=source_batch.manufacture_date,
                    expiry_date=source_batch.expiry_date,
                    status='active',
                    notes=f"Split from batch {source_batch.batch_number}. {reasons[i] if reasons and i < len(reasons) else ''}",
                    parent_batch=source_batch,
                    quality_score=new_quality_score,
                    batch_metadata={
                        'split_operation': True,
                        'source_batch_id': source_batch.id,
                        'split_index': i + 1,
                        'split_reason': reasons[i] if reasons and i < len(reasons) else 'Batch split'
                    },
                    temperature_controlled=source_batch.temperature_controlled,
                    storage_temperature=source_batch.storage_temperature,
                    handling_notes=source_batch.handling_notes
                )
                created_batches.append(new_batch)

            # Update source batch
            source_batch.quantity_available -= total_split_quantity
            if source_batch.quantity_available == 0:
                source_batch.status = 'sold_out'
            source_batch.save()

            result.updated_batches.append(source_batch)

            # Create operation record
            operation_record = BatchOperation.objects.create(
                owner=source_batch.owner,
                operation_type='split',
                source_batches=[{
                    'batch_id': source_batch.id,
                    'batch_number': source_batch.batch_number,
                    'original_quantity': float(source_batch.quantity_available + total_split_quantity),
                    'remaining_quantity': float(source_batch.quantity_available)
                }],
                target_batches=[{
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'quantity': float(batch.quantity_available),
                    'quality_score': batch.quality_score
                } for batch in created_batches],
                operation_metadata=operation_data,
                reason=f"Split batch {source_batch.batch_number} into {len(split_quantities)} parts",
                performed_by=performed_by
            )

            result.success = True
            result.message = f"Successfully split batch {source_batch.batch_number} into {len(created_batches)} new batches"
            result.created_batches = created_batches
            result.operation_record = operation_record

            logger.info(f"Split batch {source_batch.batch_number} into {len(created_batches)} batches")

        except Exception as e:
            result.success = False
            result.message = f"Batch split failed: {str(e)}"
            logger.error(f"Batch split error: {str(e)}")

        return result

    @staticmethod
    @transaction.atomic
    def merge_batches(source_batches: List[StockBatch], target_location: Location,
                     merge_reason: str, performed_by=None,
                     new_batch_number: str = None, quality_calculation: str = 'weighted_average') -> BatchOperationResult:
        """
        Merge compatible batches into a single batch

        Args:
            source_batches: List of batches to merge
            target_location: Location for the merged batch
            merge_reason: Reason for merging
            performed_by: User performing the operation
            new_batch_number: Optional custom batch number for merged batch
            quality_calculation: Method for calculating merged quality ('weighted_average', 'minimum', 'maximum')
        """
        result = BatchOperationResult()

        try:
            # Validate batch compatibility
            is_compatible, compatibility_message = BatchManagementService.validate_batch_compatibility(source_batches)
            if not is_compatible:
                result.message = compatibility_message
                return result

            if compatibility_message:  # Warning message
                result.warnings.append(compatibility_message)

            # Calculate merged batch properties
            total_quantity = sum(batch.quantity_available for batch in source_batches)
            weighted_price = sum(batch.quantity_available * batch.purchase_price for batch in source_batches) / total_quantity

            # Calculate quality score
            if quality_calculation == 'weighted_average':
                merged_quality = sum(batch.quantity_available * batch.quality_score for batch in source_batches) / total_quantity
                merged_quality = int(merged_quality.quantize(Decimal('1'), rounding=ROUND_HALF_UP))
            elif quality_calculation == 'minimum':
                merged_quality = min(batch.quality_score for batch in source_batches)
            elif quality_calculation == 'maximum':
                merged_quality = max(batch.quality_score for batch in source_batches)
            else:
                merged_quality = int(sum(batch.quality_score for batch in source_batches) / len(source_batches))

            # Determine merged batch properties
            earliest_received = min(batch.received_date for batch in source_batches)
            earliest_expiry = min(batch.expiry_date for batch in source_batches if batch.expiry_date) if any(batch.expiry_date for batch in source_batches) else None
            earliest_manufacture = min(batch.manufacture_date for batch in source_batches if batch.manufacture_date) if any(batch.manufacture_date for batch in source_batches) else None

            # Generate batch number if not provided
            if not new_batch_number:
                timestamp = timezone.now().strftime('%Y%m%d%H%M')
                new_batch_number = f"MERGED-{timestamp}"

            # Get representative batch for common properties
            representative_batch = source_batches[0]

            # Create merged batch
            merged_batch = StockBatch.objects.create(
                owner=representative_batch.owner,
                product=representative_batch.product,
                location=target_location,
                supplier=representative_batch.supplier,  # Use first batch's supplier
                batch_number=new_batch_number,
                purchase_order_number=representative_batch.purchase_order_number,
                quantity_received=total_quantity,
                quantity_available=total_quantity,
                purchase_price=weighted_price,
                received_date=earliest_received,
                manufacture_date=earliest_manufacture,
                expiry_date=earliest_expiry,
                status='active',
                notes=f"Merged from batches: {', '.join(batch.batch_number for batch in source_batches)}. Reason: {merge_reason}",
                quality_score=merged_quality,
                batch_metadata={
                    'merge_operation': True,
                    'source_batch_ids': [batch.id for batch in source_batches],
                    'source_batch_numbers': [batch.batch_number for batch in source_batches],
                    'merge_reason': merge_reason,
                    'quality_calculation_method': quality_calculation,
                    'original_quantities': {batch.batch_number: float(batch.quantity_available) for batch in source_batches}
                },
                temperature_controlled=any(batch.temperature_controlled for batch in source_batches),
                storage_temperature=representative_batch.storage_temperature,
                handling_notes=representative_batch.handling_notes
            )

            # Update source batches to sold_out status
            for batch in source_batches:
                batch.quantity_available = 0
                batch.status = 'sold_out'
                batch.notes = f"{batch.notes} | Merged into batch {new_batch_number} on {timezone.now().date()}"
                batch.save()

            result.updated_batches.extend(source_batches)

            # Create operation record
            operation_record = BatchOperation.objects.create(
                owner=representative_batch.owner,
                operation_type='merge',
                source_batches=[{
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'quantity': float(batch.quantity_received),  # Use original quantity before merging
                    'quality_score': batch.quality_score
                } for batch in source_batches],
                target_batches=[{
                    'batch_id': merged_batch.id,
                    'batch_number': merged_batch.batch_number,
                    'quantity': float(merged_batch.quantity_available),
                    'quality_score': merged_batch.quality_score
                }],
                operation_metadata={
                    'total_quantity': float(total_quantity),
                    'weighted_price': float(weighted_price),
                    'quality_calculation': quality_calculation,
                    'target_location_id': target_location.id,
                    'merged_quality_score': merged_quality
                },
                reason=merge_reason,
                performed_by=performed_by
            )

            result.success = True
            result.message = f"Successfully merged {len(source_batches)} batches into {new_batch_number}"
            result.created_batches = [merged_batch]
            result.operation_record = operation_record

            logger.info(f"Merged {len(source_batches)} batches into {new_batch_number}")

        except Exception as e:
            result.success = False
            result.message = f"Batch merge failed: {str(e)}"
            logger.error(f"Batch merge error: {str(e)}")

        return result

    @staticmethod
    @transaction.atomic
    def update_batch_quality(batch: StockBatch, new_quality_score: int,
                           reason: str, performed_by=None, quality_notes: str = "") -> BatchOperationResult:
        """
        Update batch quality with audit trail

        Args:
            batch: The batch to update
            new_quality_score: New quality score (0-100)
            reason: Reason for quality update
            performed_by: User performing the operation
            quality_notes: Additional notes about quality change
        """
        result = BatchOperationResult()

        try:
            # Validate quality score
            if not (0 <= new_quality_score <= 100):
                result.message = "Quality score must be between 0 and 100"
                return result

            old_quality_score = batch.quality_score

            # Update batch quality
            batch.quality_score = new_quality_score
            if quality_notes:
                batch.notes = f"{batch.notes} | Quality updated: {quality_notes}"
            batch.save()

            # Create operation record
            operation_record = BatchOperation.objects.create(
                owner=batch.owner,
                operation_type='quality_update',
                source_batches=[{
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'old_quality_score': old_quality_score,
                    'new_quality_score': new_quality_score
                }],
                target_batches=[{
                    'batch_id': batch.id,
                    'batch_number': batch.batch_number,
                    'quality_score': new_quality_score
                }],
                operation_metadata={
                    'old_quality_score': old_quality_score,
                    'new_quality_score': new_quality_score,
                    'quality_change': new_quality_score - old_quality_score,
                    'quality_notes': quality_notes
                },
                reason=reason,
                performed_by=performed_by
            )

            result.success = True
            result.message = f"Updated quality score from {old_quality_score} to {new_quality_score}"
            result.updated_batches = [batch]
            result.operation_record = operation_record

            # Add warning if quality dropped significantly
            if new_quality_score < old_quality_score - 20:
                result.warnings.append(f"Significant quality drop detected: {old_quality_score} → {new_quality_score}")

            logger.info(f"Updated batch {batch.batch_number} quality: {old_quality_score} → {new_quality_score}")

        except Exception as e:
            result.success = False
            result.message = f"Quality update failed: {str(e)}"
            logger.error(f"Quality update error: {str(e)}")

        return result

    @staticmethod
    def get_mergeable_batches(product: EnhancedProduct, location: Location = None,
                            quality_tolerance: int = 10, expiry_tolerance_days: int = 30) -> List[StockBatch]:
        """
        Find batches that can be merged together

        Args:
            product: Product to find mergeable batches for
            location: Optional location filter
            quality_tolerance: Maximum quality score difference allowed
            expiry_tolerance_days: Maximum expiry date difference in days
        """
        try:
            # Get active batches for the product
            queryset = StockBatch.objects.filter(
                product=product,
                status='active',
                quantity_available__gt=0
            ).order_by('received_date')

            if location:
                queryset = queryset.filter(location=location)

            batches = list(queryset)

            if len(batches) < 2:
                return []

            # Group compatible batches
            mergeable_groups = []
            for i, batch1 in enumerate(batches):
                compatible_batches = [batch1]

                for j, batch2 in enumerate(batches[i+1:], i+1):
                    # Check quality tolerance
                    if abs(batch1.quality_score - batch2.quality_score) > quality_tolerance:
                        continue

                    # Check expiry tolerance
                    if batch1.expiry_date and batch2.expiry_date:
                        if abs((batch1.expiry_date - batch2.expiry_date).days) > expiry_tolerance_days:
                            continue

                    # Check same supplier (optional)
                    if batch1.supplier != batch2.supplier:
                        continue

                    # Check temperature requirements
                    if batch1.temperature_controlled != batch2.temperature_controlled:
                        continue

                    compatible_batches.append(batch2)

                if len(compatible_batches) > 1:
                    mergeable_groups.append(compatible_batches)

            # Return the largest group of compatible batches
            if mergeable_groups:
                return max(mergeable_groups, key=len)

            return []

        except Exception as e:
            logger.error(f"Error finding mergeable batches: {str(e)}")
            return []

    @staticmethod
    def get_batch_operation_history(batch: StockBatch) -> List[Dict[str, Any]]:
        """
        Get operation history for a batch
        """
        try:
            # Find operations involving this batch
            operations = BatchOperation.objects.filter(
                Q(source_batches__contains=[{'batch_id': batch.id}]) |
                Q(target_batches__contains=[{'batch_id': batch.id}])
            ).order_by('-created_at')

            history = []
            for operation in operations:
                history.append({
                    'operation_id': operation.id,
                    'operation_type': operation.get_operation_type_display(),
                    'reason': operation.reason,
                    'performed_by': operation.performed_by.mobile_number if operation.performed_by else 'System',
                    'timestamp': operation.created_at,
                    'metadata': operation.operation_metadata
                })

            return history

        except Exception as e:
            logger.error(f"Error getting batch operation history: {str(e)}")
            return []

    @staticmethod
    def get_batch_family_tree(batch: StockBatch) -> Dict[str, Any]:
        """
        Get complete family tree for a batch (parent and children)
        """
        try:
            family_tree = {
                'current_batch': {
                    'id': batch.id,
                    'batch_number': batch.batch_number,
                    'quantity_available': float(batch.quantity_available),
                    'quality_score': batch.quality_score,
                    'status': batch.status
                },
                'parent_batch': None,
                'child_batches': [],
                'ancestor_batches': [],
                'descendant_batches': []
            }

            # Get parent batch
            if batch.parent_batch:
                family_tree['parent_batch'] = {
                    'id': batch.parent_batch.id,
                    'batch_number': batch.parent_batch.batch_number,
                    'quantity_available': float(batch.parent_batch.quantity_available),
                    'quality_score': batch.parent_batch.quality_score,
                    'status': batch.parent_batch.status
                }

            # Get child batches
            child_batches = StockBatch.objects.filter(parent_batch=batch)
            for child in child_batches:
                family_tree['child_batches'].append({
                    'id': child.id,
                    'batch_number': child.batch_number,
                    'quantity_available': float(child.quantity_available),
                    'quality_score': child.quality_score,
                    'status': child.status
                })

            # Get all ancestors
            current_parent = batch.parent_batch
            while current_parent:
                family_tree['ancestor_batches'].append({
                    'id': current_parent.id,
                    'batch_number': current_parent.batch_number,
                    'quantity_available': float(current_parent.quantity_available),
                    'quality_score': current_parent.quality_score,
                    'status': current_parent.status
                })
                current_parent = current_parent.parent_batch

            # Get all descendants (recursive)
            def get_descendants(parent_batch):
                descendants = []
                children = StockBatch.objects.filter(parent_batch=parent_batch)
                for child in children:
                    descendants.append({
                        'id': child.id,
                        'batch_number': child.batch_number,
                        'quantity_available': float(child.quantity_available),
                        'quality_score': child.quality_score,
                        'status': child.status
                    })
                    descendants.extend(get_descendants(child))
                return descendants

            family_tree['descendant_batches'] = get_descendants(batch)

            return family_tree

        except Exception as e:
            logger.error(f"Error getting batch family tree: {str(e)}")
            return {}