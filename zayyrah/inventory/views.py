from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.urls import reverse
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count
from .models import Category
from .serializers import CategoryCreateUpdateSerializer


@login_required
def category_list(request):
    """List all categories with tree structure"""
    categories = Category.objects.filter(
        owner=request.user,
        is_active=True
    ).select_related('parent').prefetch_related('subcategories')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        categories = categories.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query)
        )

    # Order categories
    categories = categories.order_by('level', 'sort_order', 'name')

    # Pagination
    paginator = Paginator(categories, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get category tree for sidebar
    category_tree = Category.get_tree_data(request.user, include_products_count=True)

    context = {
        'categories': page_obj,
        'category_tree': category_tree,
        'search_query': search_query,
        'total_categories': categories.count(),
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'inventory/category_list.html', context)


@login_required
def category_detail(request, pk):
    """Category detail view"""
    category = get_object_or_404(Category, pk=pk, owner=request.user)

    # Get subcategories
    subcategories = category.subcategories.filter(is_active=True).order_by('sort_order', 'name')

    # Get products in this category
    inventory_products = category.products.filter(is_active=True)[:10]  # Enhanced Inventory Products

    # Get ancestors for breadcrumb
    ancestors = category.get_ancestors()
    ancestors.reverse()

    context = {
        'category': category,
        'subcategories': subcategories,
        'inventory_products': inventory_products,
        'ancestors': ancestors,
        'inventory_products_count': category.products.filter(is_active=True).count(),
        'total_products_count': category.products.filter(is_active=True).count(),
        'subcategories_count': subcategories.count(),
    }

    return render(request, 'inventory/category_detail.html', context)


@login_required
def category_create(request):
    """Create new category"""
    if request.method == 'POST':
        # Get form data
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        parent_id = request.POST.get('parent')
        sort_order = request.POST.get('sort_order', 0)

        # Validation
        if not name:
            messages.error(request, 'Category name is required.')
            return redirect('inventory:category-create')

        try:
            parent = None
            if parent_id:
                parent = Category.objects.get(id=parent_id, owner=request.user)
                # Check level limit
                if parent.level >= 2:
                    messages.error(request, 'Maximum category depth is 3 levels.')
                    return redirect('inventory:category-create')

            # Check if category name already exists for this parent
            existing = Category.objects.filter(
                owner=request.user,
                parent=parent,
                name__iexact=name
            ).exists()

            if existing:
                messages.error(request, f'Category "{name}" already exists at this level.')
                return redirect('inventory:category-create')

            # Create category
            category = Category.objects.create(
                owner=request.user,
                name=name,
                description=description,
                parent=parent,
                sort_order=int(sort_order) if sort_order else 0
            )

            messages.success(request, f'Category "{category.name}" created successfully!')
            return redirect('inventory:category-detail', pk=category.pk)

        except Category.DoesNotExist:
            messages.error(request, 'Parent category not found.')
        except Exception as e:
            messages.error(request, f'Error creating category: {str(e)}')

    # Get possible parent categories (excluding level 2 categories)
    parent_categories = Category.objects.filter(
        owner=request.user,
        is_active=True,
        level__lt=2
    ).order_by('level', 'sort_order', 'name')

    context = {
        'parent_categories': parent_categories,
    }

    return render(request, 'inventory/category_form.html', context)


@login_required
def category_update(request, pk):
    """Update existing category"""
    category = get_object_or_404(Category, pk=pk, owner=request.user)

    if request.method == 'POST':
        # Get form data
        name = request.POST.get('name', '').strip()
        description = request.POST.get('description', '').strip()
        parent_id = request.POST.get('parent')
        sort_order = request.POST.get('sort_order', 0)
        is_active = request.POST.get('is_active') == 'on'

        # Validation
        if not name:
            messages.error(request, 'Category name is required.')
            return redirect('inventory:category-update', pk=pk)

        try:
            parent = None
            if parent_id:
                parent = Category.objects.get(id=parent_id, owner=request.user)

                # Check if move is valid
                if not category.can_move_to(parent):
                    messages.error(request, 'Cannot move category to this parent - would create circular reference or exceed depth limit.')
                    return redirect('inventory:category-update', pk=pk)

            # Check if category name already exists for this parent (excluding current category)
            existing = Category.objects.filter(
                owner=request.user,
                parent=parent,
                name__iexact=name
            ).exclude(pk=category.pk).exists()

            if existing:
                messages.error(request, f'Category "{name}" already exists at this level.')
                return redirect('inventory:category-update', pk=pk)

            # Update category
            old_parent = category.parent
            category.name = name
            category.description = description
            category.sort_order = int(sort_order) if sort_order else 0
            category.is_active = is_active

            # Handle parent change
            if parent != old_parent:
                category.move_to(parent)
            else:
                category.save()

            messages.success(request, f'Category "{category.name}" updated successfully!')
            return redirect('inventory:category-detail', pk=category.pk)

        except Category.DoesNotExist:
            messages.error(request, 'Parent category not found.')
        except Exception as e:
            messages.error(request, f'Error updating category: {str(e)}')

    # Get possible parent categories (excluding current category and its descendants)
    descendants = category.get_all_descendants()
    descendant_ids = [desc.id for desc in descendants] + [category.id]

    parent_categories = Category.objects.filter(
        owner=request.user,
        is_active=True,
        level__lt=2
    ).exclude(id__in=descendant_ids).order_by('level', 'sort_order', 'name')

    context = {
        'category': category,
        'parent_categories': parent_categories,
        'is_update': True,
    }

    return render(request, 'inventory/category_form.html', context)


@login_required
def category_delete(request, pk):
    """Delete category"""
    category = get_object_or_404(Category, pk=pk, owner=request.user)

    if request.method == 'POST':
        # Check if category has products
        if category.products.exists():
            messages.error(request, f'Cannot delete category "{category.name}" because it contains products.')
            return redirect('inventory:category-detail', pk=pk)

        # Check if category has subcategories
        if category.subcategories.filter(is_active=True).exists():
            messages.error(request, f'Cannot delete category "{category.name}" because it contains subcategories.')
            return redirect('inventory:category-detail', pk=pk)

        category_name = category.name
        category.delete()
        messages.success(request, f'Category "{category_name}" deleted successfully!')
        return redirect('inventory:category-list')

    context = {
        'category': category,
        'products_count': category.products.filter(is_active=True).count(),
        'subcategories_count': category.subcategories.filter(is_active=True).count(),
    }

    return render(request, 'inventory/category_confirm_delete.html', context)


@login_required
def category_tree_ajax(request):
    """AJAX endpoint for category tree data"""
    include_products_count = request.GET.get('include_products_count', 'false').lower() == 'true'
    tree_data = Category.get_tree_data(request.user, include_products_count=include_products_count)

    return JsonResponse({
        'success': True,
        'data': tree_data
    })


@login_required
def category_move_ajax(request, pk):
    """AJAX endpoint for moving categories"""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'message': 'Invalid request method'})

    category = get_object_or_404(Category, pk=pk, owner=request.user)
    new_parent_id = request.POST.get('new_parent_id')

    try:
        new_parent = None
        if new_parent_id:
            new_parent = Category.objects.get(id=new_parent_id, owner=request.user)

        if category.can_move_to(new_parent):
            old_parent_id = category.parent_id
            category.move_to(new_parent)

            return JsonResponse({
                'success': True,
                'message': 'Category moved successfully',
                'data': {
                    'category_id': category.id,
                    'old_parent_id': old_parent_id,
                    'new_parent_id': new_parent_id,
                    'new_level': category.level
                }
            })
        else:
            return JsonResponse({
                'success': False,
                'message': 'Cannot move category to this parent - would create circular reference or exceed depth limit'
            })

    except Category.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': 'New parent category not found'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'Error moving category: {str(e)}'
        })


# Product Management Views (replacing POS product views)

@login_required
def product_list(request):
    """List all enhanced products"""
    from .models import EnhancedProduct

    products = EnhancedProduct.objects.filter(
        owner=request.user,
        is_active=True
    ).select_related('category', 'brand', 'supplier').order_by('name')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        products = products.filter(
            Q(name__icontains=search_query) |
            Q(description__icontains=search_query) |
            Q(sku__icontains=search_query)
        )

    # Category filter
    category_id = request.GET.get('category')
    if category_id:
        try:
            category = Category.objects.get(id=category_id, owner=request.user)
            products = products.filter(category=category)
        except Category.DoesNotExist:
            pass

    # Pagination
    from django.core.paginator import Paginator
    paginator = Paginator(products, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'products': page_obj,
        'search_query': search_query,
        'total_products': products.count(),
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'inventory/product_list.html', context)


@login_required
def product_detail(request, pk):
    """Product detail view with stock management"""
    from .models import EnhancedProduct, StockBatch, Location
    from decimal import Decimal
    from django.utils import timezone

    product = get_object_or_404(EnhancedProduct, pk=pk, owner=request.user)

    # Handle POST requests for stock management
    if request.method == 'POST':
        action = request.POST.get('action', 'add_stock')
        print(f"DEBUG: Received POST request for product {product.id}, action: {action}")
        print(f"DEBUG: POST data: {request.POST}")

        if action == 'edit_stock':
            # Handle stock entry editing
            try:
                stock_id = int(request.POST.get('stock_id'))
                batch = get_object_or_404(StockBatch, pk=stock_id, owner=request.user, product=product)

                # Get form data
                quantity_received = int(request.POST.get('quantity_received'))
                purchase_price = Decimal(request.POST.get('purchase_price'))
                received_date = request.POST.get('received_date')
                expiry_date = request.POST.get('expiry_date') or None
                notes = request.POST.get('notes', '')

                # Validation
                if quantity_received <= 0:
                    messages.error(request, 'Quantity must be greater than 0.')
                    return redirect('inventory:product-detail', pk=pk)

                # Parse dates
                received_date_parsed = timezone.datetime.strptime(received_date, '%Y-%m-%d').date()
                expiry_date_parsed = timezone.datetime.strptime(expiry_date, '%Y-%m-%d').date() if expiry_date else None

                # Calculate the difference in quantity to adjust available stock
                quantity_difference = quantity_received - batch.quantity_received
                new_available = batch.quantity_available + quantity_difference

                if new_available < 0:
                    messages.error(request, 'Cannot reduce quantity below already sold amount.')
                    return redirect('inventory:product-detail', pk=pk)

                # Update the batch
                batch.quantity_received = quantity_received
                batch.quantity_available = new_available
                batch.purchase_price = purchase_price
                batch.received_date = received_date_parsed
                batch.expiry_date = expiry_date_parsed
                batch.notes = notes
                batch.save()

                print(f"DEBUG: Successfully updated batch {batch.id}")
                messages.success(request, f'Successfully updated stock entry!')
                return redirect('inventory:product-detail', pk=pk)

            except (ValueError, StockBatch.DoesNotExist) as e:
                print(f"DEBUG: Error updating stock: {e}")
                messages.error(request, f'Error updating stock entry: {str(e)}')

        elif action == 'delete_stock':
            # Handle stock entry deletion
            try:
                stock_id = int(request.POST.get('stock_id'))
                batch = get_object_or_404(StockBatch, pk=stock_id, owner=request.user, product=product)

                # Check if any quantity has been sold
                if batch.quantity_sold > 0:
                    messages.error(request, f'Cannot delete stock entry - {batch.quantity_sold} units have already been sold.')
                    return redirect('inventory:product-detail', pk=pk)

                batch_number = batch.batch_number
                batch.delete()

                print(f"DEBUG: Successfully deleted batch {stock_id}")
                messages.success(request, f'Successfully deleted stock entry {batch_number}!')
                return redirect('inventory:product-detail', pk=pk)

            except (ValueError, StockBatch.DoesNotExist) as e:
                print(f"DEBUG: Error deleting stock: {e}")
                messages.error(request, f'Error deleting stock entry: {str(e)}')

        else:
            # Handle stock addition (default action)
            try:
                # Get form data
                quantity_added = int(request.POST.get('quantity_added', 0))
                purchase_price = Decimal(request.POST.get('purchase_price', '0.00'))
                added_on = request.POST.get('added_on')
                expiry_date = request.POST.get('expiry_date') or None
                notes = request.POST.get('notes', '')

                print(f"DEBUG: Parsed data - quantity: {quantity_added}, price: {purchase_price}")

                # Validation
                if quantity_added <= 0:
                    messages.error(request, 'Quantity must be greater than 0.')
                    return redirect('inventory:product-detail', pk=pk)

                if purchase_price < 0:
                    messages.error(request, 'Purchase price cannot be negative.')
                    return redirect('inventory:product-detail', pk=pk)

                # Get or create default location
                location, created = Location.objects.get_or_create(
                    owner=request.user,
                    code='MAIN',
                    defaults={
                        'name': 'Main Store',
                        'location_type': 'store'
                    }
                )

                if created:
                    print(f"DEBUG: Created default location: {location}")

                # Parse dates
                added_on_date = timezone.datetime.strptime(added_on, '%Y-%m-%d').date() if added_on else timezone.localdate()
                expiry_date_parsed = timezone.datetime.strptime(expiry_date, '%Y-%m-%d').date() if expiry_date else None

                # Add stock using the model method
                batch, movement = product.add_stock(
                    quantity=quantity_added,
                    location=location,
                    purchase_price=purchase_price,
                    received_date=added_on_date,
                    expiry_date=expiry_date_parsed,
                    notes=notes
                )

                print(f"DEBUG: Successfully created batch {batch.id} and movement {movement.id}")

                messages.success(request, f'Successfully added {quantity_added} {product.unit_label} to stock!')
                return redirect('inventory:product-detail', pk=pk)

            except ValueError as e:
                print(f"DEBUG: ValueError: {e}")
                messages.error(request, f'Invalid input: {str(e)}')
            except Exception as e:
                print(f"DEBUG: Unexpected error: {e}")
                messages.error(request, f'Error adding stock: {str(e)}')

    # Get recent stock entries for display
    stock_entries = product.stock_batches.filter(status='active').order_by('-received_date')[:10]

    # Get sales history using new POS system
    from .models import POSTransactionItem, POSTransaction
    from django.db.models import Sum, Count, Avg
    from datetime import datetime, timedelta

    # Get recent sales transactions for this product
    recent_sales = POSTransactionItem.objects.filter(
        product=product,
        transaction__status='completed',
        transaction__owner=request.user
    ).select_related(
        'transaction',
        'transaction__customer',
        'transaction__owner'
    ).order_by('-transaction__completed_at')[:20]

    # Calculate sales analytics for this product
    sales_analytics = POSTransactionItem.objects.filter(
        product=product,
        transaction__status='completed',
        transaction__owner=request.user
    ).aggregate(
        total_sold=Sum('quantity'),
        total_revenue=Sum('line_total'),
        total_transactions=Count('transaction', distinct=True),
        avg_quantity_per_sale=Avg('quantity'),
        avg_price_per_unit=Avg('unit_price')
    )

    # Sales analytics by time periods
    now = timezone.now()
    today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
    week_start = today_start - timedelta(days=today_start.weekday())
    month_start = today_start.replace(day=1)

    # Today's sales
    today_sales = POSTransactionItem.objects.filter(
        product=product,
        transaction__status='completed',
        transaction__owner=request.user,
        transaction__completed_at__gte=today_start
    ).aggregate(
        quantity=Sum('quantity') or 0,
        revenue=Sum('line_total') or 0,
        transactions=Count('transaction', distinct=True)
    )

    # This week's sales
    week_sales = POSTransactionItem.objects.filter(
        product=product,
        transaction__status='completed',
        transaction__owner=request.user,
        transaction__completed_at__gte=week_start
    ).aggregate(
        quantity=Sum('quantity') or 0,
        revenue=Sum('line_total') or 0,
        transactions=Count('transaction', distinct=True)
    )

    # This month's sales
    month_sales = POSTransactionItem.objects.filter(
        product=product,
        transaction__status='completed',
        transaction__owner=request.user,
        transaction__completed_at__gte=month_start
    ).aggregate(
        quantity=Sum('quantity') or 0,
        revenue=Sum('line_total') or 0,
        transactions=Count('transaction', distinct=True)
    )

    context = {
        'product': product,
        'stock_entries': stock_entries,
        'recent_sales': recent_sales,
        'sales_analytics': sales_analytics,
        'today_sales': today_sales,
        'week_sales': week_sales,
        'month_sales': month_sales,
    }

    return render(request, 'inventory/product_detail.html', context)


@login_required
def product_create(request):
    """Create new product"""
    from .forms import EnhancedProductForm
    from .models import EnhancedProduct

    if request.method == 'POST':
        form = EnhancedProductForm(request.POST, user=request.user)
        if form.is_valid():
            product = form.save(commit=False)
            product.owner = request.user
            product.save()
            messages.success(request, f'Product "{product.name}" created successfully!')
            return redirect('inventory:product-detail', pk=product.pk)
    else:
        form = EnhancedProductForm(user=request.user)

    context = {
        'form': form,
        'title': 'Add New Product',
        'submit_text': 'Create Product'
    }
    return render(request, 'inventory/product_form.html', context)


@login_required
def product_update(request, pk):
    """Update product - redirect to API-based interface"""
    from django.shortcuts import redirect
    return redirect('inventory:product-detail', pk=pk)


@login_required
def pos_interface(request):
    """Main POS interface view"""
    from .models import EnhancedProduct
    from customers.models import Customer

    # Get POS-enabled products
    products = EnhancedProduct.objects.filter(
        owner=request.user,
        pos_enabled=True,
        is_active=True
    ).select_related('category').order_by('category__name', 'name')

    # Get categories that have POS products
    categories = Category.objects.filter(
        owner=request.user,
        is_active=True,
        products__pos_enabled=True
    ).distinct().order_by('name')

    # Get customers for quick selection
    customers = Customer.objects.filter(
        owner=request.user
    ).order_by('name')[:50]  # Limit to 50 recent customers

    context = {
        'products': products,
        'categories': categories,
        'customers': customers,
        'page_title': 'POS System',
    }

    return render(request, 'inventory/pos_interface.html', context)


@login_required
def pos_transactions(request):
    """POS transactions list view"""
    from .models import POSTransaction
    from django.core.paginator import Paginator
    from django.db.models import Q

    # Get all transactions for the user
    transactions = POSTransaction.objects.filter(
        owner=request.user
    ).select_related('customer').prefetch_related('items__product').order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        transactions = transactions.filter(
            Q(transaction_number__icontains=search_query) |
            Q(customer__name__icontains=search_query) |
            Q(customer__mobile_number__icontains=search_query) |
            Q(notes__icontains=search_query)
        )

    # Status filter
    status_filter = request.GET.get('status', '')
    if status_filter:
        transactions = transactions.filter(status=status_filter)

    # Payment status filter
    payment_status_filter = request.GET.get('payment_status', '')
    if payment_status_filter:
        transactions = transactions.filter(payment_status=payment_status_filter)

    # Date range filter
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    if date_from:
        from django.utils.dateparse import parse_date
        start_date = parse_date(date_from)
        if start_date:
            transactions = transactions.filter(created_at__date__gte=start_date)

    if date_to:
        from django.utils.dateparse import parse_date
        end_date = parse_date(date_to)
        if end_date:
            transactions = transactions.filter(created_at__date__lte=end_date)

    # Pagination
    paginator = Paginator(transactions, 20)  # 20 transactions per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get summary statistics
    total_transactions = transactions.count()
    completed_transactions = transactions.filter(status='completed').count()
    total_sales = sum(t.total_amount for t in transactions.filter(status='completed'))

    context = {
        'transactions': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'payment_status_filter': payment_status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'total_transactions': total_transactions,
        'completed_transactions': completed_transactions,
        'total_sales': total_sales,
        'page_title': 'POS Transactions',
        'is_paginated': page_obj.has_other_pages(),
        'page_obj': page_obj,
    }

    return render(request, 'inventory/pos_transactions.html', context)
