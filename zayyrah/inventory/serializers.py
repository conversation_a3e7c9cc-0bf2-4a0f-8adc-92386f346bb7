from rest_framework import serializers
from django.core.exceptions import ValidationError
from decimal import Decimal
from django.utils import timezone
from django.utils.dateparse import parse_datetime

from .models import (
    Category, Supplier, Location, ProductBrand, EnhancedProduct,
    ProductImage, StockBatch, StockMovement
)


class FlutterCompatibleSerializerMixin:
    """
    Mixin to handle null values in API responses for Flutter compatibility.
    Replaces null values with appropriate defaults based on field type.
    """

    def to_representation(self, instance):
        """Override to clean null values from the response"""
        data = super().to_representation(instance)
        return self.clean_null_values(data)

    def clean_null_values(self, data):
        """Clean null values from response data"""
        if isinstance(data, dict):
            cleaned_data = {}
            for key, value in data.items():
                if value is None:
                    cleaned_data[key] = self.get_null_replacement(key, value)
                elif isinstance(value, (dict, list)):
                    cleaned_data[key] = self.clean_null_values(value)
                else:
                    cleaned_data[key] = value
            return cleaned_data
        elif isinstance(data, list):
            return [self.clean_null_values(item) for item in data]
        return data

    def get_null_replacement(self, field_name, original_value):
        """Get appropriate replacement value for null fields based on field name and type"""

        # String fields that should be empty string
        string_fields = [
            'sku', 'barcode', 'qr_code', 'description', 'dimensions',
            'tags', 'notes', 'alt_text', 'unit_custom_label', 'address',
            'tax_number', 'payment_terms', 'contact_person', 'email',
            'purchase_order_number', 'reference_number'
        ]

        # String fields that should have descriptive text
        descriptive_string_fields = {
            'brand_name': 'No brand',
            'supplier_name': 'No supplier',
            'category_name': 'No category',
            'location_name': 'No location',
            'product_name': 'No name',
            'batch_number': 'No batch number'
        }

        # Numeric fields that should be 0
        numeric_zero_fields = [
            'maximum_stock_level', 'reorder_level', 'quantity_reserved',
            'quantity_damaged', 'location_id', 'supplier', 'brand',
            'category', 'product', 'location', 'reference_id'
        ]

        # Decimal fields that should be 0.00
        decimal_fields = [
            'wholesale_price', 'minimum_selling_price', 'weight',
            'purchase_price', 'selling_price', 'tax_rate', 'rating',
            'total_stock_value', 'batch_value', 'unit_price', 'discount_amount'
        ]

        # Date fields that should be empty string
        date_fields = [
            'expiry_date', 'manufacture_date', 'received_date',
            'oldest_batch_date', 'newest_batch_date'
        ]

        # Check field type and return appropriate default
        if field_name in string_fields:
            return ""
        elif field_name in descriptive_string_fields:
            return descriptive_string_fields[field_name]
        elif field_name in numeric_zero_fields:
            return 0
        elif field_name in decimal_fields:
            return "0.00"
        elif field_name in date_fields:
            return ""
        else:
            # Default fallback based on common patterns
            if field_name.endswith('_name') or field_name.endswith('_number'):
                return ""
            elif field_name.endswith('_id') or field_name.endswith('_count'):
                return 0
            elif field_name.endswith('_price') or field_name.endswith('_amount') or field_name.endswith('_value'):
                return "0.00"
            elif field_name.endswith('_date') or field_name.endswith('_time'):
                return ""
            else:
                # Generic fallback
                return ""


class DateOrDateTimeField(serializers.DateField):
    """Accept either date-only strings or full ISO 8601 timestamps."""

    def to_internal_value(self, value):
        if isinstance(value, str):
            dt_value = parse_datetime(value)
            if dt_value is not None:
                if timezone.is_aware(dt_value):
                    dt_value = timezone.localtime(dt_value)
                return dt_value.date()
        return super().to_internal_value(value)


# Category Serializers
class CategoryListSerializer(serializers.ModelSerializer):
    """List serializer for categories with basic info"""
    full_path = serializers.ReadOnlyField()
    children_count = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id', 'name', 'parent', 'level', 'is_active', 'sort_order',
            'description', 'created_at', 'updated_at', 'full_path', 'children_count'
        ]

    def get_children_count(self, obj):
        return obj.subcategories.filter(is_active=True).count()


class CategoryDetailSerializer(serializers.ModelSerializer):
    """Detailed category serializer with hierarchy info"""
    full_path = serializers.ReadOnlyField()
    children = CategoryListSerializer(source='get_children', many=True, read_only=True)
    products_count = serializers.SerializerMethodField()

    class Meta:
        model = Category
        fields = [
            'id', 'name', 'parent', 'level', 'is_active', 'sort_order',
            'description', 'created_at', 'updated_at', 'full_path',
            'children', 'products_count'
        ]

    def get_products_count(self, obj):
        return obj.products.filter(is_active=True).count()


class CategoryCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating categories"""

    class Meta:
        model = Category
        fields = [
            'name', 'parent', 'is_active', 'sort_order', 'description'
        ]

    def validate(self, data):
        """Custom validation for category data"""
        request = self.context.get('request')
        if request:
            data['owner'] = request.user

        # Check parent category belongs to same owner
        parent = data.get('parent')
        if parent and parent.owner != request.user:
            raise serializers.ValidationError('Parent category must belong to you.')

        # Check maximum depth
        if parent and parent.level >= 2:
            raise serializers.ValidationError('Maximum category depth is 3 levels.')

        # Check unique name per parent per owner
        name = data.get('name')
        if name and parent:
            existing = Category.objects.filter(
                owner=request.user,
                parent=parent,
                name__iexact=name
            )
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Category with this name already exists under the same parent.')

        return data

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


# Supplier Serializers
class SupplierListSerializer(serializers.ModelSerializer):
    """List serializer for suppliers"""
    products_count = serializers.SerializerMethodField()

    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'phone', 'email',
            'is_active', 'rating', 'created_at', 'products_count'
        ]

    def get_products_count(self, obj):
        return obj.products.filter(is_active=True).count()


class SupplierDetailSerializer(serializers.ModelSerializer):
    """Detailed supplier serializer"""
    products_count = serializers.SerializerMethodField()
    stock_batches_count = serializers.SerializerMethodField()

    class Meta:
        model = Supplier
        fields = [
            'id', 'name', 'contact_person', 'phone', 'email', 'address',
            'tax_number', 'payment_terms', 'is_active', 'rating', 'notes',
            'created_at', 'updated_at', 'products_count', 'stock_batches_count'
        ]

    def get_products_count(self, obj):
        return obj.products.filter(is_active=True).count()

    def get_stock_batches_count(self, obj):
        return obj.stock_batches.filter(status='active').count()


class SupplierCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating suppliers"""

    class Meta:
        model = Supplier
        fields = [
            'name', 'contact_person', 'phone', 'email', 'address',
            'tax_number', 'payment_terms', 'is_active', 'rating', 'notes'
        ]

    def validate_name(self, value):
        """Validate unique supplier name per owner"""
        request = self.context.get('request')
        if request:
            existing = Supplier.objects.filter(owner=request.user, name__iexact=value)
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Supplier with this name already exists.')
        return value

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


# Location Serializers
class LocationListSerializer(serializers.ModelSerializer):
    """List serializer for locations"""
    stock_batches_count = serializers.SerializerMethodField()

    class Meta:
        model = Location
        fields = [
            'id', 'name', 'code', 'location_type', 'is_active',
            'created_at', 'stock_batches_count'
        ]

    def get_stock_batches_count(self, obj):
        return obj.stock_batches.filter(status='active').count()


class LocationDetailSerializer(serializers.ModelSerializer):
    """Detailed location serializer"""
    stock_batches_count = serializers.SerializerMethodField()
    total_stock_value = serializers.SerializerMethodField()

    class Meta:
        model = Location
        fields = [
            'id', 'name', 'code', 'location_type', 'address', 'is_active',
            'created_at', 'updated_at', 'stock_batches_count', 'total_stock_value'
        ]

    def get_stock_batches_count(self, obj):
        return obj.stock_batches.filter(status='active').count()

    def get_total_stock_value(self, obj):
        total = Decimal('0.00')
        for batch in obj.stock_batches.filter(status='active', quantity_available__gt=0):
            total += batch.batch_value
        return total


class LocationCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating locations"""

    class Meta:
        model = Location
        fields = ['name', 'code', 'location_type', 'address', 'is_active']

    def validate_code(self, value):
        """Validate unique location code per owner"""
        request = self.context.get('request')
        if request:
            existing = Location.objects.filter(owner=request.user, code__iexact=value)
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Location with this code already exists.')
        return value

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


# Product Brand Serializers
class ProductBrandSerializer(serializers.ModelSerializer):
    """Serializer for product brands"""
    products_count = serializers.SerializerMethodField()

    class Meta:
        model = ProductBrand
        fields = ['id', 'name', 'is_active', 'created_at', 'updated_at', 'products_count']

    def get_products_count(self, obj):
        return obj.products.filter(is_active=True).count()

    def validate_name(self, value):
        """Validate unique brand name per owner"""
        request = self.context.get('request')
        if request:
            existing = ProductBrand.objects.filter(owner=request.user, name__iexact=value)
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Brand with this name already exists.')
        return value

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


# Enhanced Product Serializers
class EnhancedProductListSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """List serializer for products with essential info"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    unit_label = serializers.ReadOnlyField()
    current_stock = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    is_out_of_stock = serializers.ReadOnlyField()

    class Meta:
        model = EnhancedProduct
        fields = [
            'id', 'name', 'sku', 'barcode', 'category', 'category_name',
            'brand', 'brand_name', 'supplier', 'supplier_name',
            'selling_price', 'purchase_price', 'unit', 'unit_label',
            'current_stock', 'reorder_level', 'is_low_stock', 'is_out_of_stock',
            'is_active', 'is_featured', 'created_at', 'updated_at'
        ]


class EnhancedProductDetailSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """Detailed product serializer with all information"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_path = serializers.CharField(source='category.full_path', read_only=True)
    brand_name = serializers.CharField(source='brand.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    unit_label = serializers.ReadOnlyField()
    current_stock = serializers.ReadOnlyField()
    total_stock_value = serializers.ReadOnlyField()
    is_low_stock = serializers.ReadOnlyField()
    is_out_of_stock = serializers.ReadOnlyField()

    # Related data
    images = serializers.SerializerMethodField()
    active_batches_count = serializers.SerializerMethodField()

    class Meta:
        model = EnhancedProduct
        fields = [
            'id', 'name', 'description', 'sku', 'barcode', 'qr_code',
            'category', 'category_name', 'category_path',
            'brand', 'brand_name', 'supplier', 'supplier_name',
            'purchase_price', 'selling_price', 'wholesale_price', 'minimum_selling_price',
            'track_stock', 'allow_negative_stock', 'reorder_level', 'maximum_stock_level',
            'unit', 'unit_label', 'unit_custom_label', 'weight', 'dimensions',
            'tax_rate', 'tax_exempt', 'is_active', 'is_featured', 'tags',
            'current_stock', 'total_stock_value', 'is_low_stock', 'is_out_of_stock',
            'images', 'active_batches_count',
            'created_at', 'updated_at'
        ]

    def get_images(self, obj):
        # Return basic image info since image field is disabled
        return obj.images.values('id', 'is_primary', 'alt_text', 'sort_order')

    def get_active_batches_count(self, obj):
        return obj.stock_batches.filter(status='active').count()


class EnhancedProductCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating products"""

    class Meta:
        model = EnhancedProduct
        fields = [
            'name', 'description', 'sku', 'barcode', 'qr_code',
            'category', 'brand', 'supplier',
            'purchase_price', 'selling_price', 'wholesale_price', 'minimum_selling_price',
            'track_stock', 'allow_negative_stock', 'reorder_level', 'maximum_stock_level',
            'unit', 'unit_custom_label', 'weight', 'dimensions',
            'tax_rate', 'tax_exempt', 'is_active', 'is_featured', 'tags'
        ]

    def validate(self, data):
        """Custom validation for product data"""
        request = self.context.get('request')

        # Validate related objects belong to owner
        category = data.get('category')
        if category and category.owner != request.user:
            raise serializers.ValidationError('Category must belong to you.')

        brand = data.get('brand')
        if brand and brand.owner != request.user:
            raise serializers.ValidationError('Brand must belong to you.')

        supplier = data.get('supplier')
        if supplier and supplier.owner != request.user:
            raise serializers.ValidationError('Supplier must belong to you.')

        # Validate pricing
        purchase_price = data.get('purchase_price', Decimal('0.00'))
        selling_price = data.get('selling_price')
        wholesale_price = data.get('wholesale_price')
        minimum_selling_price = data.get('minimum_selling_price')

        if wholesale_price and wholesale_price < purchase_price:
            raise serializers.ValidationError('Wholesale price cannot be less than purchase price.')

        if minimum_selling_price and minimum_selling_price > selling_price:
            raise serializers.ValidationError('Minimum selling price cannot be greater than selling price.')

        # Validate SKU uniqueness
        sku = data.get('sku')
        if sku:
            existing = EnhancedProduct.objects.filter(owner=request.user, sku=sku)
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Product with this SKU already exists.')

        # Validate barcode uniqueness and handle empty values
        barcode = data.get('barcode')
        if barcode and barcode.strip():
            barcode = barcode.strip()
            existing = EnhancedProduct.objects.filter(owner=request.user, barcode=barcode)
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Product with this barcode already exists.')
            data['barcode'] = barcode
        else:
            # Convert empty/whitespace-only barcodes to None to avoid unique constraint issues
            data['barcode'] = None

        return data

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


# Stock Batch Serializers
class StockBatchListSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """List serializer for stock batches"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    batch_value = serializers.ReadOnlyField()
    quantity_sold = serializers.ReadOnlyField()
    is_expiring_soon = serializers.SerializerMethodField()

    class Meta:
        model = StockBatch
        fields = [
            'id', 'batch_number', 'product', 'product_name', 'product_sku',
            'location', 'location_name', 'supplier', 'supplier_name',
            'quantity_received', 'quantity_available', 'quantity_reserved',
            'quantity_damaged', 'quantity_sold', 'purchase_price', 'batch_value',
            'received_date', 'expiry_date', 'status', 'is_expiring_soon',
            'created_at', 'updated_at'
        ]

    def get_is_expiring_soon(self, obj):
        return obj.is_expiring_soon


class StockBatchDetailSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """Detailed stock batch serializer"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    batch_value = serializers.ReadOnlyField()
    quantity_sold = serializers.ReadOnlyField()
    is_expiring_soon = serializers.SerializerMethodField()
    movements_count = serializers.SerializerMethodField()

    class Meta:
        model = StockBatch
        fields = [
            'id', 'batch_number', 'purchase_order_number',
            'product', 'product_name', 'location', 'location_name',
            'supplier', 'supplier_name',
            'quantity_received', 'quantity_available', 'quantity_reserved',
            'quantity_damaged', 'quantity_sold', 'purchase_price', 'batch_value',
            'received_date', 'manufacture_date', 'expiry_date', 'status',
            'is_expiring_soon', 'movements_count', 'notes',
            'created_at', 'updated_at'
        ]

    def get_is_expiring_soon(self, obj):
        return obj.is_expiring_soon

    def get_movements_count(self, obj):
        return obj.movements.count()


class StockBatchCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating stock batches"""

    received_date = DateOrDateTimeField(required=False, allow_null=True)
    manufacture_date = DateOrDateTimeField(required=False, allow_null=True)
    expiry_date = DateOrDateTimeField(required=False, allow_null=True)

    class Meta:
        model = StockBatch
        fields = [
            'batch_number', 'purchase_order_number', 'product', 'location', 'supplier',
            'quantity_received', 'quantity_available', 'quantity_reserved', 'quantity_damaged',
            'purchase_price', 'received_date', 'manufacture_date', 'expiry_date',
            'status', 'notes'
        ]
        extra_kwargs = {
            'purchase_order_number': {'required': False, 'allow_blank': True},
            'supplier': {'required': False, 'allow_null': True},
            'quantity_available': {'required': False},
            'quantity_reserved': {'required': False},
            'quantity_damaged': {'required': False},
            'manufacture_date': {'required': False, 'allow_null': True},
            'expiry_date': {'required': False, 'allow_null': True},
            'status': {'required': False},
            'notes': {'required': False, 'allow_blank': True},
        }

    def validate(self, data):
        """Custom validation for stock batch data"""
        request = self.context.get('request')

        # Validate related objects belong to owner
        product = data.get('product')
        if product and product.owner != request.user:
            raise serializers.ValidationError('Product must belong to you.')

        location = data.get('location')
        if location and location.owner != request.user:
            raise serializers.ValidationError('Location must belong to you.')

        supplier = data.get('supplier')
        if supplier and supplier.owner != request.user:
            raise serializers.ValidationError('Supplier must belong to you.')

        # Validate quantities
        instance = self.instance
        quantity_received = data.get('quantity_received')
        if quantity_received is None:
            if instance:
                quantity_received = instance.quantity_received
            else:
                raise serializers.ValidationError('Quantity received is required.')

        # Auto-fill defaults for omitted optional quantity fields
        if 'quantity_available' not in data or data.get('quantity_available') is None:
            if instance:
                data['quantity_available'] = instance.quantity_available
            else:
                data['quantity_available'] = quantity_received

        if 'quantity_reserved' not in data or data.get('quantity_reserved') is None:
            data['quantity_reserved'] = instance.quantity_reserved if instance else Decimal('0')

        if 'quantity_damaged' not in data or data.get('quantity_damaged') is None:
            data['quantity_damaged'] = instance.quantity_damaged if instance else Decimal('0')

        quantity_available = data['quantity_available']
        quantity_damaged = data['quantity_damaged']

        if quantity_available > quantity_received:
            raise serializers.ValidationError('Available quantity cannot exceed received quantity.')

        if quantity_damaged > quantity_received:
            raise serializers.ValidationError('Damaged quantity cannot exceed received quantity.')

        # Validate dates
        manufacture_date = data.get('manufacture_date')
        expiry_date = data.get('expiry_date')

        if manufacture_date and expiry_date and expiry_date <= manufacture_date:
            raise serializers.ValidationError('Expiry date must be after manufacture date.')

        # Validate unique batch number per product per owner
        batch_number = data.get('batch_number')
        if batch_number and product:
            existing = StockBatch.objects.filter(
                owner=request.user,
                product=product,
                batch_number=batch_number
            )
            if self.instance:
                existing = existing.exclude(pk=self.instance.pk)
            if existing.exists():
                raise serializers.ValidationError('Batch with this number already exists for this product.')

        return data

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        # Set quantity_available to quantity_received if not specified
        if 'quantity_available' not in validated_data:
            validated_data['quantity_available'] = validated_data['quantity_received']
        return super().create(validated_data)


# Stock Movement Serializers
class StockMovementListSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """List serializer for stock movements"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    batch_number = serializers.CharField(source='batch.batch_number', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.mobile_number', read_only=True)
    is_incoming = serializers.ReadOnlyField()
    is_outgoing = serializers.ReadOnlyField()

    class Meta:
        model = StockMovement
        fields = [
            'id', 'product', 'product_name', 'batch', 'batch_number',
            'location', 'location_name', 'movement_type', 'quantity',
            'reference_number', 'reference_type', 'reference_id',
            'is_incoming', 'is_outgoing', 'created_by', 'created_by_name',
            'created_at', 'notes'
        ]


class StockMovementDetailSerializer(FlutterCompatibleSerializerMixin, serializers.ModelSerializer):
    """Detailed stock movement serializer"""
    product_name = serializers.CharField(source='product.name', read_only=True)
    batch_number = serializers.CharField(source='batch.batch_number', read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.mobile_number', read_only=True)
    is_incoming = serializers.ReadOnlyField()
    is_outgoing = serializers.ReadOnlyField()

    class Meta:
        model = StockMovement
        fields = [
            'id', 'product', 'product_name', 'batch', 'batch_number',
            'location', 'location_name', 'movement_type', 'quantity',
            'reference_number', 'reference_type', 'reference_id',
            'is_incoming', 'is_outgoing', 'notes',
            'created_by', 'created_by_name', 'created_at'
        ]


class StockMovementCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating stock movements"""

    class Meta:
        model = StockMovement
        fields = [
            'product', 'batch', 'location', 'movement_type', 'quantity',
            'reference_number', 'reference_type', 'reference_id', 'notes'
        ]

    def validate(self, data):
        """Custom validation for stock movements"""
        request = self.context.get('request')

        # Validate related objects belong to owner
        product = data.get('product')
        if product and product.owner != request.user:
            raise serializers.ValidationError('Product must belong to you.')

        batch = data.get('batch')
        if batch and batch.owner != request.user:
            raise serializers.ValidationError('Batch must belong to you.')

        location = data.get('location')
        if location and location.owner != request.user:
            raise serializers.ValidationError('Location must belong to you.')

        # Validate batch belongs to product
        if batch and product and batch.product != product:
            raise serializers.ValidationError('Batch must belong to the specified product.')

        # Validate batch is in the specified location
        if batch and location and batch.location != location:
            raise serializers.ValidationError('Batch must be in the specified location.')

        # Validate quantity for outgoing movements
        quantity = data.get('quantity', 0)
        if quantity < 0 and batch:
            if abs(quantity) > batch.quantity_available:
                raise serializers.ValidationError(
                    f'Cannot remove {abs(quantity)} items. Only {batch.quantity_available} available in batch.'
                )

        return data

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        validated_data['created_by'] = self.context['request'].user
        return super().create(validated_data)


class ProductImageSerializer(serializers.ModelSerializer):
    """Serializer for product images"""
    image_url = serializers.SerializerMethodField()
    thumbnail_url = serializers.SerializerMethodField()
    file_size_mb = serializers.SerializerMethodField()

    class Meta:
        model = ProductImage
        fields = [
            'id', 'product', 'image', 'thumbnail', 'image_url', 'thumbnail_url',
            'is_primary', 'alt_text', 'sort_order', 'image_size', 'file_size_mb',
            'image_width', 'image_height', 'upload_session_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'thumbnail', 'image_url', 'thumbnail_url', 'image_size',
                           'image_width', 'image_height', 'file_size_mb', 'created_at', 'updated_at']

    def get_image_url(self, obj):
        """Get the full URL for the image"""
        if obj.image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.image.url)
            return obj.image.url
        return None

    def get_thumbnail_url(self, obj):
        """Get the full URL for the thumbnail"""
        if obj.thumbnail:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.thumbnail.url)
            return obj.thumbnail.url
        return None

    def get_file_size_mb(self, obj):
        """Get file size in MB"""
        if obj.image_size:
            return round(obj.image_size / (1024 * 1024), 2)
        return None

    def validate(self, data):
        """Custom validation for product images"""
        # Ensure only one primary image per product
        if data.get('is_primary'):
            product = data.get('product')
            if product and self.instance:
                # If updating and setting as primary, allow it
                existing_primary = ProductImage.objects.filter(
                    product=product,
                    is_primary=True
                ).exclude(id=self.instance.id if self.instance else None)
            else:
                existing_primary = ProductImage.objects.filter(
                    product=product,
                    is_primary=True
                )

            if existing_primary.exists():
                # Will be handled in the service layer
                pass

        return data
