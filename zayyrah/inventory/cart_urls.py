"""
Cart-focused URL patterns for mobile apps
"""

from django.urls import path
from . import cart_views, checkout_status_views

app_name = 'cart_api'

urlpatterns = [
    # Cart management
    path('', cart_views.CartView.as_view(), name='cart'),

    # Cart items
    path('items/', cart_views.CartItemView.as_view(), name='cart-items'),
    path('items/update-price/', cart_views.CartItemView.as_view(), name='cart-items-update-price'),

    # Quick operations
    path('quick-add/', cart_views.quick_add_to_cart, name='quick-add'),
    path('add-by-price/', cart_views.add_to_cart_by_price, name='add-by-price'),

    # Checkout status and validation
    path('checkout/status/', checkout_status_views.CheckoutStatusView.as_view(), name='checkout-status'),
    path('checkout/validate/', checkout_status_views.CheckoutValidationView.as_view(), name='checkout-validate'),

    # Checkout
    path('checkout/preview/', cart_views.CheckoutPreviewView.as_view(), name='checkout-preview'),
    path('checkout/', cart_views.QuickCheckoutView.as_view(), name='quick-checkout'),
]