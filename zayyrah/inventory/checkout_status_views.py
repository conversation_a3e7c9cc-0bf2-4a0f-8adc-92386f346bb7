"""
Checkout status and readiness APIs for Flutter mobile app
"""

from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from .models import POSTransaction


class CheckoutStatusView(APIView):
    """Get checkout status and readiness for mobile apps"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get checkout status - works whether cart is empty or full"""
        print(f"\n=== CHECKOUT STATUS API ===")
        print(f"Method: GET")
        print(f"User: {request.user}")
        print(f"Query Params: {dict(request.GET)}")
        try:
            # Get active cart
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            # Base response structure
            response_data = {
                'success': True,
                'checkout_ready': False,
                'cart_exists': False,
                'items_count': 0,
                'total_amount': 0.00,
                'messages': []
            }

            if not cart:
                response_data.update({
                    'messages': ['No active cart found. Start adding items to begin checkout.'],
                    'next_action': 'add_items',
                    'suggested_actions': [
                        'Browse products',
                        'Scan barcode',
                        'Add items to cart'
                    ]
                })
                return Response(response_data)

            # Cart exists
            response_data['cart_exists'] = True
            response_data['items_count'] = cart.total_item_count
            response_data['total_amount'] = float(cart.total_amount)

            if not cart.items.exists():
                response_data.update({
                    'messages': ['Cart is empty. Add items to proceed with checkout.'],
                    'next_action': 'add_items',
                    'suggested_actions': [
                        'Browse products',
                        'Scan barcode',
                        'Add items to cart'
                    ]
                })
                return Response(response_data)

            # Cart has items - ready for checkout
            response_data.update({
                'checkout_ready': True,
                'next_action': 'checkout',
                'cart_summary': {
                    'transaction_id': cart.id,
                    'transaction_number': cart.transaction_number,
                    'items_count': cart.total_item_count,
                    'subtotal': float(cart.subtotal),
                    'discount_total': float(cart.discount_total),
                    'tax_total': float(cart.tax_total),
                    'total_amount': float(cart.total_amount),
                    'customer_name': cart.customer.display_name if cart.customer else None
                },
                'available_actions': [
                    'preview_checkout',
                    'complete_checkout',
                    'add_more_items',
                    'edit_cart'
                ]
            })

            # Check payment options
            payment_options = {
                'cash_available': True,
                'card_available': True,
                'credit_available': False,
                'minimum_payment': float(cart.total_amount)
            }

            if cart.customer and cart.customer.available_credit >= cart.total_amount:
                payment_options['credit_available'] = True

            response_data['payment_options'] = payment_options

            if cart.total_amount > 0:
                response_data['messages'] = [f'Cart ready for checkout. Total: {cart.total_amount:.2f}']
            else:
                response_data['messages'] = ['Cart has items but total is 0.00. Check item prices.']

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'error': f'Error checking checkout status: {str(e)}',
                'checkout_ready': False,
                'cart_exists': False
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckoutValidationView(APIView):
    """Validate checkout before processing"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Validate checkout request before final processing"""
        print(f"\n=== CHECKOUT VALIDATION API ===")
        print(f"Method: POST")
        print(f"User: {request.user}")
        print(f"Request Data: {request.data}")
        try:
            data = request.data

            # Get active cart
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            validation_result = {
                'success': True,
                'valid': True,
                'errors': [],
                'warnings': []
            }

            # Basic cart validation
            if not cart:
                validation_result.update({
                    'valid': False,
                    'errors': ['No active cart found']
                })
                return Response(validation_result, status=status.HTTP_400_BAD_REQUEST)

            if not cart.items.exists():
                validation_result.update({
                    'valid': False,
                    'errors': ['Cart is empty']
                })
                return Response(validation_result, status=status.HTTP_400_BAD_REQUEST)

            # Payment validation
            cash_amount = float(data.get('cash_amount', 0))
            loan_amount = float(data.get('loan_amount', 0))
            card_amount = float(data.get('card_amount', 0))
            total_payment = cash_amount + loan_amount + card_amount

            if total_payment < float(cart.total_amount):
                validation_result.update({
                    'valid': False,
                    'errors': [f'Insufficient payment. Required: {cart.total_amount:.2f}, Provided: {total_payment:.2f}']
                })

            # Loan validation
            if loan_amount > 0:
                if not cart.customer:
                    validation_result['errors'].append('Customer required for loan payment')

            # Warnings
            if total_payment > float(cart.total_amount):
                change_amount = total_payment - float(cart.total_amount)
                validation_result['warnings'].append(f'Change required: {change_amount:.2f}')

            if cash_amount > 1000:
                validation_result['warnings'].append('Large cash payment - please verify amount')

            # Update validity based on errors
            if validation_result['errors']:
                validation_result['valid'] = False

            return Response(validation_result)

        except Exception as e:
            return Response({
                'success': False,
                'valid': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)