"""
Product Image Service for handling image operations
"""
import os
import uuid
from decimal import Decimal
from typing import List, Dict, Optional, Tuple, Any
from PIL import Image
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage
from django.db import transaction
from django.utils import timezone

from .models import ProductImage, EnhancedProduct


class ProductImageService:
    """Service for handling product image operations"""

    THUMBNAIL_SIZE = (300, 300)
    MAX_IMAGE_SIZE = (1200, 1200)
    ALLOWED_FORMATS = ['JPEG', 'PNG', 'WEBP']
    MAX_FILE_SIZE = 5 * 1024 * 1024  # 5MB

    @classmethod
    def create_thumbnail(cls, image_file, size=None) -> ContentFile:
        """
        Create thumbnail from image file
        """
        if size is None:
            size = cls.THUMBNAIL_SIZE

        # Open the image
        with Image.open(image_file) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # Create thumbnail
            img.thumbnail(size, Image.Resampling.LANCZOS)

            # Save to bytes
            from io import BytesIO
            output = BytesIO()
            img.save(output, format='JPEG', quality=85, optimize=True)
            output.seek(0)

            return ContentFile(output.read())

    @classmethod
    def validate_image(cls, image_file):
        """
        Validate image file
        Returns: (is_valid, error_message)
        """
        try:
            # Check file size
            if hasattr(image_file, 'size') and image_file.size > cls.MAX_FILE_SIZE:
                return False, "Image file too large. Maximum size is 5MB."

            # Check if it's a valid image
            with Image.open(image_file) as img:
                # Check format
                if img.format not in cls.ALLOWED_FORMATS:
                    return False, f"Unsupported format. Allowed: {', '.join(cls.ALLOWED_FORMATS)}"

                # Check dimensions
                if img.width > cls.MAX_IMAGE_SIZE[0] or img.height > cls.MAX_IMAGE_SIZE[1]:
                    return False, f"Image too large. Maximum size: {cls.MAX_IMAGE_SIZE[0]}x{cls.MAX_IMAGE_SIZE[1]}"

                return True, ""

        except Exception as e:
            return False, f"Invalid image file: {str(e)}"

    @classmethod
    def optimize_image(cls, image_file) -> ContentFile:
        """
        Optimize image for web use
        """
        with Image.open(image_file) as img:
            # Convert to RGB if necessary
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')

            # Resize if too large
            if img.width > cls.MAX_IMAGE_SIZE[0] or img.height > cls.MAX_IMAGE_SIZE[1]:
                img.thumbnail(cls.MAX_IMAGE_SIZE, Image.Resampling.LANCZOS)

            # Save optimized version
            from io import BytesIO
            output = BytesIO()
            img.save(output, format='JPEG', quality=90, optimize=True)
            output.seek(0)

            return ContentFile(output.read())

    @classmethod
    @transaction.atomic
    def add_product_image(cls, product: EnhancedProduct, image_file,
                         alt_text: str = "", is_primary: bool = False,
                         upload_session_id: str = None) -> ProductImage:
        """
        Add a new image to a product
        """
        # Validate image
        is_valid, error_msg = cls.validate_image(image_file)
        if not is_valid:
            raise ValueError(error_msg)

        # If setting as primary, remove primary flag from other images
        if is_primary:
            ProductImage.objects.filter(product=product, is_primary=True).update(is_primary=False)

        # Optimize image
        optimized_image = cls.optimize_image(image_file)

        # Create thumbnail
        thumbnail = cls.create_thumbnail(image_file)

        # Get image metadata
        with Image.open(image_file) as img:
            width, height = img.size
            file_size = len(optimized_image.read())
            optimized_image.seek(0)

        # Create ProductImage instance
        product_image = ProductImage(
            product=product,
            alt_text=alt_text,
            is_primary=is_primary,
            upload_session_id=upload_session_id or str(uuid.uuid4()),
            image_width=width,
            image_height=height,
            image_size=file_size,
            sort_order=cls.get_next_sort_order(product)
        )

        # Generate unique filenames
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
        image_name = f"{product.id}_{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
        thumbnail_name = f"thumb_{image_name}"

        # Save files
        product_image.image.save(image_name, optimized_image, save=False)
        product_image.thumbnail.save(thumbnail_name, thumbnail, save=False)

        # Save the instance
        product_image.save()

        return product_image

    @classmethod
    def get_next_sort_order(cls, product: EnhancedProduct) -> int:
        """
        Get the next sort order for product images
        """
        last_image = ProductImage.objects.filter(product=product).order_by('-sort_order').first()
        return (last_image.sort_order + 1) if last_image else 0

    @classmethod
    def reorder_images(cls, product: EnhancedProduct, image_ids: List[int]) -> bool:
        """
        Reorder product images based on provided image IDs list
        """
        try:
            with transaction.atomic():
                for index, image_id in enumerate(image_ids):
                    ProductImage.objects.filter(
                        id=image_id,
                        product=product
                    ).update(sort_order=index)
            return True
        except Exception:
            return False

    @classmethod
    def set_primary_image(cls, product: EnhancedProduct, image_id: int) -> bool:
        """
        Set a specific image as primary for the product
        """
        try:
            with transaction.atomic():
                # Remove primary flag from all images
                ProductImage.objects.filter(product=product).update(is_primary=False)

                # Set the specified image as primary
                updated = ProductImage.objects.filter(
                    id=image_id,
                    product=product
                ).update(is_primary=True)

                return updated > 0
        except Exception:
            return False

    @classmethod
    def delete_image(cls, product: EnhancedProduct, image_id: int) -> bool:
        """
        Delete a product image and its files
        """
        try:
            image = ProductImage.objects.get(id=image_id, product=product)

            # Delete files from storage
            if image.image:
                default_storage.delete(image.image.name)
            if image.thumbnail:
                default_storage.delete(image.thumbnail.name)

            # Delete database record
            image.delete()

            return True
        except ProductImage.DoesNotExist:
            return False
        except Exception:
            return False

    @classmethod
    def get_product_images(cls, product: EnhancedProduct):
        """
        Get all images for a product with metadata
        """
        images = ProductImage.objects.filter(product=product).order_by('sort_order', 'id')

        return [{
            'id': img.id,
            'image_url': img.image.url if img.image else None,
            'thumbnail_url': img.thumbnail.url if img.thumbnail else None,
            'alt_text': img.alt_text,
            'is_primary': img.is_primary,
            'sort_order': img.sort_order,
            'width': img.image_width,
            'height': img.image_height,
            'file_size': img.image_size,
            'created_at': img.created_at,
        } for img in images]

    @classmethod
    def bulk_upload_images(cls, product: EnhancedProduct,
                          image_files: List, upload_session_id: str = None) -> List[ProductImage]:
        """
        Upload multiple images for a product
        """
        if not upload_session_id:
            upload_session_id = str(uuid.uuid4())

        uploaded_images = []
        errors = []

        for i, image_file in enumerate(image_files):
            try:
                # First image is primary if product has no images
                is_primary = (i == 0 and not ProductImage.objects.filter(product=product).exists())

                product_image = cls.add_product_image(
                    product=product,
                    image_file=image_file,
                    is_primary=is_primary,
                    upload_session_id=upload_session_id
                )
                uploaded_images.append(product_image)

            except Exception as e:
                errors.append(f"Image {i+1}: {str(e)}")

        return uploaded_images

    @classmethod
    def get_image_stats(cls, product: EnhancedProduct):
        """
        Get statistics about product images
        """
        images = ProductImage.objects.filter(product=product)

        total_size = sum(img.image_size or 0 for img in images)

        return {
            'total_images': images.count(),
            'total_size_bytes': total_size,
            'total_size_mb': round(total_size / (1024 * 1024), 2),
            'has_primary': images.filter(is_primary=True).exists(),
            'primary_image_id': images.filter(is_primary=True).first().id if images.filter(is_primary=True).exists() else None,
        }


# POS Business Logic Services

class POSService:
    """
    Main POS service class that orchestrates all POS operations
    """

    def __init__(self, user, location = None):
        from django.contrib.auth import get_user_model
        User = get_user_model()

        self.user = user
        self.location = location or self._get_default_location()

    def _get_default_location(self):
        """Get or create default location for user"""
        from .models import Location
        location, created = Location.objects.get_or_create(
            owner=self.user,
            code='MAIN',
            defaults={
                'name': 'Main Store',
                'location_type': 'store'
            }
        )
        return location

    def get_pos_products(self, category_id = None, search: str = ''):
        """
        Get products available for POS with stock validation
        """
        from django.db import models

        queryset = EnhancedProduct.objects.filter(
            owner=self.user,
            is_active=True,
            pos_enabled=True
        ).select_related('category', 'brand', 'supplier')

        # Filter by category if specified
        if category_id:
            queryset = queryset.filter(category_id=category_id)

        # Search functionality
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(pos_display_name__icontains=search) |
                models.Q(sku__icontains=search) |
                models.Q(barcode__icontains=search)
            )

        # Order by POS sort order, then name
        return queryset.order_by('pos_sort_order', 'name')

    def validate_product_availability(self, product, quantity: Decimal):
        """
        Validate if product has sufficient stock for sale
        """
        if not product.pos_enabled:
            return False, f"Product '{product.name}' is not available for POS sales"

        if not product.is_active:
            return False, f"Product '{product.name}' is inactive"

        current_stock = product.current_stock
        if current_stock < quantity:
            return False, f"Insufficient stock. Available: {current_stock} {product.unit_label}, Requested: {quantity}"

        return True, "Product available"

    def validate_customer_credit(self, customer, amount: Decimal):
        """
        Validate customer credit limit for the transaction
        """
        if not customer:
            return True, "No credit validation needed"

        if customer.credit_limit <= 0:
            return True, "No credit limit set"

        if not customer.can_purchase(amount):
            return False, f"Credit limit exceeded. Available credit: Rs. {customer.available_credit}"

        return True, "Credit limit OK"

    def _get_or_create_walk_in_customer(self):
        """
        Get or create a walk-in customer record for the user
        """
        from customers.models import Customer
        from django.utils import timezone

        # Try to find existing walk-in customer
        walk_in_customer = Customer.objects.filter(
            owner=self.user,
            name__icontains='Walk-in',
            mobile_number__isnull=True
        ).first()

        if not walk_in_customer:
            # Create a new walk-in customer
            current_date = timezone.now().strftime('%Y-%m-%d')
            walk_in_customer = Customer.objects.create(
                owner=self.user,
                name=f'Walk-in Customer ({current_date})',
                mobile_number=None,
                email='',
                notes='Auto-created for walk-in POS transactions',
                opening_balance=0.00,
                credit_limit=0.00
            )

        return walk_in_customer

    @transaction.atomic
    def create_transaction(self, customer=None, notes: str = '', create_walk_in=True):
        """
        Create a new POS transaction
        If no customer is provided and create_walk_in is True, creates a walk-in customer record
        """
        from .models import POSTransaction

        # If no customer provided, create a walk-in customer record
        if customer is None and create_walk_in:
            customer = self._get_or_create_walk_in_customer()

        pos_transaction = POSTransaction.objects.create(
            owner=self.user,
            customer=customer,
            location=self.location,
            notes=notes,
            cashier=self.user
        )
        return pos_transaction

    @transaction.atomic
    def add_item_to_transaction(
        self,
        transaction,
        product,
        quantity: Decimal,
        custom_price = None,
        discount_percent: Decimal = Decimal('0.00'),
        discount_amount: Decimal = Decimal('0.00')
    ):
        """
        Add an item to a POS transaction with validation
        """
        from .models import POSTransactionItem

        try:
            # Validate transaction status
            if transaction.status != 'active':
                return False, f"Cannot add items to {transaction.status} transaction", None

            # Validate product availability
            is_available, message = self.validate_product_availability(product, quantity)
            if not is_available:
                return False, message, None

            # Validate discount permissions
            if discount_percent > 0 and not product.allow_discount:
                return False, f"Discounts not allowed for {product.name}", None

            if discount_percent > product.max_discount_percent:
                return False, f"Discount exceeds maximum allowed ({product.max_discount_percent}%)", None

            # Determine unit price
            unit_price = custom_price if custom_price else product.pos_price

            # Check if item already exists in transaction
            existing_item = transaction.items.filter(product=product).first()

            if existing_item:
                # Update existing item
                new_quantity = existing_item.quantity + quantity

                # Validate total quantity
                is_available, message = self.validate_product_availability(product, new_quantity)
                if not is_available:
                    return False, message, None

                existing_item.quantity = new_quantity
                existing_item.discount_percent = discount_percent
                existing_item.discount_amount = discount_amount
                existing_item.save()

                item = existing_item
            else:
                # Create new item
                item = POSTransactionItem.objects.create(
                    transaction=transaction,
                    product=product,
                    quantity=quantity,
                    unit_price=unit_price,
                    original_price=product.selling_price,
                    discount_percent=discount_percent,
                    discount_amount=discount_amount,
                    tax_rate=product.tax_rate
                )

            # Recalculate transaction totals
            transaction.calculate_totals()
            transaction.save()

            return True, f"Added {quantity} {product.unit_label} of {product.name}", item

        except Exception as e:
            return False, f"Error adding item: {str(e)}", None

    @transaction.atomic
    def add_item_to_transaction_by_price(self, transaction, product, price_amount, discount_percent=0):
        """
        Add item to transaction by specifying total price amount
        Quantity is auto-calculated based on unit price

        Args:
            transaction: POSTransaction instance
            product: EnhancedProduct instance
            price_amount: Decimal - Total price amount to add
            discount_percent: Decimal - Discount percentage (0-100)

        Returns:
            tuple: (success: bool, message: str, item: POSTransactionItem or None)
        """
        try:
            from .models import POSTransactionItem
            if transaction.status != 'active':
                return False, f"Cannot add items to {transaction.status} transaction", None

            # Validate inputs
            if price_amount <= 0:
                return False, "Price amount must be greater than 0", None

            # Calculate quantity based on price amount and unit price
            unit_price = product.selling_price
            if unit_price <= 0:
                return False, f"Product {product.name} has invalid selling price", None

            # Calculate quantity: price_amount / unit_price
            calculated_quantity = price_amount / unit_price

            # Apply discount if specified
            discount_amount = Decimal('0')
            if discount_percent > 0:
                discount_amount = (price_amount * discount_percent) / Decimal('100')
                # Adjust quantity based on discounted amount
                effective_amount = price_amount - discount_amount
                calculated_quantity = effective_amount / unit_price

            # Check if product allows fractional quantities
            if not product.allow_fractional_quantities:
                # For whole-unit products, round to nearest quantity for better UX
                # This ensures customer gets closest value to their target price
                original_quantity = calculated_quantity
                calculated_quantity = round(float(calculated_quantity))

                if calculated_quantity == 0:
                    return False, f"Price amount ${price_amount} is too low for whole unit (min: ${unit_price})", None

                # Inform about the rounding
                actual_price = calculated_quantity * unit_price
                print(f"  → Rounded from {original_quantity} to {calculated_quantity} units")
                print(f"  → Actual price will be ${actual_price} (target was ${price_amount})")
            else:
                # For fractional quantities, limit to 3 decimal places maximum (more practical)
                calculated_quantity = calculated_quantity.quantize(Decimal('0.001'))

            print(f"\n=== ADD BY PRICE CALCULATION ===")
            print(f"Product: {product.name}")
            print(f"Unit Price: ${unit_price}")
            print(f"Target Price Amount: ${price_amount}")
            print(f"Discount: {discount_percent}%")
            print(f"Calculated Quantity: {calculated_quantity}")
            print(f"Allows Fractional: {product.allow_fractional_quantities}")

            # Check for existing item with same product
            existing_item = transaction.items.filter(product=product).first()

            if existing_item:
                # Add to existing quantity
                new_quantity = existing_item.quantity + calculated_quantity
                existing_item.quantity = new_quantity
                # Keep the same unit price and discount
                existing_item.save()
                item = existing_item
                action_message = f"Added ${price_amount} worth ({calculated_quantity} {product.unit_label}) to existing item"
            else:
                # Create new item
                item = POSTransactionItem.objects.create(
                    transaction=transaction,
                    product=product,
                    quantity=calculated_quantity,
                    unit_price=unit_price,
                    original_price=product.selling_price,
                    discount_percent=discount_percent,
                    discount_amount=discount_amount,
                    tax_rate=product.tax_rate
                )
                action_message = f"Added ${price_amount} worth ({calculated_quantity} {product.unit_label}) of {product.name}"

            # Recalculate transaction totals
            transaction.calculate_totals()
            transaction.save()

            return True, action_message, item

        except Exception as e:
            return False, f"Error adding item by price: {str(e)}", None

    @transaction.atomic
    def remove_item_from_transaction(self, transaction, item):
        """
        Remove an item from a POS transaction
        """
        try:
            if transaction.status != 'active':
                return False, f"Cannot remove items from {transaction.status} transaction"

            product_name = item.product.name
            quantity = item.quantity

            item.delete()

            # Recalculate transaction totals
            transaction.calculate_totals()
            transaction.save()

            return True, f"Removed {quantity} {item.product.unit_label} of {product_name}"

        except Exception as e:
            return False, f"Error removing item: {str(e)}"

    @transaction.atomic
    def update_item_quantity(self, transaction, item, new_quantity: Decimal):
        """
        Update quantity of an item in transaction
        """
        try:
            if transaction.status != 'active':
                return False, f"Cannot update items in {transaction.status} transaction"

            if new_quantity <= 0:
                return self.remove_item_from_transaction(transaction, item)

            # Validate new quantity availability
            is_available, message = self.validate_product_availability(item.product, new_quantity)
            if not is_available:
                return False, message

            old_quantity = item.quantity
            item.quantity = new_quantity
            item.save()

            # Recalculate transaction totals
            transaction.calculate_totals()
            transaction.save()

            return True, f"Updated quantity from {old_quantity} to {new_quantity}"

        except Exception as e:
            return False, f"Error updating quantity: {str(e)}"

    def update_item_price(self, transaction, item, new_price: Decimal):
        """
        Update unit price of an item in transaction
        """
        try:
            if transaction.status != 'active':
                return False, f"Cannot update items in {transaction.status} transaction"

            if new_price < 0:
                return False, "Price cannot be negative"

            # Validate minimum selling price if set
            if item.product.minimum_selling_price and new_price < item.product.minimum_selling_price:
                return False, f"Price cannot be below minimum selling price: {item.product.minimum_selling_price}"

            old_price = item.unit_price
            item.unit_price = new_price

            # If this is a custom price, mark it
            if new_price != item.product.selling_price:
                item.original_price = item.product.selling_price
            else:
                item.original_price = new_price

            item.save()

            # Recalculate transaction totals
            transaction.calculate_totals()
            transaction.save()

            return True, f"Updated price from {old_price} to {new_price}"

        except Exception as e:
            return False, f"Error updating price: {str(e)}"

    def calculate_payment_breakdown(
        self,
        total_amount: Decimal,
        cash_amount: Decimal = Decimal('0.00'),
        loan_amount: Decimal = Decimal('0.00'),
        card_amount: Decimal = Decimal('0.00'),
        payment_method: str = None
    ):
        """
        Calculate payment breakdown and change
        """
        total_paid = cash_amount + loan_amount + card_amount
        change_amount = max(Decimal('0.00'), total_paid - total_amount)
        remaining_balance = max(Decimal('0.00'), total_amount - total_paid)

        # Use provided payment method or auto-detect
        if payment_method:
            final_payment_method = payment_method
        else:
            # Auto-detect payment method
            payment_methods_used = sum([
                1 if cash_amount > 0 else 0,
                1 if loan_amount > 0 else 0,
                1 if card_amount > 0 else 0
            ])

            if payment_methods_used > 1:
                final_payment_method = 'mixed'
            elif cash_amount > 0:
                final_payment_method = 'cash'
            elif loan_amount > 0:
                final_payment_method = 'loan'
            elif card_amount > 0:
                final_payment_method = 'card'
            else:
                final_payment_method = 'cash'

        return {
            'payment_method': final_payment_method,
            'total_paid': total_paid,
            'change_amount': change_amount,
            'remaining_balance': remaining_balance,
            'cash_amount': cash_amount,
            'loan_amount': loan_amount,
            'card_amount': card_amount
        }

    @transaction.atomic
    def process_payment(
        self,
        transaction,
        cash_amount: Decimal = Decimal('0.00'),
        loan_amount: Decimal = Decimal('0.00'),
        card_amount: Decimal = Decimal('0.00'),
        loan_due_date=None,
        loan_interest_rate: Decimal = Decimal('0.00'),
        payment_method: str = None
    ):
        """
        Process payment for a transaction
        """
        try:
            if transaction.status != 'active':
                return False, f"Cannot process payment for {transaction.status} transaction", {}

            # Validate customer loan capability if using loan payment
            if loan_amount > 0:
                if not transaction.customer:
                    return False, "Customer required for loan payments", {}

                is_valid, message = self.validate_customer_credit(transaction.customer, loan_amount)
                if not is_valid:
                    return False, message, {}

            # Calculate payment breakdown
            payment_info = self.calculate_payment_breakdown(
                transaction.total_amount, cash_amount, loan_amount, card_amount, payment_method
            )

            # Update transaction with payment information
            transaction.payment_method = payment_info['payment_method']
            transaction.amount_paid = payment_info['total_paid']
            transaction.cash_amount = cash_amount
            transaction.loan_amount = loan_amount
            transaction.card_amount = card_amount
            transaction.change_amount = payment_info['change_amount']

            # Handle loan-specific fields
            if loan_amount > 0:
                if loan_due_date:
                    from datetime import datetime
                    if isinstance(loan_due_date, str):
                        transaction.loan_due_date = datetime.strptime(loan_due_date, '%Y-%m-%d').date()
                    else:
                        transaction.loan_due_date = loan_due_date
                else:
                    # Default to 30 days from now
                    from datetime import date, timedelta
                    transaction.loan_due_date = date.today() + timedelta(days=30)

                transaction.loan_interest_rate = loan_interest_rate
                transaction.loan_status = 'active'

                # Create CustomerLoan record for better tracking
                self.create_customer_loan(transaction, loan_amount, loan_interest_rate, transaction.loan_due_date)

            # Update payment status
            if payment_info['remaining_balance'] <= 0:
                transaction.payment_status = 'paid'
            else:
                transaction.payment_status = 'partial'

            transaction.save()

            return True, "Payment processed successfully", payment_info

        except Exception as e:
            return False, f"Error processing payment: {str(e)}", {}

    def create_customer_loan(self, transaction, loan_amount, interest_rate, due_date):
        """Create a CustomerLoan record for tracking"""
        try:
            from .models import CustomerLoan

            loan = CustomerLoan.objects.create(
                owner=transaction.owner,
                customer=transaction.customer,
                transaction=transaction,
                loan_amount=loan_amount,
                interest_rate=interest_rate,
                due_date=due_date,
                notes=f"Loan from transaction {transaction.transaction_number}"
            )
            return loan
        except Exception as e:
            # Don't fail the transaction if loan creation fails
            print(f"Warning: Could not create CustomerLoan record: {e}")
            return None

    @transaction.atomic
    def complete_transaction(self, transaction):
        """
        Complete a POS transaction and process stock deduction
        """
        try:
            if transaction.status != 'active':
                return False, f"Cannot complete {transaction.status} transaction", {}

            if transaction.payment_status not in ['paid', 'partial']:
                return False, "Payment must be processed before completing transaction", {}

            # Validate all items have sufficient stock
            stock_validation_errors = []
            for item in transaction.items.all():
                is_available, message = self.validate_product_availability(item.product, item.quantity)
                if not is_available:
                    stock_validation_errors.append(f"{item.product.name}: {message}")

            if stock_validation_errors:
                return False, "Stock validation failed: " + "; ".join(stock_validation_errors), {}

            # Process stock deduction for each item
            stock_movements = []
            for item in transaction.items.all():
                try:
                    success = item.process_stock_deduction()
                    if success:
                        stock_movements.extend(item.stock_movements)
                    else:
                        raise ValueError(f"Failed to process stock deduction for {item.product.name}")
                except Exception as e:
                    return False, f"Stock deduction error for {item.product.name}: {str(e)}", {}

            # Complete the transaction
            transaction.status = 'completed'
            transaction.completed_at = timezone.now()
            transaction.save()

            # Prepare result summary
            result = {
                'transaction_number': transaction.transaction_number,
                'total_amount': transaction.total_amount,
                'payment_method': transaction.payment_method,
                'change_amount': transaction.change_amount,
                'items_count': transaction.total_item_count,
                'total_items': transaction.total_items,
                'stock_movements': stock_movements,
                'completed_at': transaction.completed_at
            }

            return True, f"Transaction {transaction.transaction_number} completed successfully", result

        except Exception as e:
            return False, f"Error completing transaction: {str(e)}", {}

    @transaction.atomic
    def cancel_transaction(self, transaction, reason: str = ''):
        """
        Cancel a POS transaction
        """
        try:
            if transaction.status == 'completed':
                return False, "Cannot cancel completed transaction"

            transaction.cancel_transaction(reason)
            return True, f"Transaction {transaction.transaction_number} cancelled"

        except Exception as e:
            return False, f"Error cancelling transaction: {str(e)}"


class POSReportingService:
    """
    Service for POS reporting and analytics
    """

    def __init__(self, user):
        self.user = user

    def get_daily_sales_summary(self, date=None):
        """
        Get daily sales summary
        """
        from django.db import models
        from .models import POSTransaction

        if not date:
            date = timezone.localdate()

        transactions = POSTransaction.objects.filter(
            owner=self.user,
            status='completed',
            completed_at__date=date
        )

        total_sales = transactions.aggregate(
            total_amount=models.Sum('total_amount'),
            total_transactions=models.Count('id')
        )

        payment_breakdown = transactions.aggregate(
            cash_total=models.Sum('cash_amount'),
            loan_total=models.Sum('loan_amount'),
            card_total=models.Sum('card_amount')
        )

        return {
            'date': date,
            'total_sales': total_sales['total_amount'] or Decimal('0.00'),
            'total_transactions': total_sales['total_transactions'] or 0,
            'cash_sales': payment_breakdown['cash_total'] or Decimal('0.00'),
            'loan_sales': payment_breakdown['loan_total'] or Decimal('0.00'),
            'card_sales': payment_breakdown['card_total'] or Decimal('0.00'),
        }

    def get_product_sales_report(self, start_date, end_date):
        """
        Get product sales report for a date range
        """
        from django.db import models
        from .models import POSTransactionItem

        items = POSTransactionItem.objects.filter(
            transaction__owner=self.user,
            transaction__status='completed',
            transaction__completed_at__date__range=[start_date, end_date]
        ).select_related('product').values(
            'product__name',
            'product__sku'
        ).annotate(
            total_quantity=models.Sum('quantity'),
            total_revenue=models.Sum('line_total'),
            transaction_count=models.Count('transaction', distinct=True)
        ).order_by('-total_revenue')

        return list(items)

    def get_low_stock_alerts(self, threshold: int = 10):
        """
        Get products with low stock levels
        """
        from django.db import models

        products = EnhancedProduct.objects.filter(
            owner=self.user,
            is_active=True,
            pos_enabled=True
        ).annotate(
            current_stock_calc=models.Sum('stock_batches__quantity_available')
        ).filter(
            models.Q(current_stock_calc__lte=threshold) |
            models.Q(current_stock_calc__isnull=True)
        ).values(
            'id', 'name', 'sku', 'current_stock_calc', 'reorder_level'
        )

        return list(products)


class POSValidationService:
    """
    Service for POS validation rules and business logic
    """

    @staticmethod
    def validate_transaction_item(
        product,
        quantity: Decimal,
        discount_percent: Decimal = Decimal('0.00')
    ):
        """
        Validate a transaction item before adding to cart
        """
        errors = []

        # Product validation
        if not product.is_active:
            errors.append(f"Product '{product.name}' is inactive")

        if not product.pos_enabled:
            errors.append(f"Product '{product.name}' is not available for POS sales")

        # Quantity validation
        if quantity <= 0:
            errors.append("Quantity must be greater than 0")

        if product.current_stock < quantity:
            errors.append(f"Insufficient stock. Available: {product.current_stock}")

        # Discount validation
        if discount_percent > 0:
            if not product.allow_discount:
                errors.append(f"Discounts not allowed for '{product.name}'")
            elif discount_percent > product.max_discount_percent:
                errors.append(f"Discount exceeds maximum allowed ({product.max_discount_percent}%)")

        return len(errors) == 0, errors

    @staticmethod
    def validate_payment_amounts(
        total_amount: Decimal,
        cash_amount: Decimal = Decimal('0.00'),
        loan_amount: Decimal = Decimal('0.00'),
        card_amount: Decimal = Decimal('0.00')
    ):
        """
        Validate payment amounts
        """
        errors = []

        if total_amount <= 0:
            errors.append("Total amount must be greater than 0")

        total_paid = cash_amount + loan_amount + card_amount

        if total_paid <= 0:
            errors.append("At least one payment amount must be greater than 0")

        if cash_amount < 0 or loan_amount < 0 or card_amount < 0:
            errors.append("Payment amounts cannot be negative")

        return len(errors) == 0, errors