from django import forms
from .models import EnhancedProduct, Category, ProductBrand, Supplier


class EnhancedProductForm(forms.ModelForm):
    """Form for creating and updating EnhancedProduct"""

    class Meta:
        model = EnhancedProduct
        fields = [
            'name', 'description', 'category', 'brand', 'supplier',
            'sku', 'barcode', 'purchase_price', 'selling_price',
            'wholesale_price', 'minimum_selling_price', 'track_stock',
            'allow_negative_stock', 'reorder_level', 'maximum_stock_level',
            'unit', 'unit_custom_label', 'allow_fractional_quantities',
            'weight', 'dimensions', 'tax_rate', 'tax_exempt',
            'is_active', 'is_featured', 'tags', 'has_variants',
            'variant_type', 'track_expiry', 'perishable', 'shelf_life_days'
        ]

        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter product name',
                'required': True
            }),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 3,
                'placeholder': 'Product description'
            }),
            'category': forms.Select(attrs={'class': 'form-control'}),
            'brand': forms.Select(attrs={'class': 'form-control'}),
            'supplier': forms.Select(attrs={'class': 'form-control'}),
            'sku': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'SKU (Stock Keeping Unit)'
            }),
            'barcode': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Barcode'
            }),
            'purchase_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'selling_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'required': True
            }),
            'wholesale_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'minimum_selling_price': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0'
            }),
            'reorder_level': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'maximum_stock_level': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0'
            }),
            'unit': forms.Select(attrs={'class': 'form-control'}),
            'unit_custom_label': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Custom unit label'
            }),
            'weight': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.001',
                'min': '0'
            }),
            'dimensions': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'L×W×H (e.g. 10×5×3 cm)'
            }),
            'tax_rate': forms.NumberInput(attrs={
                'class': 'form-control',
                'step': '0.01',
                'min': '0',
                'max': '100'
            }),
            'tags': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Comma-separated tags'
            }),
            'variant_type': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'e.g. color, size, flavor'
            }),
            'shelf_life_days': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1'
            }),
            'track_stock': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'allow_negative_stock': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'allow_fractional_quantities': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'tax_exempt': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_featured': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'has_variants': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'track_expiry': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'perishable': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Store user for use in clean methods
        self._user = user

        if user:
            # Filter choices to user's own data
            self.fields['category'].queryset = Category.objects.filter(
                owner=user, is_active=True
            ).order_by('name')
            self.fields['brand'].queryset = ProductBrand.objects.filter(
                owner=user, is_active=True
            ).order_by('name')
            self.fields['supplier'].queryset = Supplier.objects.filter(
                owner=user, is_active=True
            ).order_by('name')

        # Make empty choices more user-friendly
        self.fields['category'].empty_label = "Select a category"
        self.fields['brand'].empty_label = "Select a brand (optional)"
        self.fields['supplier'].empty_label = "Select a supplier (optional)"

    def clean_selling_price(self):
        selling_price = self.cleaned_data.get('selling_price')
        if selling_price is None or selling_price <= 0:
            raise forms.ValidationError("Selling price must be greater than 0")
        return selling_price

    def clean_sku(self):
        sku = self.cleaned_data.get('sku')
        if sku:
            # Check for duplicate SKU for the same user
            user = getattr(self, '_user', None)
            if user:
                existing = EnhancedProduct.objects.filter(
                    owner=user, sku=sku
                ).exclude(pk=self.instance.pk if self.instance else None)
                if existing.exists():
                    raise forms.ValidationError("A product with this SKU already exists")
        return sku

    def clean_barcode(self):
        barcode = self.cleaned_data.get('barcode')
        if barcode and barcode.strip():
            # Check for duplicate barcode for the same user
            user = getattr(self, '_user', None)
            if user:
                existing = EnhancedProduct.objects.filter(
                    owner=user, barcode=barcode.strip()
                ).exclude(pk=self.instance.pk if self.instance else None)
                if existing.exists():
                    raise forms.ValidationError("A product with this barcode already exists")
            return barcode.strip()
        # Return None for empty/whitespace-only barcodes to avoid unique constraint issues
        return None