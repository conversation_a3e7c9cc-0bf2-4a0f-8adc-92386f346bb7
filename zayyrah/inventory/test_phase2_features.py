"""
Comprehensive test suite for Phase 2 advanced inventory features
"""
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal
from unittest.mock import patch
from django.core.cache import cache
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import Category, Supplier, Location, ProductBrand, EnhancedProduct, StockBatch, StockMovement
from .cache_utils import InventoryCache

User = get_user_model()


class CategoryTreeAdvancedTestCase(TestCase):
    """Test advanced category tree operations"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )

    def test_category_ancestors_and_descendants(self):
        """Test category tree traversal methods"""
        # Create hierarchy: Electronics > Mobile > Smartphones
        electronics = Category.objects.create(owner=self.user, name='Electronics')
        mobile = Category.objects.create(owner=self.user, name='Mobile', parent=electronics)
        smartphones = Category.objects.create(owner=self.user, name='Smartphones', parent=mobile)

        # Test ancestors
        ancestors = smartphones.get_ancestors()
        self.assertEqual(len(ancestors), 2)
        self.assertIn(mobile, ancestors)
        self.assertIn(electronics, ancestors)

        # Test root
        self.assertEqual(smartphones.get_root(), electronics)

        # Test descendants
        descendants = electronics.get_all_descendants()
        self.assertEqual(len(descendants), 2)
        self.assertIn(mobile, descendants)
        self.assertIn(smartphones, descendants)

        # Test siblings
        tablets = Category.objects.create(owner=self.user, name='Tablets', parent=mobile)
        siblings = smartphones.get_siblings()
        self.assertIn(tablets, siblings)
        self.assertNotIn(smartphones, siblings)

    def test_category_move_validation_and_execution(self):
        """Test category move validation and execution"""
        parent = Category.objects.create(owner=self.user, name='Parent')
        child = Category.objects.create(owner=self.user, name='Child', parent=parent)
        grandchild = Category.objects.create(owner=self.user, name='Grandchild', parent=child)

        # Test circular reference prevention
        self.assertFalse(parent.can_move_to(child))
        self.assertFalse(parent.can_move_to(grandchild))

        # Test self-move prevention
        self.assertFalse(child.can_move_to(child))

        # Test valid move
        new_parent = Category.objects.create(owner=self.user, name='NewParent')
        self.assertTrue(child.can_move_to(new_parent))

        # Execute move
        old_level = child.level
        child.move_to(new_parent)
        self.assertEqual(child.parent, new_parent)
        self.assertEqual(child.level, new_parent.level + 1)

        # Check descendants level update
        grandchild.refresh_from_db()
        self.assertEqual(grandchild.level, child.level + 1)

    def test_bulk_category_operations(self):
        """Test bulk category move operations"""
        parent1 = Category.objects.create(owner=self.user, name='Parent1')
        parent2 = Category.objects.create(owner=self.user, name='Parent2')
        child1 = Category.objects.create(owner=self.user, name='Child1', parent=parent1)
        child2 = Category.objects.create(owner=self.user, name='Child2', parent=parent1)

        operations = [
            {'category_id': child1.id, 'new_parent_id': parent2.id},
            {'category_id': child2.id, 'new_parent_id': parent2.id}
        ]

        result = Category.bulk_move(self.user, operations)

        self.assertEqual(len(result['successful_moves']), 2)
        self.assertEqual(len(result['errors']), 0)

        # Verify moves
        child1.refresh_from_db()
        child2.refresh_from_db()
        self.assertEqual(child1.parent, parent2)
        self.assertEqual(child2.parent, parent2)


class AdvancedStockTrackingTestCase(TestCase):
    """Test advanced stock tracking features"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        self.product = self._create_test_product()
        self.location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')
        self.location2 = Location.objects.create(owner=self.user, name='Store 2', code='STOR2')

    def _create_test_product(self):
        category = Category.objects.create(owner=self.user, name='Electronics')
        supplier = Supplier.objects.create(owner=self.user, name='Test Supplier')
        return EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            supplier=supplier,
            purchase_price=Decimal('100.00'),
            selling_price=Decimal('150.00'),
            reorder_level=10
        )

    def test_fifo_lifo_allocation(self):
        """Test FIFO and LIFO stock allocation algorithms"""
        # Create batches with different dates
        batch1 = StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH001',
            quantity_received=50,
            quantity_available=50,
            purchase_price=Decimal('100.00'),
            received_date=timezone.now().date() - timedelta(days=2)
        )

        batch2 = StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH002',
            quantity_received=30,
            quantity_available=30,
            purchase_price=Decimal('110.00'),
            received_date=timezone.now().date() - timedelta(days=1)
        )

        # Test FIFO allocation
        allocations, remaining = self.product.allocate_stock(60, self.location, 'FIFO')
        self.assertEqual(remaining, 0)
        self.assertEqual(len(allocations), 2)
        self.assertEqual(allocations[0]['batch'], batch1)  # Older batch first
        self.assertEqual(allocations[0]['quantity'], 50)

        # Test LIFO allocation
        allocations, remaining = self.product.allocate_stock(60, self.location, 'LIFO')
        self.assertEqual(remaining, 0)
        self.assertEqual(len(allocations), 2)
        self.assertEqual(allocations[0]['batch'], batch2)  # Newer batch first
        self.assertEqual(allocations[0]['quantity'], 30)

    def test_stock_reservation_system(self):
        """Test stock reservation and release"""
        batch = StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('100.00')
        )

        # Reserve 30 units
        success, reservations = self.product.reserve_stock(30, self.location)
        self.assertTrue(success)

        batch.refresh_from_db()
        self.assertEqual(batch.quantity_available, 70)
        self.assertEqual(batch.quantity_reserved, 30)

        # Try to reserve more than available
        success, message = self.product.reserve_stock(80, self.location)
        self.assertFalse(success)
        self.assertIn('insufficient', message.lower())

        # Release reservation
        self.product.release_stock_reservation(reservations)
        batch.refresh_from_db()
        self.assertEqual(batch.quantity_available, 100)
        self.assertEqual(batch.quantity_reserved, 0)

    def test_stock_transfer_between_locations(self):
        """Test stock transfer functionality"""
        batch = StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('100.00')
        )

        # Transfer 30 units
        success, message, movements = self.product.transfer_stock(30, self.location, self.location2)
        self.assertTrue(success)
        self.assertEqual(len(movements), 2)  # One out, one in

        # Check stock levels by location
        location_stock = self.product.get_stock_by_location()
        self.assertEqual(location_stock[self.location.name]['quantity'], 70)
        self.assertEqual(location_stock[self.location2.name]['quantity'], 30)

    def test_stock_valuation_methods(self):
        """Test different stock valuation methods"""
        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH001',
            quantity_received=50,
            quantity_available=50,
            purchase_price=Decimal('100.00'),
            received_date=timezone.now().date() - timedelta(days=2)
        )

        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH002',
            quantity_received=30,
            quantity_available=30,
            purchase_price=Decimal('120.00'),
            received_date=timezone.now().date() - timedelta(days=1)
        )

        # Test FIFO valuation
        fifo_valuation = self.product.get_stock_valuation('FIFO')
        self.assertEqual(fifo_valuation['total_quantity'], 80)
        self.assertEqual(fifo_valuation['total_value'], Decimal('8600.00'))  # 50*100 + 30*120

        # Test weighted average valuation
        avg_valuation = self.product.get_stock_valuation('WEIGHTED_AVERAGE')
        self.assertEqual(avg_valuation['total_quantity'], 80)
        expected_avg_price = Decimal('8600.00') / 80
        self.assertEqual(avg_valuation['average_price'], expected_avg_price)


class SupplierPerformanceAdvancedTestCase(TestCase):
    """Test advanced supplier performance tracking"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        self.supplier = Supplier.objects.create(
            owner=self.user,
            name='Test Supplier',
            rating=4
        )
        self.product = self._create_test_product()
        self.location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')

    def _create_test_product(self):
        category = Category.objects.create(owner=self.user, name='Electronics')
        return EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            supplier=self.supplier,
            purchase_price=Decimal('100.00'),
            selling_price=Decimal('150.00')
        )

    def test_supplier_performance_metrics_calculation(self):
        """Test comprehensive supplier performance metrics"""
        # Create stock batches with some damage
        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            supplier=self.supplier,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=90,
            quantity_damaged=10,  # 10% damage rate
            purchase_price=Decimal('100.00')
        )

        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            supplier=self.supplier,
            batch_number='BATCH002',
            quantity_received=50,
            quantity_available=50,
            quantity_damaged=0,
            purchase_price=Decimal('110.00')
        )

        metrics = self.supplier.get_performance_metrics()

        self.assertEqual(metrics['total_orders'], 2)
        self.assertEqual(metrics['total_products'], 1)
        self.assertEqual(metrics['total_value'], Decimal('15500.00'))  # 100*100 + 50*110
        self.assertLess(metrics['quality_score'], 100)  # Due to damaged items

    def test_supplier_product_catalog_generation(self):
        """Test supplier product catalog with pricing history"""
        # Create multiple batches with different prices
        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            supplier=self.supplier,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('100.00'),
            received_date=timezone.now().date() - timedelta(days=10)
        )

        StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            supplier=self.supplier,
            batch_number='BATCH002',
            quantity_received=50,
            quantity_available=50,
            purchase_price=Decimal('110.00'),
            received_date=timezone.now().date() - timedelta(days=5)
        )

        catalog = self.supplier.get_product_catalog()

        self.assertEqual(len(catalog), 1)
        product_data = catalog[0]
        self.assertEqual(product_data['product_id'], self.product.id)
        self.assertEqual(product_data['price_stats']['total_batches'], 2)
        self.assertEqual(product_data['price_stats']['min_price'], Decimal('100.00'))
        self.assertEqual(product_data['price_stats']['max_price'], Decimal('110.00'))


class StockMovementAuditTestCase(TestCase):
    """Test stock movement logging and audit trail"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        self.product = self._create_test_product()
        self.location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')
        self.batch = self._create_test_batch()

    def _create_test_product(self):
        category = Category.objects.create(owner=self.user, name='Electronics')
        return EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            purchase_price=Decimal('100.00'),
            selling_price=Decimal('150.00')
        )

    def _create_test_batch(self):
        return StockBatch.objects.create(
            owner=self.user,
            product=self.product,
            location=self.location,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('100.00')
        )

    def test_comprehensive_movement_logging(self):
        """Test comprehensive stock movement logging"""
        movement = StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='sale',
            quantity=-10,
            created_by=self.user,
            reference_number='SALE-001',
            reference_type='sale',
            notes='Test sale'
        )

        self.assertEqual(movement.quantity, -10)
        self.assertEqual(movement.movement_type, 'sale')
        self.assertEqual(movement.reference_number, 'SALE-001')
        self.assertTrue(movement.is_outgoing)

    def test_movement_audit_trail_generation(self):
        """Test audit trail generation"""
        # Create multiple movements
        StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='purchase',
            quantity=100,
            created_by=self.user
        )

        StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='sale',
            quantity=-10,
            created_by=self.user
        )

        audit_trail = StockMovement.get_audit_report(self.user)
        self.assertEqual(len(audit_trail), 2)

        # Check audit trail content
        for entry in audit_trail:
            self.assertIn('movement_id', entry)
            self.assertIn('timestamp', entry)
            self.assertIn('user', entry)
            self.assertIn('product', entry)
            self.assertIn('movement_type', entry)
            self.assertIn('quantity', entry)

    def test_movement_summary_analytics(self):
        """Test movement summary and analytics"""
        # Create various types of movements
        StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='purchase',
            quantity=100,
            created_by=self.user
        )

        StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='sale',
            quantity=-20,
            created_by=self.user
        )

        summary = StockMovement.get_movement_summary(self.user)

        self.assertIn('purchase', summary)
        self.assertIn('sale', summary)

        self.assertEqual(summary['purchase']['total_movements'], 1)
        self.assertEqual(summary['purchase']['total_quantity'], 100)
        self.assertEqual(summary['sale']['total_movements'], 1)
        self.assertEqual(summary['sale']['total_quantity'], -20)

    def test_anomaly_detection(self):
        """Test stock movement anomaly detection"""
        # Create normal movements to establish baseline
        for i in range(10):
            StockMovement.create_movement_log(
                owner=self.user,
                product=self.product,
                batch=self.batch,
                location=self.location,
                movement_type='sale',
                quantity=-5,  # Normal quantity
                created_by=self.user
            )

        # Add some purchase movements for variety
        for i in range(3):
            StockMovement.create_movement_log(
                owner=self.user,
                product=self.product,
                batch=self.batch,
                location=self.location,
                movement_type='purchase',
                quantity=20,  # Normal purchases
                created_by=self.user
            )

        # Create anomalous movement (much larger quantity)
        StockMovement.create_movement_log(
            owner=self.user,
            product=self.product,
            batch=self.batch,
            location=self.location,
            movement_type='sale',
            quantity=-100,  # Anomalously large
            created_by=self.user
        )

        anomalies = StockMovement.detect_anomalies(self.user, days_back=30)
        self.assertTrue(len(anomalies) > 0)

        # Check if the large movement was flagged
        unusual_qty_anomalies = [a for a in anomalies if a['type'] == 'unusual_quantity']
        self.assertTrue(len(unusual_qty_anomalies) > 0)


class AdvancedSearchTestCase(TestCase):
    """Test advanced search functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        self._create_test_data()

    def _create_test_data(self):
        """Create test data for search"""
        self.category = Category.objects.create(owner=self.user, name='Electronics')
        self.brand = ProductBrand.objects.create(owner=self.user, name='Samsung')
        self.supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')

        self.product1 = EnhancedProduct.objects.create(
            owner=self.user,
            name='Samsung Galaxy S21',
            category=self.category,
            brand=self.brand,
            supplier=self.supplier,
            selling_price=Decimal('750.00'),
            tags='smartphone, android, premium',
            sku='SAM-GS21',
            barcode='**********123'
        )

        self.product2 = EnhancedProduct.objects.create(
            owner=self.user,
            name='iPhone 13',
            category=self.category,
            selling_price=Decimal('950.00'),
            tags='smartphone, ios, apple',
            sku='APL-IP13',
            barcode='**********124'
        )

    def test_advanced_search_query(self):
        """Test full-text search"""
        results = EnhancedProduct.advanced_search(
            owner=self.user,
            query='Samsung'
        )

        self.assertIn(self.product1, results)
        self.assertNotIn(self.product2, results)

    def test_advanced_search_filters(self):
        """Test filtering in advanced search"""
        filters = {
            'category_id': self.category.id,
            'min_price': 800.00
        }

        results = EnhancedProduct.advanced_search(
            owner=self.user,
            filters=filters
        )

        self.assertNotIn(self.product1, results)  # Price too low
        self.assertIn(self.product2, results)

    def test_search_suggestions(self):
        """Test search suggestions"""
        suggestions = EnhancedProduct.get_search_suggestions(self.user, 'Sam', 5)

        self.assertTrue(any('Samsung' in suggestion for suggestion in suggestions))

    def test_filter_options(self):
        """Test filter options generation"""
        options = EnhancedProduct.get_filter_options(self.user)

        self.assertIn('categories', options)
        self.assertIn('brands', options)
        self.assertIn('suppliers', options)
        self.assertIn('price_range', options)
        self.assertTrue(len(options['categories']) > 0)


class CachingSystemTestCase(TestCase):
    """Test the caching system implementation"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        cache.clear()

    def test_category_tree_caching(self):
        """Test category tree caching functionality"""
        # Create test categories
        parent = Category.objects.create(owner=self.user, name='Parent')
        child = Category.objects.create(owner=self.user, name='Child', parent=parent)

        # First call should miss cache
        tree_data = InventoryCache.get_category_tree(self.user.id)
        self.assertIsNone(tree_data)

        # Generate and cache tree data
        tree_data = Category.get_tree_data(self.user)
        InventoryCache.set_category_tree(self.user.id, tree_data)

        # Second call should hit cache
        cached_data = InventoryCache.get_category_tree(self.user.id)
        self.assertIsNotNone(cached_data)
        self.assertEqual(len(cached_data), 1)

    def test_cache_invalidation_on_model_changes(self):
        """Test that cache is properly invalidated when models change"""
        # Set initial cache
        tree_data = Category.get_tree_data(self.user)
        InventoryCache.set_category_tree(self.user.id, tree_data)

        # Verify cache exists
        self.assertIsNotNone(InventoryCache.get_category_tree(self.user.id))

        # Create new category (should trigger cache invalidation via signal)
        Category.objects.create(owner=self.user, name='New Category')

        # Cache should be invalidated
        self.assertIsNone(InventoryCache.get_category_tree(self.user.id))

    def test_filter_options_caching(self):
        """Test filter options caching"""
        # Create test data
        Category.objects.create(owner=self.user, name='Electronics')
        ProductBrand.objects.create(owner=self.user, name='Samsung')
        Supplier.objects.create(owner=self.user, name='ABC Electronics')

        # First call should generate and cache
        options = EnhancedProduct.get_filter_options(self.user)
        InventoryCache.set_filter_options(self.user.id, options)

        # Second call should hit cache
        cached_options = InventoryCache.get_filter_options(self.user.id)
        self.assertIsNotNone(cached_options)
        self.assertEqual(len(cached_options['categories']), 1)
        self.assertEqual(len(cached_options['brands']), 1)
        self.assertEqual(len(cached_options['suppliers']), 1)


class Phase2APIIntegrationTestCase(APITestCase):
    """Integration tests for Phase 2 API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        refresh = RefreshToken.for_user(self.user)
        self.access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')

    def test_category_tree_endpoint_with_caching(self):
        """Test category tree endpoint with caching headers"""
        # Create test categories
        parent = Category.objects.create(owner=self.user, name='Electronics')
        child = Category.objects.create(owner=self.user, name='Mobile', parent=parent)

        # First call should populate cache
        response = self.client.get('/api/v1/inventory/categories/tree/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('tree', response.data['data'])
        first_call_cached = response.data['data'].get('cached', False)

        # Second call should hit cache
        response = self.client.get('/api/v1/inventory/categories/tree/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        second_call_cached = response.data['data'].get('cached', False)

        # Second call should be from cache if caching is working
        # (Note: This might not work in test environment without proper cache backend)

    def test_advanced_search_endpoint(self):
        """Test advanced search API endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            selling_price=Decimal('100.00'),
            tags='test, electronics'
        )

        # Test query search
        response = self.client.get('/api/v1/inventory/products/advanced_search/', {'q': 'Test'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        # Test filter search
        response = self.client.get('/api/v1/inventory/products/advanced_search/', {
            'category_id': category.id,
            'min_price': 50.00,
            'max_price': 150.00
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        results = response.data['data']['results']
        self.assertTrue(len(results) > 0)

    def test_stock_transfer_endpoint(self):
        """Test stock transfer API endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            selling_price=Decimal('100.00')
        )

        location1 = Location.objects.create(owner=self.user, name='Store 1', code='STOR1')
        location2 = Location.objects.create(owner=self.user, name='Store 2', code='STOR2')

        # Create stock batch
        StockBatch.objects.create(
            owner=self.user,
            product=product,
            location=location1,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('50.00')
        )

        # Test transfer
        response = self.client.post(f'/api/v1/inventory/products/{product.id}/transfer_stock/', {
            'quantity': 30,
            'from_location_id': location1.id,
            'to_location_id': location2.id
        }, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['data']['quantity_transferred'], 30)

    def test_supplier_performance_metrics_endpoint(self):
        """Test supplier performance metrics endpoint"""
        supplier = Supplier.objects.create(owner=self.user, name='Test Supplier')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            supplier=supplier,
            selling_price=Decimal('100.00')
        )
        location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')

        # Create stock batch to generate metrics
        StockBatch.objects.create(
            owner=self.user,
            product=product,
            location=location,
            supplier=supplier,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('50.00')
        )

        response = self.client.get(f'/api/v1/inventory/suppliers/{supplier.id}/performance_metrics/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('performance_metrics', response.data['data'])

        metrics = response.data['data']['performance_metrics']
        self.assertEqual(metrics['total_orders'], 1)
        self.assertEqual(metrics['total_products'], 1)

    def test_stock_movement_audit_report_endpoint(self):
        """Test stock movement audit report endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            category=category,
            selling_price=Decimal('100.00')
        )
        location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')
        batch = StockBatch.objects.create(
            owner=self.user,
            product=product,
            location=location,
            batch_number='BATCH001',
            quantity_received=100,
            quantity_available=100,
            purchase_price=Decimal('50.00')
        )

        # Create a movement
        StockMovement.create_movement_log(
            owner=self.user,
            product=product,
            batch=batch,
            location=location,
            movement_type='purchase',
            quantity=100,
            created_by=self.user
        )

        response = self.client.get('/api/v1/inventory/movements/audit_report/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('audit_trail', response.data['data'])
        self.assertTrue(response.data['data']['total_entries'] > 0)

    def test_bulk_stock_update_endpoint(self):
        """Test bulk stock update endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        product1 = EnhancedProduct.objects.create(
            owner=self.user,
            name='Product 1',
            category=category,
            selling_price=Decimal('100.00')
        )
        product2 = EnhancedProduct.objects.create(
            owner=self.user,
            name='Product 2',
            category=category,
            selling_price=Decimal('150.00'),
            sku='SKU2',
            barcode='BARCODE2'
        )
        location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')

        updates = [
            {
                'product_id': product1.id,
                'location_id': location.id,
                'quantity': 50,
                'operation': 'add',
                'notes': 'Initial stock'
            },
            {
                'product_id': product2.id,
                'location_id': location.id,
                'quantity': 30,
                'operation': 'add',
                'notes': 'Initial stock'
            }
        ]

        response = self.client.post('/api/v1/inventory/products/bulk_stock_update/', {
            'updates': updates
        }, format='json')

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        result = response.data['data']
        self.assertEqual(len(result['successful_updates']), 2)
        self.assertEqual(len(result['errors']), 0)

    def test_search_suggestions_endpoint(self):
        """Test search suggestions endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Samsung Galaxy',
            category=category,
            selling_price=Decimal('100.00')
        )

        response = self.client.get('/api/v1/inventory/products/search_suggestions/', {'q': 'Sam'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('suggestions', response.data['data'])

    def test_filter_options_endpoint(self):
        """Test filter options endpoint"""
        category = Category.objects.create(owner=self.user, name='Electronics')
        brand = ProductBrand.objects.create(owner=self.user, name='Samsung')
        supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')

        response = self.client.get('/api/v1/inventory/products/filter_options/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])

        data = response.data['data']
        self.assertIn('categories', data)
        self.assertIn('brands', data)
        self.assertIn('suppliers', data)
        self.assertIn('price_range', data)
        self.assertIn('sort_options', data)