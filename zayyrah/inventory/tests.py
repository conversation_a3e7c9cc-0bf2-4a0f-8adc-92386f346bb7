from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from decimal import Decimal
from unittest.mock import patch
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta

from .models import Category, Supplier, Location, ProductBrand, EnhancedProduct, StockBatch, StockMovement
from .cache_utils import InventoryCache
from pos.models import Product

User = get_user_model()


class InventoryModelTestCase(TestCase):
    """Test inventory models functionality"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )

    def test_category_creation(self):
        """Test creating categories with tree structure"""
        # Create parent category
        parent = Category.objects.create(
            owner=self.user,
            name='Electronics',
            description='Electronic items'
        )
        self.assertEqual(parent.level, 0)
        self.assertEqual(parent.full_path, 'Electronics')

        # Create child category
        child = Category.objects.create(
            owner=self.user,
            name='Smartphones',
            parent=parent,
            description='Mobile phones'
        )
        self.assertEqual(child.level, 1)
        self.assertEqual(child.full_path, 'Electronics > Smartphones')

    def test_supplier_creation(self):
        """Test supplier creation"""
        supplier = Supplier.objects.create(
            owner=self.user,
            name='ABC Electronics',
            contact_person='John Doe',
            phone='**********',
            email='<EMAIL>',
            rating=4
        )
        self.assertEqual(supplier.name, 'ABC Electronics')
        self.assertEqual(supplier.rating, 4)

    def test_location_creation(self):
        """Test location creation"""
        location = Location.objects.create(
            owner=self.user,
            name='Main Store',
            code='MAIN',
            location_type='store',
            address='123 Main Street'
        )
        self.assertEqual(str(location), 'Main Store (MAIN)')

    def test_product_brand_creation(self):
        """Test product brand creation"""
        brand = ProductBrand.objects.create(
            owner=self.user,
            name='Samsung'
        )
        self.assertEqual(brand.name, 'Samsung')

    def test_enhanced_product_creation(self):
        """Test enhanced product creation with all relationships"""
        # Create related objects
        category = Category.objects.create(owner=self.user, name='Electronics')
        brand = ProductBrand.objects.create(owner=self.user, name='Samsung')
        supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')

        # Create product
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Galaxy S21',
            description='Latest Samsung smartphone',
            sku='SAM-GS21',
            barcode='**********123',
            category=category,
            brand=brand,
            supplier=supplier,
            purchase_price=Decimal('500.00'),
            selling_price=Decimal('750.00'),
            reorder_level=10,
            unit=Product.Unit.EACH
        )

        self.assertEqual(product.name, 'Galaxy S21')
        self.assertEqual(product.unit_label, 'Each')
        self.assertEqual(product.current_stock, 0)  # No stock batches yet
        self.assertTrue(product.is_out_of_stock)

    def test_stock_batch_creation(self):
        """Test stock batch creation and stock calculations"""
        # Create required objects
        category = Category.objects.create(owner=self.user, name='Electronics')
        location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')
        supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')

        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            selling_price=Decimal('100.00'),
            category=category,
            supplier=supplier
        )

        # Create stock batch
        batch = StockBatch.objects.create(
            owner=self.user,
            product=product,
            location=location,
            supplier=supplier,
            batch_number='BATCH001',
            quantity_received=50,
            quantity_available=50,
            purchase_price=Decimal('60.00')
        )

        self.assertEqual(batch.quantity_sold, 0)
        self.assertEqual(batch.batch_value, Decimal('3000.00'))  # 50 * 60
        self.assertEqual(product.current_stock, 50)
        self.assertFalse(product.is_low_stock)  # Reorder level is 5, current stock is 50


class InventoryAPITestCase(APITestCase):
    """Test inventory API endpoints"""

    def setUp(self):
        self.user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Test Shop'
        )
        self.client.force_authenticate(user=self.user)

    def test_category_crud_operations(self):
        """Test Category CRUD operations via API"""
        # CREATE
        data = {
            'name': 'Electronics',
            'description': 'Electronic items',
            'is_active': True,
            'sort_order': 1
        }
        response = self.client.post('/api/v1/inventory/categories/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        category_id = response.data['data']['id']

        # READ
        response = self.client.get(f'/api/v1/inventory/categories/{category_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], 'Electronics')

        # UPDATE
        update_data = {'name': 'Electronics Updated', 'description': 'Updated description'}
        response = self.client.patch(f'/api/v1/inventory/categories/{category_id}/', update_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], 'Electronics Updated')

        # LIST
        response = self.client.get('/api/v1/inventory/categories/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 1)

    def test_supplier_crud_operations(self):
        """Test Supplier CRUD operations via API"""
        # CREATE
        data = {
            'name': 'ABC Electronics',
            'contact_person': 'John Doe',
            'phone': '**********',
            'email': '<EMAIL>',
            'rating': 4,
            'is_active': True
        }
        response = self.client.post('/api/v1/inventory/suppliers/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])

        # LIST
        response = self.client.get('/api/v1/inventory/suppliers/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 1)

    def test_location_crud_operations(self):
        """Test Location CRUD operations via API"""
        # CREATE
        data = {
            'name': 'Main Store',
            'code': 'MAIN',
            'location_type': 'store',
            'address': '123 Main Street',
            'is_active': True
        }
        response = self.client.post('/api/v1/inventory/locations/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])

    def test_product_brand_crud_operations(self):
        """Test ProductBrand CRUD operations via API"""
        # CREATE
        data = {
            'name': 'Samsung',
            'is_active': True
        }
        response = self.client.post('/api/v1/inventory/brands/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])

    def test_enhanced_product_crud_operations(self):
        """Test EnhancedProduct CRUD operations via API"""
        # Create dependencies first
        category = Category.objects.create(owner=self.user, name='Electronics')
        brand = ProductBrand.objects.create(owner=self.user, name='Samsung')
        supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')

        # CREATE
        data = {
            'name': 'Galaxy S21',
            'description': 'Latest Samsung smartphone',
            'sku': 'SAM-GS21',
            'barcode': '**********123',
            'category': category.id,
            'brand': brand.id,
            'supplier': supplier.id,
            'purchase_price': '500.00',
            'selling_price': '750.00',
            'reorder_level': 10,
            'unit': 'each',
            'tax_rate': '5.00',
            'is_active': True
        }
        response = self.client.post('/api/v1/inventory/products/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        product_id = response.data['data']['id']

        # READ
        response = self.client.get(f'/api/v1/inventory/products/{product_id}/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['name'], 'Galaxy S21')

        # Test low stock endpoint
        response = self.client.get('/api/v1/inventory/products/low_stock/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['data']['count'], 1)  # Product should be low stock (0 < 10)

    def test_stock_batch_operations(self):
        """Test StockBatch operations via API"""
        # Create dependencies
        category = Category.objects.create(owner=self.user, name='Electronics')
        location = Location.objects.create(owner=self.user, name='Main Store', code='MAIN')
        supplier = Supplier.objects.create(owner=self.user, name='ABC Electronics')
        product = EnhancedProduct.objects.create(
            owner=self.user,
            name='Test Product',
            selling_price=Decimal('100.00'),
            category=category,
            supplier=supplier
        )

        # CREATE
        data = {
            'batch_number': 'BATCH001',
            'product': product.id,
            'location': location.id,
            'supplier': supplier.id,
            'quantity_received': 50,
            'quantity_available': 50,
            'purchase_price': '60.00',
            'status': 'active'
        }
        response = self.client.post('/api/v1/inventory/batches/', data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])

        # Test expiring soon endpoint
        response = self.client.get('/api/v1/inventory/batches/expiring_soon/')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should be 0 since we didn't set expiry date

    def test_authentication_required(self):
        """Test that authentication is required for all endpoints"""
        self.client.force_authenticate(user=None)  # Remove authentication

        endpoints = [
            '/api/v1/inventory/categories/',
            '/api/v1/inventory/suppliers/',
            '/api/v1/inventory/locations/',
            '/api/v1/inventory/brands/',
            '/api/v1/inventory/products/',
            '/api/v1/inventory/batches/',
            '/api/v1/inventory/movements/',
        ]

        for endpoint in endpoints:
            response = self.client.get(endpoint)
            self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_owner_permission(self):
        """Test that users can only access their own data"""
        # Create another user
        other_user = User.objects.create_user(
            mobile_number='**********',
            password='testpass123',
            account_type='business',
            shop_name='Other Shop'
        )

        # Create category for first user
        category = Category.objects.create(owner=self.user, name='My Category')

        # Try to access with second user
        self.client.force_authenticate(user=other_user)
        response = self.client.get(f'/api/v1/inventory/categories/{category.id}/')
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
