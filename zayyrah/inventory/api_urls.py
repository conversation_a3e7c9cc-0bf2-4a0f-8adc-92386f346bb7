from django.urls import path, include
from rest_framework.routers import DefaultRouter

from .api_views import (
    CategoryViewSet, SupplierViewSet, LocationViewSet, ProductBrandViewSet,
    EnhancedProductViewSet, ProductImageViewSet, StockBatchViewSet, StockMovementViewSet
)

# Create router and register viewsets
router = DefaultRouter()
router.register(r'categories', CategoryViewSet, basename='category')
router.register(r'suppliers', SupplierViewSet, basename='supplier')
router.register(r'locations', LocationViewSet, basename='location')
router.register(r'brands', ProductBrandViewSet, basename='brand')
router.register(r'products', EnhancedProductViewSet, basename='product')
router.register(r'product-images', ProductImageViewSet, basename='product-image')
router.register(r'batches', StockBatchViewSet, basename='batch')
router.register(r'movements', StockMovementViewSet, basename='movement')

app_name = 'inventory'

urlpatterns = [
    path('', include(router.urls)),
]