from decimal import Decimal

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.db import models
from django.utils import timezone

# from pos.models import Product  # Removed - using EnhancedProduct directly


class Category(models.Model):
    """Multi-level category system with tree structure"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=120)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    level = models.PositiveIntegerField(default=0)  # 0=root, 1=sub, 2=sub-sub
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    # image = models.ImageField(upload_to='categories/', blank=True)  # Temporarily disabled
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['level', 'sort_order', 'name']
        unique_together = ('owner', 'parent', 'name')
        indexes = [
            models.Index(fields=['owner', 'parent', 'is_active']),
            models.Index(fields=['level', 'sort_order']),
        ]
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name

    def clean(self):
        super().clean()
        if self.parent and self.parent.owner != self.owner:
            raise ValidationError('Parent category must belong to the same owner.')
        if self.parent and self.parent.level >= 2:
            raise ValidationError('Maximum category depth is 3 levels.')

    def save(self, *args, **kwargs):
        if self.parent:
            self.level = self.parent.level + 1
        else:
            self.level = 0
        super().save(*args, **kwargs)

    @property
    def full_path(self):
        """Get full category path like 'Electronics > Smartphones > Android'"""
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name

    def get_children(self):
        """Get direct children categories"""
        return self.subcategories.filter(is_active=True).order_by('sort_order', 'name')

    def get_all_descendants(self):
        """Get all descendant categories recursively"""
        descendants = []
        for child in self.get_children():
            descendants.append(child)
            descendants.extend(child.get_all_descendants())
        return descendants

    def get_ancestors(self):
        """Get all ancestor categories up to root"""
        ancestors = []
        current = self.parent
        while current:
            ancestors.append(current)
            current = current.parent
        return ancestors

    def get_root(self):
        """Get the root category of this category tree"""
        current = self
        while current.parent:
            current = current.parent
        return current

    def get_siblings(self):
        """Get sibling categories (same parent)"""
        if self.parent:
            return self.parent.subcategories.filter(is_active=True).exclude(id=self.id)
        else:
            return Category.objects.filter(owner=self.owner, parent=None, is_active=True).exclude(id=self.id)

    def can_move_to(self, new_parent):
        """Check if category can be moved to new parent without creating circular reference"""
        if new_parent is None:
            return True

        # Cannot move to self
        if new_parent.id == self.id:
            return False

        # Cannot move to own descendant
        if new_parent in self.get_all_descendants():
            return False

        # Check depth limit
        if new_parent.level >= 2:
            return False

        return True

    def move_to(self, new_parent):
        """Move category to a new parent with validation"""
        if not self.can_move_to(new_parent):
            raise ValidationError('Cannot move category to this parent due to circular reference or depth limit.')

        old_parent = self.parent
        self.parent = new_parent
        self.save()

        # Update all descendants' levels
        self._update_descendant_levels()
        return True

    def _update_descendant_levels(self):
        """Update levels for all descendant categories"""
        for descendant in self.get_all_descendants():
            descendant.level = descendant.parent.level + 1 if descendant.parent else 0
            descendant.save()

    @classmethod
    def get_tree_data(cls, owner, include_products_count=False):
        """Get optimized tree data for frontend rendering"""
        categories = cls.objects.filter(owner=owner, is_active=True).select_related('parent')

        # Build tree structure
        tree_data = []
        category_map = {}

        # Create category map
        for category in categories:
            category_data = {
                'id': category.id,
                'name': category.name,
                'level': category.level,
                'sort_order': category.sort_order,
                'description': category.description,
                'parent_id': category.parent_id,
                'children': []
            }

            if include_products_count:
                category_data['products_count'] = category.products.filter(is_active=True).count()

            category_map[category.id] = category_data

        # Build tree structure
        for category_data in category_map.values():
            if category_data['parent_id'] is None:
                tree_data.append(category_data)
            else:
                parent = category_map.get(category_data['parent_id'])
                if parent:
                    parent['children'].append(category_data)

        return tree_data

    @classmethod
    def bulk_move(cls, owner, move_operations):
        """Perform bulk category move operations"""
        errors = []
        successful_moves = []

        for operation in move_operations:
            try:
                category_id = operation['category_id']
                new_parent_id = operation.get('new_parent_id')

                category = cls.objects.get(id=category_id, owner=owner)
                new_parent = cls.objects.get(id=new_parent_id, owner=owner) if new_parent_id else None

                if category.can_move_to(new_parent):
                    category.move_to(new_parent)
                    successful_moves.append({
                        'category_id': category_id,
                        'old_parent_id': category.parent_id,
                        'new_parent_id': new_parent_id
                    })
                else:
                    errors.append({
                        'category_id': category_id,
                        'error': 'Invalid move operation - would create circular reference or exceed depth limit'
                    })
            except cls.DoesNotExist:
                errors.append({
                    'category_id': operation.get('category_id'),
                    'error': 'Category not found'
                })
            except Exception as e:
                errors.append({
                    'category_id': operation.get('category_id'),
                    'error': str(e)
                })

        return {
            'successful_moves': successful_moves,
            'errors': errors
        }


class Supplier(models.Model):
    """Supplier/Vendor management"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='suppliers')
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=100, blank=True)
    phone = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    rating = models.PositiveIntegerField(default=5, validators=[MinValueValidator(1), MaxValueValidator(5)])
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = ('owner', 'name')
        indexes = [
            models.Index(fields=['owner', 'is_active']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return self.name

    @property
    def total_orders_count(self):
        """Get total number of purchase orders from this supplier"""
        return self.stock_batches.count()

    @property
    def total_products_supplied(self):
        """Get count of unique products supplied"""
        return self.stock_batches.values('product').distinct().count()

    @property
    def total_purchase_value(self):
        """Calculate total purchase value from this supplier"""
        from django.db.models import Sum, F
        total = self.stock_batches.aggregate(
            total_value=Sum(F('quantity_received') * F('purchase_price'))
        )['total_value']
        return total or Decimal('0.00')

    @property
    def average_delivery_performance(self):
        """Calculate average delivery performance score"""
        # This would be calculated based on delivery dates vs expected dates
        # For now, return a basic calculation based on order count
        orders_count = self.total_orders_count
        if orders_count == 0:
            return 0
        # Simplified metric - in real implementation, track actual delivery performance
        return min(self.rating * 20, 100)  # Convert 1-5 rating to 0-100%

    @property
    def recent_orders_count(self):
        """Get count of orders in last 30 days"""
        from django.utils import timezone
        from datetime import timedelta

        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        return self.stock_batches.filter(received_date__gte=thirty_days_ago).count()

    def get_performance_metrics(self):
        """Get comprehensive supplier performance metrics"""
        from django.db.models import Sum, F, Avg, Count
        from django.utils import timezone
        from datetime import timedelta

        # Time periods for analysis
        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        ninety_days_ago = timezone.now().date() - timedelta(days=90)

        # Basic metrics
        total_batches = self.stock_batches.count()
        total_products = self.stock_batches.values('product').distinct().count()
        total_value = self.total_purchase_value

        # Recent activity
        recent_30_days = self.stock_batches.filter(received_date__gte=thirty_days_ago)
        recent_90_days = self.stock_batches.filter(received_date__gte=ninety_days_ago)

        # Quality metrics (based on damage rates)
        quality_score = 100  # Default
        if total_batches > 0:
            damage_rate = self.stock_batches.aggregate(
                avg_damage_rate=Avg(F('quantity_damaged') * 100.0 / F('quantity_received'))
            )['avg_damage_rate'] or 0
            quality_score = max(0, 100 - damage_rate)

        return {
            'total_orders': total_batches,
            'total_products': total_products,
            'total_value': total_value,
            'recent_orders_30_days': recent_30_days.count(),
            'recent_orders_90_days': recent_90_days.count(),
            'quality_score': round(quality_score, 2),
            'reliability_rating': self.rating,
            'delivery_performance': self.average_delivery_performance,
            'recent_value_30_days': recent_30_days.aggregate(
                total=Sum(F('quantity_received') * F('purchase_price'))
            )['total'] or Decimal('0.00'),
            'average_order_value': total_value / total_batches if total_batches > 0 else Decimal('0.00')
        }

    def get_product_catalog(self):
        """Get products supplied by this supplier with pricing history"""
        from django.db.models import Min, Max, Avg, Count, Sum

        products_data = []
        supplied_products = self.products.filter(is_active=True)

        for product in supplied_products:
            batches = self.stock_batches.filter(product=product)
            if batches.exists():
                price_stats = batches.aggregate(
                    min_price=Min('purchase_price'),
                    max_price=Max('purchase_price'),
                    avg_price=Avg('purchase_price'),
                    total_batches=Count('id'),
                    total_quantity=Sum('quantity_received')
                )

                products_data.append({
                    'product_id': product.id,
                    'product_name': product.name,
                    'sku': product.sku,
                    'current_selling_price': product.selling_price,
                    'price_stats': price_stats,
                    'last_batch_date': batches.latest('received_date').received_date,
                    'total_supplied': price_stats['total_quantity'] or 0
                })

        return products_data

    @classmethod
    def get_top_suppliers(cls, owner, limit=10, metric='total_value'):
        """Get top performing suppliers by various metrics"""
        suppliers = cls.objects.filter(owner=owner, is_active=True)

        supplier_data = []
        for supplier in suppliers:
            metrics = supplier.get_performance_metrics()
            supplier_data.append({
                'supplier': supplier,
                'metrics': metrics,
                'sort_value': metrics.get(metric, 0)
            })

        # Sort by the specified metric
        supplier_data.sort(key=lambda x: x['sort_value'], reverse=True)

        return supplier_data[:limit]


class Location(models.Model):
    """Storage locations/warehouses"""
    LOCATION_TYPE_CHOICES = [
        ('warehouse', 'Warehouse'),
        ('store', 'Store'),
        ('section', 'Section')
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    location_type = models.CharField(max_length=20, choices=LOCATION_TYPE_CHOICES, default='store')
    address = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = ('owner', 'code')
        indexes = [
            models.Index(fields=['owner', 'is_active']),
            models.Index(fields=['code']),
        ]

    def __str__(self):
        return f"{self.name} ({self.code})"


class ProductBrand(models.Model):
    """Product brands"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='brands')
    name = models.CharField(max_length=100)
    # logo = models.ImageField(upload_to='brands/', blank=True)  # Temporarily disabled
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = ('owner', 'name')
        indexes = [
            models.Index(fields=['owner', 'is_active']),
        ]

    def __str__(self):
        return self.name


class EnhancedProduct(models.Model):
    """Enhanced product model replacing existing Product"""

    class Unit(models.TextChoices):
        EACH = ('each', 'Each')
        KILOGRAM = ('kg', 'Kilogram')
        GRAM = ('g', 'Gram')
        LITER = ('l', 'Liter')
        MILLILITER = ('ml', 'Milliliter')
        METER = ('m', 'Meter')
        CENTIMETER = ('cm', 'Centimeter')
        BOX = ('box', 'Box')
        PACK = ('pack', 'Pack')
        CUSTOM = ('custom', 'Custom')
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enhanced_products')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    brand = models.ForeignKey(ProductBrand, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')

    # Basic Information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    sku = models.CharField(max_length=64, blank=True, null=True)
    barcode = models.CharField(max_length=50, blank=True, null=True)
    qr_code = models.CharField(max_length=100, blank=True)

    # Pricing
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    selling_price = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(Decimal('0.00'))])
    wholesale_price = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    minimum_selling_price = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)

    # Stock Management
    track_stock = models.BooleanField(default=True)
    allow_negative_stock = models.BooleanField(default=False)
    reorder_level = models.PositiveIntegerField(default=5)
    maximum_stock_level = models.PositiveIntegerField(blank=True, null=True)

    # Physical Attributes
    unit = models.CharField(max_length=24, choices=Unit.choices, default=Unit.EACH)
    unit_custom_label = models.CharField(max_length=32, blank=True)
    base_unit = models.CharField(max_length=24, choices=Unit.choices, blank=True, help_text="Base unit for conversions")
    unit_precision = models.PositiveIntegerField(default=2, validators=[MaxValueValidator(6)], help_text="Decimal places for quantities")
    allow_fractional_quantities = models.BooleanField(default=False, help_text="Allow fractional quantities")
    weight = models.DecimalField(max_digits=8, decimal_places=3, blank=True, null=True)
    dimensions = models.CharField(max_length=50, blank=True, help_text="L×W×H")

    # Tax and Accounting
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    tax_exempt = models.BooleanField(default=False)

    # Status and Metadata
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")

    # Enhanced Features
    has_variants = models.BooleanField(default=False, help_text="Product has color/size/flavor variants")
    variant_type = models.CharField(max_length=50, blank=True, help_text="Type of variation (color, size, flavor)")
    track_expiry = models.BooleanField(default=False, help_text="Track expiry dates for this product")
    perishable = models.BooleanField(default=False, help_text="Product is perishable")
    shelf_life_days = models.PositiveIntegerField(blank=True, null=True, help_text="Expected shelf life in days")

    # POS Integration Fields
    pos_enabled = models.BooleanField(default=True, help_text="Available for POS sales")
    pos_display_name = models.CharField(max_length=100, blank=True, help_text="Display name in POS (optional)")
    pos_sort_order = models.PositiveIntegerField(default=0, help_text="Sort order in POS interface")
    pos_category_override = models.CharField(max_length=50, blank=True, help_text="POS category override")
    quick_sale_enabled = models.BooleanField(default=False, help_text="Enable for quick sale buttons")
    pos_button_color = models.CharField(max_length=7, blank=True, help_text="Button color in POS (hex code)")

    # Discount and Pricing Options for POS
    allow_discount = models.BooleanField(default=True, help_text="Allow discounts on this product")
    max_discount_percent = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=Decimal('100.00'),
        help_text="Maximum discount percentage allowed"
    )
    pos_price_override = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
        help_text="Override selling price for POS (optional)"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = [('owner', 'sku'), ('owner', 'barcode')]
        indexes = [
            models.Index(fields=['owner', 'is_active']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['sku']),
            models.Index(fields=['barcode']),
            models.Index(fields=['reorder_level']),
            models.Index(fields=['name']),
        ]

    def __str__(self):
        return self.name

    @property
    def unit_label(self):
        if self.unit == self.Unit.CUSTOM and self.unit_custom_label:
            return self.unit_custom_label
        return self.get_unit_display()

    @property
    def stock_quantity(self):
        """Alias for current_stock for template compatibility"""
        return self.current_stock

    @property
    def pos_price(self):
        """Get effective POS selling price (override or regular)"""
        return self.pos_price_override if self.pos_price_override else self.selling_price

    @property
    def pos_name(self):
        """Get effective POS display name"""
        return self.pos_display_name if self.pos_display_name else self.name

    @property
    def current_stock(self):
        """Calculate current stock from all active batches"""
        return sum(batch.quantity_available for batch in self.stock_batches.filter(status='active'))

    @property
    def total_stock_value(self):
        """Calculate total stock value based on purchase prices"""
        total_value = Decimal('0.00')
        for batch in self.stock_batches.filter(status='active', quantity_available__gt=0):
            total_value += batch.quantity_available * batch.purchase_price
        return total_value

    @property
    def is_low_stock(self):
        """Check if product is below reorder level"""
        return self.track_stock and self.current_stock <= self.reorder_level

    @property
    def is_out_of_stock(self):
        """Check if product is out of stock"""
        return self.track_stock and self.current_stock <= 0

    def clean(self):
        super().clean()
        if self.sku and not self.sku.strip():
            self.sku = None
        if self.barcode and not self.barcode.strip():
            self.barcode = None
        if self.wholesale_price and self.wholesale_price < self.purchase_price:
            raise ValidationError('Wholesale price cannot be less than purchase price.')
        if self.minimum_selling_price and self.minimum_selling_price > self.selling_price:
            raise ValidationError('Minimum selling price cannot be greater than selling price.')

    def allocate_stock(self, quantity, location, allocation_method='FIFO'):
        """
        Allocate stock using FIFO or LIFO algorithm
        Returns list of allocations: [{'batch': batch, 'quantity': qty}, ...]
        """
        if not self.track_stock:
            return []

        available_batches = self.stock_batches.filter(
            location=location,
            status='active',
            quantity_available__gt=0
        )

        if allocation_method == 'FIFO':
            available_batches = available_batches.order_by('received_date', 'created_at')
        elif allocation_method == 'LIFO':
            available_batches = available_batches.order_by('-received_date', '-created_at')
        else:
            raise ValueError("Allocation method must be 'FIFO' or 'LIFO'")

        allocations = []
        remaining_quantity = quantity

        for batch in available_batches:
            if remaining_quantity <= 0:
                break

            available_in_batch = batch.quantity_available
            allocated_quantity = min(remaining_quantity, available_in_batch)

            allocations.append({
                'batch': batch,
                'quantity': allocated_quantity
            })

            remaining_quantity -= allocated_quantity

        return allocations, remaining_quantity

    def reserve_stock(self, quantity, location, allocation_method='FIFO'):
        """Reserve stock for pending orders"""
        allocations, remaining = self.allocate_stock(quantity, location, allocation_method)

        if remaining > 0:
            return False, f"Insufficient stock. Short by {remaining} units."

        # Apply reservations
        for allocation in allocations:
            batch = allocation['batch']
            qty = allocation['quantity']
            batch.quantity_available -= qty
            batch.quantity_reserved += qty
            batch.save()

        return True, allocations

    def release_stock_reservation(self, reservations):
        """Release previously reserved stock"""
        for reservation in reservations:
            batch = reservation['batch']
            qty = reservation['quantity']
            batch.quantity_available += qty
            batch.quantity_reserved -= qty
            batch.save()

    def consume_stock(self, quantity, location, allocation_method='FIFO', reference_number='', reference_type='', notes=''):
        """
        Consume stock from inventory (for sales, adjustments, etc.)
        Creates stock movements and updates batch quantities
        """
        allocations, remaining = self.allocate_stock(quantity, location, allocation_method)

        if remaining > 0 and not self.allow_negative_stock:
            return False, f"Insufficient stock. Short by {remaining} units.", []

        movements = []

        # Process allocations
        for allocation in allocations:
            batch = allocation['batch']
            qty = allocation['quantity']

            # Update batch quantity
            batch.quantity_available -= qty
            if batch.quantity_available <= 0:
                batch.status = 'sold_out'
            batch.save()

            # Create stock movement
            movement = StockMovement.objects.create(
                owner=self.owner,
                product=self,
                batch=batch,
                location=location,
                movement_type='sale',
                quantity=-qty,  # Negative for outgoing
                reference_number=reference_number,
                reference_type=reference_type,
                notes=notes
            )
            movements.append(movement)

        return True, "Stock consumed successfully", movements

    def add_stock(self, quantity, location, supplier=None, batch_number=None, purchase_price=None, **batch_kwargs):
        """
        Add stock to inventory (for purchases, adjustments, etc.)
        Creates new batch and stock movement
        """
        if not batch_number:
            from django.utils import timezone
            batch_number = f"BATCH-{timezone.now().strftime('%Y%m%d%H%M%S')}-{self.id}"

        if purchase_price is None:
            purchase_price = self.purchase_price

        # Create stock batch
        batch = StockBatch.objects.create(
            owner=self.owner,
            product=self,
            location=location,
            supplier=supplier,
            batch_number=batch_number,
            quantity_received=quantity,
            quantity_available=quantity,
            purchase_price=purchase_price,
            **batch_kwargs
        )

        # Create stock movement
        movement = StockMovement.objects.create(
            owner=self.owner,
            product=self,
            batch=batch,
            location=location,
            movement_type='purchase',
            quantity=quantity,
            reference_number=batch_number,
            reference_type='purchase',
            notes=batch_kwargs.get('notes', f'Stock added - Batch {batch_number}')
        )

        return batch, movement

    def transfer_stock(self, quantity, from_location, to_location, allocation_method='FIFO'):
        """Transfer stock between locations"""
        # Consume from source location
        success, message, movements_out = self.consume_stock(
            quantity, from_location, allocation_method,
            reference_type='transfer',
            notes=f'Transfer to {to_location.name}'
        )

        if not success:
            return False, message, []

        # Add to destination location (using weighted average price)
        total_value = sum(abs(movement.quantity) * movement.batch.purchase_price for movement in movements_out)
        avg_price = total_value / quantity if quantity > 0 else Decimal('0.00')

        batch, movement_in = self.add_stock(
            quantity, to_location,
            purchase_price=avg_price,
            notes=f'Transfer from {from_location.name}'
        )

        # Update movement reference
        movement_in.reference_type = 'transfer'
        movement_in.save()

        return True, "Stock transferred successfully", movements_out + [movement_in]

    def get_stock_by_location(self):
        """Get stock levels by location"""
        from django.db.models import Sum

        location_stock = {}
        batches = self.stock_batches.filter(status='active').values('location__name', 'location_id').annotate(
            total_quantity=Sum('quantity_available'),
            total_value=Sum('quantity_available') * Sum('purchase_price')
        )

        for batch_data in batches:
            location_stock[batch_data['location__name']] = {
                'location_id': batch_data['location_id'],
                'quantity': batch_data['total_quantity'] or 0,
                'value': batch_data['total_value'] or Decimal('0.00')
            }

        return location_stock

    def get_stock_valuation(self, method='FIFO'):
        """Calculate stock valuation using specified method"""
        active_batches = self.stock_batches.filter(status='active', quantity_available__gt=0)

        if method == 'FIFO':
            # Use oldest batch prices first
            batches = active_batches.order_by('received_date', 'created_at')
        elif method == 'LIFO':
            # Use newest batch prices first
            batches = active_batches.order_by('-received_date', '-created_at')
        elif method == 'WEIGHTED_AVERAGE':
            # Calculate weighted average
            total_quantity = sum(batch.quantity_available for batch in active_batches)
            total_value = sum(batch.quantity_available * batch.purchase_price for batch in active_batches)
            avg_price = total_value / total_quantity if total_quantity > 0 else Decimal('0.00')
            return {
                'method': method,
                'total_quantity': total_quantity,
                'average_price': avg_price,
                'total_value': total_value
            }

        total_quantity = sum(batch.quantity_available for batch in batches)
        total_value = sum(batch.quantity_available * batch.purchase_price for batch in batches)

        return {
            'method': method,
            'total_quantity': total_quantity,
            'total_value': total_value,
            'batches': [{
                'batch_id': batch.id,
                'quantity': batch.quantity_available,
                'price': batch.purchase_price,
                'value': batch.quantity_available * batch.purchase_price
            } for batch in batches]
        }

    @classmethod
    def advanced_search(cls, owner, query=None, filters=None, sort_by=None):
        """
        Advanced search with full-text search and complex filtering
        """
        from django.db.models import Q, F, Case, When, IntegerField, Value
        from django.contrib.postgres.search import SearchVector, SearchQuery, SearchRank

        products = cls.objects.filter(owner=owner, is_active=True).select_related(
            'category', 'brand', 'supplier'
        ).prefetch_related('images', 'stock_batches')

        # Apply filters
        if filters:
            # Category filter
            if filters.get('category_id'):
                products = products.filter(category_id=filters['category_id'])

            # Category hierarchy filter (include subcategories)
            if filters.get('category_tree'):
                category = Category.objects.get(id=filters['category_tree'], owner=owner)
                descendant_categories = category.get_all_descendants()
                category_ids = [category.id] + [desc.id for desc in descendant_categories]
                products = products.filter(category_id__in=category_ids)

            # Brand filter
            if filters.get('brand_id'):
                products = products.filter(brand_id=filters['brand_id'])

            # Supplier filter
            if filters.get('supplier_id'):
                products = products.filter(supplier_id=filters['supplier_id'])

            # Price range filter
            if filters.get('min_price'):
                products = products.filter(selling_price__gte=filters['min_price'])
            if filters.get('max_price'):
                products = products.filter(selling_price__lte=filters['max_price'])

            # Stock level filters
            if filters.get('stock_status'):
                if filters['stock_status'] == 'in_stock':
                    # Products with available stock
                    products = products.filter(stock_batches__status='active', stock_batches__quantity_available__gt=0).distinct()
                elif filters['stock_status'] == 'low_stock':
                    # This requires custom filtering since current_stock is a property
                    low_stock_products = []
                    for product in products:
                        if product.is_low_stock:
                            low_stock_products.append(product.id)
                    products = products.filter(id__in=low_stock_products)
                elif filters['stock_status'] == 'out_of_stock':
                    # Products with no available stock
                    out_of_stock_products = []
                    for product in products:
                        if product.is_out_of_stock:
                            out_of_stock_products.append(product.id)
                    products = products.filter(id__in=out_of_stock_products)

            # Location filter
            if filters.get('location_id'):
                products = products.filter(stock_batches__location_id=filters['location_id']).distinct()

            # Tags filter
            if filters.get('tags'):
                tag_query = Q()
                for tag in filters['tags']:
                    tag_query |= Q(tags__icontains=tag)
                products = products.filter(tag_query)

            # Date range filters
            if filters.get('created_after'):
                products = products.filter(created_at__date__gte=filters['created_after'])
            if filters.get('created_before'):
                products = products.filter(created_at__date__lte=filters['created_before'])

            # SKU and Barcode filters
            if filters.get('sku'):
                products = products.filter(sku__icontains=filters['sku'])
            if filters.get('barcode'):
                products = products.filter(barcode__icontains=filters['barcode'])

        # Full-text search
        if query:
            # Basic search across multiple fields
            search_query = Q(name__icontains=query) | \
                          Q(description__icontains=query) | \
                          Q(sku__icontains=query) | \
                          Q(barcode__icontains=query) | \
                          Q(tags__icontains=query) | \
                          Q(category__name__icontains=query) | \
                          Q(brand__name__icontains=query) | \
                          Q(supplier__name__icontains=query)

            products = products.filter(search_query).distinct()

        # Sorting
        if sort_by:
            if sort_by == 'name_asc':
                products = products.order_by('name')
            elif sort_by == 'name_desc':
                products = products.order_by('-name')
            elif sort_by == 'price_asc':
                products = products.order_by('selling_price')
            elif sort_by == 'price_desc':
                products = products.order_by('-selling_price')
            elif sort_by == 'created_asc':
                products = products.order_by('created_at')
            elif sort_by == 'created_desc':
                products = products.order_by('-created_at')
            elif sort_by == 'popularity':
                # Sort by number of stock movements (approximate popularity)
                products = products.annotate(
                    popularity_score=Case(
                        When(stock_movements__isnull=True, then=Value(0)),
                        default=F('stock_movements__id'),
                        output_field=IntegerField()
                    )
                ).order_by('-popularity_score')

        return products

    @classmethod
    def get_search_suggestions(cls, owner, query, limit=10):
        """Get search suggestions based on partial query"""
        if not query or len(query) < 2:
            return []

        suggestions = set()

        # Product name suggestions
        products = cls.objects.filter(
            owner=owner,
            is_active=True,
            name__icontains=query
        ).values_list('name', flat=True)[:limit]
        suggestions.update(products)

        # Category suggestions
        categories = Category.objects.filter(
            owner=owner,
            is_active=True,
            name__icontains=query
        ).values_list('name', flat=True)[:limit]
        suggestions.update(f"Category: {cat}" for cat in categories)

        # Brand suggestions
        brands = ProductBrand.objects.filter(
            owner=owner,
            is_active=True,
            name__icontains=query
        ).values_list('name', flat=True)[:limit]
        suggestions.update(f"Brand: {brand}" for brand in brands)

        # Tag suggestions
        tag_products = cls.objects.filter(
            owner=owner,
            is_active=True,
            tags__icontains=query
        ).values_list('tags', flat=True)
        for tags_string in tag_products:
            if tags_string:
                tags = [tag.strip() for tag in tags_string.split(',')]
                matching_tags = [tag for tag in tags if query.lower() in tag.lower()]
                suggestions.update(f"Tag: {tag}" for tag in matching_tags)

        return list(suggestions)[:limit]

    @classmethod
    def get_filter_options(cls, owner):
        """Get available filter options for advanced search"""
        from django.db.models import Min, Max

        # Price range
        price_range = cls.objects.filter(owner=owner, is_active=True).aggregate(
            min_price=Min('selling_price'),
            max_price=Max('selling_price')
        )

        # Categories
        categories = Category.objects.filter(owner=owner, is_active=True).values(
            'id', 'name', 'level', 'parent_id'
        )

        # Brands
        brands = ProductBrand.objects.filter(owner=owner, is_active=True).values(
            'id', 'name'
        )

        # Suppliers
        suppliers = Supplier.objects.filter(owner=owner, is_active=True).values(
            'id', 'name'
        )

        # Locations
        locations = Location.objects.filter(owner=owner, is_active=True).values(
            'id', 'name', 'location_type'
        )

        # Popular tags
        popular_tags = []
        tag_products = cls.objects.filter(owner=owner, is_active=True, tags__isnull=False).exclude(tags='')
        tag_counts = {}
        for product in tag_products:
            tags = [tag.strip() for tag in product.tags.split(',') if tag.strip()]
            for tag in tags:
                tag_counts[tag] = tag_counts.get(tag, 0) + 1

        popular_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:20]

        return {
            'price_range': price_range,
            'categories': list(categories),
            'brands': list(brands),
            'suppliers': list(suppliers),
            'locations': list(locations),
            'popular_tags': [{'name': tag, 'count': count} for tag, count in popular_tags],
            'stock_status_options': [
                {'value': 'in_stock', 'label': 'In Stock'},
                {'value': 'low_stock', 'label': 'Low Stock'},
                {'value': 'out_of_stock', 'label': 'Out of Stock'}
            ],
            'sort_options': [
                {'value': 'name_asc', 'label': 'Name (A-Z)'},
                {'value': 'name_desc', 'label': 'Name (Z-A)'},
                {'value': 'price_asc', 'label': 'Price (Low to High)'},
                {'value': 'price_desc', 'label': 'Price (High to Low)'},
                {'value': 'created_asc', 'label': 'Oldest First'},
                {'value': 'created_desc', 'label': 'Newest First'},
                {'value': 'popularity', 'label': 'Most Popular'}
            ]
        }

    def save(self, *args, **kwargs):
        # Auto-set fractional quantities based on unit type for new products
        if not self.pk:  # Only for new products
            # Units that should allow fractional quantities
            fractional_units = ['kg', 'g', 'l', 'ml', 'm', 'cm']
            self.allow_fractional_quantities = self.unit in fractional_units

        super().save(*args, **kwargs)


class ProductImage(models.Model):
    """Enhanced product images with thumbnail support"""
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/%Y/%m/', max_length=500, blank=True, null=True)
    thumbnail = models.ImageField(upload_to='products/thumbnails/%Y/%m/', blank=True, null=True, max_length=500)
    is_primary = models.BooleanField(default=False)
    alt_text = models.CharField(max_length=200, blank=True)
    sort_order = models.PositiveIntegerField(default=0)

    # Enhanced metadata
    image_size = models.PositiveIntegerField(blank=True, null=True, help_text="File size in bytes")
    image_width = models.PositiveIntegerField(blank=True, null=True)
    image_height = models.PositiveIntegerField(blank=True, null=True)
    upload_session_id = models.CharField(max_length=100, blank=True, help_text="For bulk uploads")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['sort_order', 'id']
        indexes = [
            models.Index(fields=['product', 'is_primary']),
            models.Index(fields=['product', 'sort_order']),
            models.Index(fields=['upload_session_id']),
        ]

    def __str__(self):
        return f"Image for {self.product.name}"

    def save(self, *args, **kwargs):
        # Ensure only one primary image per product
        if self.is_primary:
            ProductImage.objects.filter(
                product=self.product,
                is_primary=True
            ).exclude(pk=self.pk).update(is_primary=False)
        super().save(*args, **kwargs)

    @property
    def image_url(self):
        """Get image URL safely"""
        return self.image.url if self.image else None

    @property
    def thumbnail_url(self):
        """Get thumbnail URL safely"""
        return self.thumbnail.url if self.thumbnail else self.image_url


class UnitConversion(models.Model):
    """Unit conversion management for multi-unit products"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='unit_conversions')
    from_unit = models.CharField(max_length=24, choices=EnhancedProduct.Unit.choices)
    to_unit = models.CharField(max_length=24, choices=EnhancedProduct.Unit.choices)
    conversion_factor = models.DecimalField(
        max_digits=12,
        decimal_places=6,
        validators=[MinValueValidator(Decimal('0.000001'))]
    )
    is_active = models.BooleanField(default=True)

    # Enhanced Phase 2 fields
    conversion_path = models.JSONField(default=list, blank=True, help_text="Chain of conversions for complex unit paths")
    context_type = models.CharField(max_length=50, blank=True, help_text="Product category context (e.g., 'food_items', 'textiles')")
    precision_level = models.PositiveIntegerField(default=6, validators=[MaxValueValidator(10)], help_text="Decimal precision for conversions")
    is_system_defined = models.BooleanField(default=False, help_text="System vs user-defined conversion")

    # Metadata
    description = models.CharField(max_length=200, blank=True, help_text="e.g., '1 kg = 1000 g'")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['from_unit', 'to_unit']
        unique_together = ('owner', 'from_unit', 'to_unit')
        indexes = [
            models.Index(fields=['owner', 'from_unit']),
            models.Index(fields=['owner', 'to_unit']),
            models.Index(fields=['owner', 'from_unit', 'to_unit']),
            models.Index(fields=['context_type', 'is_active']),
            models.Index(fields=['is_system_defined', 'is_active']),
        ]

    def __str__(self):
        return f"{self.get_from_unit_display()} → {self.get_to_unit_display()} ({self.conversion_factor})"

    def clean(self):
        super().clean()
        if self.from_unit == self.to_unit:
            raise ValidationError('From unit and to unit cannot be the same.')
        if self.conversion_factor <= 0:
            raise ValidationError('Conversion factor must be positive.')

    @classmethod
    def get_conversion_factor(cls, owner, from_unit, to_unit):
        """Get conversion factor between two units"""
        if from_unit == to_unit:
            return Decimal('1.0')

        try:
            conversion = cls.objects.get(
                owner=owner,
                from_unit=from_unit,
                to_unit=to_unit,
                is_active=True
            )
            return conversion.conversion_factor
        except cls.DoesNotExist:
            # Try reverse conversion
            try:
                reverse_conversion = cls.objects.get(
                    owner=owner,
                    from_unit=to_unit,
                    to_unit=from_unit,
                    is_active=True
                )
                return Decimal('1.0') / reverse_conversion.conversion_factor
            except cls.DoesNotExist:
                return None

    @classmethod
    def convert_quantity(cls, owner, quantity, from_unit, to_unit):
        """Convert quantity from one unit to another"""
        factor = cls.get_conversion_factor(owner, from_unit, to_unit)
        if factor is None:
            raise ValueError(f"No conversion available from {from_unit} to {to_unit}")
        return quantity * factor


class StockBatch(models.Model):
    """Enhanced stock batch tracking"""
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('reserved', 'Reserved'),
        ('expired', 'Expired'),
        ('damaged', 'Damaged'),
        ('sold_out', 'Sold Out')
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='stock_batches')
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='stock_batches')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='stock_batches')
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True, related_name='stock_batches')

    # Batch Information
    batch_number = models.CharField(max_length=50)
    purchase_order_number = models.CharField(max_length=50, blank=True)

    # Quantities
    quantity_received = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        validators=[MinValueValidator(Decimal('0.0001'))],
        help_text="Total quantity received in this batch"
    )
    quantity_available = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        help_text="Quantity currently available in this batch"
    )
    quantity_reserved = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=Decimal('0'),
        help_text="Quantity reserved but not yet consumed"
    )
    quantity_damaged = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        default=Decimal('0'),
        help_text="Quantity marked as damaged/unusable"
    )

    # Pricing and Dates
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, validators=[MinValueValidator(Decimal('0.00'))])
    received_date = models.DateField(default=timezone.localdate)
    manufacture_date = models.DateField(blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True)

    # Status
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')

    notes = models.TextField(blank=True)

    # Enhanced Features
    parent_batch = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='child_batches', help_text="Parent batch if this is a split batch")
    quality_score = models.PositiveIntegerField(default=100, validators=[MinValueValidator(0), MaxValueValidator(100)], help_text="Quality score (0-100%)")
    batch_metadata = models.JSONField(default=dict, blank=True, help_text="Additional batch-specific data")
    temperature_controlled = models.BooleanField(default=False, help_text="Requires temperature control")
    storage_temperature = models.CharField(max_length=50, blank=True, help_text="Required storage temperature")
    handling_notes = models.TextField(blank=True, help_text="Special handling instructions")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-received_date', '-created_at']
        unique_together = ('owner', 'batch_number', 'product')
        indexes = [
            models.Index(fields=['owner', 'product', 'status']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['received_date']),
            models.Index(fields=['batch_number']),
            models.Index(fields=['quality_score']),
            models.Index(fields=['temperature_controlled']),
            models.Index(fields=['parent_batch']),
        ]

    def __str__(self):
        return f"Batch {self.batch_number} - {self.product.name}"

    @property
    def quantity_sold(self):
        """Calculate quantity sold from this batch"""
        return self.quantity_received - self.quantity_available - self.quantity_damaged

    @property
    def is_expiring_soon(self, days=30):
        """Check if batch is expiring within specified days"""
        if not self.expiry_date:
            return False
        return (self.expiry_date - timezone.now().date()).days <= days

    @property
    def batch_value(self):
        """Calculate current value of available stock in this batch"""
        return self.quantity_available * self.purchase_price

    def clean(self):
        super().clean()
        if self.quantity_available > self.quantity_received:
            raise ValidationError('Available quantity cannot exceed received quantity.')
        if self.quantity_damaged > self.quantity_received:
            raise ValidationError('Damaged quantity cannot exceed received quantity.')
        if self.expiry_date and self.manufacture_date:
            if self.expiry_date <= self.manufacture_date:
                raise ValidationError('Expiry date must be after manufacture date.')


class StockMovement(models.Model):
    """Track all stock movements"""
    MOVEMENT_TYPE_CHOICES = [
        ('purchase', 'Purchase'),
        ('sale', 'Sale'),
        ('return', 'Return'),
        ('adjustment', 'Adjustment'),
        ('transfer', 'Transfer'),
        ('damage', 'Damage'),
        ('expired', 'Expired')
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='stock_movements')
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='stock_movements')
    batch = models.ForeignKey(StockBatch, on_delete=models.CASCADE, related_name='movements')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='stock_movements')

    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPE_CHOICES)
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=4,
        help_text="Quantity moved (can be negative for outgoing stock)"
    )
    reference_number = models.CharField(max_length=50, blank=True)
    reference_type = models.CharField(max_length=20, blank=True)  # 'sale', 'purchase_order', etc.
    reference_id = models.PositiveIntegerField(blank=True, null=True)

    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='stock_movements_created'
    )

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner', 'product', 'created_at']),
            models.Index(fields=['movement_type', 'created_at']),
            models.Index(fields=['batch', 'created_at']),
        ]

    def __str__(self):
        movement_prefix = '+' if self.quantity > 0 else ''
        return f"{self.get_movement_type_display()}: {movement_prefix}{self.quantity} {self.product.name}"

    @property
    def is_incoming(self):
        """Check if this is an incoming movement"""
        return self.quantity > 0

    @property
    def is_outgoing(self):
        """Check if this is an outgoing movement"""
        return self.quantity < 0

    @property
    def movement_value(self):
        """Calculate monetary value of this movement"""
        return abs(self.quantity) * self.batch.purchase_price

    def get_audit_trail(self):
        """Get audit trail information for this movement"""
        return {
            'movement_id': self.id,
            'timestamp': self.created_at,
            'user': self.created_by.mobile_number if self.created_by else 'System',
            'product': self.product.name,
            'batch': self.batch.batch_number,
            'location': self.location.name,
            'movement_type': self.get_movement_type_display(),
            'quantity': self.quantity,
            'value': self.movement_value,
            'reference': f"{self.reference_type}: {self.reference_number}" if self.reference_number else None,
            'notes': self.notes
        }

    @classmethod
    def create_movement_log(cls, owner, product, batch, location, movement_type, quantity,
                           created_by=None, reference_number='', reference_type='', notes=''):
        """Create a comprehensive stock movement log entry"""
        movement = cls.objects.create(
            owner=owner,
            product=product,
            batch=batch,
            location=location,
            movement_type=movement_type,
            quantity=quantity,
            reference_number=reference_number,
            reference_type=reference_type,
            notes=notes,
            created_by=created_by
        )

        # Auto-generate reference number if not provided
        if not reference_number and movement_type != 'adjustment':
            movement.reference_number = f"{movement_type.upper()}-{movement.id:06d}"
            movement.save()

        return movement

    @classmethod
    def get_movement_summary(cls, owner, product=None, location=None, start_date=None, end_date=None):
        """Get movement summary with filtering options"""
        movements = cls.objects.filter(owner=owner)

        if product:
            movements = movements.filter(product=product)
        if location:
            movements = movements.filter(location=location)
        if start_date:
            movements = movements.filter(created_at__date__gte=start_date)
        if end_date:
            movements = movements.filter(created_at__date__lte=end_date)

        # Calculate summaries by movement type
        from django.db.models import Sum, Count, Avg

        summary = {}
        for movement_type, _ in cls.MOVEMENT_TYPE_CHOICES:
            type_movements = movements.filter(movement_type=movement_type)
            if type_movements.exists():
                stats = type_movements.aggregate(
                    total_movements=Count('id'),
                    total_quantity=Sum('quantity'),
                    avg_quantity=Avg('quantity')
                )
                total_value = sum(movement.movement_value for movement in type_movements)

                summary[movement_type] = {
                    'total_movements': stats['total_movements'],
                    'total_quantity': stats['total_quantity'],
                    'average_quantity': round(stats['avg_quantity'], 2) if stats['avg_quantity'] else 0,
                    'total_value': total_value,
                    'average_value': total_value / stats['total_movements'] if stats['total_movements'] > 0 else 0
                }

        return summary

    @classmethod
    def get_audit_report(cls, owner, start_date=None, end_date=None, product=None, user=None):
        """Generate comprehensive audit report"""
        movements = cls.objects.filter(owner=owner).select_related(
            'product', 'batch', 'location', 'created_by'
        )

        if start_date:
            movements = movements.filter(created_at__date__gte=start_date)
        if end_date:
            movements = movements.filter(created_at__date__lte=end_date)
        if product:
            movements = movements.filter(product=product)
        if user:
            movements = movements.filter(created_by=user)

        return [movement.get_audit_trail() for movement in movements.order_by('-created_at')]

    @classmethod
    def detect_anomalies(cls, owner, days_back=30):
        """Detect potential anomalies in stock movements"""
        from django.utils import timezone
        from datetime import timedelta
        from django.db.models import Sum, Avg, Count

        cutoff_date = timezone.now().date() - timedelta(days=days_back)
        recent_movements = cls.objects.filter(owner=owner, created_at__date__gte=cutoff_date)

        anomalies = []

        # Check for unusual quantity movements
        avg_quantities = recent_movements.values('movement_type').annotate(
            avg_quantity=Avg('quantity'),
            count=Count('id')
        )

        for movement in recent_movements:
            movement_avg = next(
                (item['avg_quantity'] for item in avg_quantities
                 if item['movement_type'] == movement.movement_type),
                0
            )

            # Flag movements that are significantly larger than average
            if movement_avg != 0 and abs(movement.quantity) > (abs(movement_avg) * 3):
                anomalies.append({
                    'type': 'unusual_quantity',
                    'movement_id': movement.id,
                    'description': f'Movement quantity {movement.quantity} is {abs(movement.quantity) / movement_avg:.1f}x larger than average',
                    'movement': movement.get_audit_trail()
                })

        # Check for rapid consecutive movements on same product
        products_with_rapid_movements = recent_movements.values('product').annotate(
            movement_count=Count('id')
        ).filter(movement_count__gte=10)  # 10+ movements in 30 days

        for product_data in products_with_rapid_movements:
            product_movements = recent_movements.filter(product_id=product_data['product'])
            anomalies.append({
                'type': 'rapid_movements',
                'product_id': product_data['product'],
                'description': f'{product_data["movement_count"]} movements in {days_back} days',
                'movement_count': product_data['movement_count']
            })

        return anomalies


# POS Integration Models

class POSTransaction(models.Model):
    """Complete POS transaction with inventory integration"""
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('loan', 'Loan (Customer Credit)'),
        ('card', 'Card/Digital'),
        ('mixed', 'Mixed (Cash + Loan)'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('pending', 'Payment Pending'),
        ('partial', 'Partially Paid'),
        ('paid', 'Fully Paid'),
        ('refunded', 'Refunded'),
    ]

    TRANSACTION_STATUS_CHOICES = [
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
        ('refunded', 'Refunded'),
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='pos_transactions')
    customer = models.ForeignKey('customers.Customer', on_delete=models.SET_NULL, null=True, blank=True, related_name='pos_transactions')
    location = models.ForeignKey('Location', on_delete=models.SET_NULL, null=True, blank=True, related_name='pos_transactions')

    # Transaction Details
    transaction_number = models.CharField(max_length=50, unique=True, help_text="Unique transaction identifier")
    status = models.CharField(max_length=20, choices=TRANSACTION_STATUS_CHOICES, default='active')

    # Financial Fields
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    tax_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    discount_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Payment Information
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='pending')
    amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    cash_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    loan_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), help_text="Amount given as loan to customer")
    card_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    change_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Enhanced Loan Tracking
    loan_due_date = models.DateField(null=True, blank=True, help_text="When loan amount is due")
    loan_interest_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'), help_text="Interest rate for loan amount")
    loan_status = models.CharField(max_length=20, choices=[
        ('active', 'Active Loan'),
        ('paid', 'Loan Paid'),
        ('overdue', 'Overdue'),
        ('partial', 'Partially Paid')
    ], default='active', help_text="Status of loan component")

    # Transaction Metadata
    notes = models.TextField(blank=True, help_text="Transaction notes or special instructions")
    cashier = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cashier_transactions',
        help_text="Staff member who processed the transaction"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner', 'status', 'created_at']),
            models.Index(fields=['customer', 'created_at']),
            models.Index(fields=['transaction_number']),
            models.Index(fields=['payment_status', 'created_at']),
            models.Index(fields=['cashier', 'created_at']),
        ]

    def __str__(self):
        return f"POS-{self.transaction_number}"

    @property
    def remaining_balance(self):
        """Calculate remaining balance"""
        return max(Decimal('0.00'), self.total_amount - self.amount_paid)

    @property
    def is_fully_paid(self):
        """Check if transaction is fully paid"""
        return self.amount_paid >= self.total_amount

    @property
    def total_items(self):
        """Get total number of items in transaction"""
        return sum(item.quantity for item in self.items.all())

    @property
    def total_item_count(self):
        """Get count of different items (line items)"""
        return self.items.count()

    def generate_transaction_number(self):
        """Generate unique transaction number"""
        from django.utils import timezone
        import random
        timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
        random_suffix = random.randint(1000, 9999)
        return f"{timestamp}-{self.owner.id}-{random_suffix}"

    def calculate_totals(self):
        """Recalculate transaction totals from items"""
        items = self.items.all()
        self.subtotal = sum(item.line_total for item in items)
        self.tax_total = sum(item.tax_amount for item in items)
        self.total_amount = self.subtotal + self.tax_total - self.discount_total

    def complete_transaction(self):
        """Mark transaction as completed and process stock deductions"""
        if self.status != 'active':
            raise ValueError(f"Cannot complete transaction with status: {self.status}")

        # Process each item
        for item in self.items.all():
            item.process_stock_deduction()

        # Update transaction status
        self.status = 'completed'
        self.completed_at = timezone.now()
        self.payment_status = 'paid' if self.is_fully_paid else 'partial'
        self.save()

        return True

    def cancel_transaction(self, reason=''):
        """Cancel transaction and restore any reserved stock"""
        if self.status == 'completed':
            raise ValueError("Cannot cancel completed transaction")

        self.status = 'cancelled'
        self.notes = f"{self.notes}\nCancelled: {reason}".strip()
        self.save()

        return True

    def save(self, *args, **kwargs):
        if not self.transaction_number:
            self.transaction_number = self.generate_transaction_number()
        super().save(*args, **kwargs)


class POSTransactionItem(models.Model):
    """Individual line item in a POS transaction"""
    transaction = models.ForeignKey(POSTransaction, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(EnhancedProduct, on_delete=models.PROTECT, related_name='pos_transaction_items')

    # Item Details
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        validators=[MinValueValidator(Decimal('0.001'))]
    )
    unit_price = models.DecimalField(max_digits=12, decimal_places=2)
    original_price = models.DecimalField(max_digits=12, decimal_places=2, help_text="Original product price")

    # Discounts
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    discount_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Tax
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    tax_amount = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Calculated Fields
    line_total = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))

    # Stock Integration
    stock_movements = models.JSONField(default=list, help_text="Record of stock movements for this item")
    stock_processed = models.BooleanField(default=False, help_text="Whether stock has been deducted")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['id']
        indexes = [
            models.Index(fields=['transaction', 'product']),
            models.Index(fields=['product', 'created_at']),
            models.Index(fields=['stock_processed']),
        ]

    def __str__(self):
        return f"{self.product.name} x {self.quantity}"

    @property
    def effective_unit_price(self):
        """Get unit price after discount"""
        if self.discount_amount > 0:
            return self.unit_price - (self.discount_amount / self.quantity)
        elif self.discount_percent > 0:
            return self.unit_price * (1 - self.discount_percent / 100)
        return self.unit_price

    def calculate_amounts(self):
        """Calculate all monetary amounts for this line item"""
        # Calculate discount amount if percentage is set
        if self.discount_percent > 0 and self.discount_amount == 0:
            self.discount_amount = (self.unit_price * self.quantity * self.discount_percent) / 100

        # Calculate line total
        base_amount = self.unit_price * self.quantity
        discounted_amount = base_amount - self.discount_amount
        self.line_total = discounted_amount

        # Calculate tax
        if self.tax_rate > 0:
            self.tax_amount = (discounted_amount * self.tax_rate) / 100
        else:
            self.tax_amount = Decimal('0.00')

    def process_stock_deduction(self):
        """Process stock deduction using inventory system"""
        if self.stock_processed:
            return True

        try:
            # Use the sophisticated inventory stock consumption
            success, message, movements = self.product.consume_stock(
                quantity=self.quantity,  # Use actual decimal quantity
                location=self.transaction.location,
                reference_number=self.transaction.transaction_number,
                reference_type='pos_sale',
                notes=f'POS Sale - Transaction: {self.transaction.transaction_number}'
            )

            if success:
                # Record the stock movements
                self.stock_movements = [movement.id for movement in movements]
                self.stock_processed = True
                self.save(update_fields=['stock_movements', 'stock_processed'])
                return True
            else:
                raise ValueError(f"Stock deduction failed: {message}")

        except Exception as e:
            raise ValueError(f"Error processing stock deduction: {str(e)}")

    def save(self, *args, **kwargs):
        # Always recalculate amounts when saving
        self.calculate_amounts()
        super().save(*args, **kwargs)


# Phase 2 Enhanced Models

class BatchOperation(models.Model):
    """Track batch operations like split, merge, quality updates"""
    OPERATION_TYPES = [
        ('split', 'Split Batch'),
        ('merge', 'Merge Batches'),
        ('quality_update', 'Quality Update'),
        ('temperature_breach', 'Temperature Breach'),
        ('status_change', 'Status Change'),
        ('parent_change', 'Parent Batch Change'),
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='batch_operations')
    operation_type = models.CharField(max_length=20, choices=OPERATION_TYPES)
    source_batches = models.JSONField(help_text="List of source batch IDs and details")
    target_batches = models.JSONField(help_text="List of target batch IDs and details")
    operation_metadata = models.JSONField(default=dict, help_text="Additional operation data")
    reason = models.TextField(help_text="Reason for the operation")

    # Audit fields
    performed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='batch_operations_performed'
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner', 'operation_type', 'created_at']),
            models.Index(fields=['created_at']),
            models.Index(fields=['performed_by', 'created_at']),
        ]

    def __str__(self):
        return f"{self.get_operation_type_display()} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

    def get_summary(self):
        """Get operation summary for display"""
        source_count = len(self.source_batches) if isinstance(self.source_batches, list) else 0
        target_count = len(self.target_batches) if isinstance(self.target_batches, list) else 0

        return {
            'operation_type': self.get_operation_type_display(),
            'source_batches_count': source_count,
            'target_batches_count': target_count,
            'performed_by': self.performed_by.mobile_number if self.performed_by else 'System',
            'timestamp': self.created_at,
            'reason': self.reason
        }


class ExpiryAlert(models.Model):
    """Track expiry alerts for batches"""
    ALERT_TYPES = [
        ('approaching_expiry', 'Approaching Expiry'),
        ('expired', 'Expired'),
        ('quality_degradation', 'Quality Degradation'),
        ('temperature_breach', 'Temperature Breach'),
        ('shelf_life_exceeded', 'Shelf Life Exceeded'),
    ]

    ALERT_STATUS = [
        ('active', 'Active'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
        ('dismissed', 'Dismissed'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='expiry_alerts')
    batch = models.ForeignKey('StockBatch', on_delete=models.CASCADE, related_name='expiry_alerts')
    alert_type = models.CharField(max_length=20, choices=ALERT_TYPES)
    status = models.CharField(max_length=15, choices=ALERT_STATUS, default='active')
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='medium')

    alert_date = models.DateTimeField(auto_now_add=True)
    expiry_date = models.DateField()
    days_until_expiry = models.IntegerField()
    message = models.TextField()

    # Alert metadata
    alert_metadata = models.JSONField(default=dict, help_text="Additional alert data")

    # Acknowledgment tracking
    acknowledged_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='acknowledged_alerts'
    )
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-alert_date']
        indexes = [
            models.Index(fields=['owner', 'status', 'alert_date']),
            models.Index(fields=['batch', 'alert_type']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['priority', 'status']),
            models.Index(fields=['alert_type', 'status']),
        ]

    def __str__(self):
        return f"{self.get_alert_type_display()} - {self.batch.batch_number}"

    def acknowledge(self, user, notes=""):
        """Acknowledge the alert"""
        self.status = 'acknowledged'
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()

    def resolve(self, user, notes=""):
        """Resolve the alert"""
        self.status = 'resolved'
        self.acknowledged_by = user
        self.acknowledged_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()

    @property
    def is_overdue(self):
        """Check if alert is overdue (past expiry date)"""
        return timezone.now().date() > self.expiry_date


class StockAnomalyDetection(models.Model):
    """Track stock anomalies and patterns"""
    ANOMALY_TYPES = [
        ('unusual_quantity', 'Unusual Quantity Movement'),
        ('rapid_movements', 'Rapid Consecutive Movements'),
        ('quality_drop', 'Quality Score Drop'),
        ('temperature_breach', 'Temperature Breach'),
        ('unauthorized_movement', 'Unauthorized Movement'),
        ('negative_stock_warning', 'Negative Stock Warning'),
        ('price_variance', 'Price Variance'),
        ('pattern_deviation', 'Pattern Deviation'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('critical', 'Critical'),
    ]

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='stock_anomalies')
    anomaly_type = models.CharField(max_length=25, choices=ANOMALY_TYPES)
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='medium')

    # Related objects (at least one should be set)
    product = models.ForeignKey('EnhancedProduct', on_delete=models.CASCADE, null=True, blank=True, related_name='anomalies')
    batch = models.ForeignKey('StockBatch', on_delete=models.CASCADE, null=True, blank=True, related_name='anomalies')
    movement = models.ForeignKey('StockMovement', on_delete=models.CASCADE, null=True, blank=True, related_name='anomalies')

    detected_at = models.DateTimeField(auto_now_add=True)
    anomaly_data = models.JSONField(default=dict, help_text="Detailed anomaly information")
    severity_score = models.PositiveIntegerField(
        default=50,
        validators=[MaxValueValidator(100)],
        help_text="Severity score 0-100"
    )

    # Review and resolution
    is_false_positive = models.BooleanField(default=False)
    is_resolved = models.BooleanField(default=False)
    reviewed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='reviewed_anomalies'
    )
    reviewed_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    class Meta:
        ordering = ['-detected_at']
        indexes = [
            models.Index(fields=['owner', 'anomaly_type', 'detected_at']),
            models.Index(fields=['severity_score', 'is_false_positive']),
            models.Index(fields=['detected_at']),
            models.Index(fields=['severity', 'is_resolved']),
            models.Index(fields=['product', 'anomaly_type']),
            models.Index(fields=['batch', 'anomaly_type']),
        ]

    def __str__(self):
        return f"{self.get_anomaly_type_display()} - {self.get_severity_display()}"

    def mark_false_positive(self, user, notes=""):
        """Mark anomaly as false positive"""
        self.is_false_positive = True
        self.reviewed_by = user
        self.reviewed_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()

    def resolve(self, user, notes=""):
        """Resolve the anomaly"""
        self.is_resolved = True
        self.reviewed_by = user
        self.reviewed_at = timezone.now()
        if notes:
            self.resolution_notes = notes
        self.save()

    @property
    def related_object_name(self):
        """Get name of related object for display"""
        if self.product:
            return f"Product: {self.product.name}"
        elif self.batch:
            return f"Batch: {self.batch.batch_number}"
        elif self.movement:
            return f"Movement: {self.movement.id}"
        return "Unknown"

    @classmethod
    def create_anomaly(cls, owner, anomaly_type, severity='medium', **kwargs):
        """Create a new anomaly detection record"""
        anomaly = cls.objects.create(
            owner=owner,
            anomaly_type=anomaly_type,
            severity=severity,
            product=kwargs.get('product'),
            batch=kwargs.get('batch'),
            movement=kwargs.get('movement'),
            anomaly_data=kwargs.get('anomaly_data', {}),
            severity_score=kwargs.get('severity_score', 50)
        )

        # Log the anomaly creation
        logger.info(f"Created anomaly {anomaly.id}: {anomaly_type} for {anomaly.related_object_name}")

        return anomaly


# Customer Loan Management Models

class CustomerLoan(models.Model):
    """
    Comprehensive customer loan management for POS transactions
    Tracks loans given to customers with payment history
    """
    LOAN_STATUS_CHOICES = [
        ('active', 'Active Loan'),
        ('paid', 'Fully Paid'),
        ('overdue', 'Overdue'),
        ('partial', 'Partially Paid'),
        ('defaulted', 'Defaulted'),
        ('cancelled', 'Cancelled'),
    ]

    # Core loan information
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='customer_loans')
    customer = models.ForeignKey('customers.Customer', on_delete=models.CASCADE, related_name='loans')
    transaction = models.ForeignKey(POSTransaction, on_delete=models.CASCADE, related_name='loans', null=True, blank=True)

    # Loan details
    loan_amount = models.DecimalField(max_digits=12, decimal_places=2, help_text="Original loan amount")
    amount_paid = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'), help_text="Amount paid back")
    interest_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'), help_text="Interest rate percentage")

    # Dates
    loan_date = models.DateTimeField(auto_now_add=True)
    due_date = models.DateField(help_text="When full payment is due")
    last_payment_date = models.DateTimeField(null=True, blank=True)

    # Status and tracking
    status = models.CharField(max_length=20, choices=LOAN_STATUS_CHOICES, default='active')
    notes = models.TextField(blank=True, help_text="Loan terms and conditions")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['owner', 'due_date']),
            models.Index(fields=['status', 'due_date']),
        ]

    def __str__(self):
        return f"Loan #{self.id} - {self.customer.display_name} - Rs.{self.loan_amount}"

    @property
    def remaining_balance(self):
        """Calculate remaining loan balance"""
        return self.loan_amount - self.amount_paid

    @property
    def total_with_interest(self):
        """Calculate total amount with interest"""
        interest = (self.loan_amount * self.interest_rate) / 100
        return self.loan_amount + interest

    @property
    def interest_amount(self):
        """Calculate interest amount"""
        return (self.loan_amount * self.interest_rate) / 100

    @property
    def is_overdue(self):
        """Check if loan is overdue"""
        from django.utils import timezone
        return self.due_date < timezone.now().date() and self.status == 'active'

    @property
    def days_overdue(self):
        """Get number of days overdue"""
        if not self.is_overdue:
            return 0
        from django.utils import timezone
        return (timezone.now().date() - self.due_date).days

    def record_payment(self, amount, payment_method='cash', notes=''):
        """Record a payment against this loan"""
        from django.utils import timezone

        # Create payment record
        payment = LoanPayment.objects.create(
            loan=self,
            amount=amount,
            payment_method=payment_method,
            notes=notes,
            payment_date=timezone.now()
        )

        # Update loan amount paid
        self.amount_paid += amount
        self.last_payment_date = timezone.now()

        # Update status based on payment
        if self.amount_paid >= self.total_with_interest:
            self.status = 'paid'
        elif self.amount_paid > 0:
            self.status = 'partial'

        self.save()
        return payment


class LoanPayment(models.Model):
    """Individual payment records for customer loans"""
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('card', 'Card/Digital'),
        ('transfer', 'Bank Transfer'),
        ('adjustment', 'Adjustment'),
    ]

    loan = models.ForeignKey(CustomerLoan, on_delete=models.CASCADE, related_name='payments')
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    payment_date = models.DateTimeField(auto_now_add=True)
    reference_number = models.CharField(max_length=100, blank=True, help_text="Receipt or reference number")
    notes = models.TextField(blank=True)
    recorded_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['loan', 'payment_date']),
            models.Index(fields=['payment_method', 'payment_date']),
        ]

    def __str__(self):
        return f"Payment Rs.{self.amount} for Loan #{self.loan.id}"
