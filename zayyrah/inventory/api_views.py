from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON>Part<PERSON>arser, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from django.core.cache import cache
from django.db import connection

from .cache_utils import InventoryCache, QueryOptimizer, PerformanceMonitor
from .services import ProductImageService

from .models import (
    Category, Supplier, Location, ProductBrand, EnhancedProduct,
    ProductImage, StockBatch, StockMovement
)
from .serializers import (
    CategoryListSerializer, CategoryDetailSerializer, CategoryCreateUpdateSerializer,
    SupplierListSerializer, SupplierDetailSerializer, SupplierCreateUpdateSerializer,
    LocationListSerializer, LocationDetailSerializer, LocationCreateUpdateSerializer,
    ProductBrandSerializer,
    EnhancedProductListSerializer, EnhancedProductDetailSerializer, EnhancedProductCreateUpdateSerializer,
    Stock<PERSON>atchListSerializer, StockBatchDetailSerializer, StockBatchCreateUpdateSerializer,
    StockMovementListSerializer, StockMovementDetailSerializer, StockMovementCreateSerializer,
    FlutterCompatibleSerializerMixin
)


def clean_response_for_flutter(data):
    """
    Utility function to clean null values from API response data for Flutter compatibility
    """
    mixin = FlutterCompatibleSerializerMixin()
    return mixin.clean_null_values(data)


class IsOwnerPermission:
    """Custom permission to only allow owners of an object to access it."""

    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return obj.owner == request.user


class CategoryViewSet(viewsets.ModelViewSet):
    """Complete category management with tree operations"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['parent', 'level', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'level', 'sort_order', 'created_at']
    ordering = ['level', 'sort_order', 'name']

    def get_queryset(self):
        return Category.objects.filter(owner=self.request.user).select_related('parent')

    def get_serializer_class(self):
        if self.action == 'list':
            return CategoryListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return CategoryCreateUpdateSerializer
        return CategoryDetailSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            category = serializer.save()
            return Response({
                'success': True,
                'message': 'Category created successfully',
                'data': CategoryDetailSerializer(category).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_category = serializer.save()
            return Response({
                'success': True,
                'message': 'Category updated successfully',
                'data': CategoryDetailSerializer(updated_category).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        # Check if category has products
        if instance.products.exists():
            return Response({
                'success': False,
                'message': 'Cannot delete category that contains products'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.delete()
        return Response({
            'success': True,
            'message': 'Category deleted successfully'
        })

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """Get full category tree with optimized structure and caching"""
        start_time = timezone.now()
        initial_query_count = len(connection.queries)

        include_products_count = request.query_params.get('include_products_count', 'false').lower() == 'true'

        # Try to get from cache first
        cache_key = f"category_tree_{request.user.id}_{include_products_count}"
        tree_data = InventoryCache.get_category_tree(request.user.id)

        if tree_data is None:
            # Cache miss - generate tree data
            tree_data = Category.get_tree_data(request.user, include_products_count=include_products_count)
            InventoryCache.set_category_tree(request.user.id, tree_data)
            cache_hit = False
        else:
            cache_hit = True

        # Performance monitoring
        end_time = timezone.now()
        execution_time = (end_time - start_time).total_seconds()
        query_count = len(connection.queries) - initial_query_count

        PerformanceMonitor.log_query_performance(
            'CategoryViewSet.tree',
            query_count,
            1 if cache_hit else 0,
            execution_time
        )

        return Response({
            'success': True,
            'data': {
                'tree': tree_data,
                'total_categories': len([item for item in tree_data if item['level'] == 0]) +
                                  sum(self._count_tree_items(item['children']) for item in tree_data),
                'cached': cache_hit
            }
        })

    def _count_tree_items(self, items):
        """Recursively count items in tree structure"""
        if not items:
            return 0
        count = len(items)
        for item in items:
            count += self._count_tree_items(item.get('children', []))
        return count

    @action(detail=True, methods=['post'])
    def move(self, request, pk=None):
        """Move category to different parent"""
        category = self.get_object()
        new_parent_id = request.data.get('new_parent_id')

        try:
            new_parent = None
            if new_parent_id:
                new_parent = Category.objects.get(id=new_parent_id, owner=request.user)

            if category.can_move_to(new_parent):
                old_parent_id = category.parent_id
                category.move_to(new_parent)

                return Response({
                    'success': True,
                    'message': 'Category moved successfully',
                    'data': {
                        'category_id': category.id,
                        'old_parent_id': old_parent_id,
                        'new_parent_id': new_parent_id,
                        'new_level': category.level
                    }
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Cannot move category to this parent - would create circular reference or exceed depth limit'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Category.DoesNotExist:
            return Response({
                'success': False,
                'message': 'New parent category not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'])
    def bulk_move(self, request):
        """Perform bulk category move operations"""
        move_operations = request.data.get('operations', [])

        if not move_operations:
            return Response({
                'success': False,
                'message': 'No move operations provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        result = Category.bulk_move(request.user, move_operations)

        return Response({
            'success': True,
            'message': f'Bulk move completed: {len(result["successful_moves"])} successful, {len(result["errors"])} failed',
            'data': result
        })

    @action(detail=False, methods=['post'])
    def reorder(self, request):
        """Reorder categories within the same parent"""
        category_orders = request.data.get('category_orders', [])

        if not category_orders:
            return Response({
                'success': False,
                'message': 'No category orders provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            updated_categories = []
            for order_data in category_orders:
                category_id = order_data['category_id']
                sort_order = order_data['sort_order']

                category = Category.objects.get(id=category_id, owner=request.user)
                category.sort_order = sort_order
                category.save()
                updated_categories.append({
                    'category_id': category_id,
                    'sort_order': sort_order
                })

            return Response({
                'success': True,
                'message': 'Categories reordered successfully',
                'data': {
                    'updated_categories': updated_categories
                }
            })

        except Category.DoesNotExist:
            return Response({
                'success': False,
                'message': 'One or more categories not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def ancestors(self, request, pk=None):
        """Get category ancestors (breadcrumb path)"""
        category = self.get_object()
        ancestors = category.get_ancestors()
        ancestors.reverse()  # Show from root to current

        serializer = CategoryListSerializer(ancestors, many=True)
        return Response({
            'success': True,
            'data': {
                'category': CategoryDetailSerializer(category).data,
                'ancestors': serializer.data,
                'full_path': category.full_path
            }
        })

    @action(detail=True, methods=['get'])
    def descendants(self, request, pk=None):
        """Get all descendant categories"""
        category = self.get_object()
        descendants = category.get_all_descendants()

        serializer = CategoryListSerializer(descendants, many=True)
        return Response({
            'success': True,
            'data': {
                'category': CategoryDetailSerializer(category).data,
                'descendants': serializer.data,
                'descendants_count': len(descendants)
            }
        })


class SupplierViewSet(viewsets.ModelViewSet):
    """Supplier management"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'rating']
    search_fields = ['name', 'contact_person', 'phone', 'email']
    ordering_fields = ['name', 'rating', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        return Supplier.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.action == 'list':
            return SupplierListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return SupplierCreateUpdateSerializer
        return SupplierDetailSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            supplier = serializer.save()
            return Response({
                'success': True,
                'message': 'Supplier created successfully',
                'data': SupplierDetailSerializer(supplier).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_supplier = serializer.save()
            return Response({
                'success': True,
                'message': 'Supplier updated successfully',
                'data': SupplierDetailSerializer(updated_supplier).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({
            'success': True,
            'message': 'Supplier deleted successfully'
        })

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get products from this supplier"""
        supplier = self.get_object()
        products = supplier.products.filter(is_active=True)
        serializer = EnhancedProductListSerializer(products, many=True)
        return Response({
            'success': True,
            'data': {
                'supplier': SupplierDetailSerializer(supplier).data,
                'products': serializer.data
            }
        })

    @action(detail=True, methods=['get'])
    def performance_metrics(self, request, pk=None):
        """Get comprehensive supplier performance metrics"""
        supplier = self.get_object()
        metrics = supplier.get_performance_metrics()

        return Response({
            'success': True,
            'data': {
                'supplier': SupplierDetailSerializer(supplier).data,
                'performance_metrics': metrics
            }
        })

    @action(detail=True, methods=['get'])
    def product_catalog(self, request, pk=None):
        """Get products catalog with pricing history from this supplier"""
        supplier = self.get_object()
        catalog = supplier.get_product_catalog()

        return Response({
            'success': True,
            'data': {
                'supplier': SupplierDetailSerializer(supplier).data,
                'product_catalog': catalog,
                'catalog_size': len(catalog)
            }
        })

    @action(detail=False, methods=['get'])
    def top_performers(self, request):
        """Get top performing suppliers by various metrics"""
        metric = request.query_params.get('metric', 'total_value')
        limit = int(request.query_params.get('limit', 10))

        valid_metrics = ['total_value', 'total_orders', 'quality_score', 'delivery_performance', 'recent_orders_30_days']
        if metric not in valid_metrics:
            return Response({
                'success': False,
                'message': f'Invalid metric. Valid options: {", ".join(valid_metrics)}'
            }, status=status.HTTP_400_BAD_REQUEST)

        top_suppliers = Supplier.get_top_suppliers(request.user, limit, metric)

        return Response({
            'success': True,
            'data': {
                'metric_used': metric,
                'top_suppliers': [
                    {
                        'supplier': SupplierDetailSerializer(item['supplier']).data,
                        'metrics': item['metrics']
                    } for item in top_suppliers
                ]
            }
        })

    @action(detail=False, methods=['get'])
    def analytics_dashboard(self, request):
        """Get supplier analytics dashboard data"""
        suppliers = self.get_queryset()

        # Overall metrics
        total_suppliers = suppliers.count()
        active_suppliers = suppliers.filter(is_active=True).count()

        # Rating distribution
        rating_distribution = {}
        for rating in range(1, 6):
            count = suppliers.filter(rating=rating).count()
            rating_distribution[f'{rating}_star'] = count

        # Recent activity
        from django.utils import timezone
        from datetime import timedelta

        thirty_days_ago = timezone.now().date() - timedelta(days=30)
        recent_orders_count = StockBatch.objects.filter(
            owner=request.user,
            received_date__gte=thirty_days_ago
        ).count()

        # Top suppliers by different metrics
        top_by_value = Supplier.get_top_suppliers(request.user, 5, 'total_value')
        top_by_orders = Supplier.get_top_suppliers(request.user, 5, 'total_orders')
        top_by_quality = Supplier.get_top_suppliers(request.user, 5, 'quality_score')

        return Response({
            'success': True,
            'data': {
                'overview': {
                    'total_suppliers': total_suppliers,
                    'active_suppliers': active_suppliers,
                    'inactive_suppliers': total_suppliers - active_suppliers,
                    'rating_distribution': rating_distribution,
                    'recent_orders_30_days': recent_orders_count
                },
                'top_performers': {
                    'by_value': [
                        {
                            'id': item['supplier'].id,
                            'name': item['supplier'].name,
                            'value': float(item['metrics']['total_value'])
                        } for item in top_by_value
                    ],
                    'by_orders': [
                        {
                            'id': item['supplier'].id,
                            'name': item['supplier'].name,
                            'orders': item['metrics']['total_orders']
                        } for item in top_by_orders
                    ],
                    'by_quality': [
                        {
                            'id': item['supplier'].id,
                            'name': item['supplier'].name,
                            'quality_score': item['metrics']['quality_score']
                        } for item in top_by_quality
                    ]
                }
            }
        })


class LocationViewSet(viewsets.ModelViewSet):
    """Location management"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['location_type', 'is_active']
    search_fields = ['name', 'code', 'address']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        return Location.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.action == 'list':
            return LocationListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return LocationCreateUpdateSerializer
        return LocationDetailSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            location = serializer.save()
            return Response({
                'success': True,
                'message': 'Location created successfully',
                'data': LocationDetailSerializer(location).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_location = serializer.save()
            return Response({
                'success': True,
                'message': 'Location updated successfully',
                'data': LocationDetailSerializer(updated_location).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({
            'success': True,
            'message': 'Location deleted successfully'
        })

    @action(detail=True, methods=['get'])
    def stock(self, request, pk=None):
        """Get stock summary for location"""
        location = self.get_object()
        stock_batches = location.stock_batches.filter(status='active', quantity_available__gt=0)

        total_value = sum(batch.batch_value for batch in stock_batches)
        products_count = stock_batches.values('product').distinct().count()

        return Response({
            'success': True,
            'data': {
                'location': LocationDetailSerializer(location).data,
                'stock_summary': {
                    'total_batches': stock_batches.count(),
                    'unique_products': products_count,
                    'total_value': total_value
                }
            }
        })


class ProductBrandViewSet(viewsets.ModelViewSet):
    """Product brand management"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']
    serializer_class = ProductBrandSerializer

    def get_queryset(self):
        return ProductBrand.objects.filter(owner=self.request.user)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            brand = serializer.save()
            return Response({
                'success': True,
                'message': 'Brand created successfully',
                'data': serializer.data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Brand updated successfully',
                'data': serializer.data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({
            'success': True,
            'message': 'Brand deleted successfully'
        })


class EnhancedProductViewSet(viewsets.ModelViewSet):
    """Advanced product management with stock integration"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'brand', 'supplier', 'is_active', 'track_stock', 'is_featured']
    search_fields = ['name', 'description', 'sku', 'barcode', 'tags']
    ordering_fields = ['name', 'selling_price', 'created_at', 'reorder_level']
    ordering = ['name']

    def get_queryset(self):
        queryset = EnhancedProduct.objects.filter(owner=self.request.user)
        queryset = QueryOptimizer.optimize_product_queryset(queryset)

        # Filter for low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock and low_stock.lower() == 'true':
            # This would require custom filtering since current_stock is a property
            # For now, we'll get all products and filter manually
            pass

        return queryset

    def get_serializer_class(self):
        if self.action == 'list':
            return EnhancedProductListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return EnhancedProductCreateUpdateSerializer
        return EnhancedProductDetailSerializer

    def list(self, request, *args, **kwargs):
        """
        Enhanced product list with comprehensive filtering

        Query Parameters:
        - status: 'active', 'inactive', 'all' (default: 'active')
        - category: category_id for exact match
        - category_tree: category_id for hierarchical search (includes subcategories)
        - brand: brand_id
        - supplier: supplier_id
        - location: location_id (products with stock in this location)
        - stock_status: 'in_stock', 'low_stock', 'out_of_stock', 'all'
        - search: search query for name, sku, barcode, description
        - min_price: minimum selling price
        - max_price: maximum selling price
        - featured: 'true' for featured products only
        - tags: comma-separated tags
        - ordering: '-created_at', 'name', '-selling_price', etc.
        - page: page number (default: 1)
        - page_size: items per page (default: 20, max: 100)
        """
        start_time = timezone.now()

        # ========================================
        # 1. BASE QUERYSET WITH OWNERSHIP FILTER
        # ========================================
        queryset = self.get_queryset()

        # ========================================
        # 2. STATUS FILTER (PRIMARY FILTER)
        # ========================================
        status = request.query_params.get('status', 'active').lower()
        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'all':
            pass  # No filter, show all products
        else:
            return Response({
                'success': False,
                'message': 'Invalid status. Use: active, inactive, or all'
            }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 3. CATEGORY FILTERS
        # ========================================
        # Exact category match
        category_id = request.query_params.get('category')
        if category_id:
            try:
                category = Category.objects.get(id=int(category_id), owner=request.user)
                queryset = queryset.filter(category=category)
            except (Category.DoesNotExist, ValueError):
                return Response({
                    'success': False,
                    'message': 'Invalid category ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Hierarchical category search (includes subcategories)
        category_tree_id = request.query_params.get('category_tree')
        if category_tree_id and not category_id:  # Don't apply if exact category is already set
            try:
                category = Category.objects.get(id=int(category_tree_id), owner=request.user)
                descendant_categories = category.get_all_descendants()
                category_ids = [category.id] + [desc.id for desc in descendant_categories]
                queryset = queryset.filter(category_id__in=category_ids)
            except (Category.DoesNotExist, ValueError):
                return Response({
                    'success': False,
                    'message': 'Invalid category_tree ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 4. BRAND AND SUPPLIER FILTERS
        # ========================================
        brand_id = request.query_params.get('brand')
        if brand_id:
            try:
                queryset = queryset.filter(brand_id=int(brand_id))
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid brand ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        supplier_id = request.query_params.get('supplier')
        if supplier_id:
            try:
                queryset = queryset.filter(supplier_id=int(supplier_id))
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid supplier ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 5. LOCATION FILTER (products with stock)
        # ========================================
        location_id = request.query_params.get('location')
        if location_id:
            try:
                queryset = queryset.filter(
                    stock_batches__location_id=int(location_id),
                    stock_batches__status='active',
                    stock_batches__quantity_available__gt=0
                ).distinct()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid location ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 6. STOCK STATUS FILTER
        # ========================================
        stock_status = request.query_params.get('stock_status', 'all').lower()
        if stock_status != 'all':
            if stock_status == 'in_stock':
                # Products with available stock
                queryset = queryset.filter(
                    stock_batches__status='active',
                    stock_batches__quantity_available__gt=0
                ).distinct()
            elif stock_status == 'low_stock':
                # Filter products with low stock (this requires evaluation)
                low_stock_products = []
                for product in queryset:
                    if product.is_low_stock and not product.is_out_of_stock:
                        low_stock_products.append(product.id)
                queryset = queryset.filter(id__in=low_stock_products)
            elif stock_status == 'out_of_stock':
                # Products with no available stock
                out_of_stock_products = []
                for product in queryset:
                    if product.is_out_of_stock:
                        out_of_stock_products.append(product.id)
                queryset = queryset.filter(id__in=out_of_stock_products)
            else:
                return Response({
                    'success': False,
                    'message': 'Invalid stock_status. Use: in_stock, low_stock, out_of_stock, or all'
                }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 7. SEARCH FUNCTIONALITY
        # ========================================
        search_query = request.query_params.get('search', '').strip()
        if search_query:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(barcode__icontains=search_query) |
                Q(tags__icontains=search_query) |
                Q(category__name__icontains=search_query) |
                Q(brand__name__icontains=search_query) |
                Q(supplier__name__icontains=search_query)
            ).distinct()

        # ========================================
        # 8. PRICE RANGE FILTERS
        # ========================================
        min_price = request.query_params.get('min_price')
        if min_price:
            try:
                queryset = queryset.filter(selling_price__gte=float(min_price))
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid min_price value'
                }, status=status.HTTP_400_BAD_REQUEST)

        max_price = request.query_params.get('max_price')
        if max_price:
            try:
                queryset = queryset.filter(selling_price__lte=float(max_price))
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid max_price value'
                }, status=status.HTTP_400_BAD_REQUEST)

        # ========================================
        # 9. FEATURED PRODUCTS FILTER
        # ========================================
        featured = request.query_params.get('featured')
        if featured and featured.lower() == 'true':
            queryset = queryset.filter(is_featured=True)

        # ========================================
        # 10. TAGS FILTER
        # ========================================
        tags = request.query_params.get('tags')
        if tags:
            tag_list = [tag.strip() for tag in tags.split(',') if tag.strip()]
            if tag_list:
                from django.db.models import Q
                tag_query = Q()
                for tag in tag_list:
                    tag_query |= Q(tags__icontains=tag)
                queryset = queryset.filter(tag_query)

        # ========================================
        # 11. ORDERING
        # ========================================
        ordering = request.query_params.get('ordering', 'name')
        valid_orderings = [
            'name', '-name', 'selling_price', '-selling_price',
            'created_at', '-created_at', 'updated_at', '-updated_at',
            'reorder_level', '-reorder_level', 'sku', '-sku'
        ]

        if ordering in valid_orderings:
            queryset = queryset.order_by(ordering)
        else:
            queryset = queryset.order_by('name')  # Default ordering

        # ========================================
        # 12. PAGINATION
        # ========================================
        page_size = min(int(request.query_params.get('page_size', 20)), 100)  # Max 100 items
        page = self.paginate_queryset(queryset)

        # ========================================
        # 13. SERIALIZE DATA
        # ========================================
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_response = self.get_paginated_response(serializer.data)

            # ========================================
            # 14. ENHANCED RESPONSE WITH METADATA
            # ========================================
            end_time = timezone.now()
            execution_time = (end_time - start_time).total_seconds()

            return Response({
                'success': True,
                'data': {
                    **paginated_response.data,
                    'filters_applied': {
                        'status': status,
                        'category': category_id,
                        'category_tree': category_tree_id,
                        'brand': brand_id,
                        'supplier': supplier_id,
                        'location': location_id,
                        'stock_status': stock_status,
                        'search': search_query,
                        'min_price': min_price,
                        'max_price': max_price,
                        'featured': featured,
                        'tags': tags,
                        'ordering': ordering
                    },
                    'metadata': {
                        'execution_time_seconds': round(execution_time, 3),
                        'total_products_in_system': EnhancedProduct.objects.filter(owner=request.user).count(),
                        'filters_count': sum(1 for param in [
                            category_id, category_tree_id, brand_id, supplier_id,
                            location_id, search_query, min_price, max_price,
                            featured, tags
                        ] if param) + (1 if stock_status != 'all' else 0) + (1 if status != 'active' else 0)
                    }
                }
            })

        # Non-paginated response (fallback)
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': len(queryset) if isinstance(queryset, list) else queryset.count(),
                'results': serializer.data,
                'filters_applied': {
                    'status': status,
                    'category': category_id,
                    'category_tree': category_tree_id,
                    'brand': brand_id,
                    'supplier': supplier_id,
                    'location': location_id,
                    'stock_status': stock_status,
                    'search': search_query,
                    'min_price': min_price,
                    'max_price': max_price,
                    'featured': featured,
                    'tags': tags,
                    'ordering': ordering
                }
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            product = serializer.save()
            return Response({
                'success': True,
                'message': 'Product created successfully',
                'data': EnhancedProductDetailSerializer(product).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_product = serializer.save()
            return Response({
                'success': True,
                'message': 'Product updated successfully',
                'data': EnhancedProductDetailSerializer(updated_product).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        # Check if product has stock
        if instance.stock_batches.filter(quantity_available__gt=0).exists():
            return Response({
                'success': False,
                'message': 'Cannot delete product that has stock'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.delete()
        return Response({
            'success': True,
            'message': 'Product deleted successfully'
        })

    @action(detail=True, methods=['get'])
    def stock_summary(self, request, pk=None):
        """Get comprehensive stock summary for product"""
        product = self.get_object()
        batches = product.stock_batches.filter(status='active')

        stock_summary = {
            'current_stock': product.current_stock,
            'total_stock_value': product.total_stock_value,
            'is_low_stock': product.is_low_stock,
            'is_out_of_stock': product.is_out_of_stock,
            'reorder_level': product.reorder_level,
            'active_batches_count': batches.count(),
            'expiring_batches': batches.filter(
                expiry_date__lte=timezone.now().date() + timedelta(days=30)
            ).count() if batches.filter(expiry_date__isnull=False).exists() else 0
        }

        return Response({
            'success': True,
            'data': {
                'product': EnhancedProductDetailSerializer(product).data,
                'stock_summary': stock_summary
            }
        })

    @action(detail=True, methods=['post'])
    def adjust_stock(self, request, pk=None):
        """Manual stock adjustment with reason tracking"""
        product = self.get_object()

        required_fields = ['location', 'adjustment_type', 'quantity', 'reason']
        for field in required_fields:
            if field not in request.data:
                return Response({
                    'success': False,
                    'message': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)

        try:
            location_id = request.data['location']
            location = Location.objects.get(id=location_id, owner=request.user)
        except Location.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Location not found'
            }, status=status.HTTP_404_NOT_FOUND)

        adjustment_type = request.data['adjustment_type']
        quantity = int(request.data['quantity'])
        reason = request.data['reason']

        if adjustment_type not in ['increase', 'decrease']:
            return Response({
                'success': False,
                'message': 'adjustment_type must be "increase" or "decrease"'
            }, status=status.HTTP_400_BAD_REQUEST)

        # For simplicity, create a new batch for stock increases
        # In a real implementation, you might want more sophisticated logic
        if adjustment_type == 'increase':
            batch = StockBatch.objects.create(
                owner=request.user,
                product=product,
                location=location,
                batch_number=f"ADJ-{timezone.now().strftime('%Y%m%d%H%M%S')}",
                quantity_received=quantity,
                quantity_available=quantity,
                purchase_price=product.purchase_price,
                notes=f"Manual adjustment: {reason}"
            )

            # Create movement record
            StockMovement.objects.create(
                owner=request.user,
                product=product,
                batch=batch,
                location=location,
                movement_type='adjustment',
                quantity=quantity,
                reference_number=batch.batch_number,
                reference_type='adjustment',
                notes=reason,
                created_by=request.user
            )

            message = f'Stock increased by {quantity} units'
        else:
            # Find the oldest batch with available stock for decrease
            oldest_batch = product.stock_batches.filter(
                location=location,
                status='active',
                quantity_available__gt=0
            ).order_by('received_date', 'created_at').first()

            if not oldest_batch or oldest_batch.quantity_available < quantity:
                return Response({
                    'success': False,
                    'message': 'Insufficient stock to decrease'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Update batch
            oldest_batch.quantity_available -= quantity
            oldest_batch.save()

            # Create movement record
            StockMovement.objects.create(
                owner=request.user,
                product=product,
                batch=oldest_batch,
                location=location,
                movement_type='adjustment',
                quantity=-quantity,
                reference_number=oldest_batch.batch_number,
                reference_type='adjustment',
                notes=reason,
                created_by=request.user
            )

            message = f'Stock decreased by {quantity} units'

        return Response({
            'success': True,
            'message': message,
            'data': {
                'new_stock_level': product.current_stock,
                'adjustment': {
                    'type': adjustment_type,
                    'quantity': quantity,
                    'reason': reason
                }
            }
        })

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products below reorder level"""
        products = self.get_queryset()
        low_stock_products = [p for p in products if p.is_low_stock]

        serializer = EnhancedProductListSerializer(low_stock_products, many=True)
        return Response({
            'success': True,
            'data': {
                'count': len(low_stock_products),
                'results': serializer.data
            }
        })

    @action(detail=True, methods=['post'])
    def reserve_stock(self, request, pk=None):
        """Reserve stock for pending orders"""
        product = self.get_object()

        required_fields = ['quantity', 'location_id']
        for field in required_fields:
            if field not in request.data:
                return Response({
                    'success': False,
                    'message': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)

        try:
            location = Location.objects.get(id=request.data['location_id'], owner=request.user)
        except Location.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Location not found'
            }, status=status.HTTP_404_NOT_FOUND)

        quantity = int(request.data['quantity'])
        allocation_method = request.data.get('allocation_method', 'FIFO')

        success, result = product.reserve_stock(quantity, location, allocation_method)

        if success:
            return Response({
                'success': True,
                'message': 'Stock reserved successfully',
                'data': {
                    'reservations': [
                        {
                            'batch_id': alloc['batch'].id,
                            'batch_number': alloc['batch'].batch_number,
                            'quantity': alloc['quantity']
                        } for alloc in result
                    ]
                }
            })
        else:
            return Response({
                'success': False,
                'message': result
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def transfer_stock(self, request, pk=None):
        """Transfer stock between locations"""
        product = self.get_object()

        required_fields = ['quantity', 'from_location_id', 'to_location_id']
        for field in required_fields:
            if field not in request.data:
                return Response({
                    'success': False,
                    'message': f'{field} is required'
                }, status=status.HTTP_400_BAD_REQUEST)

        try:
            from_location = Location.objects.get(id=request.data['from_location_id'], owner=request.user)
            to_location = Location.objects.get(id=request.data['to_location_id'], owner=request.user)
        except Location.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Location not found'
            }, status=status.HTTP_404_NOT_FOUND)

        quantity = int(request.data['quantity'])
        allocation_method = request.data.get('allocation_method', 'FIFO')

        success, message, movements = product.transfer_stock(quantity, from_location, to_location, allocation_method)

        if success:
            return Response({
                'success': True,
                'message': message,
                'data': {
                    'quantity_transferred': quantity,
                    'from_location': from_location.name,
                    'to_location': to_location.name,
                    'movements_count': len(movements)
                }
            })
        else:
            return Response({
                'success': False,
                'message': message
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['get'])
    def stock_by_location(self, request, pk=None):
        """Get stock levels by location for this product"""
        product = self.get_object()
        location_stock = product.get_stock_by_location()

        return Response({
            'success': True,
            'data': {
                'product': EnhancedProductDetailSerializer(product).data,
                'stock_by_location': location_stock
            }
        })

    @action(detail=True, methods=['get'])
    def stock_valuation(self, request, pk=None):
        """Get stock valuation using different methods"""
        product = self.get_object()
        method = request.query_params.get('method', 'FIFO')

        if method not in ['FIFO', 'LIFO', 'WEIGHTED_AVERAGE']:
            return Response({
                'success': False,
                'message': 'Invalid valuation method. Use FIFO, LIFO, or WEIGHTED_AVERAGE'
            }, status=status.HTTP_400_BAD_REQUEST)

        valuation = product.get_stock_valuation(method)

        return Response({
            'success': True,
            'data': {
                'product': EnhancedProductDetailSerializer(product).data,
                'valuation': valuation
            }
        })

    @action(detail=False, methods=['post'])
    def bulk_stock_update(self, request):
        """Bulk update stock levels for multiple products"""
        updates = request.data.get('updates', [])

        if not updates:
            return Response({
                'success': False,
                'message': 'No stock updates provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        results = {
            'successful_updates': [],
            'errors': []
        }

        for update in updates:
            try:
                product_id = update['product_id']
                location_id = update['location_id']
                quantity = update['quantity']
                operation = update.get('operation', 'add')  # 'add' or 'set'

                product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
                location = Location.objects.get(id=location_id, owner=request.user)

                if operation == 'add':
                    batch, movement = product.add_stock(
                        quantity=quantity,
                        location=location,
                        notes=update.get('notes', 'Bulk stock update')
                    )
                    results['successful_updates'].append({
                        'product_id': product_id,
                        'operation': operation,
                        'quantity': quantity,
                        'new_batch_id': batch.id
                    })
                elif operation == 'set':
                    # This would require more complex logic to adjust to exact quantity
                    current_stock = sum(
                        batch.quantity_available
                        for batch in product.stock_batches.filter(location=location, status='active')
                    )
                    difference = quantity - current_stock

                    if difference > 0:
                        batch, movement = product.add_stock(
                            quantity=difference,
                            location=location,
                            notes=f'Bulk adjustment to set stock to {quantity}'
                        )
                    elif difference < 0:
                        success, message, movements = product.consume_stock(
                            quantity=abs(difference),
                            location=location,
                            notes=f'Bulk adjustment to set stock to {quantity}'
                        )
                        if not success:
                            results['errors'].append({
                                'product_id': product_id,
                                'error': message
                            })
                            continue

                    results['successful_updates'].append({
                        'product_id': product_id,
                        'operation': operation,
                        'quantity': quantity,
                        'adjustment': difference
                    })

            except (EnhancedProduct.DoesNotExist, Location.DoesNotExist):
                results['errors'].append({
                    'product_id': update.get('product_id'),
                    'error': 'Product or location not found'
                })
            except Exception as e:
                results['errors'].append({
                    'product_id': update.get('product_id'),
                    'error': str(e)
                })

        return Response({
            'success': True,
            'message': f'Bulk update completed: {len(results["successful_updates"])} successful, {len(results["errors"])} failed',
            'data': results
        })

    @action(detail=False, methods=['get'])
    def advanced_search(self, request):
        """Advanced search with full-text search and complex filtering"""
        query = request.query_params.get('q', '')
        sort_by = request.query_params.get('sort_by', 'name_asc')

        # Parse filters from query parameters
        filters = {}
        if request.query_params.get('category_id'):
            filters['category_id'] = int(request.query_params.get('category_id'))
        if request.query_params.get('category_tree'):
            filters['category_tree'] = int(request.query_params.get('category_tree'))
        if request.query_params.get('brand_id'):
            filters['brand_id'] = int(request.query_params.get('brand_id'))
        if request.query_params.get('supplier_id'):
            filters['supplier_id'] = int(request.query_params.get('supplier_id'))
        if request.query_params.get('location_id'):
            filters['location_id'] = int(request.query_params.get('location_id'))
        if request.query_params.get('min_price'):
            filters['min_price'] = float(request.query_params.get('min_price'))
        if request.query_params.get('max_price'):
            filters['max_price'] = float(request.query_params.get('max_price'))
        if request.query_params.get('stock_status'):
            filters['stock_status'] = request.query_params.get('stock_status')
        if request.query_params.get('tags'):
            filters['tags'] = request.query_params.get('tags').split(',')
        if request.query_params.get('sku'):
            filters['sku'] = request.query_params.get('sku')
        if request.query_params.get('barcode'):
            filters['barcode'] = request.query_params.get('barcode')

        # Date filters
        if request.query_params.get('created_after'):
            from datetime import datetime
            try:
                filters['created_after'] = datetime.strptime(
                    request.query_params.get('created_after'), '%Y-%m-%d'
                ).date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid created_after date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        if request.query_params.get('created_before'):
            from datetime import datetime
            try:
                filters['created_before'] = datetime.strptime(
                    request.query_params.get('created_before'), '%Y-%m-%d'
                ).date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid created_before date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        # Perform search
        products = EnhancedProduct.advanced_search(
            owner=request.user,
            query=query,
            filters=filters,
            sort_by=sort_by
        )

        # Apply pagination
        page = self.paginate_queryset(products)
        if page is not None:
            serializer = EnhancedProductListSerializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = EnhancedProductListSerializer(products, many=True)
        return Response({
            'success': True,
            'data': {
                'count': products.count(),
                'results': serializer.data
            }
        })

    @action(detail=False, methods=['get'])
    def search_suggestions(self, request):
        """Get search suggestions based on partial query"""
        query = request.query_params.get('q', '')
        limit = int(request.query_params.get('limit', 10))

        if len(query) < 2:
            return Response({
                'success': False,
                'message': 'Query must be at least 2 characters long'
            }, status=status.HTTP_400_BAD_REQUEST)

        suggestions = EnhancedProduct.get_search_suggestions(request.user, query, limit)

        return Response({
            'success': True,
            'data': {
                'query': query,
                'suggestions': suggestions
            }
        })

    @action(detail=False, methods=['get'])
    def filter_options(self, request):
        """Get available filter options for enhanced product list view"""
        from django.db.models import Count

        # Try to get from cache first
        cache_key = f"product_filter_options_{request.user.id}"
        filter_options = cache.get(cache_key)

        if filter_options is None:
            # Cache miss - generate filter options

            # Status options
            status_options = [
                {'value': 'active', 'label': 'Active Products', 'is_default': True},
                {'value': 'inactive', 'label': 'Inactive Products', 'is_default': False},
                {'value': 'all', 'label': 'All Products', 'is_default': False}
            ]

            # Categories with product counts
            categories = Category.objects.filter(
                owner=request.user,
                is_active=True
            ).annotate(
                products_count=Count('products', filter=Q(products__is_active=True))
            ).order_by('level', 'sort_order', 'name')

            category_options = []
            for category in categories:
                category_options.append({
                    'id': category.id,
                    'name': category.name,
                    'level': category.level,
                    'parent_id': category.parent_id,
                    'full_path': category.full_path,
                    'products_count': category.products_count,
                    'has_children': category.subcategories.filter(is_active=True).exists()
                })

            # Brands with product counts
            brands = ProductBrand.objects.filter(
                owner=request.user,
                is_active=True
            ).annotate(
                products_count=Count('products', filter=Q(products__is_active=True))
            ).order_by('name')

            brand_options = [
                {
                    'id': brand.id,
                    'name': brand.name,
                    'products_count': brand.products_count
                } for brand in brands if brand.products_count > 0
            ]

            # Suppliers with product counts
            suppliers = Supplier.objects.filter(
                owner=request.user,
                is_active=True
            ).annotate(
                products_count=Count('products', filter=Q(products__is_active=True))
            ).order_by('name')

            supplier_options = [
                {
                    'id': supplier.id,
                    'name': supplier.name,
                    'products_count': supplier.products_count
                } for supplier in suppliers if supplier.products_count > 0
            ]

            # Locations with stock counts
            locations = Location.objects.filter(
                owner=request.user,
                is_active=True
            ).annotate(
                products_with_stock=Count(
                    'stock_batches__product',
                    filter=Q(
                        stock_batches__status='active',
                        stock_batches__quantity_available__gt=0,
                        stock_batches__product__is_active=True
                    ),
                    distinct=True
                )
            ).order_by('name')

            location_options = [
                {
                    'id': location.id,
                    'name': location.name,
                    'code': location.code,
                    'location_type': location.location_type,
                    'products_with_stock': location.products_with_stock
                } for location in locations
            ]

            # Stock status options
            stock_status_options = [
                {'value': 'all', 'label': 'All Stock Levels', 'is_default': True},
                {'value': 'in_stock', 'label': 'In Stock', 'icon': '✅'},
                {'value': 'low_stock', 'label': 'Low Stock', 'icon': '⚠️'},
                {'value': 'out_of_stock', 'label': 'Out of Stock', 'icon': '❌'}
            ]

            # Price range
            from django.db.models import Min, Max
            price_range = EnhancedProduct.objects.filter(
                owner=request.user,
                is_active=True
            ).aggregate(
                min_price=Min('selling_price'),
                max_price=Max('selling_price')
            )

            # Popular tags
            popular_tags = []
            tag_products = EnhancedProduct.objects.filter(
                owner=request.user,
                is_active=True,
                tags__isnull=False
            ).exclude(tags='')

            tag_counts = {}
            for product in tag_products:
                tags = [tag.strip() for tag in product.tags.split(',') if tag.strip()]
                for tag in tags:
                    tag_counts[tag] = tag_counts.get(tag, 0) + 1

            popular_tags = [
                {'name': tag, 'count': count}
                for tag, count in sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:20]
            ]

            # Ordering options
            ordering_options = [
                {'value': 'name', 'label': 'Name (A-Z)', 'is_default': True},
                {'value': '-name', 'label': 'Name (Z-A)'},
                {'value': 'selling_price', 'label': 'Price (Low to High)'},
                {'value': '-selling_price', 'label': 'Price (High to Low)'},
                {'value': '-created_at', 'label': 'Newest First'},
                {'value': 'created_at', 'label': 'Oldest First'},
                {'value': '-updated_at', 'label': 'Recently Updated'},
                {'value': 'sku', 'label': 'SKU (A-Z)'},
                {'value': '-sku', 'label': 'SKU (Z-A)'},
                {'value': 'reorder_level', 'label': 'Reorder Level (Low to High)'},
                {'value': '-reorder_level', 'label': 'Reorder Level (High to Low)'}
            ]

            # Page size options
            page_size_options = [
                {'value': 20, 'label': '20 per page', 'is_default': True},
                {'value': 50, 'label': '50 per page'},
                {'value': 100, 'label': '100 per page'}
            ]

            filter_options = {
                'status': status_options,
                'categories': category_options,
                'brands': brand_options,
                'suppliers': supplier_options,
                'locations': location_options,
                'stock_status': stock_status_options,
                'price_range': {
                    'min_price': float(price_range['min_price'] or 0),
                    'max_price': float(price_range['max_price'] or 0),
                    'step': 0.01
                },
                'popular_tags': popular_tags,
                'ordering': ordering_options,
                'page_size': page_size_options,
                'defaults': {
                    'status': 'active',
                    'stock_status': 'all',
                    'ordering': 'name',
                    'page_size': 20,
                    'page': 1
                }
            }

            # Cache for 5 minutes
            cache.set(cache_key, filter_options, 300)
            cache_hit = False
        else:
            cache_hit = True

        return Response({
            'success': True,
            'data': filter_options,
            'cached': cache_hit,
            'cache_key': cache_key
        })

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """Get product dashboard statistics for list view"""
        from django.db.models import Count, Sum, Avg, Min, Max

        # Base queryset
        all_products = EnhancedProduct.objects.filter(owner=request.user)
        active_products = all_products.filter(is_active=True)

        # Basic counts
        total_products = all_products.count()
        active_count = active_products.count()
        inactive_count = total_products - active_count

        # Stock status counts
        in_stock_count = 0
        low_stock_count = 0
        out_of_stock_count = 0

        for product in active_products:
            if product.is_out_of_stock:
                out_of_stock_count += 1
            elif product.is_low_stock:
                low_stock_count += 1
            else:
                in_stock_count += 1

        # Category distribution
        category_stats = active_products.values(
            'category__name'
        ).annotate(
            product_count=Count('id')
        ).order_by('-product_count')[:5]

        # Price statistics
        price_stats = active_products.aggregate(
            min_price=Min('selling_price'),
            max_price=Max('selling_price'),
            avg_price=Avg('selling_price')
        )

        # Recent products (last 7 days)
        from datetime import timedelta
        from django.utils import timezone
        week_ago = timezone.now() - timedelta(days=7)
        recent_products_count = active_products.filter(created_at__gte=week_ago).count()

        # Featured products count
        featured_count = active_products.filter(is_featured=True).count()

        # Brand distribution
        brand_stats = active_products.filter(brand__isnull=False).values(
            'brand__name'
        ).annotate(
            product_count=Count('id')
        ).order_by('-product_count')[:5]

        # Stock value calculation
        total_stock_value = sum(product.total_stock_value for product in active_products)

        return Response({
            'success': True,
            'data': {
                'overview': {
                    'total_products': total_products,
                    'active_products': active_count,
                    'inactive_products': inactive_count,
                    'featured_products': featured_count,
                    'recent_products_week': recent_products_count
                },
                'stock_status': {
                    'in_stock': in_stock_count,
                    'low_stock': low_stock_count,
                    'out_of_stock': out_of_stock_count,
                    'total_stock_value': float(total_stock_value)
                },
                'pricing': {
                    'min_price': float(price_stats['min_price'] or 0),
                    'max_price': float(price_stats['max_price'] or 0),
                    'avg_price': float(price_stats['avg_price'] or 0)
                },
                'category_distribution': [
                    {
                        'category_name': item['category__name'] or 'Uncategorized',
                        'product_count': item['product_count']
                    } for item in category_stats
                ],
                'brand_distribution': [
                    {
                        'brand_name': item['brand__name'],
                        'product_count': item['product_count']
                    } for item in brand_stats
                ],
                'alerts': {
                    'low_stock_alert': low_stock_count > 0,
                    'out_of_stock_alert': out_of_stock_count > 0,
                    'no_stock_value': total_stock_value == 0,
                    'uncategorized_products': active_products.filter(category__isnull=True).count()
                }
            }
        })

    @action(detail=False, methods=['get'])
    def quick_search(self, request):
        """Quick search for autocomplete functionality"""
        query = request.query_params.get('q', '')
        limit = int(request.query_params.get('limit', 5))

        if len(query) < 2:
            return Response({
                'success': True,
                'data': {'results': []}
            })

        # Quick search focusing on name, SKU, and barcode
        products = EnhancedProduct.objects.filter(
            owner=request.user,
            is_active=True
        ).filter(
            Q(name__icontains=query) |
            Q(sku__icontains=query) |
            Q(barcode__icontains=query)
        ).select_related('category', 'brand')[:limit]

        results = []
        for product in products:
            results.append({
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'barcode': product.barcode,
                'selling_price': product.selling_price,
                'category_name': product.category.name if product.category else None,
                'brand_name': product.brand.name if product.brand else None,
                'current_stock': product.current_stock
            })

        return Response({
            'success': True,
            'data': {
                'query': query,
                'results': results
            }
        })

    @action(detail=True, methods=['get'])
    def sales_history(self, request, pk=None):
        """Get comprehensive sales history for a specific product"""
        try:
            from datetime import datetime, timedelta
            from django.utils import timezone
            from django.db.models import Q, Count, Sum, Avg
            from django.core.paginator import Paginator
            from inventory.models import POSTransaction, POSTransactionItem

            product = self.get_object()

            # === TIME FILTERING ===
            time_filter = request.query_params.get('time_filter', 'last_30_days')
            today = timezone.now().date()

            # Base queryset - sales transactions for this product
            sales_items = POSTransactionItem.objects.filter(
                product=product,
                transaction__owner=request.user,
                transaction__status='completed'
            ).select_related('transaction', 'transaction__customer').order_by('-transaction__created_at')

            if time_filter == 'today':
                sales_items = sales_items.filter(transaction__created_at__date=today)
            elif time_filter == 'yesterday':
                yesterday = today - timedelta(days=1)
                sales_items = sales_items.filter(transaction__created_at__date=yesterday)
            elif time_filter == 'last_7_days':
                week_ago = today - timedelta(days=7)
                sales_items = sales_items.filter(transaction__created_at__date__gte=week_ago)
            elif time_filter == 'last_30_days':  # Default
                month_ago = today - timedelta(days=30)
                sales_items = sales_items.filter(transaction__created_at__date__gte=month_ago)
            elif time_filter == 'last_90_days':
                quarter_ago = today - timedelta(days=90)
                sales_items = sales_items.filter(transaction__created_at__date__gte=quarter_ago)
            elif time_filter == 'this_month':
                month_start = today.replace(day=1)
                sales_items = sales_items.filter(transaction__created_at__date__gte=month_start)
            elif time_filter == 'this_year':
                year_start = today.replace(month=1, day=1)
                sales_items = sales_items.filter(transaction__created_at__date__gte=year_start)
            elif time_filter == 'custom':
                start_date = request.query_params.get('start_date')
                end_date = request.query_params.get('end_date')
                if start_date:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                    sales_items = sales_items.filter(transaction__created_at__date__gte=start_date)
                if end_date:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                    sales_items = sales_items.filter(transaction__created_at__date__lte=end_date)

            # === SEARCH FILTERING ===
            search = request.query_params.get('search', '').strip()
            if search:
                sales_items = sales_items.filter(
                    Q(transaction__transaction_number__icontains=search) |
                    Q(transaction__customer__name__icontains=search) |
                    Q(transaction__notes__icontains=search)
                )

            # === PAGINATION ===
            page_size = int(request.query_params.get('page_size', 20))
            page_number = int(request.query_params.get('page', 1))

            paginator = Paginator(sales_items, page_size)
            page_obj = paginator.get_page(page_number)

            # === PREPARE SALES DATA ===
            sales_list = []
            for sale_item in page_obj:
                transaction = sale_item.transaction
                sales_data = {
                    'id': sale_item.id,
                    'transaction_id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'date': transaction.created_at.date().isoformat(),
                    'time': transaction.created_at.time().strftime('%H:%M:%S'),
                    'created_at': transaction.created_at.isoformat(),
                    'quantity': float(sale_item.quantity),
                    'unit_price': float(sale_item.unit_price),
                    'line_total': float(sale_item.line_total),
                    'discount_amount': float(sale_item.discount_amount),
                    'customer': {
                        'id': transaction.customer.id if transaction.customer else None,
                        'name': transaction.customer.display_name if transaction.customer else 'Walk-in Customer',
                        'mobile_number': transaction.customer.mobile_number if transaction.customer else None
                    },
                    'payment_method': transaction.payment_method,
                    'transaction_total': float(transaction.total_amount),
                    'notes': transaction.notes or ''
                }
                sales_list.append(sales_data)

            # === ANALYTICS FOR FILTERED PERIOD ===
            period_analytics = sales_items.aggregate(
                total_quantity_sold=Sum('quantity'),
                total_revenue=Sum('line_total'),
                total_transactions=Count('transaction', distinct=True),
                avg_quantity_per_sale=Avg('quantity'),
                avg_price_per_unit=Avg('unit_price')
            )

            # === ALL-TIME ANALYTICS ===
            all_sales = POSTransactionItem.objects.filter(
                product=product,
                transaction__owner=request.user,
                transaction__status='completed'
            )

            lifetime_analytics = all_sales.aggregate(
                lifetime_quantity_sold=Sum('quantity'),
                lifetime_revenue=Sum('line_total'),
                lifetime_transactions=Count('transaction', distinct=True),
                lifetime_avg_quantity=Avg('quantity'),
                lifetime_avg_price=Avg('unit_price')
            )

            # === MONTHLY SALES TREND ===
            from django.db.models.functions import TruncMonth
            monthly_sales = all_sales.annotate(
                month=TruncMonth('transaction__created_at')
            ).values('month').annotate(
                quantity_sold=Sum('quantity'),
                revenue=Sum('line_total'),
                transactions=Count('transaction', distinct=True)
            ).order_by('-month')[:12]

            # === TOP CUSTOMERS FOR THIS PRODUCT ===
            top_customers = all_sales.filter(
                transaction__customer__isnull=False
            ).values(
                'transaction__customer__id',
                'transaction__customer__name',
                'transaction__customer__mobile_number'
            ).annotate(
                total_quantity=Sum('quantity'),
                total_spent=Sum('line_total'),
                purchase_count=Count('transaction', distinct=True)
            ).order_by('-total_quantity')[:5]

            response_data = {
                'success': True,
                'product': {
                    'id': product.id,
                    'name': product.name,
                    'sku': product.sku,
                    'selling_price': float(product.selling_price),
                    'current_stock': float(product.current_stock)
                },
                'sales': sales_list,
                'pagination': {
                    'current_page': page_obj.number,
                    'total_pages': paginator.num_pages,
                    'total_sales': paginator.count,
                    'page_size': page_size,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                },
                'analytics': {
                    'period': {
                        'total_quantity_sold': float(period_analytics['total_quantity_sold'] or 0),
                        'total_revenue': float(period_analytics['total_revenue'] or 0),
                        'total_transactions': period_analytics['total_transactions'],
                        'avg_quantity_per_sale': float(period_analytics['avg_quantity_per_sale'] or 0),
                        'avg_price_per_unit': float(period_analytics['avg_price_per_unit'] or 0)
                    },
                    'lifetime': {
                        'total_quantity_sold': float(lifetime_analytics['lifetime_quantity_sold'] or 0),
                        'total_revenue': float(lifetime_analytics['lifetime_revenue'] or 0),
                        'total_transactions': lifetime_analytics['lifetime_transactions'],
                        'avg_quantity_per_sale': float(lifetime_analytics['lifetime_avg_quantity'] or 0),
                        'avg_price_per_unit': float(lifetime_analytics['lifetime_avg_price'] or 0)
                    },
                    'monthly_trend': [
                        {
                            'month': item['month'].strftime('%Y-%m'),
                            'month_name': item['month'].strftime('%B %Y'),
                            'quantity_sold': float(item['quantity_sold']),
                            'revenue': float(item['revenue']),
                            'transactions': item['transactions']
                        } for item in monthly_sales
                    ],
                    'top_customers': [
                        {
                            'customer_id': customer['transaction__customer__id'],
                            'customer_name': customer['transaction__customer__name'],
                            'mobile_number': customer['transaction__customer__mobile_number'],
                            'total_quantity': float(customer['total_quantity']),
                            'total_spent': float(customer['total_spent']),
                            'purchase_count': customer['purchase_count']
                        } for customer in top_customers
                    ]
                },
                'filters_applied': {
                    'time_filter': time_filter,
                    'search': search
                }
            }

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['get'])
    def comprehensive_detail(self, request, pk=None):
        """
        Get comprehensive product detail with stock management and sales history
        Matches the web product detail page functionality
        """
        try:
            from datetime import datetime, timedelta
            from django.utils import timezone
            from django.db.models import Q, Count, Sum, Avg

            product = self.get_object()

            # ============================================
            # 1. BASIC PRODUCT INFORMATION
            # ============================================
            product_serializer = EnhancedProductDetailSerializer(product)
            product_data = product_serializer.data

            # ============================================
            # 2. STOCK MANAGEMENT DATA
            # ============================================
            # Get stock batches
            active_batches = product.stock_batches.filter(
                status='active',
                quantity_available__gt=0
            ).order_by('-received_date')

            # Stock summary
            stock_summary = {
                'current_stock': product.current_stock,
                'total_stock_value': product.total_stock_value,
                'is_low_stock': product.is_low_stock,
                'is_out_of_stock': product.is_out_of_stock,
                'reorder_level': product.reorder_level,
                'maximum_stock_level': product.maximum_stock_level,
                'total_batches': active_batches.count(),
                'oldest_batch_date': active_batches.last().received_date if active_batches.exists() else None,
                'newest_batch_date': active_batches.first().received_date if active_batches.exists() else None
            }

            # Stock entries (recent batches)
            from .serializers import StockBatchDetailSerializer
            stock_entries = StockBatchDetailSerializer(active_batches[:10], many=True).data

            # Stock by location
            stock_by_location = product.get_stock_by_location()

            # Stock valuation
            stock_valuation = product.get_stock_valuation('FIFO')

            # ============================================
            # 3. SALES HISTORY & ANALYTICS
            # ============================================
            # Get sales data using the existing sales_history logic
            from inventory.models import POSTransaction, POSTransactionItem

            # Time periods for analytics
            today = timezone.now().date()
            week_ago = today - timedelta(days=7)
            month_ago = today - timedelta(days=30)

            # Base sales queryset
            sales_items = POSTransactionItem.objects.filter(
                product=product,
                transaction__owner=request.user,
                transaction__status='completed'
            ).select_related('transaction', 'transaction__customer')

            # Lifetime analytics
            lifetime_stats = sales_items.aggregate(
                total_quantity_sold=Sum('quantity'),
                total_revenue=Sum('line_total'),
                total_transactions=Count('transaction', distinct=True),
                avg_quantity_per_sale=Avg('quantity'),
                avg_price_per_unit=Avg('unit_price')
            )

            # Today's sales
            today_sales = sales_items.filter(
                transaction__created_at__date=today
            ).aggregate(
                quantity=Sum('quantity') or 0,
                revenue=Sum('line_total') or 0,
                transactions=Count('transaction', distinct=True) or 0
            )

            # This week's sales
            week_sales = sales_items.filter(
                transaction__created_at__date__gte=week_ago
            ).aggregate(
                quantity=Sum('quantity') or 0,
                revenue=Sum('line_total') or 0,
                transactions=Count('transaction', distinct=True) or 0
            )

            # This month's sales
            month_sales = sales_items.filter(
                transaction__created_at__date__gte=month_ago
            ).aggregate(
                quantity=Sum('quantity') or 0,
                revenue=Sum('line_total') or 0,
                transactions=Count('transaction', distinct=True) or 0
            )

            # Recent sales transactions (last 10)
            recent_sales = sales_items.order_by('-transaction__created_at')[:10]
            recent_sales_data = []

            for item in recent_sales:
                recent_sales_data.append({
                    'transaction_number': item.transaction.transaction_number,
                    'date': item.transaction.created_at.strftime('%Y-%m-%d'),
                    'time': item.transaction.created_at.strftime('%H:%M'),
                    'quantity': float(item.quantity),
                    'unit_price': float(item.unit_price),
                    'line_total': float(item.line_total),
                    'discount_amount': float(item.discount_amount),
                    'customer': {
                        'name': item.transaction.customer.display_name if item.transaction.customer else 'Walk-in Customer',
                        'mobile_number': item.transaction.customer.mobile_number if item.transaction.customer else None
                    },
                    'payment_method': item.transaction.payment_method,
                    'transaction_total': float(item.transaction.total_amount)
                })

            # ============================================
            # 4. COMPREHENSIVE RESPONSE
            # ============================================
            comprehensive_data = {
                # Product Information
                'product': product_data,

                # Stock Management
                'stock_management': {
                    'summary': stock_summary,
                    'stock_entries': stock_entries,
                    'stock_by_location': stock_by_location,
                    'stock_valuation': stock_valuation
                },

                # Sales Analytics
                'sales_analytics': {
                    'lifetime': {
                        'total_quantity_sold': float(lifetime_stats['total_quantity_sold'] or 0),
                        'total_revenue': float(lifetime_stats['total_revenue'] or 0),
                        'total_transactions': lifetime_stats['total_transactions'] or 0,
                        'avg_quantity_per_sale': float(lifetime_stats['avg_quantity_per_sale'] or 0),
                        'avg_price_per_unit': float(lifetime_stats['avg_price_per_unit'] or 0)
                    },
                    'period_analysis': {
                        'today': today_sales,
                        'this_week': week_sales,
                        'this_month': month_sales
                    },
                    'recent_transactions': recent_sales_data
                },

                # Additional Metadata
                'metadata': {
                    'generated_at': timezone.now().isoformat(),
                    'api_version': 'v1.0',
                    'data_freshness': 'real_time',
                    'available_actions': [
                        'adjust_stock',
                        'transfer_stock',
                        'reserve_stock',
                        'add_stock_batch',
                        'update_product'
                    ]
                },

                # Available API Endpoints
                'related_endpoints': {
                    'stock_summary': f'/api/v1/inventory/products/{product.id}/stock_summary/',
                    'sales_history': f'/api/v1/inventory/products/{product.id}/sales_history/',
                    'stock_by_location': f'/api/v1/inventory/products/{product.id}/stock_by_location/',
                    'stock_valuation': f'/api/v1/inventory/products/{product.id}/stock_valuation/',
                    'adjust_stock': f'/api/v1/inventory/products/{product.id}/adjust_stock/',
                    'transfer_stock': f'/api/v1/inventory/products/{product.id}/transfer_stock/',
                    'reserve_stock': f'/api/v1/inventory/products/{product.id}/reserve_stock/'
                }
            }

            # Clean null values for Flutter compatibility
            cleaned_data = clean_response_for_flutter(comprehensive_data)

            return Response({
                'success': True,
                'data': cleaned_data
            })

        except Exception as e:
            import traceback
            return Response({
                'success': False,
                'error': str(e),
                'traceback': traceback.format_exc() if request.user.is_superuser else None
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class StockBatchViewSet(viewsets.ModelViewSet):
    """Batch-level stock management"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'location', 'supplier', 'status']
    search_fields = ['batch_number', 'purchase_order_number']
    ordering_fields = ['received_date', 'expiry_date', 'quantity_available']
    ordering = ['-received_date', '-created_at']

    def get_queryset(self):
        queryset = StockBatch.objects.filter(owner=self.request.user)
        return QueryOptimizer.optimize_stock_batch_queryset(queryset)

    def get_serializer_class(self):
        if self.action == 'list':
            return StockBatchListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return StockBatchCreateUpdateSerializer
        return StockBatchDetailSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            batch = serializer.save()

            # Create initial stock movement
            StockMovement.objects.create(
                owner=request.user,
                product=batch.product,
                batch=batch,
                location=batch.location,
                movement_type='purchase',
                quantity=batch.quantity_received,
                reference_number=batch.batch_number,
                reference_type='purchase',
                notes=f'Initial stock entry for batch {batch.batch_number}',
                created_by=request.user
            )

            return Response({
                'success': True,
                'message': 'Stock batch created successfully',
                'data': StockBatchDetailSerializer(batch).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_batch = serializer.save()
            return Response({
                'success': True,
                'message': 'Stock batch updated successfully',
                'data': StockBatchDetailSerializer(updated_batch).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.quantity_available > 0:
            return Response({
                'success': False,
                'message': 'Cannot delete batch with available stock'
            }, status=status.HTTP_400_BAD_REQUEST)

        instance.delete()
        return Response({
            'success': True,
            'message': 'Stock batch deleted successfully'
        })

    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """Get batches expiring within specified days"""
        days = int(request.query_params.get('days', 30))
        expiry_threshold = timezone.now().date() + timedelta(days=days)

        queryset = self.get_queryset().filter(
            expiry_date__lte=expiry_threshold,
            expiry_date__isnull=False,
            status='active',
            quantity_available__gt=0
        )

        serializer = StockBatchListSerializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'expiry_threshold': expiry_threshold.isoformat(),
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """Get batches for specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response({
                'success': False,
                'message': 'product_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        queryset = self.get_queryset().filter(product=product)
        serializer = StockBatchListSerializer(queryset, many=True)

        return Response({
            'success': True,
            'data': {
                'product': EnhancedProductDetailSerializer(product).data,
                'batches': serializer.data
            }
        })


class StockMovementViewSet(viewsets.ModelViewSet):
    """Stock movement tracking"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'batch', 'location', 'movement_type']
    search_fields = ['reference_number', 'notes']
    ordering_fields = ['created_at', 'quantity']
    ordering = ['-created_at']

    # Only allow reading and creating movements, not updating/deleting
    http_method_names = ['get', 'post', 'head', 'options']

    def get_queryset(self):
        queryset = StockMovement.objects.filter(owner=self.request.user)
        return QueryOptimizer.optimize_stock_movement_queryset(queryset)

    def get_serializer_class(self):
        if self.action == 'create':
            return StockMovementCreateSerializer
        elif self.action == 'list':
            return StockMovementListSerializer
        return StockMovementDetailSerializer

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            movement = serializer.save()

            # Update batch quantities
            batch = movement.batch
            if movement.quantity > 0:
                # Incoming movement
                batch.quantity_available += movement.quantity
            else:
                # Outgoing movement
                batch.quantity_available += movement.quantity  # quantity is negative

            batch.save()

            return Response({
                'success': True,
                'message': 'Stock movement recorded successfully',
                'data': StockMovementDetailSerializer(movement).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """Get movement history for specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response({
                'success': False,
                'message': 'product_id parameter is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        queryset = self.get_queryset().filter(product=product)
        serializer = StockMovementListSerializer(queryset, many=True)

        return Response({
            'success': True,
            'data': {
                'product': EnhancedProductDetailSerializer(product).data,
                'movements': serializer.data
            }
        })

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get stock movement summary with filtering"""
        from datetime import datetime

        # Parse filter parameters
        product_id = request.query_params.get('product_id')
        location_id = request.query_params.get('location_id')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')

        product = None
        location = None

        if product_id:
            try:
                product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
            except EnhancedProduct.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Product not found'
                }, status=status.HTTP_404_NOT_FOUND)

        if location_id:
            try:
                location = Location.objects.get(id=location_id, owner=request.user)
            except Location.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Location not found'
                }, status=status.HTTP_404_NOT_FOUND)

        # Parse dates
        parsed_start_date = None
        parsed_end_date = None

        if start_date:
            try:
                parsed_start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid start_date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        if end_date:
            try:
                parsed_end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid end_date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        summary = StockMovement.get_movement_summary(
            request.user, product, location, parsed_start_date, parsed_end_date
        )

        return Response({
            'success': True,
            'data': {
                'filters': {
                    'product': EnhancedProductDetailSerializer(product).data if product else None,
                    'location': LocationDetailSerializer(location).data if location else None,
                    'start_date': start_date,
                    'end_date': end_date
                },
                'summary': summary
            }
        })

    @action(detail=False, methods=['get'])
    def audit_report(self, request):
        """Generate comprehensive audit report"""
        from datetime import datetime

        # Parse filter parameters
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        product_id = request.query_params.get('product_id')
        user_id = request.query_params.get('user_id')

        parsed_start_date = None
        parsed_end_date = None
        product = None
        user = None

        if start_date:
            try:
                parsed_start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid start_date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        if end_date:
            try:
                parsed_end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
            except ValueError:
                return Response({
                    'success': False,
                    'message': 'Invalid end_date format. Use YYYY-MM-DD'
                }, status=status.HTTP_400_BAD_REQUEST)

        if product_id:
            try:
                product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
            except EnhancedProduct.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Product not found'
                }, status=status.HTTP_404_NOT_FOUND)

        if user_id:
            try:
                from django.contrib.auth import get_user_model
                User = get_user_model()
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'User not found'
                }, status=status.HTTP_404_NOT_FOUND)

        audit_trail = StockMovement.get_audit_report(
            request.user, parsed_start_date, parsed_end_date, product, user
        )

        return Response({
            'success': True,
            'data': {
                'filters': {
                    'start_date': start_date,
                    'end_date': end_date,
                    'product_id': product_id,
                    'user_id': user_id
                },
                'audit_trail': audit_trail,
                'total_entries': len(audit_trail)
            }
        })

    @action(detail=False, methods=['get'])
    def detect_anomalies(self, request):
        """Detect potential anomalies in stock movements"""
        days_back = int(request.query_params.get('days_back', 30))

        if days_back > 365:
            return Response({
                'success': False,
                'message': 'Maximum days_back is 365'
            }, status=status.HTTP_400_BAD_REQUEST)

        anomalies = StockMovement.detect_anomalies(request.user, days_back)

        return Response({
            'success': True,
            'data': {
                'analysis_period': f'{days_back} days',
                'anomalies_found': len(anomalies),
                'anomalies': anomalies
            }
        })

    @action(detail=False, methods=['post'])
    def bulk_movements(self, request):
        """Create multiple stock movements in batch"""
        movements_data = request.data.get('movements', [])

        if not movements_data:
            return Response({
                'success': False,
                'message': 'No movements data provided'
            }, status=status.HTTP_400_BAD_REQUEST)

        results = {
            'successful_movements': [],
            'errors': []
        }

        for movement_data in movements_data:
            try:
                # Validate required fields
                required_fields = ['product_id', 'batch_id', 'location_id', 'movement_type', 'quantity']
                for field in required_fields:
                    if field not in movement_data:
                        raise ValueError(f'Missing required field: {field}')

                # Get related objects
                product = EnhancedProduct.objects.get(id=movement_data['product_id'], owner=request.user)
                batch = StockBatch.objects.get(id=movement_data['batch_id'], owner=request.user)
                location = Location.objects.get(id=movement_data['location_id'], owner=request.user)

                # Create movement
                movement = StockMovement.create_movement_log(
                    owner=request.user,
                    product=product,
                    batch=batch,
                    location=location,
                    movement_type=movement_data['movement_type'],
                    quantity=movement_data['quantity'],
                    created_by=request.user,
                    reference_number=movement_data.get('reference_number', ''),
                    reference_type=movement_data.get('reference_type', ''),
                    notes=movement_data.get('notes', '')
                )

                # Update batch quantities
                if movement.quantity > 0:
                    batch.quantity_available += movement.quantity
                else:
                    batch.quantity_available += movement.quantity  # quantity is negative

                batch.save()

                results['successful_movements'].append({
                    'movement_id': movement.id,
                    'product_id': product.id,
                    'batch_id': batch.id,
                    'quantity': movement.quantity
                })

            except (EnhancedProduct.DoesNotExist, StockBatch.DoesNotExist, Location.DoesNotExist):
                results['errors'].append({
                    'movement_data': movement_data,
                    'error': 'Product, batch, or location not found'
                })
            except Exception as e:
                results['errors'].append({
                    'movement_data': movement_data,
                    'error': str(e)
                })

        return Response({
            'success': True,
            'message': f'Bulk movements completed: {len(results["successful_movements"])} successful, {len(results["errors"])} failed',
            'data': results
        })


class ProductImageViewSet(viewsets.ModelViewSet):
    """Product image management with upload capabilities"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    parser_classes = [MultiPartParser, FormParser]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['product', 'is_primary']
    ordering_fields = ['sort_order', 'created_at']
    ordering = ['sort_order', 'id']

    def get_queryset(self):
        return ProductImage.objects.filter(product__owner=self.request.user).select_related('product')

    def get_serializer_class(self):
        from .serializers import ProductImageSerializer
        return ProductImageSerializer

    @action(detail=False, methods=['post'], url_path='upload/(?P<product_id>[^/.]+)')
    def upload_image(self, request, product_id=None):
        """
        Upload a single image for a product
        POST /api/inventory/product-images/upload/{product_id}/
        """
        try:
            # Get the product
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)

            # Get the uploaded file
            if 'image' not in request.FILES:
                return Response({
                    'success': False,
                    'message': 'No image file provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            image_file = request.FILES['image']
            alt_text = request.data.get('alt_text', '')
            is_primary = request.data.get('is_primary', 'false').lower() == 'true'

            # Use the service to add the image
            product_image = ProductImageService.add_product_image(
                product=product,
                image_file=image_file,
                alt_text=alt_text,
                is_primary=is_primary
            )

            # Return the created image data
            return Response({
                'success': True,
                'message': 'Image uploaded successfully',
                'data': {
                    'id': product_image.id,
                    'image_url': product_image.image.url if product_image.image else None,
                    'thumbnail_url': product_image.thumbnail.url if product_image.thumbnail else None,
                    'alt_text': product_image.alt_text,
                    'is_primary': product_image.is_primary,
                    'sort_order': product_image.sort_order,
                    'width': product_image.image_width,
                    'height': product_image.image_height,
                    'file_size': product_image.image_size,
                }
            }, status=status.HTTP_201_CREATED)

        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        except ValueError as e:
            return Response({
                'success': False,
                'message': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Upload failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='bulk-upload/(?P<product_id>[^/.]+)')
    def bulk_upload(self, request, product_id=None):
        """
        Upload multiple images for a product
        POST /api/inventory/product-images/bulk-upload/{product_id}/
        """
        try:
            # Get the product
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)

            # Get uploaded files
            image_files = request.FILES.getlist('images')
            if not image_files:
                return Response({
                    'success': False,
                    'message': 'No image files provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Use the service for bulk upload
            uploaded_images = ProductImageService.bulk_upload_images(
                product=product,
                image_files=image_files
            )

            # Return results
            return Response({
                'success': True,
                'message': f'Uploaded {len(uploaded_images)} of {len(image_files)} images',
                'data': {
                    'uploaded_count': len(uploaded_images),
                    'total_count': len(image_files),
                    'images': [{
                        'id': img.id,
                        'image_url': img.image.url if img.image else None,
                        'thumbnail_url': img.thumbnail.url if img.thumbnail else None,
                        'is_primary': img.is_primary,
                        'sort_order': img.sort_order,
                    } for img in uploaded_images]
                }
            }, status=status.HTTP_201_CREATED)

        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Bulk upload failed: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], url_path='(?P<product_id>[^/.]+)/reorder')
    def reorder_images(self, request, product_id=None):
        """
        Reorder product images
        POST /api/inventory/product-images/{product_id}/reorder/
        Body: {"image_ids": [3, 1, 2]}
        """
        try:
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)
            image_ids = request.data.get('image_ids', [])

            if not image_ids:
                return Response({
                    'success': False,
                    'message': 'No image IDs provided'
                }, status=status.HTTP_400_BAD_REQUEST)

            success = ProductImageService.reorder_images(product, image_ids)

            if success:
                return Response({
                    'success': True,
                    'message': 'Images reordered successfully'
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to reorder images'
                }, status=status.HTTP_400_BAD_REQUEST)

        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

    @action(detail=False, methods=['post'], url_path='(?P<product_id>[^/.]+)/set-primary/(?P<image_id>[^/.]+)')
    def set_primary(self, request, product_id=None, image_id=None):
        """
        Set an image as primary for the product
        POST /api/inventory/product-images/{product_id}/set-primary/{image_id}/
        """
        try:
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)

            success = ProductImageService.set_primary_image(product, int(image_id))

            if success:
                return Response({
                    'success': True,
                    'message': 'Primary image updated successfully'
                })
            else:
                return Response({
                    'success': False,
                    'message': 'Image not found or update failed'
                }, status=status.HTTP_404_NOT_FOUND)

        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        except ValueError:
            return Response({
                'success': False,
                'message': 'Invalid image ID'
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='(?P<product_id>[^/.]+)/list')
    def list_product_images(self, request, product_id=None):
        """
        Get all images for a product with metadata
        GET /api/inventory/product-images/{product_id}/list/
        """
        try:
            product = EnhancedProduct.objects.get(id=product_id, owner=request.user)

            images_data = ProductImageService.get_product_images(product)
            stats = ProductImageService.get_image_stats(product)

            return Response({
                'success': True,
                'data': {
                    'images': images_data,
                    'stats': stats
                }
            })

        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

    def destroy(self, request, pk=None):
        """
        Delete a product image
        DELETE /api/inventory/product-images/{image_id}/
        """
        try:
            image = ProductImage.objects.get(id=pk, product__owner=request.user)
            product = image.product

            success = ProductImageService.delete_image(product, int(pk))

            if success:
                return Response({
                    'success': True,
                    'message': 'Image deleted successfully'
                }, status=status.HTTP_204_NO_CONTENT)
            else:
                return Response({
                    'success': False,
                    'message': 'Failed to delete image'
                }, status=status.HTTP_400_BAD_REQUEST)

        except ProductImage.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Image not found'
            }, status=status.HTTP_404_NOT_FOUND)