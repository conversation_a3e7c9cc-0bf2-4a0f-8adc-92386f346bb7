from django.contrib import admin
from .models import (
    Category, Supplier, Location, ProductBrand, EnhancedProduct,
    ProductImage, UnitConversion, StockBatch, StockMovement,
    POSTransaction, POSTransactionItem, BatchOperation, ExpiryAlert,
    StockAnomalyDetection, CustomerLoan, LoanPayment
)


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent', 'level', 'is_active', 'sort_order', 'created_at']
    list_filter = ['level', 'is_active', 'created_at']
    search_fields = ['name', 'description']
    ordering = ['level', 'sort_order', 'name']


@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = ['name', 'contact_person', 'phone', 'email', 'is_active', 'rating']
    list_filter = ['is_active', 'rating', 'created_at']
    search_fields = ['name', 'contact_person', 'phone', 'email']
    ordering = ['name']


@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'location_type', 'is_active', 'created_at']
    list_filter = ['location_type', 'is_active', 'created_at']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(ProductBrand)
class ProductBrandAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name']
    ordering = ['name']


@admin.register(EnhancedProduct)
class EnhancedProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'sku', 'category', 'brand', 'selling_price', 'is_active', 'track_stock']
    list_filter = ['category', 'brand', 'supplier', 'is_active', 'track_stock', 'is_featured']
    search_fields = ['name', 'sku', 'barcode', 'description']
    ordering = ['name']


@admin.register(StockBatch)
class StockBatchAdmin(admin.ModelAdmin):
    list_display = ['batch_number', 'product', 'location', 'quantity_available', 'status', 'received_date']
    list_filter = ['status', 'location', 'received_date', 'expiry_date']
    search_fields = ['batch_number', 'product__name', 'purchase_order_number']
    ordering = ['-received_date']


@admin.register(StockMovement)
class StockMovementAdmin(admin.ModelAdmin):
    list_display = ['product', 'batch', 'movement_type', 'quantity', 'created_at', 'created_by']
    list_filter = ['movement_type', 'created_at']
    search_fields = ['product__name', 'batch__batch_number', 'reference_number']
    ordering = ['-created_at']
    readonly_fields = ['created_at']


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    list_display = ['product', 'image', 'is_primary', 'created_at']
    list_filter = ['is_primary', 'created_at']
    search_fields = ['product__name']
    ordering = ['-created_at']


@admin.register(UnitConversion)
class UnitConversionAdmin(admin.ModelAdmin):
    list_display = ['from_unit', 'to_unit', 'conversion_factor', 'is_active', 'created_at']
    list_filter = ['from_unit', 'to_unit', 'is_active', 'created_at']
    search_fields = ['from_unit', 'to_unit', 'description']
    ordering = ['from_unit', 'to_unit']


@admin.register(POSTransaction)
class POSTransactionAdmin(admin.ModelAdmin):
    list_display = ['transaction_number', 'customer', 'total_amount', 'payment_method', 'status', 'created_at']
    list_filter = ['payment_method', 'status', 'created_at']
    search_fields = ['transaction_number', 'customer__name']
    ordering = ['-created_at']
    readonly_fields = ['transaction_number', 'created_at']


@admin.register(POSTransactionItem)
class POSTransactionItemAdmin(admin.ModelAdmin):
    list_display = ['transaction', 'product', 'quantity', 'unit_price', 'line_total']
    list_filter = ['transaction__created_at']
    search_fields = ['transaction__transaction_number', 'product__name']
    ordering = ['-transaction__created_at']


@admin.register(BatchOperation)
class BatchOperationAdmin(admin.ModelAdmin):
    list_display = ['operation_type', 'performed_by', 'created_at']
    list_filter = ['operation_type', 'created_at']
    search_fields = ['reason', 'operation_type']
    ordering = ['-created_at']


@admin.register(ExpiryAlert)
class ExpiryAlertAdmin(admin.ModelAdmin):
    list_display = ['batch', 'alert_type', 'status', 'alert_date', 'days_until_expiry']
    list_filter = ['alert_type', 'status', 'priority', 'alert_date']
    search_fields = ['batch__batch_number', 'message']
    ordering = ['-alert_date']


@admin.register(StockAnomalyDetection)
class StockAnomalyDetectionAdmin(admin.ModelAdmin):
    list_display = ['product', 'anomaly_type', 'severity', 'is_resolved', 'detected_at']
    list_filter = ['anomaly_type', 'severity', 'is_resolved', 'detected_at']
    search_fields = ['product__name']
    ordering = ['-detected_at']


@admin.register(CustomerLoan)
class CustomerLoanAdmin(admin.ModelAdmin):
    list_display = ['customer', 'transaction', 'loan_amount', 'amount_paid', 'status', 'due_date']
    list_filter = ['status', 'due_date', 'loan_date']
    search_fields = ['customer__name', 'transaction__transaction_number']
    ordering = ['-loan_date']


@admin.register(LoanPayment)
class LoanPaymentAdmin(admin.ModelAdmin):
    list_display = ['loan', 'amount', 'payment_method', 'created_at']
    list_filter = ['payment_method', 'created_at']
    search_fields = ['loan__customer__name']
    ordering = ['-created_at']
