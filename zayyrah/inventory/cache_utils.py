"""
Caching utilities for inventory management
"""
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
import json


class InventoryCache:
    """Centralized caching for inventory operations"""

    @staticmethod
    def get_cache_key(prefix, owner_id, identifier=None):
        """Generate consistent cache keys"""
        if identifier:
            return f"inventory:{prefix}:{owner_id}:{identifier}"
        return f"inventory:{prefix}:{owner_id}"

    @classmethod
    def get_category_tree(cls, owner_id):
        """Get cached category tree"""
        cache_key = cls.get_cache_key('category_tree', owner_id)
        return cache.get(cache_key)

    @classmethod
    def set_category_tree(cls, owner_id, tree_data, timeout=3600):
        """Cache category tree for 1 hour"""
        cache_key = cls.get_cache_key('category_tree', owner_id)
        cache.set(cache_key, tree_data, timeout)

    @classmethod
    def invalidate_category_tree(cls, owner_id):
        """Invalidate category tree cache"""
        cache_key = cls.get_cache_key('category_tree', owner_id)
        cache.delete(cache_key)

    @classmethod
    def get_product_stock(cls, owner_id, product_id):
        """Get cached product stock levels"""
        cache_key = cls.get_cache_key('product_stock', owner_id, product_id)
        return cache.get(cache_key)

    @classmethod
    def set_product_stock(cls, owner_id, product_id, stock_data, timeout=300):
        """Cache product stock for 5 minutes"""
        cache_key = cls.get_cache_key('product_stock', owner_id, product_id)
        cache.set(cache_key, stock_data, timeout)

    @classmethod
    def invalidate_product_stock(cls, owner_id, product_id=None):
        """Invalidate product stock cache"""
        if product_id:
            cache_key = cls.get_cache_key('product_stock', owner_id, product_id)
            cache.delete(cache_key)
        else:
            # Invalidate all product stocks for owner (less efficient but thorough)
            pattern = cls.get_cache_key('product_stock', owner_id, '*')
            # Note: This would require Redis for pattern deletion
            # For now, we'll use individual invalidation

    @classmethod
    def get_supplier_metrics(cls, owner_id, supplier_id):
        """Get cached supplier performance metrics"""
        cache_key = cls.get_cache_key('supplier_metrics', owner_id, supplier_id)
        return cache.get(cache_key)

    @classmethod
    def set_supplier_metrics(cls, owner_id, supplier_id, metrics_data, timeout=1800):
        """Cache supplier metrics for 30 minutes"""
        cache_key = cls.get_cache_key('supplier_metrics', owner_id, supplier_id)
        cache.set(cache_key, metrics_data, timeout)

    @classmethod
    def invalidate_supplier_metrics(cls, owner_id, supplier_id=None):
        """Invalidate supplier metrics cache"""
        if supplier_id:
            cache_key = cls.get_cache_key('supplier_metrics', owner_id, supplier_id)
            cache.delete(cache_key)

    @classmethod
    def get_search_suggestions(cls, owner_id, query):
        """Get cached search suggestions"""
        cache_key = cls.get_cache_key('search_suggestions', owner_id, query[:20])  # Limit query length
        return cache.get(cache_key)

    @classmethod
    def set_search_suggestions(cls, owner_id, query, suggestions, timeout=900):
        """Cache search suggestions for 15 minutes"""
        cache_key = cls.get_cache_key('search_suggestions', owner_id, query[:20])
        cache.set(cache_key, suggestions, timeout)

    @classmethod
    def get_filter_options(cls, owner_id):
        """Get cached filter options"""
        cache_key = cls.get_cache_key('filter_options', owner_id)
        return cache.get(cache_key)

    @classmethod
    def set_filter_options(cls, owner_id, options, timeout=3600):
        """Cache filter options for 1 hour"""
        cache_key = cls.get_cache_key('filter_options', owner_id)
        cache.set(cache_key, options, timeout)

    @classmethod
    def invalidate_filter_options(cls, owner_id):
        """Invalidate filter options cache"""
        cache_key = cls.get_cache_key('filter_options', owner_id)
        cache.delete(cache_key)


# Signal handlers for cache invalidation
@receiver(post_save, sender='inventory.Category')
@receiver(post_delete, sender='inventory.Category')
def invalidate_category_cache(sender, instance, **kwargs):
    """Invalidate category-related caches when categories change"""
    InventoryCache.invalidate_category_tree(instance.owner_id)
    InventoryCache.invalidate_filter_options(instance.owner_id)


@receiver(post_save, sender='inventory.EnhancedProduct')
@receiver(post_delete, sender='inventory.EnhancedProduct')
def invalidate_product_cache(sender, instance, **kwargs):
    """Invalidate product-related caches when products change"""
    InventoryCache.invalidate_product_stock(instance.owner_id, instance.id)
    InventoryCache.invalidate_filter_options(instance.owner_id)


@receiver(post_save, sender='inventory.StockBatch')
@receiver(post_delete, sender='inventory.StockBatch')
def invalidate_stock_cache(sender, instance, **kwargs):
    """Invalidate stock-related caches when stock changes"""
    InventoryCache.invalidate_product_stock(instance.owner_id, instance.product_id)
    InventoryCache.invalidate_supplier_metrics(instance.owner_id, instance.supplier_id)


@receiver(post_save, sender='inventory.StockMovement')
def invalidate_movement_cache(sender, instance, **kwargs):
    """Invalidate movement-related caches"""
    InventoryCache.invalidate_product_stock(instance.owner_id, instance.product_id)
    if hasattr(instance.batch, 'supplier_id') and instance.batch.supplier_id:
        InventoryCache.invalidate_supplier_metrics(instance.owner_id, instance.batch.supplier_id)


@receiver(post_save, sender='inventory.Supplier')
@receiver(post_delete, sender='inventory.Supplier')
def invalidate_supplier_cache(sender, instance, **kwargs):
    """Invalidate supplier-related caches"""
    InventoryCache.invalidate_supplier_metrics(instance.owner_id, instance.id)
    InventoryCache.invalidate_filter_options(instance.owner_id)


def cached_method(cache_key_func, timeout=300):
    """
    Decorator for caching method results

    Args:
        cache_key_func: Function that generates cache key from method args
        timeout: Cache timeout in seconds
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            cache_key = cache_key_func(*args, **kwargs)
            result = cache.get(cache_key)

            if result is None:
                result = func(*args, **kwargs)
                cache.set(cache_key, result, timeout)

            return result
        return wrapper
    return decorator


def batch_cache_get_or_set(cache_keys_and_funcs, timeout=300):
    """
    Batch get or set multiple cache entries

    Args:
        cache_keys_and_funcs: List of tuples (cache_key, func_to_call_if_missing)
        timeout: Cache timeout in seconds

    Returns:
        Dictionary mapping cache_keys to results
    """
    results = {}
    missing_keys = []

    # First, try to get all from cache
    for cache_key, func in cache_keys_and_funcs:
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            results[cache_key] = cached_result
        else:
            missing_keys.append((cache_key, func))

    # For missing keys, compute and cache
    for cache_key, func in missing_keys:
        result = func()
        results[cache_key] = result
        cache.set(cache_key, result, timeout)

    return results


class QueryOptimizer:
    """Query optimization utilities"""

    @staticmethod
    def optimize_product_queryset(queryset):
        """Optimize product querysets with proper select_related and prefetch_related"""
        return queryset.select_related(
            'category',
            'brand',
            'supplier'
        ).prefetch_related(
            'images',
            'stock_batches',
            'stock_movements'
        )

    @staticmethod
    def optimize_stock_batch_queryset(queryset):
        """Optimize stock batch querysets"""
        return queryset.select_related(
            'product',
            'location',
            'supplier'
        ).prefetch_related(
            'movements'
        )

    @staticmethod
    def optimize_stock_movement_queryset(queryset):
        """Optimize stock movement querysets"""
        return queryset.select_related(
            'product',
            'batch',
            'location',
            'created_by'
        )


class PerformanceMonitor:
    """Monitor query performance and cache hit rates"""

    @staticmethod
    def log_query_performance(view_name, query_count, cache_hits, execution_time):
        """Log performance metrics for monitoring"""
        from django.utils import timezone
        import logging

        logger = logging.getLogger('inventory.performance')
        logger.info(
            f"Performance: {view_name} | "
            f"Queries: {query_count} | "
            f"Cache hits: {cache_hits} | "
            f"Time: {execution_time:.3f}s | "
            f"Timestamp: {timezone.now()}"
        )

    @staticmethod
    def track_cache_hit_rate(cache_key, hit=True):
        """Track cache hit rates for analytics"""
        stats_key = f"cache_stats:{cache_key}"
        stats = cache.get(stats_key, {'hits': 0, 'misses': 0})

        if hit:
            stats['hits'] += 1
        else:
            stats['misses'] += 1

        cache.set(stats_key, stats, 86400)  # Store stats for 24 hours