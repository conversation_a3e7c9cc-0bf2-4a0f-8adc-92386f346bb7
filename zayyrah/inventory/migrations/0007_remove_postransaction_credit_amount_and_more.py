# Generated by Django 5.2.6 on 2025-09-23 09:59

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0002_customer_clearance_custom_date_and_more'),
        ('inventory', '0006_update_stock_decimal_quantities'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.RemoveField(
            model_name='postransaction',
            name='credit_amount',
        ),
        migrations.AddField(
            model_name='postransaction',
            name='loan_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount given as loan to customer', max_digits=12),
        ),
        migrations.AddField(
            model_name='postransaction',
            name='loan_due_date',
            field=models.DateField(blank=True, help_text='When loan amount is due', null=True),
        ),
        migrations.AddField(
            model_name='postransaction',
            name='loan_interest_rate',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Interest rate for loan amount', max_digits=5),
        ),
        migrations.AddField(
            model_name='postransaction',
            name='loan_status',
            field=models.CharField(choices=[('active', 'Active Loan'), ('paid', 'Loan Paid'), ('overdue', 'Overdue'), ('partial', 'Partially Paid')], default='active', help_text='Status of loan component', max_length=20),
        ),
        migrations.AlterField(
            model_name='postransaction',
            name='payment_method',
            field=models.CharField(choices=[('cash', 'Cash'), ('loan', 'Loan (Customer Credit)'), ('card', 'Card/Digital'), ('mixed', 'Mixed (Cash + Loan)')], default='cash', max_length=20),
        ),
        migrations.AlterField(
            model_name='stockbatch',
            name='quantity_received',
            field=models.DecimalField(decimal_places=4, help_text='Total quantity received in this batch', max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.0001'))]),
        ),
        migrations.CreateModel(
            name='CustomerLoan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('loan_amount', models.DecimalField(decimal_places=2, help_text='Original loan amount', max_digits=12)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount paid back', max_digits=12)),
                ('interest_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Interest rate percentage', max_digits=5)),
                ('loan_date', models.DateTimeField(auto_now_add=True)),
                ('due_date', models.DateField(help_text='When full payment is due')),
                ('last_payment_date', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active Loan'), ('paid', 'Fully Paid'), ('overdue', 'Overdue'), ('partial', 'Partially Paid'), ('defaulted', 'Defaulted'), ('cancelled', 'Cancelled')], default='active', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Loan terms and conditions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='customers.customer')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_loans', to=settings.AUTH_USER_MODEL)),
                ('transaction', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='loans', to='inventory.postransaction')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='LoanPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('card', 'Card/Digital'), ('transfer', 'Bank Transfer'), ('adjustment', 'Adjustment')], default='cash', max_length=20)),
                ('payment_date', models.DateTimeField(auto_now_add=True)),
                ('reference_number', models.CharField(blank=True, help_text='Receipt or reference number', max_length=100)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('loan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='inventory.customerloan')),
                ('recorded_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-payment_date'],
            },
        ),
        migrations.AddIndex(
            model_name='customerloan',
            index=models.Index(fields=['customer', 'status'], name='inventory_c_custome_23384e_idx'),
        ),
        migrations.AddIndex(
            model_name='customerloan',
            index=models.Index(fields=['owner', 'due_date'], name='inventory_c_owner_i_ace80e_idx'),
        ),
        migrations.AddIndex(
            model_name='customerloan',
            index=models.Index(fields=['status', 'due_date'], name='inventory_c_status_1ec2fc_idx'),
        ),
        migrations.AddIndex(
            model_name='loanpayment',
            index=models.Index(fields=['loan', 'payment_date'], name='inventory_l_loan_id_3484b0_idx'),
        ),
        migrations.AddIndex(
            model_name='loanpayment',
            index=models.Index(fields=['payment_method', 'payment_date'], name='inventory_l_payment_d92333_idx'),
        ),
    ]
