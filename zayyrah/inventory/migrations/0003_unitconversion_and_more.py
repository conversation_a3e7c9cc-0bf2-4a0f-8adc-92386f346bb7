# Generated by Django 5.2.6 on 2025-09-20 16:28

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_alter_stockbatch_received_date'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='UnitConversion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('from_unit', models.CharField(choices=[('each', 'Each'), ('kg', 'Kilogram'), ('g', 'Gram'), ('l', 'Liter'), ('ml', 'Milliliter'), ('m', 'Meter'), ('cm', 'Centimeter'), ('box', 'Box'), ('pack', 'Pack'), ('custom', 'Custom')], max_length=24)),
                ('to_unit', models.CharField(choices=[('each', 'Each'), ('kg', 'Kilogram'), ('g', 'Gram'), ('l', 'Liter'), ('ml', 'Milliliter'), ('m', 'Meter'), ('cm', 'Centimeter'), ('box', 'Box'), ('pack', 'Pack'), ('custom', 'Custom')], max_length=24)),
                ('conversion_factor', models.DecimalField(decimal_places=6, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.000001'))])),
                ('is_active', models.BooleanField(default=True)),
                ('description', models.CharField(blank=True, help_text="e.g., '1 kg = 1000 g'", max_length=200)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['from_unit', 'to_unit'],
            },
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='allow_fractional_quantities',
            field=models.BooleanField(default=False, help_text='Allow fractional quantities'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='base_unit',
            field=models.CharField(blank=True, choices=[('each', 'Each'), ('kg', 'Kilogram'), ('g', 'Gram'), ('l', 'Liter'), ('ml', 'Milliliter'), ('m', 'Meter'), ('cm', 'Centimeter'), ('box', 'Box'), ('pack', 'Pack'), ('custom', 'Custom')], help_text='Base unit for conversions', max_length=24),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='has_variants',
            field=models.BooleanField(default=False, help_text='Product has color/size/flavor variants'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='perishable',
            field=models.BooleanField(default=False, help_text='Product is perishable'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='shelf_life_days',
            field=models.PositiveIntegerField(blank=True, help_text='Expected shelf life in days', null=True),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='track_expiry',
            field=models.BooleanField(default=False, help_text='Track expiry dates for this product'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='unit_precision',
            field=models.PositiveIntegerField(default=2, help_text='Decimal places for quantities', validators=[django.core.validators.MaxValueValidator(6)]),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='variant_type',
            field=models.CharField(blank=True, help_text='Type of variation (color, size, flavor)', max_length=50),
        ),
        migrations.AddField(
            model_name='productimage',
            name='image',
            field=models.ImageField(blank=True, max_length=500, null=True, upload_to='products/%Y/%m/'),
        ),
        migrations.AddField(
            model_name='productimage',
            name='image_height',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='productimage',
            name='image_size',
            field=models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True),
        ),
        migrations.AddField(
            model_name='productimage',
            name='image_width',
            field=models.PositiveIntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='productimage',
            name='thumbnail',
            field=models.ImageField(blank=True, max_length=500, null=True, upload_to='products/thumbnails/%Y/%m/'),
        ),
        migrations.AddField(
            model_name='productimage',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='productimage',
            name='upload_session_id',
            field=models.CharField(blank=True, help_text='For bulk uploads', max_length=100),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='batch_metadata',
            field=models.JSONField(blank=True, default=dict, help_text='Additional batch-specific data'),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='handling_notes',
            field=models.TextField(blank=True, help_text='Special handling instructions'),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='parent_batch',
            field=models.ForeignKey(blank=True, help_text='Parent batch if this is a split batch', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_batches', to='inventory.stockbatch'),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='quality_score',
            field=models.PositiveIntegerField(default=100, help_text='Quality score (0-100%)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='storage_temperature',
            field=models.CharField(blank=True, help_text='Required storage temperature', max_length=50),
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='temperature_controlled',
            field=models.BooleanField(default=False, help_text='Requires temperature control'),
        ),
        migrations.AddIndex(
            model_name='productimage',
            index=models.Index(fields=['product', 'sort_order'], name='inventory_p_product_97c0f0_idx'),
        ),
        migrations.AddIndex(
            model_name='productimage',
            index=models.Index(fields=['upload_session_id'], name='inventory_p_upload__d7e655_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['quality_score'], name='inventory_s_quality_6b9965_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['temperature_controlled'], name='inventory_s_tempera_3aaf91_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['parent_batch'], name='inventory_s_parent__c7a066_idx'),
        ),
        migrations.AddField(
            model_name='unitconversion',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='unit_conversions', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='unitconversion',
            index=models.Index(fields=['owner', 'from_unit'], name='inventory_u_owner_i_e9e034_idx'),
        ),
        migrations.AddIndex(
            model_name='unitconversion',
            index=models.Index(fields=['owner', 'to_unit'], name='inventory_u_owner_i_b78190_idx'),
        ),
        migrations.AddIndex(
            model_name='unitconversion',
            index=models.Index(fields=['owner', 'from_unit', 'to_unit'], name='inventory_u_owner_i_bf4d68_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='unitconversion',
            unique_together={('owner', 'from_unit', 'to_unit')},
        ),
    ]
