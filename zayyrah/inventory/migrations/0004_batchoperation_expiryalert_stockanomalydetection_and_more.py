# Generated by Django 5.2.6 on 2025-09-20 16:43

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0003_unitconversion_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='BatchOperation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('operation_type', models.CharField(choices=[('split', 'Split Batch'), ('merge', 'Merge Batches'), ('quality_update', 'Quality Update'), ('temperature_breach', 'Temperature Breach'), ('status_change', 'Status Change'), ('parent_change', 'Parent Batch Change')], max_length=20)),
                ('source_batches', models.JSONField(help_text='List of source batch IDs and details')),
                ('target_batches', models.J<PERSON><PERSON><PERSON>(help_text='List of target batch IDs and details')),
                ('operation_metadata', models.J<PERSON><PERSON><PERSON>(default=dict, help_text='Additional operation data')),
                ('reason', models.TextField(help_text='Reason for the operation')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ExpiryAlert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('alert_type', models.CharField(choices=[('approaching_expiry', 'Approaching Expiry'), ('expired', 'Expired'), ('quality_degradation', 'Quality Degradation'), ('temperature_breach', 'Temperature Breach'), ('shelf_life_exceeded', 'Shelf Life Exceeded')], max_length=20)),
                ('status', models.CharField(choices=[('active', 'Active'), ('acknowledged', 'Acknowledged'), ('resolved', 'Resolved'), ('dismissed', 'Dismissed')], default='active', max_length=15)),
                ('priority', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('alert_date', models.DateTimeField(auto_now_add=True)),
                ('expiry_date', models.DateField()),
                ('days_until_expiry', models.IntegerField()),
                ('message', models.TextField()),
                ('alert_metadata', models.JSONField(default=dict, help_text='Additional alert data')),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-alert_date'],
            },
        ),
        migrations.CreateModel(
            name='StockAnomalyDetection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('anomaly_type', models.CharField(choices=[('unusual_quantity', 'Unusual Quantity Movement'), ('rapid_movements', 'Rapid Consecutive Movements'), ('quality_drop', 'Quality Score Drop'), ('temperature_breach', 'Temperature Breach'), ('unauthorized_movement', 'Unauthorized Movement'), ('negative_stock_warning', 'Negative Stock Warning'), ('price_variance', 'Price Variance'), ('pattern_deviation', 'Pattern Deviation')], max_length=25)),
                ('severity', models.CharField(choices=[('low', 'Low'), ('medium', 'Medium'), ('high', 'High'), ('critical', 'Critical')], default='medium', max_length=10)),
                ('detected_at', models.DateTimeField(auto_now_add=True)),
                ('anomaly_data', models.JSONField(default=dict, help_text='Detailed anomaly information')),
                ('severity_score', models.PositiveIntegerField(default=50, help_text='Severity score 0-100', validators=[django.core.validators.MaxValueValidator(100)])),
                ('is_false_positive', models.BooleanField(default=False)),
                ('is_resolved', models.BooleanField(default=False)),
                ('reviewed_at', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True)),
            ],
            options={
                'ordering': ['-detected_at'],
            },
        ),
        migrations.AddField(
            model_name='unitconversion',
            name='context_type',
            field=models.CharField(blank=True, help_text="Product category context (e.g., 'food_items', 'textiles')", max_length=50),
        ),
        migrations.AddField(
            model_name='unitconversion',
            name='conversion_path',
            field=models.JSONField(blank=True, default=list, help_text='Chain of conversions for complex unit paths'),
        ),
        migrations.AddField(
            model_name='unitconversion',
            name='is_system_defined',
            field=models.BooleanField(default=False, help_text='System vs user-defined conversion'),
        ),
        migrations.AddField(
            model_name='unitconversion',
            name='precision_level',
            field=models.PositiveIntegerField(default=6, help_text='Decimal precision for conversions', validators=[django.core.validators.MaxValueValidator(10)]),
        ),
        migrations.AddIndex(
            model_name='unitconversion',
            index=models.Index(fields=['context_type', 'is_active'], name='inventory_u_context_9c3646_idx'),
        ),
        migrations.AddIndex(
            model_name='unitconversion',
            index=models.Index(fields=['is_system_defined', 'is_active'], name='inventory_u_is_syst_e05868_idx'),
        ),
        migrations.AddField(
            model_name='batchoperation',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='batch_operations', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='batchoperation',
            name='performed_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='batch_operations_performed', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='expiryalert',
            name='acknowledged_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='acknowledged_alerts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='expiryalert',
            name='batch',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expiry_alerts', to='inventory.stockbatch'),
        ),
        migrations.AddField(
            model_name='expiryalert',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expiry_alerts', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockanomalydetection',
            name='batch',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='anomalies', to='inventory.stockbatch'),
        ),
        migrations.AddField(
            model_name='stockanomalydetection',
            name='movement',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='anomalies', to='inventory.stockmovement'),
        ),
        migrations.AddField(
            model_name='stockanomalydetection',
            name='owner',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_anomalies', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockanomalydetection',
            name='product',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='anomalies', to='inventory.enhancedproduct'),
        ),
        migrations.AddField(
            model_name='stockanomalydetection',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_anomalies', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='batchoperation',
            index=models.Index(fields=['owner', 'operation_type', 'created_at'], name='inventory_b_owner_i_76d94f_idx'),
        ),
        migrations.AddIndex(
            model_name='batchoperation',
            index=models.Index(fields=['created_at'], name='inventory_b_created_8a13d4_idx'),
        ),
        migrations.AddIndex(
            model_name='batchoperation',
            index=models.Index(fields=['performed_by', 'created_at'], name='inventory_b_perform_cf381b_idx'),
        ),
        migrations.AddIndex(
            model_name='expiryalert',
            index=models.Index(fields=['owner', 'status', 'alert_date'], name='inventory_e_owner_i_a57f8f_idx'),
        ),
        migrations.AddIndex(
            model_name='expiryalert',
            index=models.Index(fields=['batch', 'alert_type'], name='inventory_e_batch_i_bd8692_idx'),
        ),
        migrations.AddIndex(
            model_name='expiryalert',
            index=models.Index(fields=['expiry_date'], name='inventory_e_expiry__ec51f5_idx'),
        ),
        migrations.AddIndex(
            model_name='expiryalert',
            index=models.Index(fields=['priority', 'status'], name='inventory_e_priorit_a3fbcc_idx'),
        ),
        migrations.AddIndex(
            model_name='expiryalert',
            index=models.Index(fields=['alert_type', 'status'], name='inventory_e_alert_t_403336_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['owner', 'anomaly_type', 'detected_at'], name='inventory_s_owner_i_df6855_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['severity_score', 'is_false_positive'], name='inventory_s_severit_dd6cff_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['detected_at'], name='inventory_s_detecte_5f823d_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['severity', 'is_resolved'], name='inventory_s_severit_e964d0_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['product', 'anomaly_type'], name='inventory_s_product_b52cd3_idx'),
        ),
        migrations.AddIndex(
            model_name='stockanomalydetection',
            index=models.Index(fields=['batch', 'anomaly_type'], name='inventory_s_batch_i_06300c_idx'),
        ),
    ]
