# Generated by Django 5.2.6 on 2025-09-21 01:26

import django.core.validators
import django.db.models.deletion
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0002_customer_clearance_custom_date_and_more'),
        ('inventory', '0004_batchoperation_expiryalert_stockanomalydetection_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='enhancedproduct',
            name='allow_discount',
            field=models.BooleanField(default=True, help_text='Allow discounts on this product'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='max_discount_percent',
            field=models.DecimalField(decimal_places=2, default=Decimal('100.00'), help_text='Maximum discount percentage allowed', max_digits=5),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_button_color',
            field=models.CharField(blank=True, help_text='Button color in POS (hex code)', max_length=7),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_category_override',
            field=models.CharField(blank=True, help_text='POS category override', max_length=50),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_display_name',
            field=models.CharField(blank=True, help_text='Display name in POS (optional)', max_length=100),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_enabled',
            field=models.BooleanField(default=True, help_text='Available for POS sales'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_price_override',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Override selling price for POS (optional)', max_digits=12, null=True),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='pos_sort_order',
            field=models.PositiveIntegerField(default=0, help_text='Sort order in POS interface'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='quick_sale_enabled',
            field=models.BooleanField(default=False, help_text='Enable for quick sale buttons'),
        ),
        migrations.CreateModel(
            name='POSTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_number', models.CharField(help_text='Unique transaction identifier', max_length=50, unique=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('completed', 'Completed'), ('cancelled', 'Cancelled'), ('refunded', 'Refunded')], default='active', max_length=20)),
                ('subtotal', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('tax_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('discount_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('payment_method', models.CharField(choices=[('cash', 'Cash'), ('credit', 'Customer Credit'), ('card', 'Card/Digital'), ('mixed', 'Mixed Payment')], default='cash', max_length=20)),
                ('payment_status', models.CharField(choices=[('pending', 'Payment Pending'), ('partial', 'Partially Paid'), ('paid', 'Fully Paid'), ('refunded', 'Refunded')], default='pending', max_length=20)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('cash_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('credit_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('card_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('change_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('notes', models.TextField(blank=True, help_text='Transaction notes or special instructions')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cashier', models.ForeignKey(blank=True, help_text='Staff member who processed the transaction', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cashier_transactions', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pos_transactions', to='customers.customer')),
                ('location', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='pos_transactions', to='inventory.location')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pos_transactions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='POSTransactionItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.DecimalField(decimal_places=3, max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.001'))])),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('original_price', models.DecimalField(decimal_places=2, help_text='Original product price', max_digits=12)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('line_total', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('stock_movements', models.JSONField(default=list, help_text='Record of stock movements for this item')),
                ('stock_processed', models.BooleanField(default=False, help_text='Whether stock has been deducted')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='pos_transaction_items', to='inventory.enhancedproduct')),
                ('transaction', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.postransaction')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.AddIndex(
            model_name='postransaction',
            index=models.Index(fields=['owner', 'status', 'created_at'], name='inventory_p_owner_i_409ab7_idx'),
        ),
        migrations.AddIndex(
            model_name='postransaction',
            index=models.Index(fields=['customer', 'created_at'], name='inventory_p_custome_ee0787_idx'),
        ),
        migrations.AddIndex(
            model_name='postransaction',
            index=models.Index(fields=['transaction_number'], name='inventory_p_transac_ec5cbd_idx'),
        ),
        migrations.AddIndex(
            model_name='postransaction',
            index=models.Index(fields=['payment_status', 'created_at'], name='inventory_p_payment_ea26ef_idx'),
        ),
        migrations.AddIndex(
            model_name='postransaction',
            index=models.Index(fields=['cashier', 'created_at'], name='inventory_p_cashier_1a1ec7_idx'),
        ),
        migrations.AddIndex(
            model_name='postransactionitem',
            index=models.Index(fields=['transaction', 'product'], name='inventory_p_transac_cc3a6e_idx'),
        ),
        migrations.AddIndex(
            model_name='postransactionitem',
            index=models.Index(fields=['product', 'created_at'], name='inventory_p_product_b0df5c_idx'),
        ),
        migrations.AddIndex(
            model_name='postransactionitem',
            index=models.Index(fields=['stock_processed'], name='inventory_p_stock_p_9fdb56_idx'),
        ),
    ]
