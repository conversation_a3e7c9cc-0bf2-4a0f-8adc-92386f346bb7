# Generated by Django 5.2.6 on 2025-09-23 06:05

from django.db import migrations, models
from decimal import Decimal


def migrate_quantities_to_decimal(apps, schema_editor):
    """Convert existing integer quantities to decimal"""
    # No data conversion needed since we're going from int to decimal
    # Django handles this automatically
    pass


def reverse_migration(apps, schema_editor):
    """Convert back to integer (with potential data loss)"""
    # This is irreversible without data loss
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0005_enhancedproduct_allow_discount_and_more'),
    ]

    operations = [
        # Update StockMovement.quantity to DecimalField
        migrations.AlterField(
            model_name='stockmovement',
            name='quantity',
            field=models.DecimalField(
                max_digits=10,
                decimal_places=4,
                help_text="Quantity moved (can be negative for outgoing stock)"
            ),
        ),

        # Update StockBatch quantity fields to DecimalField
        migrations.AlterField(
            model_name='stockbatch',
            name='quantity_received',
            field=models.DecimalField(
                max_digits=10,
                decimal_places=4,
                help_text="Total quantity received in this batch"
            ),
        ),

        migrations.AlterField(
            model_name='stockbatch',
            name='quantity_available',
            field=models.DecimalField(
                max_digits=10,
                decimal_places=4,
                help_text="Quantity currently available in this batch"
            ),
        ),

        migrations.AlterField(
            model_name='stockbatch',
            name='quantity_reserved',
            field=models.DecimalField(
                max_digits=10,
                decimal_places=4,
                default=Decimal('0'),
                help_text="Quantity reserved but not yet consumed"
            ),
        ),

        migrations.AlterField(
            model_name='stockbatch',
            name='quantity_damaged',
            field=models.DecimalField(
                max_digits=10,
                decimal_places=4,
                default=Decimal('0'),
                help_text="Quantity marked as damaged/unusable"
            ),
        ),

        # Run data migration
        migrations.RunPython(
            migrate_quantities_to_decimal,
            reverse_migration,
        ),
    ]
