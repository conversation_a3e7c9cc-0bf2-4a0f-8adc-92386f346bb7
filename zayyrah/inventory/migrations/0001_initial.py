# Generated by Django 5.2.6 on 2025-09-19 07:42

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=120)),
                ('level', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to=settings.AUTH_USER_MODEL)),
                ('parent', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='inventory.category')),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['level', 'sort_order', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Location',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('code', models.CharField(max_length=20)),
                ('location_type', models.CharField(choices=[('warehouse', 'Warehouse'), ('store', 'Store'), ('section', 'Section')], default='store', max_length=20)),
                ('address', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductBrand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='brands', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EnhancedProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True)),
                ('sku', models.CharField(blank=True, max_length=64)),
                ('barcode', models.CharField(blank=True, max_length=50)),
                ('qr_code', models.CharField(blank=True, max_length=100)),
                ('purchase_price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('wholesale_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('minimum_selling_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('track_stock', models.BooleanField(default=True)),
                ('allow_negative_stock', models.BooleanField(default=False)),
                ('reorder_level', models.PositiveIntegerField(default=5)),
                ('maximum_stock_level', models.PositiveIntegerField(blank=True, null=True)),
                ('unit', models.CharField(choices=[('each', 'Each'), ('kg', 'Kilogram'), ('g', 'Gram'), ('l', 'Liter'), ('ml', 'Milliliter'), ('m', 'Meter'), ('cm', 'Centimeter'), ('box', 'Box'), ('pack', 'Pack'), ('custom', 'Custom')], default='each', max_length=24)),
                ('unit_custom_label', models.CharField(blank=True, max_length=32)),
                ('weight', models.DecimalField(blank=True, decimal_places=3, max_digits=8, null=True)),
                ('dimensions', models.CharField(blank=True, help_text='L×W×H', max_length=50)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('tax_exempt', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('is_featured', models.BooleanField(default=False)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.category')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enhanced_products', to=settings.AUTH_USER_MODEL)),
                ('brand', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.productbrand')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ProductImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_primary', models.BooleanField(default=False)),
                ('alt_text', models.CharField(blank=True, max_length=200)),
                ('sort_order', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='images', to='inventory.enhancedproduct')),
            ],
            options={
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='StockBatch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('batch_number', models.CharField(max_length=50)),
                ('purchase_order_number', models.CharField(blank=True, max_length=50)),
                ('quantity_received', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('quantity_available', models.PositiveIntegerField()),
                ('quantity_reserved', models.PositiveIntegerField(default=0)),
                ('quantity_damaged', models.PositiveIntegerField(default=0)),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=12, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('received_date', models.DateField(default=django.utils.timezone.now)),
                ('manufacture_date', models.DateField(blank=True, null=True)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('status', models.CharField(choices=[('active', 'Active'), ('reserved', 'Reserved'), ('expired', 'Expired'), ('damaged', 'Damaged'), ('sold_out', 'Sold Out')], default='active', max_length=20)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_batches', to='inventory.location')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_batches', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_batches', to='inventory.enhancedproduct')),
            ],
            options={
                'ordering': ['-received_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_type', models.CharField(choices=[('purchase', 'Purchase'), ('sale', 'Sale'), ('return', 'Return'), ('adjustment', 'Adjustment'), ('transfer', 'Transfer'), ('damage', 'Damage'), ('expired', 'Expired')], max_length=20)),
                ('quantity', models.IntegerField()),
                ('reference_number', models.CharField(blank=True, max_length=50)),
                ('reference_type', models.CharField(blank=True, max_length=20)),
                ('reference_id', models.PositiveIntegerField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('batch', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='movements', to='inventory.stockbatch')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_movements_created', to=settings.AUTH_USER_MODEL)),
                ('location', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.location')),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.enhancedproduct')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Supplier',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('contact_person', models.CharField(blank=True, max_length=100)),
                ('phone', models.CharField(blank=True, max_length=15)),
                ('email', models.EmailField(blank=True, max_length=254)),
                ('address', models.TextField(blank=True)),
                ('tax_number', models.CharField(blank=True, max_length=50)),
                ('payment_terms', models.CharField(blank=True, max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('rating', models.PositiveIntegerField(default=5, validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)])),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='suppliers', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='stockbatch',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_batches', to='inventory.supplier'),
        ),
        migrations.AddField(
            model_name='enhancedproduct',
            name='supplier',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='inventory.supplier'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['owner', 'parent', 'is_active'], name='inventory_c_owner_i_1bc1bc_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['level', 'sort_order'], name='inventory_c_level_5b5095_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='category',
            unique_together={('owner', 'parent', 'name')},
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['owner', 'is_active'], name='inventory_l_owner_i_396f05_idx'),
        ),
        migrations.AddIndex(
            model_name='location',
            index=models.Index(fields=['code'], name='inventory_l_code_55fb32_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='location',
            unique_together={('owner', 'code')},
        ),
        migrations.AddIndex(
            model_name='productbrand',
            index=models.Index(fields=['owner', 'is_active'], name='inventory_p_owner_i_3ae0e1_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='productbrand',
            unique_together={('owner', 'name')},
        ),
        migrations.AddIndex(
            model_name='productimage',
            index=models.Index(fields=['product', 'is_primary'], name='inventory_p_product_d9a757_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['owner', 'product', 'created_at'], name='inventory_s_owner_i_0c1f57_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['movement_type', 'created_at'], name='inventory_s_movemen_ed5291_idx'),
        ),
        migrations.AddIndex(
            model_name='stockmovement',
            index=models.Index(fields=['batch', 'created_at'], name='inventory_s_batch_i_8a6d71_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['owner', 'is_active'], name='inventory_s_owner_i_36c0fc_idx'),
        ),
        migrations.AddIndex(
            model_name='supplier',
            index=models.Index(fields=['name'], name='inventory_s_name_d435cf_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='supplier',
            unique_together={('owner', 'name')},
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['owner', 'product', 'status'], name='inventory_s_owner_i_a71e32_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['expiry_date'], name='inventory_s_expiry__709598_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['received_date'], name='inventory_s_receive_119a66_idx'),
        ),
        migrations.AddIndex(
            model_name='stockbatch',
            index=models.Index(fields=['batch_number'], name='inventory_s_batch_n_3f0960_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockbatch',
            unique_together={('owner', 'batch_number', 'product')},
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['owner', 'is_active'], name='inventory_e_owner_i_ebe875_idx'),
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['category', 'is_active'], name='inventory_e_categor_b34826_idx'),
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['sku'], name='inventory_e_sku_2f6045_idx'),
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['barcode'], name='inventory_e_barcode_2bcf69_idx'),
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['reorder_level'], name='inventory_e_reorder_81432f_idx'),
        ),
        migrations.AddIndex(
            model_name='enhancedproduct',
            index=models.Index(fields=['name'], name='inventory_e_name_af8477_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='enhancedproduct',
            unique_together={('owner', 'barcode'), ('owner', 'sku')},
        ),
    ]
