from django.urls import path, include
from . import views

app_name = 'inventory'

urlpatterns = [
    # Category management URLs
    path('categories/', views.category_list, name='category-list'),
    path('categories/create/', views.category_create, name='category-create'),
    path('categories/<int:pk>/', views.category_detail, name='category-detail'),
    path('categories/<int:pk>/edit/', views.category_update, name='category-update'),
    path('categories/<int:pk>/delete/', views.category_delete, name='category-delete'),

    # Product management URLs (replacing POS products)
    path('products/', views.product_list, name='product-list'),
    path('products/add/', views.product_create, name='product-create'),
    path('products/<int:pk>/', views.product_detail, name='product-detail'),
    path('products/<int:pk>/edit/', views.product_update, name='product-update'),

    # POS interface
    path('pos/', views.pos_interface, name='pos-interface'),
    path('pos/transactions/', views.pos_transactions, name='pos-transactions'),

    # POS API endpoints
    path('pos/api/', include('inventory.pos_urls')),

    # Cart API endpoints
    path('cart/api/', include('inventory.cart_urls')),

    # AJAX endpoints
    path('ajax/categories/tree/', views.category_tree_ajax, name='category-tree-ajax'),
    path('ajax/categories/<int:pk>/move/', views.category_move_ajax, name='category-move-ajax'),
]