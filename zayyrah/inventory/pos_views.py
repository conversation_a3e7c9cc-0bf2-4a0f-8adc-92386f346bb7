"""
POS API Views for handling Point of Sale operations
"""

from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.db import transaction, models
import json

from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from .models import EnhancedProduct, POSTransaction, POSTransactionItem
from .services import POSService, POSReportingService, POSValidationService
from customers.models import Customer


class POSProductListView(APIView):
    """
    📱 OFFICIAL POS PRODUCTS API for Mobile Apps

    This is the recommended API endpoint for POS mobile applications.

    Features:
    - POS-optimized product data
    - Real-time stock quantities
    - Mobile-friendly response format
    - Discount and pricing controls
    - Quick-sale button configuration

    Usage: GET /inventory/pos/api/products/
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        pos_service = POSService(request.user)

        # Get query parameters
        category_id = request.GET.get('category')
        search = request.GET.get('search', '')

        try:
            # Get products
            products = pos_service.get_pos_products(category_id, search)

            # Serialize products
            products_data = []
            for product in products:
                products_data.append({
                    'id': product.id,
                    'name': product.pos_name,
                    'sku': product.sku,
                    'barcode': product.barcode,
                    'price': float(product.pos_price),
                    'original_price': float(product.selling_price),
                    'current_stock': float(product.current_stock),
                    'unit_label': product.unit_label,
                    'category_name': product.category.name if product.category else '',
                    'allow_discount': product.allow_discount,
                    'max_discount_percent': float(product.max_discount_percent),
                    'quick_sale_enabled': product.quick_sale_enabled,
                    'pos_button_color': product.pos_button_color,
                    'image_url': product.primary_image.image.url if hasattr(product, 'primary_image') and product.primary_image else None
                })

            return Response({
                'success': True,
                'products': products_data,
                'count': len(products_data)
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSTransactionView(APIView):
    """API view for managing POS transactions"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Create new POS transaction"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get customer if specified
            customer = None
            customer_id = data.get('customer_id')
            if customer_id:
                customer = get_object_or_404(Customer, id=customer_id, owner=request.user)

            # Create transaction
            transaction = pos_service.create_transaction(
                customer=customer,
                notes=data.get('notes', '')
            )

            return Response({
                'success': True,
                'transaction': {
                    'id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'status': transaction.status,
                    'total_amount': float(transaction.total_amount),
                    'customer_name': transaction.customer.display_name if transaction.customer else None
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def get(self, request, transaction_id):
        """Get transaction details"""
        try:
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )

            # Get transaction items
            items = []
            for item in transaction.items.all():
                items.append({
                    'id': item.id,
                    'product_id': item.product.id,
                    'product_name': item.product.pos_name,
                    'quantity': float(item.quantity),
                    'unit_price': float(item.unit_price),
                    'discount_percent': float(item.discount_percent),
                    'discount_amount': float(item.discount_amount),
                    'line_total': float(item.line_total),
                    'tax_amount': float(item.tax_amount),
                    'unit_label': item.product.unit_label
                })

            return Response({
                'success': True,
                'transaction': {
                    'id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'status': transaction.status,
                    'subtotal': float(transaction.subtotal),
                    'tax_total': float(transaction.tax_total),
                    'discount_total': float(transaction.discount_total),
                    'total_amount': float(transaction.total_amount),
                    'payment_method': transaction.payment_method,
                    'payment_status': transaction.payment_status,
                    'amount_paid': float(transaction.amount_paid),
                    'customer_name': transaction.customer.display_name if transaction.customer else None,
                    'items': items,
                    'total_items': float(transaction.total_items),
                    'items_count': transaction.total_item_count
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSTransactionItemView(APIView):
    """API view for managing transaction items"""
    permission_classes = [IsAuthenticated]

    def post(self, request, transaction_id):
        """Add item to transaction"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get transaction and product
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )
            product = get_object_or_404(
                EnhancedProduct,
                id=data['product_id'],
                owner=request.user
            )

            # Add item to transaction
            success, message, item = pos_service.add_item_to_transaction(
                transaction=transaction,
                product=product,
                quantity=Decimal(str(data['quantity'])),
                custom_price=Decimal(str(data.get('custom_price', 0))) if data.get('custom_price') else None,
                discount_percent=Decimal(str(data.get('discount_percent', 0))),
                discount_amount=Decimal(str(data.get('discount_amount', 0)))
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'item': {
                        'id': item.id,
                        'product_name': item.product.pos_name,
                        'quantity': float(item.quantity),
                        'unit_price': float(item.unit_price),
                        'line_total': float(item.line_total)
                    },
                    'transaction_total': float(transaction.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request, transaction_id, item_id):
        """Update item quantity"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get transaction and item
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )
            item = get_object_or_404(
                POSTransactionItem,
                id=item_id,
                transaction=transaction
            )

            # Update quantity
            success, message = pos_service.update_item_quantity(
                transaction=transaction,
                item=item,
                new_quantity=Decimal(str(data['quantity']))
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'transaction_total': float(transaction.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request, transaction_id, item_id):
        """Remove item from transaction"""
        try:
            pos_service = POSService(request.user)

            # Get transaction and item
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )
            item = get_object_or_404(
                POSTransactionItem,
                id=item_id,
                transaction=transaction
            )

            # Remove item
            success, message = pos_service.remove_item_from_transaction(
                transaction=transaction,
                item=item
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'transaction_total': float(transaction.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSPaymentView(APIView):
    """API view for processing payments"""
    permission_classes = [IsAuthenticated]

    def post(self, request, transaction_id):
        """Process payment for transaction"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get transaction
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )

            # Process payment with enhanced loan support
            success, message, payment_info = pos_service.process_payment(
                transaction=transaction,
                cash_amount=Decimal(str(data.get('cash_amount', 0))),
                loan_amount=Decimal(str(data.get('loan_amount', 0))),
                card_amount=Decimal(str(data.get('card_amount', 0))),
                loan_due_date=data.get('loan_due_date'),
                loan_interest_rate=Decimal(str(data.get('loan_interest_rate', 0)))
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'payment_info': {
                        'payment_method': payment_info['payment_method'],
                        'total_paid': float(payment_info['total_paid']),
                        'change_amount': float(payment_info['change_amount']),
                        'remaining_balance': float(payment_info['remaining_balance'])
                    },
                    'transaction': {
                        'payment_status': transaction.payment_status,
                        'amount_paid': float(transaction.amount_paid)
                    }
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSCompleteTransactionView(APIView):
    """API view for completing transactions"""
    permission_classes = [IsAuthenticated]

    def post(self, request, transaction_id):
        """Complete transaction and process stock deduction"""
        try:
            pos_service = POSService(request.user)

            # Get transaction
            transaction = get_object_or_404(
                POSTransaction,
                id=transaction_id,
                owner=request.user
            )

            # Complete transaction
            success, message, result = pos_service.complete_transaction(transaction)

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'result': {
                        'transaction_number': result['transaction_number'],
                        'total_amount': float(result['total_amount']),
                        'payment_method': result['payment_method'],
                        'change_amount': float(result['change_amount']),
                        'items_count': result['items_count'],
                        'completed_at': result['completed_at'].isoformat()
                    }
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSCustomerListView(APIView):
    """API view for getting customers for POS"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get customers for POS selection"""
        try:
            search = request.GET.get('search', '')

            customers = Customer.objects.filter(owner=request.user)

            if search:
                customers = customers.filter(
                    models.Q(name__icontains=search) |
                    models.Q(mobile_number__icontains=search)
                )

            customers_data = []
            for customer in customers[:20]:  # Limit to 20 results
                customers_data.append({
                    'id': customer.id,
                    'name': customer.display_name,
                    'mobile_number': customer.mobile_number,
                    'credit_limit': float(customer.credit_limit),
                    'current_balance': float(customer.current_balance),
                    'available_credit': float(customer.available_credit)
                })

            return Response({
                'success': True,
                'customers': customers_data
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class POSReportsView(APIView):
    """API view for POS reports"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get POS reports"""
        try:
            reporting_service = POSReportingService(request.user)
            report_type = request.GET.get('type', 'daily')

            if report_type == 'daily':
                from django.utils import timezone
                date_str = request.GET.get('date')
                date = timezone.datetime.strptime(date_str, '%Y-%m-%d').date() if date_str else None

                report = reporting_service.get_daily_sales_summary(date)

                return Response({
                    'success': True,
                    'report': {
                        'date': report['date'].isoformat(),
                        'total_sales': float(report['total_sales']),
                        'total_transactions': report['total_transactions'],
                        'cash_sales': float(report['cash_sales']),
                        'credit_sales': float(report['credit_sales']),
                        'card_sales': float(report['card_sales'])
                    }
                })

            elif report_type == 'low_stock':
                threshold = int(request.GET.get('threshold', 10))
                alerts = reporting_service.get_low_stock_alerts(threshold)

                return Response({
                    'success': True,
                    'alerts': alerts
                })

            else:
                return Response({
                    'success': False,
                    'error': 'Invalid report type'
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_transaction_item(request):
    """Validate transaction item before adding"""
    try:
        data = request.data

        product = get_object_or_404(
            EnhancedProduct,
            id=data['product_id'],
            owner=request.user
        )

        is_valid, errors = POSValidationService.validate_transaction_item(
            product=product,
            quantity=Decimal(str(data['quantity'])),
            discount_percent=Decimal(str(data.get('discount_percent', 0)))
        )

        return Response({
            'success': is_valid,
            'errors': errors
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_payment_amounts(request):
    """Validate payment amounts"""
    try:
        data = request.data

        is_valid, errors = POSValidationService.validate_payment_amounts(
            total_amount=Decimal(str(data['total_amount'])),
            cash_amount=Decimal(str(data.get('cash_amount', 0))),
            loan_amount=Decimal(str(data.get('loan_amount', 0))),
            card_amount=Decimal(str(data.get('card_amount', 0)))
        )

        return Response({
            'success': is_valid,
            'errors': errors
        })

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class POSTransactionListView(APIView):
    """
    Comprehensive POS Transactions List API with Advanced Filtering

    Ultra-thinking filtering capabilities:
    - Date range filtering (default: today)
    - Status filtering
    - Payment method filtering
    - Customer filtering
    - Amount range filtering
    - Search across multiple fields
    - Pagination
    - Sorting
    """
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get filtered list of POS transactions"""
        print(f"\n=== POS TRANSACTIONS LIST API ===")
        print(f"Method: GET")
        print(f"User: {request.user}")
        print(f"Query Params: {dict(request.GET)}")

        try:
            from datetime import datetime, date, timedelta
            from django.utils import timezone
            from django.db.models import Q, Count, Sum
            from django.core.paginator import Paginator

            # Base queryset - user's transactions
            transactions = POSTransaction.objects.filter(
                owner=request.user
            ).select_related('customer').prefetch_related('items__product')

            # === DATE FILTERING (Default: Today) ===
            date_filter = request.GET.get('date_filter', 'today')
            today = timezone.now().date()

            if date_filter == 'today':
                transactions = transactions.filter(created_at__date=today)
            elif date_filter == 'yesterday':
                yesterday = today - timedelta(days=1)
                transactions = transactions.filter(created_at__date=yesterday)
            elif date_filter == 'this_week':
                week_start = today - timedelta(days=today.weekday())
                transactions = transactions.filter(created_at__date__gte=week_start)
            elif date_filter == 'this_month':
                month_start = today.replace(day=1)
                transactions = transactions.filter(created_at__date__gte=month_start)
            elif date_filter == 'last_month':
                last_month_end = today.replace(day=1) - timedelta(days=1)
                last_month_start = last_month_end.replace(day=1)
                transactions = transactions.filter(
                    created_at__date__gte=last_month_start,
                    created_at__date__lte=last_month_end
                )
            elif date_filter == 'custom':
                # Custom date range
                start_date = request.GET.get('start_date')
                end_date = request.GET.get('end_date')
                if start_date:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                    transactions = transactions.filter(created_at__date__gte=start_date)
                if end_date:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                    transactions = transactions.filter(created_at__date__lte=end_date)

            print(f"Date Filter Applied: {date_filter}")

            # === STATUS FILTERING ===
            status_filter = request.GET.get('status')
            if status_filter:
                transactions = transactions.filter(status=status_filter)
                print(f"Status Filter: {status_filter}")

            # === PAYMENT METHOD FILTERING ===
            payment_method = request.GET.get('payment_method')
            if payment_method:
                transactions = transactions.filter(payment_method=payment_method)
                print(f"Payment Method Filter: {payment_method}")

            # === CUSTOMER FILTERING ===
            customer_id = request.GET.get('customer_id')
            if customer_id:
                transactions = transactions.filter(customer_id=customer_id)
                print(f"Customer Filter: {customer_id}")

            # === AMOUNT RANGE FILTERING ===
            min_amount = request.GET.get('min_amount')
            max_amount = request.GET.get('max_amount')
            if min_amount:
                transactions = transactions.filter(total_amount__gte=float(min_amount))
            if max_amount:
                transactions = transactions.filter(total_amount__lte=float(max_amount))
            if min_amount or max_amount:
                print(f"Amount Range: {min_amount} - {max_amount}")

            # === SEARCH FUNCTIONALITY ===
            search = request.GET.get('search', '').strip()
            if search:
                transactions = transactions.filter(
                    Q(transaction_number__icontains=search) |
                    Q(customer__name__icontains=search) |
                    Q(customer__mobile_number__icontains=search) |
                    Q(notes__icontains=search) |
                    Q(items__product__name__icontains=search)
                ).distinct()
                print(f"Search Query: {search}")

            # === SORTING ===
            sort_by = request.GET.get('sort_by', '-created_at')
            valid_sort_fields = [
                'created_at', '-created_at',
                'total_amount', '-total_amount',
                'transaction_number', '-transaction_number',
                'status', '-status'
            ]
            if sort_by in valid_sort_fields:
                transactions = transactions.order_by(sort_by)
            else:
                transactions = transactions.order_by('-created_at')

            print(f"Sort By: {sort_by}")

            # === PAGINATION ===
            page_size = int(request.GET.get('page_size', 20))
            page_number = int(request.GET.get('page', 1))

            paginator = Paginator(transactions, page_size)
            page_obj = paginator.get_page(page_number)

            # === PREPARE TRANSACTION DATA ===
            transaction_list = []
            for transaction in page_obj:
                # Calculate totals
                items_data = []
                for item in transaction.items.all():
                    items_data.append({
                        'id': item.id,
                        'product_name': item.product.name,
                        'quantity': float(item.quantity),
                        'unit_price': float(item.unit_price),
                        'line_total': float(item.line_total),
                        'discount_amount': float(item.discount_amount)
                    })

                transaction_data = {
                    'id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'status': transaction.status,
                    'created_at': transaction.created_at.isoformat(),
                    'updated_at': transaction.updated_at.isoformat(),
                    'total_amount': float(transaction.total_amount),
                    'subtotal': float(transaction.subtotal),
                    'tax_total': float(transaction.tax_total),
                    'discount_total': float(transaction.discount_total),
                    'payment_method': transaction.payment_method,
                    'cash_amount': float(transaction.cash_amount),
                    'loan_amount': float(transaction.loan_amount),
                    'card_amount': float(transaction.card_amount),
                    'change_amount': float(transaction.change_amount),
                    'items_count': transaction.total_item_count,
                    'total_items': float(transaction.total_items),
                    'notes': transaction.notes or '',
                    'customer': {
                        'id': transaction.customer.id if transaction.customer else None,
                        'name': transaction.customer.display_name if transaction.customer else None,
                        'mobile_number': transaction.customer.mobile_number if transaction.customer else None
                    } if transaction.customer else None,
                    'items': items_data
                }
                transaction_list.append(transaction_data)

            # === SUMMARY STATISTICS ===
            total_transactions = paginator.count
            total_amount = transactions.aggregate(total=Sum('total_amount'))['total'] or 0

            # Group by status for quick stats
            status_summary = transactions.values('status').annotate(
                count=Count('id'),
                total=Sum('total_amount')
            )

            # Payment method summary
            payment_summary = transactions.values('payment_method').annotate(
                count=Count('id'),
                total=Sum('total_amount')
            )

            response_data = {
                'success': True,
                'transactions': transaction_list,
                'pagination': {
                    'current_page': page_obj.number,
                    'total_pages': paginator.num_pages,
                    'total_transactions': total_transactions,
                    'page_size': page_size,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                },
                'summary': {
                    'total_transactions': total_transactions,
                    'total_amount': float(total_amount),
                    'status_breakdown': list(status_summary),
                    'payment_method_breakdown': list(payment_summary)
                },
                'filters_applied': {
                    'date_filter': date_filter,
                    'status_filter': status_filter,
                    'payment_method': payment_method,
                    'customer_id': customer_id,
                    'min_amount': min_amount,
                    'max_amount': max_amount,
                    'search': search,
                    'sort_by': sort_by
                }
            }

            print(f"Returning {len(transaction_list)} transactions")
            print(f"Total Amount: {total_amount}")

            return Response(response_data)

        except Exception as e:
            print(f"Error in POSTransactionListView: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
