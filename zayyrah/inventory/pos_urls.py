"""
URL patterns for POS API endpoints
"""

from django.urls import path
from . import pos_views

app_name = 'pos_api'

urlpatterns = [
    # Product endpoints
    path('products/', pos_views.POSProductListView.as_view(), name='product-list'),

    # Transaction endpoints
    path('transactions/', pos_views.POSTransactionView.as_view(), name='transaction-create'),
    path('transactions/list/', pos_views.POSTransactionListView.as_view(), name='transaction-list'),
    path('transactions/<int:transaction_id>/', pos_views.POSTransactionView.as_view(), name='transaction-detail'),

    # Transaction item endpoints
    path('transactions/<int:transaction_id>/items/', pos_views.POSTransactionItemView.as_view(), name='transaction-item-add'),
    path('transactions/<int:transaction_id>/items/<int:item_id>/', pos_views.POSTransactionItemView.as_view(), name='transaction-item-update'),

    # Payment endpoints
    path('transactions/<int:transaction_id>/payment/', pos_views.POSPaymentView.as_view(), name='transaction-payment'),
    path('transactions/<int:transaction_id>/complete/', pos_views.POSCompleteTransactionView.as_view(), name='transaction-complete'),

    # Customer endpoints
    path('customers/', pos_views.POSCustomerListView.as_view(), name='customer-list'),

    # Validation endpoints
    path('validate/item/', pos_views.validate_transaction_item, name='validate-item'),
    path('validate/payment/', pos_views.validate_payment_amounts, name='validate-payment'),

    # Reports endpoints
    path('reports/', pos_views.POSReportsView.as_view(), name='reports'),
]