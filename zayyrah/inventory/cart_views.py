"""
Cart-focused API Views for Flutter mobile app
These APIs provide a simplified cart interface on top of the transaction system
"""

from decimal import Decimal
from django.shortcuts import get_object_or_404
from django.db import transaction as db_transaction

from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status

from .models import EnhancedProduct, POSTransaction, POSTransactionItem
from .services import POSService
from customers.models import Customer


class CartView(APIView):
    """Simplified cart management API for mobile apps"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get current active cart (transaction)"""
        try:
            # Get or create active cart for user
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            if not cart:
                return Response({
                    'success': True,
                    'cart': None,
                    'items': [],
                    'totals': {
                        'items_count': 0,
                        'subtotal': 0.00,
                        'tax_total': 0.00,
                        'discount_total': 0.00,
                        'total_amount': 0.00
                    }
                })

            # Serialize cart items
            items = []
            for item in cart.items.all():
                items.append({
                    'id': item.id,
                    'product_id': item.product.id,
                    'product_name': item.product.pos_name,
                    'sku': item.product.sku,
                    'barcode': item.product.barcode,
                    'quantity': float(item.quantity),
                    'unit_price': float(item.unit_price),
                    'original_price': float(item.original_price),
                    'discount_percent': float(item.discount_percent),
                    'discount_amount': float(item.discount_amount),
                    'line_total': float(item.line_total),
                    'unit_label': item.product.unit_label,
                    'image_url': item.product.primary_image.image.url if hasattr(item.product, 'primary_image') and item.product.primary_image else None
                })

            return Response({
                'success': True,
                'cart': {
                    'id': cart.id,
                    'transaction_number': cart.transaction_number,
                    'customer_id': cart.customer.id if cart.customer else None,
                    'customer_name': cart.customer.display_name if cart.customer else None,
                    'notes': cart.notes
                },
                'items': items,
                'totals': {
                    'items_count': cart.total_item_count,
                    'subtotal': float(cart.subtotal),
                    'tax_total': float(cart.tax_total),
                    'discount_total': float(cart.discount_total),
                    'total_amount': float(cart.total_amount)
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request):
        """Create new cart or set customer for existing cart"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get or create active cart
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            if not cart:
                # Create new cart
                customer = None
                customer_id = data.get('customer_id')
                if customer_id:
                    customer = get_object_or_404(Customer, id=customer_id, owner=request.user)

                cart = pos_service.create_transaction(
                    customer=customer,
                    notes=data.get('notes', '')
                )
            else:
                # Update existing cart
                customer_id = data.get('customer_id')
                if customer_id:
                    customer = get_object_or_404(Customer, id=customer_id, owner=request.user)
                    cart.customer = customer

                if 'notes' in data:
                    cart.notes = data['notes']

                cart.save()

            return Response({
                'success': True,
                'cart': {
                    'id': cart.id,
                    'transaction_number': cart.transaction_number,
                    'customer_id': cart.customer.id if cart.customer else None,
                    'customer_name': cart.customer.display_name if cart.customer else None
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """Clear cart (cancel active transaction)"""
        try:
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            if cart:
                cart.status = 'cancelled'
                cart.save()

            return Response({
                'success': True,
                'message': 'Cart cleared'
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CartItemView(APIView):
    """Cart item management API"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Add item to cart"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get or create active cart
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            if not cart:
                cart = pos_service.create_transaction()

            # Get product
            product = get_object_or_404(
                EnhancedProduct,
                id=data['product_id'],
                owner=request.user
            )

            # Add item to cart
            success, message, item = pos_service.add_item_to_transaction(
                transaction=cart,
                product=product,
                quantity=Decimal(str(data['quantity'])),
                custom_price=Decimal(str(data.get('custom_price', 0))) if data.get('custom_price') else None,
                discount_percent=Decimal(str(data.get('discount_percent', 0))),
                discount_amount=Decimal(str(data.get('discount_amount', 0)))
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'item': {
                        'id': item.id,
                        'product_id': item.product.id,
                        'product_name': item.product.pos_name,
                        'quantity': float(item.quantity),
                        'unit_price': float(item.unit_price),
                        'line_total': float(item.line_total)
                    },
                    'cart_total': float(cart.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def put(self, request):
        """Update cart item quantity"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get cart item
            item = get_object_or_404(
                POSTransactionItem,
                id=data['item_id'],
                transaction__owner=request.user,
                transaction__status='active'
            )

            # Update quantity
            success, message = pos_service.update_item_quantity(
                transaction=item.transaction,
                item=item,
                new_quantity=Decimal(str(data['quantity']))
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'item': {
                        'id': item.id,
                        'quantity': float(item.quantity),
                        'line_total': float(item.line_total)
                    },
                    'cart_total': float(item.transaction.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def patch(self, request):
        """Update cart item price"""
        print(f"\n=== CART ITEM PRICE UPDATE API ===")
        print(f"Method: PATCH")
        print(f"User: {request.user}")
        print(f"Request Data: {request.data}")

        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get cart item
            item = get_object_or_404(
                POSTransactionItem,
                id=data['item_id'],
                transaction__owner=request.user,
                transaction__status='active'
            )

            # Update price
            success, message = pos_service.update_item_price(
                transaction=item.transaction,
                item=item,
                new_price=Decimal(str(data['price']))
            )

            if success:
                response_data = {
                    'success': True,
                    'message': message,
                    'item': {
                        'id': item.id,
                        'product_name': item.product.name,
                        'quantity': float(item.quantity),
                        'unit_price': float(item.unit_price),
                        'original_price': float(item.original_price),
                        'line_total': float(item.line_total),
                        'is_custom_price': item.unit_price != item.product.selling_price
                    },
                    'cart_total': float(item.transaction.total_amount)
                }

                print(f"Price updated successfully:")
                print(f"  - Item ID: {item.id}")
                print(f"  - New Price: {item.unit_price}")
                print(f"  - Line Total: {item.line_total}")
                print(f"  - Cart Total: {item.transaction.total_amount}")

                return Response(response_data)
            else:
                print(f"Price update failed: {message}")
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            print(f"Error in price update: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def delete(self, request):
        """Remove item from cart"""
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get cart item
            item = get_object_or_404(
                POSTransactionItem,
                id=data['item_id'],
                transaction__owner=request.user,
                transaction__status='active'
            )

            # Remove item
            success, message = pos_service.remove_item_from_transaction(
                transaction=item.transaction,
                item=item
            )

            if success:
                return Response({
                    'success': True,
                    'message': message,
                    'cart_total': float(item.transaction.total_amount)
                })
            else:
                return Response({
                    'success': False,
                    'error': message
                }, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CheckoutPreviewView(APIView):
    """Preview checkout details before final confirmation"""
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """Get checkout preview with all details"""
        print(f"\n=== CHECKOUT PREVIEW API ===")
        print(f"Method: GET")
        print(f"User: {request.user}")
        print(f"Query Params: {dict(request.GET)}")
        try:
            # Get active cart
            cart = POSTransaction.objects.filter(
                owner=request.user,
                status='active'
            ).first()

            # Check if cart exists
            if not cart:
                return Response({
                    'success': False,
                    'error': 'No active cart found. Please add items to cart first.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Validate cart has items
            if not cart.items.exists():
                return Response({
                    'success': False,
                    'error': 'Cart is empty. Please add items to cart first.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Prepare detailed items
            preview_items = []
            for item in cart.items.all():
                preview_items.append({
                    'id': item.id,
                    'product_id': item.product.id,
                    'product_name': item.product.pos_name,
                    'sku': item.product.sku,
                    'quantity': float(item.quantity),
                    'unit_label': item.product.unit_label,
                    'unit_price': float(item.unit_price),
                    'original_price': float(item.original_price),
                    'discount_percent': float(item.discount_percent),
                    'discount_amount': float(item.discount_amount),
                    'tax_rate': float(item.tax_rate),
                    'tax_amount': float(item.tax_amount),
                    'line_total': float(item.line_total),
                })

            # Customer information
            customer_info = None
            if cart.customer:
                customer_info = {
                    'id': cart.customer.id,
                    'name': cart.customer.display_name,
                    'mobile_number': cart.customer.mobile_number
                }

            # Transaction totals
            totals = {
                'items_count': cart.total_item_count,
                'total_quantity': float(cart.total_items),
                'subtotal': float(cart.subtotal),
                'discount_total': float(cart.discount_total),
                'tax_total': float(cart.tax_total),
                'total_amount': float(cart.total_amount)
            }

            return Response({
                'success': True,
                'preview': {
                    'transaction': {
                        'id': cart.id,
                        'transaction_number': cart.transaction_number,
                        'notes': cart.notes or ''
                    },
                    'customer': customer_info,
                    'items': preview_items,
                    'totals': totals,
                    'payment_options': {
                        'can_use_cash': True,
                        'can_use_loan': cart.customer is not None,
                        'can_use_card': True,
                        'required_amount': float(cart.total_amount),
                        'customer_available': cart.customer is not None
                    }
                }
            })

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class QuickCheckoutView(APIView):
    """Comprehensive checkout API for mobile apps with complete receipt details"""
    permission_classes = [IsAuthenticated]

    def post(self, request):
        """Complete checkout in one step"""
        print(f"\n=== QUICK CHECKOUT API ===")
        print(f"Method: POST")
        print(f"User: {request.user}")
        print(f"Request Data: {request.data}")
        try:
            data = request.data
            pos_service = POSService(request.user)

            # Get active cart
            cart = get_object_or_404(
                POSTransaction,
                owner=request.user,
                status='active'
            )

            # Validate cart has items
            if not cart.items.exists():
                return Response({
                    'success': False,
                    'error': 'Cart is empty'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Log payment processing details
            cash_amount = Decimal(str(data.get('cash_amount', 0)))
            loan_amount = Decimal(str(data.get('loan_amount', 0)))
            card_amount = Decimal(str(data.get('card_amount', 0)))
            loan_due_date = data.get('loan_due_date')
            loan_interest_rate = Decimal(str(data.get('interest_rate', 0)))

            # Get payment method from request, with intelligent auto-detection
            payment_method = data.get('payment_method', None)
            if not payment_method:
                if cash_amount > 0 and loan_amount == 0 and card_amount == 0:
                    payment_method = 'cash'
                elif loan_amount > 0 and cash_amount == 0 and card_amount == 0:
                    payment_method = 'loan'
                elif card_amount > 0 and cash_amount == 0 and loan_amount == 0:
                    payment_method = 'card'
                elif cash_amount > 0 and loan_amount > 0:
                    payment_method = 'mixed'
                elif cash_amount > 0 and card_amount > 0:
                    payment_method = 'mixed'
                elif loan_amount > 0 and card_amount > 0:
                    payment_method = 'mixed'
                else:
                    payment_method = 'cash'

            # Validate payment method matches provided amounts
            if payment_method == 'loan' and cash_amount > 0:
                # If loan is specified but cash amount is also provided, treat cash as change/overpayment
                print(f"INFO: Loan payment with cash amount {cash_amount} - treating as loan with change")

            print(f"Payment Details:")
            print(f"  - Payment Method: {payment_method}")
            print(f"  - Cash Amount: {cash_amount}")
            print(f"  - Loan Amount: {loan_amount}")
            print(f"  - Card Amount: {card_amount}")
            print(f"  - Total Payment: {cash_amount + loan_amount + card_amount}")
            print(f"  - Cart Total: {cart.total_amount}")
            print(f"  - Change Due: {(cash_amount + loan_amount + card_amount) - cart.total_amount}")

            with db_transaction.atomic():
                # Process payment
                payment_success, payment_message, payment_info = pos_service.process_payment(
                    transaction=cart,
                    cash_amount=cash_amount,
                    loan_amount=loan_amount,
                    card_amount=card_amount,
                    loan_due_date=loan_due_date,
                    loan_interest_rate=loan_interest_rate,
                    payment_method=payment_method
                )

                print(f"Payment Processing Result:")
                print(f"  - Success: {payment_success}")
                print(f"  - Message: {payment_message}")
                print(f"  - Payment Info: {payment_info}")

                if not payment_success:
                    return Response({
                        'success': False,
                        'error': payment_message
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Complete transaction
                complete_success, complete_message, result = pos_service.complete_transaction(cart)

                print(f"Transaction Completion Result:")
                print(f"  - Success: {complete_success}")
                print(f"  - Message: {complete_message}")
                print(f"  - Result: {result}")

                if not complete_success:
                    return Response({
                        'success': False,
                        'error': complete_message
                    }, status=status.HTTP_400_BAD_REQUEST)

                # Get complete transaction details for receipt
                cart.refresh_from_db()

                # Prepare detailed items for receipt
                receipt_items = []
                for item in cart.items.all():
                    receipt_items.append({
                        'id': item.id,
                        'product_id': item.product.id,
                        'product_name': item.product.pos_name,
                        'sku': item.product.sku,
                        'barcode': item.product.barcode,
                        'quantity': float(item.quantity),
                        'unit_label': item.product.unit_label,
                        'unit_price': float(item.unit_price),
                        'original_price': float(item.original_price),
                        'discount_percent': float(item.discount_percent),
                        'discount_amount': float(item.discount_amount),
                        'tax_rate': float(item.tax_rate),
                        'tax_amount': float(item.tax_amount),
                        'line_total': float(item.line_total),
                        'line_subtotal': float(item.quantity * item.unit_price),
                    })

                # Business information for receipt header
                business_info = {
                    'shop_name': cart.owner.shop_name or 'POS System',
                    'mobile_number': cart.owner.mobile_number,
                    'business_type': cart.owner.account_type
                }

                # Customer information
                customer_info = None
                if cart.customer:
                    customer_info = {
                        'id': cart.customer.id,
                        'name': cart.customer.display_name,
                        'mobile_number': cart.customer.mobile_number
                    }

                # Payment breakdown
                payment_breakdown = {
                    'cash_amount': float(data.get('cash_amount', 0)),
                    'loan_amount': float(data.get('loan_amount', 0)),
                    'card_amount': float(data.get('card_amount', 0)),
                    'total_paid': float(payment_info['total_paid']),
                    'change_amount': float(payment_info['change_amount']),
                    'payment_method': payment_info['payment_method']
                }

                # Transaction totals
                transaction_totals = {
                    'items_count': cart.total_item_count,
                    'total_quantity': float(cart.total_items),
                    'subtotal': float(cart.subtotal),
                    'discount_total': float(cart.discount_total),
                    'tax_total': float(cart.tax_total),
                    'total_amount': float(cart.total_amount)
                }

                # Create Flutter-friendly response
                flutter_response = {
                    'success': True,
                    'message': 'Transaction completed successfully',
                    'transaction_id': cart.id,
                    'transaction_number': result['transaction_number'],
                    'total_amount': float(cart.total_amount),
                    'payment_method': payment_method,
                    'change_amount': float(payment_info['change_amount']),
                    'items_count': cart.total_item_count,
                    'completed_at': result['completed_at'].isoformat(),
                    'stock_updated': True
                }

                # Full detailed receipt for potential future use
                detailed_receipt = {
                    'success': True,
                    'message': 'Checkout completed successfully',
                    'flutter_data': flutter_response,  # Simple data for Flutter
                    'receipt': {
                        'transaction': {
                            'id': cart.id,
                            'transaction_number': result['transaction_number'],
                            'status': cart.status,
                            'completed_at': result['completed_at'].isoformat(),
                            'notes': cart.notes or ''
                        },
                        'business': business_info,
                        'customer': customer_info,
                        'items': receipt_items,
                        'totals': transaction_totals,
                        'payment': payment_breakdown,
                        'receipt_footer': {
                            'thank_you_message': 'Thank you for your business!',
                            'return_policy': 'Items can be returned within 7 days with receipt.',
                            'generated_at': result['completed_at'].isoformat()
                        }
                    }
                }

                print(f"\n=== FINAL RESPONSE TO FLUTTER ===")
                print(f"Flutter Response Success: {flutter_response['success']}")
                print(f"Flutter Message: {flutter_response['message']}")
                print(f"Transaction ID: {flutter_response['transaction_id']}")
                print(f"Transaction Number: {flutter_response['transaction_number']}")
                print(f"Total Amount: {flutter_response['total_amount']}")
                print(f"Payment Method: {flutter_response['payment_method']}")
                print(f"Change Amount: {flutter_response['change_amount']}")
                print(f"Items Count: {flutter_response['items_count']}")
                print(f"Stock Updated: {flutter_response['stock_updated']}")

                # Return simple Flutter-friendly response
                return Response(flutter_response)

        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_add_to_cart(request):
    """Quick add product to cart by barcode or product ID"""
    try:
        data = request.data
        pos_service = POSService(request.user)

        # Find product by barcode or ID
        product = None
        if 'barcode' in data:
            product = get_object_or_404(
                EnhancedProduct,
                barcode=data['barcode'],
                owner=request.user
            )
        elif 'product_id' in data:
            product = get_object_or_404(
                EnhancedProduct,
                id=data['product_id'],
                owner=request.user
            )
        else:
            return Response({
                'success': False,
                'error': 'Either barcode or product_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get or create active cart
        cart = POSTransaction.objects.filter(
            owner=request.user,
            status='active'
        ).first()

        if not cart:
            cart = pos_service.create_transaction()

        # Add item with default quantity
        quantity = Decimal(str(data.get('quantity', 1)))
        success, message, item = pos_service.add_item_to_transaction(
            transaction=cart,
            product=product,
            quantity=quantity
        )

        if success:
            return Response({
                'success': True,
                'message': f'Added {product.pos_name} to cart',
                'product': {
                    'id': product.id,
                    'name': product.pos_name,
                    'price': float(product.pos_price)
                },
                'cart_items_count': cart.total_item_count,
                'cart_total': float(cart.total_amount)
            })
        else:
            return Response({
                'success': False,
                'error': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_to_cart_by_price(request):
    """
    Add product to cart by specifying price amount (quantity auto-calculated)

    Ultra-thinking API for flexible cart management:
    - User enters target price amount
    - System calculates quantity based on unit price
    - Supports fractional quantities (if product allows)
    - Handles existing items (adds to quantity)
    - Auto-calculates all totals

    Request Body:
    {
        "product_id": int,
        "price_amount": decimal,
        "discount_percent": decimal (optional, 0-100)
    }

    Example:
    - Fresh Milk costs $20/liter
    - User enters price_amount: $50
    - System adds 2.5 liters to cart
    """
    print(f"\n=== ADD TO CART BY PRICE API ===")
    print(f"Method: POST")
    print(f"User: {request.user}")
    print(f"Request Data: {request.data}")

    try:
        data = request.data
        pos_service = POSService(request.user)

        # Validate required fields
        if 'product_id' not in data:
            return Response({
                'success': False,
                'error': 'product_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        if 'price_amount' not in data:
            return Response({
                'success': False,
                'error': 'price_amount is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get product
        product = get_object_or_404(
            EnhancedProduct,
            id=data['product_id'],
            owner=request.user,
            is_active=True
        )

        # Get or create active cart
        cart = POSTransaction.objects.filter(
            owner=request.user,
            status='active'
        ).first()

        if not cart:
            cart = pos_service.create_transaction()

        # Parse inputs
        price_amount = Decimal(str(data['price_amount']))
        discount_percent = Decimal(str(data.get('discount_percent', 0)))

        print(f"Adding by price:")
        print(f"  - Product: {product.name} (${product.selling_price}/{product.unit_label})")
        print(f"  - Target amount: ${price_amount}")
        print(f"  - Discount: {discount_percent}%")

        # Add item by price
        success, message, item = pos_service.add_item_to_transaction_by_price(
            transaction=cart,
            product=product,
            price_amount=price_amount,
            discount_percent=discount_percent
        )

        if success:
            # Return detailed response
            response_data = {
                'success': True,
                'message': message,
                'item': {
                    'id': item.id,
                    'product_id': product.id,
                    'product_name': product.name,
                    'unit_label': product.unit_label,
                    'calculated_quantity': float(item.quantity),
                    'unit_price': float(item.unit_price),
                    'original_price': float(item.original_price),
                    'line_total': float(item.line_total),
                    'discount_percent': float(item.discount_percent),
                    'discount_amount': float(item.discount_amount),
                    'allows_fractional': product.allow_fractional_quantities
                },
                'cart': {
                    'id': cart.id,
                    'transaction_number': cart.transaction_number,
                    'items_count': cart.items.count(),
                    'total_items': float(cart.total_items),
                    'subtotal': float(cart.subtotal),
                    'tax_total': float(cart.tax_total),
                    'discount_total': float(cart.discount_total),
                    'total_amount': float(cart.total_amount)
                },
                'calculation': {
                    'target_price_amount': float(price_amount),
                    'unit_price': float(product.selling_price),
                    'calculated_quantity': float(item.quantity),
                    'actual_line_total': float(item.line_total),
                    'formula': f"{price_amount} ÷ {product.selling_price} = {item.quantity} {product.unit_label}"
                }
            }

            print(f"Success! Added by price:")
            print(f"  - Calculated quantity: {item.quantity} {product.unit_label}")
            print(f"  - Line total: ${item.line_total}")
            print(f"  - Cart total: ${cart.total_amount}")

            return Response(response_data)
        else:
            return Response({
                'success': False,
                'error': message
            }, status=status.HTTP_400_BAD_REQUEST)

    except ValueError as e:
        return Response({
            'success': False,
            'error': f'Invalid price_amount format: {str(e)}'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        print(f"Error in add_to_cart_by_price: {str(e)}")
        return Response({
            'success': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)