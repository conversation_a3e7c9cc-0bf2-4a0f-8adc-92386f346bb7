{% extends 'base/app_base.html' %}
{% load static %}

{% block title %}{{ title }} - Zayyrah{% endblock %}
{% block nav_products %}active{% endblock %}
{% block bottom_nav_products %}active{% endblock %}

{% block page_title %}{{ title }}{% endblock %}
{% block breadcrumb_current %}{{ title }}{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Header -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
        <div>
            <h2 style="font-size: 1.5rem; font-weight: 700; color: var(--primary-green); margin-bottom: 0.5rem;">{{ title }}</h2>
            <p style="color: var(--gray-600);">Fill in the product details below</p>
        </div>
        <div>
            <a href="{% url 'inventory:product-list' %}" style="background: var(--gray-100); color: var(--gray-700); padding: 0.75rem 1.5rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease;">
                ← Back to Products
            </a>
        </div>
    </div>

    <!-- Form -->
    <form method="post" style="background: white; border-radius: 1rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid var(--gray-200); padding: 2rem;">
        {% csrf_token %}

        <!-- Basic Information -->
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-green);">
                📝 Basic Information
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Product Name *
                    </label>
                    {{ form.name }}
                    {% if form.name.errors %}
                        <div style="color: var(--danger); font-size: 0.875rem; margin-top: 0.25rem;">
                            {{ form.name.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        SKU
                    </label>
                    {{ form.sku }}
                    {% if form.sku.errors %}
                        <div style="color: var(--danger); font-size: 0.875rem; margin-top: 0.25rem;">
                            {{ form.sku.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Barcode
                    </label>
                    {{ form.barcode }}
                </div>
            </div>

            <div style="margin-top: 1rem;">
                <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                    Description
                </label>
                {{ form.description }}
            </div>
        </div>

        <!-- Category & Organization -->
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-green);">
                📁 Category & Organization
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Category
                    </label>
                    {{ form.category }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Brand
                    </label>
                    {{ form.brand }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Supplier
                    </label>
                    {{ form.supplier }}
                </div>
            </div>
        </div>

        <!-- Pricing -->
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-green);">
                💰 Pricing
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Purchase Price (Rs.)
                    </label>
                    {{ form.purchase_price }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Selling Price (Rs.) *
                    </label>
                    {{ form.selling_price }}
                    {% if form.selling_price.errors %}
                        <div style="color: var(--danger); font-size: 0.875rem; margin-top: 0.25rem;">
                            {{ form.selling_price.errors.0 }}
                        </div>
                    {% endif %}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Wholesale Price (Rs.)
                    </label>
                    {{ form.wholesale_price }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Minimum Selling Price (Rs.)
                    </label>
                    {{ form.minimum_selling_price }}
                </div>
            </div>
        </div>

        <!-- Stock Management -->
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-green);">
                📦 Stock Management
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Unit
                    </label>
                    {{ form.unit }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Custom Unit Label
                    </label>
                    {{ form.unit_custom_label }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Reorder Level
                    </label>
                    {{ form.reorder_level }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Maximum Stock Level
                    </label>
                    {{ form.maximum_stock_level }}
                </div>
            </div>

            <div style="display: flex; flex-wrap: gap: 1rem;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.track_stock }}
                    <label style="font-weight: 500; color: var(--gray-700);">Track Stock</label>
                </div>

                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.allow_negative_stock }}
                    <label style="font-weight: 500; color: var(--gray-700);">Allow Negative Stock</label>
                </div>

                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.allow_fractional_quantities }}
                    <label style="font-weight: 500; color: var(--gray-700);">Allow Fractional Quantities</label>
                </div>
            </div>
        </div>

        <!-- Tax & Other Details -->
        <div style="margin-bottom: 2rem;">
            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom: 2px solid var(--primary-green);">
                🏷️ Tax & Other Details
            </h3>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Tax Rate (%)
                    </label>
                    {{ form.tax_rate }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Weight (kg)
                    </label>
                    {{ form.weight }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Dimensions
                    </label>
                    {{ form.dimensions }}
                </div>

                <div>
                    <label style="display: block; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
                        Tags
                    </label>
                    {{ form.tags }}
                </div>
            </div>

            <div style="display: flex; flex-wrap: gap: 1rem;">
                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.tax_exempt }}
                    <label style="font-weight: 500; color: var(--gray-700);">Tax Exempt</label>
                </div>

                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.is_active }}
                    <label style="font-weight: 500; color: var(--gray-700);">Active</label>
                </div>

                <div style="display: flex; align-items: center; gap: 0.5rem;">
                    {{ form.is_featured }}
                    <label style="font-weight: 500; color: var(--gray-700);">Featured</label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div style="display: flex; justify-content: space-between; align-items: center; padding-top: 1rem; border-top: 1px solid var(--gray-200);">
            <a href="{% url 'inventory:product-list' %}" style="background: var(--gray-100); color: var(--gray-700); padding: 0.75rem 1.5rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600;">
                Cancel
            </a>

            <button type="submit" style="background: var(--primary-green); color: white; padding: 0.75rem 2rem; border: none; border-radius: 0.75rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--primary-green-light)'" onmouseout="this.style.background='var(--primary-green)'">
                {{ submit_text }}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Form styling */
.form-control {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--gray-200);
    border-radius: 0.5rem;
    font-size: 0.95rem;
    transition: all 0.2s ease;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
}

.form-check-input {
    margin-right: 0.5rem;
    transform: scale(1.2);
}

/* Responsive design */
@media (max-width: 768px) {
    .fade-in > div:first-child {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
    }

    .fade-in form > div > div {
        grid-template-columns: 1fr !important;
    }
}
</style>
{% endblock %}