{% extends "base/app_base.html" %}
{% load static %}

{% block title %}POS Transactions - {{ block.super }}{% endblock %}

{% block nav_pos_transactions %}nav-item-active{% endblock %}

{% block extra_css %}
<style>
    /* Transactions Page Styles */
    .transactions-container {
        padding: 0;
        margin: 0;
    }

    .transactions-header {
        background: linear-gradient(135deg, #0da487, #10b981);
        color: white;
        padding: 30px;
        border-radius: 12px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .transactions-header h1 {
        margin: 0 0 10px 0;
        font-size: 2.2rem;
        font-weight: 600;
    }

    .transactions-subtitle {
        opacity: 0.9;
        font-size: 1.1rem;
    }

    /* Statistics Cards */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        text-align: center;
        border-left: 4px solid #0da487;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0da487;
        margin-bottom: 8px;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.9rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Filters Section */
    .filters-section {
        background: white;
        padding: 25px;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }

    .filters-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .filters-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .filter-label {
        font-weight: 500;
        color: #374151;
        font-size: 0.9rem;
    }

    .filter-input {
        padding: 12px 15px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: border-color 0.2s;
    }

    .filter-input:focus {
        outline: none;
        border-color: #0da487;
        box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    }

    .filter-actions {
        display: flex;
        gap: 15px;
        align-items: center;
    }

    .filter-btn {
        background: #0da487;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .filter-btn:hover {
        background: #0891b2;
    }

    .clear-btn {
        background: #6b7280;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 8px;
        font-size: 0.9rem;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.2s;
    }

    .clear-btn:hover {
        background: #4b5563;
        text-decoration: none;
        color: white;
    }

    /* Transactions Table */
    .transactions-table-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .transactions-table {
        width: 100%;
        border-collapse: collapse;
    }

    .transactions-table th {
        background: #f8fafc;
        padding: 20px 15px;
        text-align: left;
        font-weight: 600;
        color: #374151;
        border-bottom: 2px solid #e2e8f0;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .transactions-table td {
        padding: 18px 15px;
        border-bottom: 1px solid #f1f5f9;
        color: #1e293b;
        font-size: 0.9rem;
    }

    .transactions-table tr:hover {
        background: #f8fafc;
    }

    /* Status Badges */
    .status-badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-completed {
        background: #d1fae5;
        color: #065f46;
    }

    .status-active {
        background: #dbeafe;
        color: #1e40af;
    }

    .status-cancelled {
        background: #fee2e2;
        color: #991b1b;
    }

    .payment-paid {
        background: #d1fae5;
        color: #065f46;
    }

    .payment-pending {
        background: #fef3c7;
        color: #92400e;
    }

    .payment-partial {
        background: #dbeafe;
        color: #1e40af;
    }

    /* Transaction Number */
    .transaction-number {
        font-family: 'Courier New', monospace;
        font-weight: 600;
        color: #0da487;
    }

    /* Customer Info */
    .customer-info {
        display: flex;
        flex-direction: column;
        gap: 2px;
    }

    .customer-name {
        font-weight: 500;
        color: #1e293b;
    }

    .customer-mobile {
        font-size: 0.8rem;
        color: #64748b;
    }

    /* Amount */
    .amount {
        font-weight: 600;
        color: #0da487;
        font-size: 1rem;
    }

    /* Items Preview */
    .items-preview {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #64748b;
        font-size: 0.85rem;
    }

    /* Actions */
    .transaction-actions {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        padding: 6px 12px;
        border: none;
        border-radius: 6px;
        font-size: 0.8rem;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: all 0.2s;
    }

    .view-btn {
        background: #e0f2fe;
        color: #0277bd;
    }

    .view-btn:hover {
        background: #b3e5fc;
        text-decoration: none;
        color: #01579b;
    }

    .print-btn {
        background: #f3e8ff;
        color: #7c3aed;
    }

    .print-btn:hover {
        background: #e9d5ff;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
    }

    .empty-state-icon {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.5;
    }

    .empty-state h3 {
        margin-bottom: 10px;
        color: #374151;
    }

    /* Pagination */
    .pagination-container {
        padding: 25px;
        text-align: center;
        background: white;
        border-radius: 0 0 12px 12px;
        border-top: 1px solid #f1f5f9;
    }

    .pagination {
        display: inline-flex;
        gap: 10px;
        align-items: center;
    }

    .pagination a,
    .pagination span {
        padding: 10px 15px;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        text-decoration: none;
        color: #374151;
        font-size: 0.9rem;
        transition: all 0.2s;
    }

    .pagination a:hover {
        background: #f8fafc;
        border-color: #0da487;
        text-decoration: none;
    }

    .pagination .current {
        background: #0da487;
        color: white;
        border-color: #0da487;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .transactions-header {
            padding: 20px;
        }

        .transactions-header h1 {
            font-size: 1.8rem;
        }

        .stats-grid {
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        }

        .filters-grid {
            grid-template-columns: 1fr;
        }

        .transactions-table-container {
            overflow-x: auto;
        }

        .transactions-table {
            min-width: 800px;
        }

        .filter-actions {
            flex-direction: column;
            align-items: stretch;
        }
    }

    /* Date inputs styling */
    input[type="date"] {
        appearance: none;
        -webkit-appearance: none;
        padding: 12px 15px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.9rem;
        transition: border-color 0.2s;
        background: white;
    }

    input[type="date"]:focus {
        outline: none;
        border-color: #0da487;
        box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="transactions-container">
    <!-- Header -->
    <div class="transactions-header">
        <h1>📋 POS Transactions</h1>
        <div class="transactions-subtitle">Manage and track all point of sale transactions</div>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-number">{{ total_transactions }}</div>
            <div class="stat-label">Total Transactions</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ completed_transactions }}</div>
            <div class="stat-label">Completed Sales</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">Rs. {{ total_sales|floatformat:0 }}</div>
            <div class="stat-label">Total Sales</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">{{ transactions|length }}</div>
            <div class="stat-label">On This Page</div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
        <div class="filters-title">
            🔍 Filter Transactions
        </div>

        <form method="get" class="filters-form">
            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">Search</label>
                    <input type="text" name="search" class="filter-input"
                           placeholder="Transaction #, customer name or mobile..."
                           value="{{ search_query }}">
                </div>

                <div class="filter-group">
                    <label class="filter-label">Status</label>
                    <select name="status" class="filter-input">
                        <option value="">All Statuses</option>
                        <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>Completed</option>
                        <option value="cancelled" {% if status_filter == 'cancelled' %}selected{% endif %}>Cancelled</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Payment Status</label>
                    <select name="payment_status" class="filter-input">
                        <option value="">All Payment Statuses</option>
                        <option value="paid" {% if payment_status_filter == 'paid' %}selected{% endif %}>Paid</option>
                        <option value="pending" {% if payment_status_filter == 'pending' %}selected{% endif %}>Pending</option>
                        <option value="partial" {% if payment_status_filter == 'partial' %}selected{% endif %}>Partial</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-label">Date From</label>
                    <input type="date" name="date_from" class="filter-input" value="{{ date_from }}">
                </div>

                <div class="filter-group">
                    <label class="filter-label">Date To</label>
                    <input type="date" name="date_to" class="filter-input" value="{{ date_to }}">
                </div>
            </div>

            <div class="filter-actions">
                <button type="submit" class="filter-btn">Apply Filters</button>
                <a href="{% url 'inventory:pos-transactions' %}" class="clear-btn">Clear Filters</a>
            </div>
        </form>
    </div>

    <!-- Transactions Table -->
    <div class="transactions-table-container">
        {% if transactions %}
            <table class="transactions-table">
                <thead>
                    <tr>
                        <th>Transaction #</th>
                        <th>Date & Time</th>
                        <th>Customer</th>
                        <th>Items</th>
                        <th>Amount</th>
                        <th>Status</th>
                        <th>Payment</th>
                        <th>Payment Method</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for transaction in transactions %}
                        <tr>
                            <td>
                                <div class="transaction-number">{{ transaction.transaction_number }}</div>
                            </td>
                            <td>
                                <div>{{ transaction.created_at|date:"M d, Y" }}</div>
                                <div style="font-size: 0.8rem; color: #64748b;">{{ transaction.created_at|time:"g:i A" }}</div>
                            </td>
                            <td>
                                {% if transaction.customer %}
                                    <div class="customer-info">
                                        <div class="customer-name">{{ transaction.customer.display_name }}</div>
                                        {% if transaction.customer.mobile_number %}
                                            <div class="customer-mobile">{{ transaction.customer.mobile_number }}</div>
                                        {% endif %}
                                    </div>
                                {% else %}
                                    <span style="color: #64748b; font-style: italic;">Walk-in Customer</span>
                                {% endif %}
                            </td>
                            <td>
                                <div style="font-weight: 500;">{{ transaction.total_item_count }} items</div>
                                <div class="items-preview">
                                    {% for item in transaction.items.all|slice:":2" %}
                                        {{ item.product.pos_name }}{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                    {% if transaction.items.all|length > 2 %}
                                        + {{ transaction.items.all|length|add:"-2" }} more
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <div class="amount">Rs. {{ transaction.total_amount|floatformat:0 }}</div>
                                {% if transaction.total_items != transaction.total_item_count %}
                                    <div style="font-size: 0.8rem; color: #64748b;">{{ transaction.total_items|floatformat:1 }} units</div>
                                {% endif %}
                            </td>
                            <td>
                                <span class="status-badge status-{{ transaction.status }}">
                                    {{ transaction.get_status_display }}
                                </span>
                            </td>
                            <td>
                                <span class="status-badge payment-{{ transaction.payment_status }}">
                                    {{ transaction.get_payment_status_display }}
                                </span>
                                {% if transaction.amount_paid > 0 %}
                                    <div style="font-size: 0.8rem; color: #64748b; margin-top: 4px;">
                                        Paid: Rs. {{ transaction.amount_paid|floatformat:0 }}
                                    </div>
                                {% endif %}
                            </td>
                            <td>
                                {% if transaction.payment_method %}
                                    <div style="text-transform: capitalize; font-weight: 500;">
                                        {{ transaction.get_payment_method_display }}
                                    </div>
                                {% else %}
                                    <span style="color: #64748b;">—</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="transaction-actions">
                                    <a href="#" class="action-btn view-btn"
                                       onclick="viewTransaction({{ transaction.id }})">View</a>
                                    {% if transaction.status == 'completed' %}
                                        <button class="action-btn print-btn"
                                                onclick="printReceipt({{ transaction.id }})">Print</button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination -->
            {% if is_paginated %}
                <div class="pagination-container">
                    <div class="pagination">
                        {% if page_obj.has_previous %}
                            <a href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">&laquo; First</a>
                            <a href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">&lsaquo; Previous</a>
                        {% endif %}

                        <span class="current">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>

                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Next &rsaquo;</a>
                            <a href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}{% if request.GET.status %}&status={{ request.GET.status }}{% endif %}{% if request.GET.payment_status %}&payment_status={{ request.GET.payment_status }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Last &raquo;</a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">📋</div>
                <h3>No Transactions Found</h3>
                <p>{% if search_query or status_filter or payment_status_filter or date_from or date_to %}
                    No transactions match your current filters. Try adjusting your search criteria.
                {% else %}
                    No POS transactions have been created yet. Start by making your first sale!
                {% endif %}</p>
                {% if not search_query and not status_filter and not payment_status_filter and not date_from and not date_to %}
                    <a href="{% url 'inventory:pos-interface' %}" class="filter-btn" style="margin-top: 20px; text-decoration: none;">
                        Start First Sale
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Transaction Detail Modal -->
<div id="transaction-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 0; border-radius: 12px; max-width: 600px; width: 90%; max-height: 90%; overflow-y: auto;">
        <div style="padding: 25px; border-bottom: 1px solid #e2e8f0; background: #f8fafc; border-radius: 12px 12px 0 0;">
            <h3 style="margin: 0; color: #374151;">Transaction Details</h3>
            <button onclick="closeTransactionModal()" style="position: absolute; top: 20px; right: 20px; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #64748b;">×</button>
        </div>
        <div id="transaction-detail-content" style="padding: 25px;">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function viewTransaction(transactionId) {
        // Show modal
        document.getElementById('transaction-modal').style.display = 'flex';

        // Load transaction details
        fetch(`/inventory/pos/api/transactions/${transactionId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTransactionDetails(data.transaction);
                } else {
                    document.getElementById('transaction-detail-content').innerHTML =
                        '<p style="color: #ef4444;">Error loading transaction details: ' + data.error + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('transaction-detail-content').innerHTML =
                    '<p style="color: #ef4444;">Error loading transaction details.</p>';
                console.error('Error:', error);
            });
    }

    function displayTransactionDetails(transaction) {
        const content = document.getElementById('transaction-detail-content');

        let itemsHtml = '';
        transaction.items.forEach(item => {
            itemsHtml += `
                <tr>
                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9;">${item.product_name}</td>
                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9; text-align: center;">${item.quantity}</td>
                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9; text-align: right;">Rs. ${item.unit_price.toFixed(0)}</td>
                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9; text-align: center;">${item.discount_percent}%</td>
                    <td style="padding: 12px; border-bottom: 1px solid #f1f5f9; text-align: right; font-weight: 600;">Rs. ${item.line_total.toFixed(0)}</td>
                </tr>
            `;
        });

        content.innerHTML = `
            <div style="margin-bottom: 25px;">
                <h4 style="margin: 0 0 15px 0; color: #374151;">Transaction Information</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; background: #f8fafc; padding: 20px; border-radius: 8px;">
                    <div>
                        <strong>Transaction #:</strong><br>
                        <code style="background: #e2e8f0; padding: 4px 8px; border-radius: 4px; font-size: 0.9rem;">${transaction.transaction_number}</code>
                    </div>
                    <div>
                        <strong>Status:</strong><br>
                        <span class="status-badge status-${transaction.status}" style="margin-top: 5px; display: inline-block;">${transaction.status}</span>
                    </div>
                    <div>
                        <strong>Customer:</strong><br>
                        ${transaction.customer_name || 'Walk-in Customer'}
                    </div>
                    <div>
                        <strong>Payment Method:</strong><br>
                        ${transaction.payment_method || 'Not specified'}
                    </div>
                </div>
            </div>

            <div style="margin-bottom: 25px;">
                <h4 style="margin: 0 0 15px 0; color: #374151;">Items Purchased</h4>
                <table style="width: 100%; border-collapse: collapse; border: 1px solid #e2e8f0; border-radius: 8px; overflow: hidden;">
                    <thead>
                        <tr style="background: #f8fafc;">
                            <th style="padding: 15px 12px; text-align: left; font-weight: 600; border-bottom: 2px solid #e2e8f0;">Product</th>
                            <th style="padding: 15px 12px; text-align: center; font-weight: 600; border-bottom: 2px solid #e2e8f0;">Qty</th>
                            <th style="padding: 15px 12px; text-align: right; font-weight: 600; border-bottom: 2px solid #e2e8f0;">Price</th>
                            <th style="padding: 15px 12px; text-align: center; font-weight: 600; border-bottom: 2px solid #e2e8f0;">Discount</th>
                            <th style="padding: 15px 12px; text-align: right; font-weight: 600; border-bottom: 2px solid #e2e8f0;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>
            </div>

            <div style="background: #f8fafc; padding: 20px; border-radius: 8px;">
                <h4 style="margin: 0 0 15px 0; color: #374151;">Payment Summary</h4>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span>Subtotal:</span>
                    <span>Rs. ${transaction.subtotal.toFixed(0)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span>Discount:</span>
                    <span>Rs. ${transaction.discount_total.toFixed(0)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                    <span>Tax:</span>
                    <span>Rs. ${transaction.tax_total.toFixed(0)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; padding-top: 10px; border-top: 2px solid #e2e8f0; font-weight: 700; font-size: 1.1rem; color: #0da487;">
                    <span>Total Amount:</span>
                    <span>Rs. ${transaction.total_amount.toFixed(0)}</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-top: 8px;">
                    <span>Amount Paid:</span>
                    <span style="color: #059669;">Rs. ${transaction.amount_paid.toFixed(0)}</span>
                </div>
            </div>
        `;
    }

    function closeTransactionModal() {
        document.getElementById('transaction-modal').style.display = 'none';
    }

    function printReceipt(transactionId) {
        // TODO: Implement receipt printing
        alert('Receipt printing functionality will be implemented in the next phase.');
    }

    // Close modal when clicking outside
    document.getElementById('transaction-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeTransactionModal();
        }
    });
</script>
{% endblock %}