{% extends 'base/app_base.html' %}

{% block title %}Delete {{ category.name }} - Categories{% endblock %}
{% block page_title %}🗑️ Delete Category{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'inventory:category-list' %}" class="breadcrumb-item">Categories</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'inventory:category-detail' category.pk %}" class="breadcrumb-item">{{ category.name }}</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">Delete</span>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        max-width: 500px;
        margin: 0 auto;
        background: var(--white);
        border-radius: 16px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
        overflow: hidden;
        animation: fadeIn 0.4s ease-out;
    }

    .delete-header {
        background: linear-gradient(135deg, #dc2626 0%, #ef4444 50%, #f87171 100%);
        color: white;
        padding: 32px;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .delete-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);
        pointer-events: none;
    }

    .delete-header-content {
        position: relative;
        z-index: 1;
    }

    .delete-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin: 0 auto 16px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
        animation: pulse 2s infinite;
    }

    .delete-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
    }

    .delete-subtitle {
        opacity: 0.9;
        font-size: 1rem;
    }

    .delete-body {
        padding: 32px;
    }

    .category-info {
        background: var(--gray-50);
        border: 2px solid var(--gray-200);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        position: relative;
    }

    .category-info::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: 12px 12px 0 0;
    }

    .category-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary-green);
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .category-path {
        color: var(--gray-600);
        font-size: 0.9rem;
        margin-bottom: 12px;
    }

    .category-stats {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 16px;
    }

    .stat-item {
        text-align: center;
        padding: 12px;
        background: var(--white);
        border-radius: 8px;
        border: 1px solid var(--gray-200);
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-green);
        display: block;
    }

    .stat-label {
        font-size: 0.8rem;
        color: var(--gray-600);
        margin-top: 4px;
    }

    .warning-box {
        background: #fef3cd;
        border: 2px solid #fbbf24;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        position: relative;
    }

    .warning-icon {
        font-size: 1.5rem;
        margin-right: 8px;
    }

    .warning-title {
        font-weight: 700;
        color: #92400e;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .warning-text {
        color: #92400e;
        line-height: 1.6;
    }

    .error-box {
        background: #fef2f2;
        border: 2px solid #fecaca;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
    }

    .error-title {
        font-weight: 700;
        color: #dc2626;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
    }

    .error-text {
        color: #dc2626;
        line-height: 1.6;
    }

    .delete-actions {
        padding: 24px 32px;
        background: var(--gray-50);
        border-top: 1px solid var(--gray-200);
        display: flex;
        gap: 12px;
        justify-content: space-between;
    }

    .btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
        flex: 1;
    }

    .btn-danger {
        background: #dc2626;
        color: white;
        border: 2px solid #dc2626;
    }

    .btn-danger:hover {
        background: #b91c1c;
        border-color: #b91c1c;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    }

    .btn-danger:disabled {
        background: var(--gray-400);
        border-color: var(--gray-400);
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-secondary {
        background: var(--white);
        color: var(--gray-700);
        border: 2px solid var(--gray-300);
    }

    .btn-secondary:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        transform: translateY(-2px);
    }

    .cannot-delete {
        text-align: center;
        padding: 20px;
    }

    .cannot-delete-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .cannot-delete h3 {
        color: var(--gray-700);
        margin-bottom: 8px;
    }

    .cannot-delete p {
        color: var(--gray-600);
        margin-bottom: 20px;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .delete-container {
            margin: 0 16px;
        }

        .delete-header {
            padding: 24px 20px;
        }

        .delete-body {
            padding: 24px 20px;
        }

        .delete-actions {
            padding: 20px;
            flex-direction: column;
        }

        .category-stats {
            grid-template-columns: 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="delete-container">
    <!-- Delete Header -->
    <div class="delete-header">
        <div class="delete-header-content">
            <div class="delete-icon">🗑️</div>
            <h1 class="delete-title">Delete Category</h1>
            <p class="delete-subtitle">This action cannot be undone</p>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        <div style="padding: 24px 32px 0;">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {% if message.tags == 'success' %}✅
                    {% elif message.tags == 'error' %}❌
                    {% elif message.tags == 'warning' %}⚠️
                    {% else %}ℹ️{% endif %}
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="delete-body">
        <!-- Category Information -->
        <div class="category-info">
            <div class="category-name">
                {% if category.level == 0 %}📁
                {% elif category.level == 1 %}📂
                {% else %}📄{% endif %}
                {{ category.name }}
            </div>

            <div class="category-path">{{ category.full_path }}</div>

            {% if category.description %}
                <p style="color: var(--gray-600); margin: 8px 0;">{{ category.description }}</p>
            {% endif %}

            <div class="category-stats">
                <div class="stat-item">
                    <span class="stat-value">{{ products_count }}</span>
                    <div class="stat-label">Products</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value">{{ subcategories_count }}</span>
                    <div class="stat-label">Subcategories</div>
                </div>
            </div>
        </div>

        <!-- Check if category can be deleted -->
        {% if products_count > 0 or subcategories_count > 0 %}
            <!-- Cannot Delete -->
            <div class="error-box">
                <div class="error-title">
                    <span class="warning-icon">❌</span>
                    Cannot Delete Category
                </div>
                <div class="error-text">
                    This category cannot be deleted because it contains:
                    <ul style="margin: 12px 0; padding-left: 20px;">
                        {% if products_count > 0 %}
                            <li><strong>{{ products_count }}</strong> product{{ products_count|pluralize }}</li>
                        {% endif %}
                        {% if subcategories_count > 0 %}
                            <li><strong>{{ subcategories_count }}</strong> subcategor{{ subcategories_count|pluralize:"y,ies" }}</li>
                        {% endif %}
                    </ul>
                    Please move or delete all products and subcategories before deleting this category.
                </div>
            </div>

            <div class="cannot-delete">
                <div class="cannot-delete-icon">🚫</div>
                <h3>Category in Use</h3>
                <p>This category is being used and cannot be deleted at this time.</p>
            </div>
        {% else %}
            <!-- Can Delete -->
            <div class="warning-box">
                <div class="warning-title">
                    <span class="warning-icon">⚠️</span>
                    Permanent Deletion Warning
                </div>
                <div class="warning-text">
                    You are about to permanently delete the category <strong>"{{ category.name }}"</strong>.
                    This action cannot be undone.
                </div>
            </div>

            <div style="text-align: center; padding: 20px; background: var(--success-lighter); border-radius: 12px; margin-bottom: 24px;">
                <div style="font-size: 2rem; margin-bottom: 12px;">✅</div>
                <div style="color: var(--primary-green); font-weight: 600;">
                    This category is empty and can be safely deleted.
                </div>
            </div>
        {% endif %}
    </div>

    <!-- Delete Actions -->
    <div class="delete-actions">
        <a href="{% url 'inventory:category-detail' category.pk %}" class="btn btn-secondary">
            ❌ Cancel
        </a>

        {% if products_count == 0 and subcategories_count == 0 %}
            <form method="post" style="flex: 1;" onsubmit="return confirmDelete()">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">
                    🗑️ Delete Category
                </button>
            </form>
        {% else %}
            <button type="button" class="btn btn-danger" disabled>
                🗑️ Cannot Delete
            </button>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete() {
    const categoryName = "{{ category.name|escapejs }}";
    const confirmation = prompt(
        `To confirm deletion, please type the category name:\n\n"${categoryName}"`
    );

    if (confirmation === null) {
        return false; // User cancelled
    }

    if (confirmation.trim() !== categoryName) {
        alert('Category name does not match. Deletion cancelled.');
        return false;
    }

    return confirm(`Are you absolutely sure you want to delete "${categoryName}"?\n\nThis action cannot be undone.`);
}

document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on the page for better UX
    console.log('Delete confirmation page loaded for category: {{ category.name }}');

    // Add warning shake animation if user tries to delete when they can't
    {% if products_count > 0 or subcategories_count > 0 %}
        const disabledBtn = document.querySelector('.btn-danger:disabled');
        if (disabledBtn) {
            disabledBtn.addEventListener('click', function() {
                this.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
            });
        }
    {% endif %}
});

// Add shake animation
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
        20%, 40%, 60%, 80% { transform: translateX(5px); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}