{% extends 'base/app_base.html' %}
{% load static %}

{% block title %}Product Management - Zayyrah{% endblock %}
{% block nav_products %}active{% endblock %}
{% block bottom_nav_products %}active{% endblock %}

{% block page_title %}Product Management{% endblock %}
{% block breadcrumb_current %}Products{% endblock %}

{% block content %}
<div class="fade-in">
    <!-- Header with Actions -->
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
        <div>
            <h2 style="font-size: 1.5rem; font-weight: 700; color: var(--primary-green); margin-bottom: 0.5rem;">Product Catalog</h2>
            <p style="color: var(--gray-600);">Manage your inventory and products</p>
        </div>
        <div style="display: flex; gap: 1rem;">
            <a href="{% url 'inventory:product-create' %}" style="background: var(--primary-green); color: white; padding: 0.75rem 1.5rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease;">
                ➕ Add Product
            </a>
            <a href="{% url 'inventory:category-list' %}" style="background: var(--gray-100); color: var(--gray-700); padding: 0.75rem 1.5rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease;">
                📁 Categories
            </a>
        </div>
    </div>

    <!-- Search Bar -->
    <div style="background: white; border-radius: 1rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid var(--gray-200); padding: 1.5rem; margin-bottom: 1.5rem;">
        <div style="display: flex; gap: 1rem;">
            <div style="flex: 1; position: relative;">
                <input type="text" placeholder="Search products..." style="width: 100%; padding: 0.75rem 1rem 0.75rem 2.5rem; border: 2px solid var(--gray-200); border-radius: 0.75rem; font-size: 0.95rem;">
                <span style="position: absolute; left: 0.75rem; top: 50%; transform: translateY(-50%); color: var(--gray-400);">🔍</span>
            </div>
        </div>
    </div>

    <!-- Products Grid/List -->
    {% if products %}
        <div style="display: grid; gap: 1.5rem;">
            {% for product in products %}
                <div style="background: white; border-radius: 1rem; box-shadow: 0 1px 3px rgba(0,0,0,0.1); border: 1px solid var(--gray-200); padding: 1.5rem; transition: all 0.2s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 1px 3px rgba(0,0,0,0.1)'">
                    <div style="display: flex; gap: 1.5rem; align-items: center;">
                        <!-- Product Info -->
                        <div style="flex: 1;">
                            <h3 style="font-size: 1.25rem; font-weight: 600; color: var(--gray-900); margin-bottom: 0.5rem;">{{ product.name }}</h3>

                            <div style="display: flex; flex-wrap: gap: 0.5rem; margin-bottom: 1rem;">
                                {% if product.sku %}
                                    <span style="background: var(--gray-100); color: var(--gray-700); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem;">SKU: {{ product.sku }}</span>
                                {% endif %}
                                {% if product.category %}
                                    <span style="background: var(--success-lighter); color: var(--primary-green); padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem; font-weight: 500;">{{ product.category.name }}</span>
                                {% endif %}
                                {% if product.unit %}
                                    <span style="background: var(--primary-green-lighter); color: white; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem;">{{ product.unit_label }}</span>
                                {% endif %}
                            </div>

                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(160px, 1fr)); gap: 1rem;">
                                <div>
                                    <span style="color: var(--gray-500); font-size: 0.875rem; display: block;">Selling Price</span>
                                    <span style="font-weight: 600; font-size: 1.125rem; color: var(--primary-green);">Rs. {{ product.selling_price }}</span>
                                </div>
                                <div>
                                    <span style="color: var(--gray-500); font-size: 0.875rem; display: block;">Purchase Price</span>
                                    <span style="font-weight: 600; color: var(--gray-700);">Rs. {{ product.purchase_price|floatformat:2 }}</span>
                                </div>
                                <div>
                                    <span style="color: var(--gray-500); font-size: 0.875rem; display: block;">Current Stock</span>
                                    <span style="font-weight: 600; color: {% if product.current_stock == 0 %}var(--danger){% elif product.current_stock < product.reorder_level %}var(--warning){% else %}var(--success-green){% endif %};">{{ product.current_stock }} {{ product.unit_label|lower }}</span>
                                </div>
                                <div>
                                    <span style="color: var(--gray-500); font-size: 0.875rem; display: block;">Tax Rate</span>
                                    <span style="font-weight: 600; color: var(--gray-700);">{{ product.tax_rate }}%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Actions -->
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <a href="{% url 'inventory:product-detail' product.pk %}" style="background: var(--primary-green); color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; text-decoration: none; font-size: 0.875rem; text-align: center; transition: all 0.2s ease;" onmouseover="this.style.background='var(--primary-green-light)'" onmouseout="this.style.background='var(--primary-green)'">
                                👁️ View
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <div style="display: flex; justify-content: center; margin-top: 2rem;">
                <div style="display: flex; gap: 0.5rem;">
                    {% if page_obj.has_previous %}
                        <a href="?page={{ page_obj.previous_page_number }}" style="padding: 0.5rem 1rem; background: var(--gray-100); color: var(--gray-700); border-radius: 0.5rem; text-decoration: none;">Previous</a>
                    {% endif %}

                    <span style="padding: 0.5rem 1rem; background: var(--primary-green); color: white; border-radius: 0.5rem;">
                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                    </span>

                    {% if page_obj.has_next %}
                        <a href="?page={{ page_obj.next_page_number }}" style="padding: 0.5rem 1rem; background: var(--gray-100); color: var(--gray-700); border-radius: 0.5rem; text-decoration: none;">Next</a>
                    {% endif %}
                </div>
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div style="text-align: center; padding: 3rem 0;">
            <div style="font-size: 4rem; margin-bottom: 1rem;">📦</div>
            <h3 style="font-size: 1.5rem; font-weight: 600; color: var(--gray-900); margin-bottom: 0.5rem;">No products found</h3>
            <p style="color: var(--gray-600); margin-bottom: 1.5rem;">Get started by creating your first product.</p>
            <a href="{% url 'inventory:category-list' %}" style="background: var(--primary-green); color: white; padding: 0.75rem 1.5rem; border-radius: 0.75rem; text-decoration: none; font-weight: 600; display: inline-flex; align-items: center; gap: 0.5rem;">
                📁 Manage Categories
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Add hover effects with CSS */
.header-btn:hover {
    background: var(--primary-green-light) !important;
    transform: translateY(-2px);
}

/* Search input focus effects */
input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .fade-in > div:first-child {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 1rem !important;
    }

    .fade-in > div:first-child > div:last-child {
        width: 100%;
        justify-content: stretch;
    }

    .fade-in > div:first-child > div:last-child a {
        width: 100%;
        text-align: center;
    }
}
</style>
{% endblock %}