{% extends "base/app_base.html" %}
{% load static %}

{% block title %}POS System - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
    /* POS Interface Styles */
    .pos-container {
        display: flex;
        height: calc(100vh - 100px);
        gap: 20px;
        padding: 0;
        margin: 0;
    }

    .pos-left-panel {
        flex: 1;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        min-height: 100%;
    }

    .pos-right-panel {
        width: 400px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        display: flex;
        flex-direction: column;
        min-height: 100%;
    }

    /* Header Styles */
    .pos-header {
        padding: 20px;
        border-bottom: 2px solid #f1f5f9;
        background: linear-gradient(135deg, #0da487, #10b981);
        color: white;
        border-radius: 12px 12px 0 0;
    }

    .pos-header h2 {
        margin: 0;
        font-size: 1.8rem;
        font-weight: 600;
    }

    /* Search and Filter Bar */
    .pos-filters {
        padding: 20px;
        border-bottom: 1px solid #e2e8f0;
        background: #f8fafc;
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        flex-direction: column;
        gap: 5px;
    }

    .filter-group label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
    }

    .filter-input {
        padding: 10px 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        font-size: 0.875rem;
        transition: border-color 0.2s;
    }

    .filter-input:focus {
        outline: none;
        border-color: #0da487;
        box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    }

    /* Products Grid */
    .products-container {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
    }

    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 15px;
    }

    .product-card {
        background: white;
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 15px;
        cursor: pointer;
        transition: all 0.2s;
        text-align: center;
    }

    .product-card:hover {
        border-color: #0da487;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(13, 164, 135, 0.15);
    }

    .product-image {
        width: 80px;
        height: 80px;
        margin: 0 auto 10px;
        background: #f1f5f9;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #64748b;
    }

    .product-name {
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 5px;
        font-size: 0.9rem;
    }

    .product-price {
        font-size: 1.1rem;
        font-weight: 700;
        color: #0da487;
        margin-bottom: 5px;
    }

    .product-stock {
        font-size: 0.8rem;
        color: #64748b;
        margin-bottom: 10px;
    }

    .stock-low {
        color: #ef4444;
        font-weight: 600;
    }

    .add-to-cart-btn {
        background: #0da487;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        width: 100%;
    }

    .add-to-cart-btn:hover {
        background: #0891b2;
    }

    .add-to-cart-btn:disabled {
        background: #94a3b8;
        cursor: not-allowed;
    }

    /* Smart Product Selector Styles */
    .product-selector {
        width: 100%;
        margin-top: 10px;
    }

    .selector-mode-toggle {
        display: flex;
        justify-content: center;
        gap: 5px;
        margin-bottom: 10px;
    }

    .mode-btn {
        background: #f1f5f9;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 6px 12px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 0.9rem;
        min-width: 40px;
    }

    .mode-btn.active {
        background: #0da487;
        color: white;
        border-color: #0da487;
    }

    .mode-btn:hover:not(.active) {
        background: #e2e8f0;
    }

    .selector-input-section {
        background: #f8fafc;
        border-radius: 8px;
        padding: 12px;
        border: 1px solid #e2e8f0;
    }

    .input-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;
    }

    .qty-minus-btn, .qty-plus-btn {
        background: #e2e8f0;
        border: none;
        width: 28px;
        height: 28px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 600;
        font-size: 0.9rem;
        transition: background-color 0.2s;
    }

    .qty-minus-btn:hover, .qty-plus-btn:hover {
        background: #cbd5e1;
    }

    .input-wrapper {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
    }

    .product-input {
        width: 100%;
        border: 1px solid #d1d5db;
        border-radius: 4px;
        padding: 6px 8px;
        text-align: center;
        font-size: 0.85rem;
        background: white;
    }

    .product-input:focus {
        outline: none;
        border-color: #0da487;
        box-shadow: 0 0 0 2px rgba(13, 164, 135, 0.1);
    }

    .input-label {
        position: absolute;
        right: 8px;
        font-size: 0.7rem;
        color: #6b7280;
        background: white;
        padding: 0 4px;
        border-radius: 2px;
    }

    .calculation-preview {
        font-size: 0.75rem;
        color: #0da487;
        text-align: center;
        margin-bottom: 8px;
        font-weight: 500;
        min-height: 16px;
    }

    .smart-add-btn {
        background: #0da487;
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 6px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
        width: 100%;
    }

    .smart-add-btn:hover {
        background: #0891b2;
    }

    /* Cart Panel */
    .cart-header {
        padding: 20px;
        border-bottom: 2px solid #f1f5f9;
        background: linear-gradient(135deg, #374151, #4b5563);
        color: white;
        border-radius: 12px 12px 0 0;
    }

    .cart-header h3 {
        margin: 0;
        font-size: 1.4rem;
        font-weight: 600;
    }

    .cart-items {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
    }

    .cart-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 12px;
        background: #f8fafc;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .cart-item-info {
        flex: 1;
    }

    .cart-item-name {
        font-weight: 600;
        color: #1e293b;
        font-size: 0.9rem;
    }

    .cart-item-price {
        color: #64748b;
        font-size: 0.8rem;
    }

    .cart-item-controls {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .qty-btn {
        background: #e2e8f0;
        border: none;
        width: 30px;
        height: 30px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-weight: 600;
        transition: background-color 0.2s;
    }

    .qty-btn:hover {
        background: #cbd5e1;
    }

    .qty-input {
        width: 50px;
        text-align: center;
        border: 1px solid #e2e8f0;
        border-radius: 4px;
        padding: 5px;
        font-size: 0.9rem;
    }

    .remove-btn {
        background: #ef4444;
        color: white;
        border: none;
        padding: 5px 8px;
        border-radius: 4px;
        font-size: 0.7rem;
        cursor: pointer;
    }

    .remove-btn:hover {
        background: #dc2626;
    }

    /* Customer Selection */
    .customer-section {
        padding: 20px;
        border-bottom: 1px solid #e2e8f0;
    }

    .customer-section h4 {
        margin: 0 0 10px 0;
        color: #374151;
        font-size: 1rem;
    }

    .customer-select {
        width: 100%;
        padding: 10px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        background: white;
    }

    /* Cart Summary */
    .cart-summary {
        padding: 20px;
        border-bottom: 1px solid #e2e8f0;
        background: #f8fafc;
    }

    .summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .summary-row.total {
        font-weight: 700;
        font-size: 1.1rem;
        color: #0da487;
        border-top: 2px solid #e2e8f0;
        padding-top: 10px;
        margin-top: 10px;
    }

    /* Payment Section */
    .payment-section {
        padding: 20px;
    }

    .payment-methods {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-bottom: 15px;
    }

    .payment-btn {
        padding: 12px;
        border: 2px solid #e2e8f0;
        border-radius: 8px;
        background: white;
        cursor: pointer;
        font-weight: 500;
        transition: all 0.2s;
    }

    .payment-btn.active {
        border-color: #0da487;
        background: #ecfdf5;
        color: #0da487;
    }

    .checkout-btn {
        width: 100%;
        background: #0da487;
        color: white;
        border: none;
        padding: 15px;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .checkout-btn:hover {
        background: #0891b2;
    }

    .checkout-btn:disabled {
        background: #94a3b8;
        cursor: not-allowed;
    }

    /* Empty State */
    .empty-cart {
        text-align: center;
        padding: 40px 20px;
        color: #64748b;
    }

    .empty-cart-icon {
        font-size: 3rem;
        margin-bottom: 15px;
        opacity: 0.5;
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .pos-container {
            flex-direction: column;
        }

        .pos-right-panel {
            width: 100%;
            order: -1;
            min-height: 300px;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        }
    }

    @media (max-width: 640px) {
        .filter-row {
            flex-direction: column;
            align-items: stretch;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
        }

        .product-card {
            padding: 10px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="pos-container">
    <!-- Left Panel - Products -->
    <div class="pos-left-panel">
        <div class="pos-header">
            <h2>🛍️ Products</h2>
        </div>

        <div class="pos-filters">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="product-search">Search Products</label>
                    <input type="text" id="product-search" class="filter-input" placeholder="Search by name or SKU..." style="width: 250px;">
                </div>

                <div class="filter-group">
                    <label for="category-filter">Category</label>
                    <select id="category-filter" class="filter-input" style="width: 200px;">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <label>&nbsp;</label>
                    <button type="button" id="clear-filters" class="filter-input" style="background: #f1f5f9; cursor: pointer; width: 100px;">
                        Clear Filters
                    </button>
                </div>
            </div>
        </div>

        <div class="products-container">
            <div class="products-grid" id="products-grid">
                {% for product in products %}
                    <div class="product-card"
                         data-product-id="{{ product.id }}"
                         data-category-id="{{ product.category.id|default:'' }}"
                         data-product-name="{{ product.pos_name|lower }}"
                         data-sku="{{ product.sku|lower }}">

                        <div class="product-image">
                            {% if product.primary_image %}
                                <img src="{{ product.primary_image.image.url }}" alt="{{ product.pos_name }}" style="width: 100%; height: 100%; object-fit: cover; border-radius: 10px;">
                            {% else %}
                                📦
                            {% endif %}
                        </div>

                        <div class="product-name">{{ product.pos_name }}</div>
                        <div class="product-price">Rs. {{ product.pos_price|floatformat:0 }}</div>
                        <div class="product-stock {% if product.current_stock < 10 %}stock-low{% endif %}">
                            Stock: {{ product.current_stock|floatformat:0 }} {{ product.unit_label }}
                        </div>

                        {% if product.current_stock <= 0 %}
                            <button class="add-to-cart-btn" disabled style="background: #f3f4f6; color: #9ca3af;">
                                Out of Stock
                            </button>
                        {% else %}
                            <!-- Smart Quantity/Price Selector -->
                            <div class="product-selector" id="selector-{{ product.id }}">
                                <!-- Mode Toggle Icons -->
                                <div class="selector-mode-toggle">
                                    <button class="mode-btn active" data-mode="quantity" data-product-id="{{ product.id }}"
                                            onclick="toggleSelectorMode({{ product.id }}, 'quantity')"
                                            title="Quantity Mode">
                                        📦
                                    </button>
                                    <button class="mode-btn" data-mode="price" data-product-id="{{ product.id }}"
                                            onclick="toggleSelectorMode({{ product.id }}, 'price')"
                                            title="Price Mode">
                                        💰
                                    </button>
                                </div>

                                <!-- Quantity/Price Input Section -->
                                <div class="selector-input-section">
                                    <div class="input-controls">
                                        <button class="qty-minus-btn" onclick="adjustProductValue({{ product.id }}, -1)"
                                                data-product-id="{{ product.id }}">-</button>

                                        <div class="input-wrapper">
                                            <input type="number"
                                                   class="product-input"
                                                   id="input-{{ product.id }}"
                                                   data-product-id="{{ product.id }}"
                                                   data-price="{{ product.pos_price }}"
                                                   data-unit="{{ product.unit_label|escapejs }}"
                                                   data-stock="{{ product.current_stock }}"
                                                   value="1"
                                                   min="0"
                                                   step="0.1"
                                                   onchange="updateProductPreview({{ product.id }})"
                                                   oninput="updateProductPreview({{ product.id }})">
                                            <span class="input-label" id="label-{{ product.id }}">{{ product.unit_label }}</span>
                                        </div>

                                        <button class="qty-plus-btn" onclick="adjustProductValue({{ product.id }}, 1)"
                                                data-product-id="{{ product.id }}">+</button>
                                    </div>

                                    <!-- Real-time Calculation Display -->
                                    <div class="calculation-preview" id="preview-{{ product.id }}">
                                        1 {{ product.unit_label }} = Rs. {{ product.pos_price }}
                                    </div>

                                    <!-- Add to Cart Button -->
                                    <button class="smart-add-btn" onclick="smartAddToCart({{ product.id }})"
                                            data-product-id="{{ product.id }}">
                                        ➕ Add to Cart
                                    </button>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% empty %}
                    <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #64748b;">
                        <div style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;">📦</div>
                        <h3>No POS Products Found</h3>
                        <p>Please enable products for POS in the inventory management section.</p>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Right Panel - Cart -->
    <div class="pos-right-panel">
        <div class="cart-header">
            <h3>🛒 Shopping Cart</h3>
        </div>

        <!-- Customer Selection -->
        <div class="customer-section">
            <h4>Customer</h4>
            <select id="customer-select" class="customer-select">
                <option value="">Walk-in Customer</option>
                {% for customer in customers %}
                    <option value="{{ customer.id }}"
                            data-credit-limit="{{ customer.credit_limit }}"
                            data-current-balance="{{ customer.current_balance }}">
                        {{ customer.display_name }}
                        {% if customer.mobile_number %}({{ customer.mobile_number }}){% endif %}
                    </option>
                {% endfor %}
            </select>
        </div>

        <!-- Cart Items -->
        <div class="cart-items" id="cart-items">
            <div class="empty-cart">
                <div class="empty-cart-icon">🛒</div>
                <h4>Cart is Empty</h4>
                <p>Add products to start a sale</p>
            </div>
        </div>

        <!-- Cart Summary -->
        <div class="cart-summary" id="cart-summary" style="display: none;">
            <div class="summary-row">
                <span>Subtotal:</span>
                <span id="subtotal">Rs. 0</span>
            </div>
            <div class="summary-row">
                <span>Discount:</span>
                <span id="discount">Rs. 0</span>
            </div>
            <div class="summary-row">
                <span>Tax:</span>
                <span id="tax">Rs. 0</span>
            </div>
            <div class="summary-row" style="padding: 5px 0;">
                <label for="discount-percent" style="font-size: 0.9rem;">Discount %:</label>
                <input type="number" id="discount-percent" placeholder="0" min="0" max="100" step="0.1"
                       style="width: 70px; padding: 2px 5px; border: 1px solid #d1d5db; border-radius: 4px; text-align: right;"
                       onchange="updateCartDisplay()">
            </div>
            <div class="summary-row" style="padding: 5px 0;">
                <label for="manual-tax" style="font-size: 0.9rem;">Manual Tax:</label>
                <input type="number" id="manual-tax" placeholder="0" min="0" step="0.01"
                       style="width: 70px; padding: 2px 5px; border: 1px solid #d1d5db; border-radius: 4px; text-align: right;"
                       onchange="updateCartDisplay()">
            </div>
            <div class="summary-row total">
                <span>Total:</span>
                <span id="total">Rs. 0</span>
            </div>
        </div>

        <!-- Payment Section -->
        <div class="payment-section">
            <h4 style="margin: 0 0 15px 0; color: #374151;">Payment Method</h4>
            <div class="payment-methods">
                <button class="payment-btn active" data-method="cash">💵 Cash</button>
                <button class="payment-btn" data-method="card">💳 Card</button>
                <button class="payment-btn" data-method="loan">🏦 Loan</button>
                <button class="payment-btn" data-method="mixed">🔄 Cash + Loan</button>
            </div>

            <!-- Mixed Payment Details (Hidden by default) -->
            <div id="mixed-payment-details" class="mixed-payment-section" style="display: none; margin-top: 15px; padding: 15px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">
                <h5 style="margin: 0 0 10px 0; color: #374151;">Mixed Payment Details</h5>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">Cash Amount:</label>
                        <input type="number" id="cash-amount-input" step="0.01" min="0"
                               style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;"
                               placeholder="0.00" onchange="updateMixedPayment()">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">Loan Amount:</label>
                        <input type="number" id="loan-amount-input" step="0.01" min="0"
                               style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;"
                               placeholder="0.00" onchange="updateMixedPayment()">
                    </div>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                    <div>
                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">Loan Due Date:</label>
                        <input type="date" id="loan-due-date"
                               style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;">
                    </div>
                    <div>
                        <label style="display: block; font-size: 12px; color: #6b7280; margin-bottom: 4px;">Interest Rate (%):</label>
                        <input type="number" id="loan-interest-rate" step="0.1" min="0" value="0"
                               style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 4px;"
                               placeholder="0.0">
                    </div>
                </div>

                <div id="mixed-payment-summary" style="background: white; padding: 10px; border-radius: 4px; border: 1px solid #e5e7eb;">
                    <div style="font-size: 12px; color: #6b7280;">Payment Breakdown:</div>
                    <div style="display: flex; justify-content: space-between; margin-top: 4px;">
                        <span>Cash:</span> <span id="cash-display">Rs. 0.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between;">
                        <span>Loan:</span> <span id="loan-display">Rs. 0.00</span>
                    </div>
                    <div style="display: flex; justify-content: space-between; font-weight: bold; border-top: 1px solid #e5e7eb; padding-top: 4px; margin-top: 4px;">
                        <span>Total:</span> <span id="total-display">Rs. 0.00</span>
                    </div>
                    <div id="balance-warning" style="color: #ef4444; font-size: 12px; margin-top: 4px; display: none;">
                        ⚠️ Total doesn't match cart amount!
                    </div>
                </div>
            </div>

            <button class="checkout-btn" id="checkout-btn" disabled onclick="processCheckout()">
                Complete Sale
            </button>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 40px; border-radius: 12px; text-align: center; max-width: 400px; margin: 20px;">
        <div style="font-size: 4rem; margin-bottom: 20px;">✅</div>
        <h3 style="color: #0da487; margin-bottom: 15px;">Sale Completed!</h3>
        <p id="success-message">Transaction processed successfully</p>
        <button onclick="closeSucessModal()" style="background: #0da487; color: white; border: none; padding: 12px 24px; border-radius: 8px; margin-top: 20px; cursor: pointer;">
            Start New Sale
        </button>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let cart = [];
    let currentTransaction = null;
    let selectedPaymentMethod = 'cash';

    // Products data with tax rates for calculations
    const products = [
        {% for product in products %}
        {
            id: {{ product.id }},
            name: "{{ product.pos_name|escapejs }}",
            price: {{ product.selling_price }},
            tax_rate: {{ product.tax_rate }},
            stock: {{ product.current_stock }},
            unit: "{{ product.unit_label|escapejs }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];

    // Product filtering
    document.getElementById('product-search').addEventListener('input', filterProducts);
    document.getElementById('category-filter').addEventListener('change', filterProducts);
    document.getElementById('clear-filters').addEventListener('click', clearFilters);

    // Payment method selection
    document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.payment-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            selectedPaymentMethod = this.dataset.method;

            // Show/hide mixed payment details
            const mixedDetails = document.getElementById('mixed-payment-details');
            if (selectedPaymentMethod === 'mixed') {
                mixedDetails.style.display = 'block';
                initializeMixedPayment();
            } else {
                mixedDetails.style.display = 'none';
            }
        });
    });

    // Mixed Payment Functions
    function initializeMixedPayment() {
        const total = parseFloat(document.getElementById('total').textContent.replace('Rs. ', '')) || 0;

        // Set default due date to 30 days from now
        const today = new Date();
        const dueDate = new Date(today);
        dueDate.setDate(today.getDate() + 30);
        document.getElementById('loan-due-date').value = dueDate.toISOString().split('T')[0];

        // Set loan amount to total by default
        document.getElementById('loan-amount-input').value = total.toFixed(2);
        document.getElementById('cash-amount-input').value = '0.00';

        updateMixedPayment();
    }

    function updateMixedPayment() {
        const total = parseFloat(document.getElementById('total').textContent.replace('Rs. ', '')) || 0;
        const cashAmount = parseFloat(document.getElementById('cash-amount-input').value) || 0;
        const loanAmount = parseFloat(document.getElementById('loan-amount-input').value) || 0;

        // Update displays
        document.getElementById('cash-display').textContent = `Rs. ${cashAmount.toFixed(2)}`;
        document.getElementById('loan-display').textContent = `Rs. ${loanAmount.toFixed(2)}`;
        document.getElementById('total-display').textContent = `Rs. ${(cashAmount + loanAmount).toFixed(2)}`;

        // Show warning if total doesn't match
        const balanceWarning = document.getElementById('balance-warning');
        if (Math.abs((cashAmount + loanAmount) - total) > 0.01) {
            balanceWarning.style.display = 'block';
            balanceWarning.textContent = `⚠️ Total (Rs. ${(cashAmount + loanAmount).toFixed(2)}) doesn't match cart amount (Rs. ${total.toFixed(2)})!`;
        } else {
            balanceWarning.style.display = 'none';
        }

        // Auto-adjust loan amount when cash amount changes
        if (event && event.target.id === 'cash-amount-input') {
            const newLoanAmount = Math.max(0, total - cashAmount);
            document.getElementById('loan-amount-input').value = newLoanAmount.toFixed(2);
            document.getElementById('loan-display').textContent = `Rs. ${newLoanAmount.toFixed(2)}`;
            document.getElementById('total-display').textContent = `Rs. ${total.toFixed(2)}`;
            balanceWarning.style.display = 'none';
        }

        // Auto-adjust cash amount when loan amount changes
        if (event && event.target.id === 'loan-amount-input') {
            const newCashAmount = Math.max(0, total - loanAmount);
            document.getElementById('cash-amount-input').value = newCashAmount.toFixed(2);
            document.getElementById('cash-display').textContent = `Rs. ${newCashAmount.toFixed(2)}`;
            document.getElementById('total-display').textContent = `Rs. ${total.toFixed(2)}`;
            balanceWarning.style.display = 'none';
        }
    }

    // Smart Product Selector Functions
    function toggleSelectorMode(productId, mode) {
        const selector = document.getElementById(`selector-${productId}`);
        const modeButtons = selector.querySelectorAll('.mode-btn');
        const input = document.getElementById(`input-${productId}`);
        const label = document.getElementById(`label-${productId}`);

        // Update active state
        modeButtons.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.mode === mode) {
                btn.classList.add('active');
            }
        });

        // Update input and label based on mode
        const unit = input.dataset.unit;
        const price = parseFloat(input.dataset.price);

        if (mode === 'price') {
            label.textContent = 'Rs.';
            input.placeholder = '0';
            input.step = '0.01';
            // Convert current quantity to price
            const currentQty = parseFloat(input.value) || 1;
            input.value = (currentQty * price).toFixed(2);
        } else {
            label.textContent = unit;
            input.placeholder = '0';
            input.step = '0.1';
            // Convert current price to quantity
            const currentPrice = parseFloat(input.value) || price;
            input.value = (currentPrice / price).toFixed(2);
        }

        // Update the preview
        updateProductPreview(productId);
    }

    function adjustProductValue(productId, change) {
        const input = document.getElementById(`input-${productId}`);
        const selector = document.getElementById(`selector-${productId}`);
        const activeMode = selector.querySelector('.mode-btn.active').dataset.mode;

        let currentValue = parseFloat(input.value) || 0;
        let newValue;

        if (activeMode === 'price') {
            const price = parseFloat(input.dataset.price);
            // Adjust by one unit worth of price
            newValue = Math.max(0, currentValue + (change * price));
            input.value = newValue.toFixed(2);
        } else {
            // Adjust quantity by step amount
            const step = parseFloat(input.step) || 0.1;
            newValue = Math.max(0, currentValue + (change * step));
            input.value = newValue.toFixed(2);
        }

        updateProductPreview(productId);
    }

    function updateProductPreview(productId) {
        const input = document.getElementById(`input-${productId}`);
        const preview = document.getElementById(`preview-${productId}`);
        const selector = document.getElementById(`selector-${productId}`);
        const activeMode = selector.querySelector('.mode-btn.active').dataset.mode;

        const inputValue = parseFloat(input.value) || 0;
        const price = parseFloat(input.dataset.price);
        const unit = input.dataset.unit;
        const stock = parseFloat(input.dataset.stock);

        if (inputValue <= 0) {
            preview.textContent = '';
            return;
        }

        let quantity, totalPrice;
        let previewText;

        if (activeMode === 'price') {
            totalPrice = inputValue;
            quantity = inputValue / price;
            previewText = `Rs. ${totalPrice.toFixed(2)} = ${quantity.toFixed(2)} ${unit}`;
        } else {
            quantity = inputValue;
            totalPrice = quantity * price;
            previewText = `${quantity.toFixed(2)} ${unit} = Rs. ${totalPrice.toFixed(2)}`;
        }

        // Check stock availability
        if (quantity > stock) {
            previewText += ` ⚠️ (Only ${stock} ${unit} available)`;
            preview.style.color = '#ef4444';
        } else {
            preview.style.color = '#0da487';
        }

        preview.textContent = previewText;
    }

    function smartAddToCart(productId) {
        const input = document.getElementById(`input-${productId}`);
        const selector = document.getElementById(`selector-${productId}`);
        const activeMode = selector.querySelector('.mode-btn.active').dataset.mode;

        const inputValue = parseFloat(input.value) || 0;
        const price = parseFloat(input.dataset.price);
        const stock = parseFloat(input.dataset.stock);

        if (inputValue <= 0) {
            alert('Please enter a valid amount');
            return;
        }

        let quantity;
        if (activeMode === 'price') {
            quantity = inputValue / price;
        } else {
            quantity = inputValue;
        }

        if (quantity > stock) {
            alert(`Only ${stock} units available in stock`);
            return;
        }

        // Get product data from the products array
        const product = products.find(p => p.id === productId);
        if (!product) {
            alert('Product not found');
            return;
        }

        // Use the existing addToCart function with proper parameters
        smartAddToCartWithQuantity(productId, product.name, product.price, product.stock, quantity);

        // Reset to default values
        if (activeMode === 'price') {
            input.value = price.toFixed(2);
        } else {
            input.value = '1';
        }
        updateProductPreview(productId);

        // Show brief success feedback
        const preview = document.getElementById(`preview-${productId}`);
        const originalText = preview.textContent;
        preview.textContent = '✅ Added to cart!';
        preview.style.color = '#10b981';
        setTimeout(() => {
            preview.textContent = originalText;
            preview.style.color = '#0da487';
        }, 1500);
    }

    function filterProducts() {
        const searchTerm = document.getElementById('product-search').value.toLowerCase();
        const categoryId = document.getElementById('category-filter').value;
        const products = document.querySelectorAll('.product-card');

        products.forEach(product => {
            const name = product.dataset.productName;
            const sku = product.dataset.sku;
            const productCategoryId = product.dataset.categoryId;

            const matchesSearch = !searchTerm || name.includes(searchTerm) || sku.includes(searchTerm);
            const matchesCategory = !categoryId || productCategoryId === categoryId;

            if (matchesSearch && matchesCategory) {
                product.style.display = 'block';
            } else {
                product.style.display = 'none';
            }
        });
    }

    function clearFilters() {
        document.getElementById('product-search').value = '';
        document.getElementById('category-filter').value = '';
        filterProducts();
    }

    function addToCart(productId, name, price, unit, stock) {
        // Check if product already in cart
        const existingItem = cart.find(item => item.productId === productId);

        if (existingItem) {
            // Check stock availability
            if (existingItem.quantity < stock) {
                existingItem.quantity += 1;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                alert('Insufficient stock available');
                return;
            }
        } else {
            // Add new item
            cart.push({
                productId: productId,
                name: name,
                price: price,
                unit: unit,
                quantity: 1,
                total: price,
                stock: stock
            });
        }

        updateCartDisplay();
    }

    function smartAddToCartWithQuantity(productId, name, price, stock, quantity) {
        // Check if product already in cart
        const existingItem = cart.find(item => item.productId === productId);

        if (existingItem) {
            // Check stock availability for additional quantity
            if (existingItem.quantity + quantity <= stock) {
                existingItem.quantity += quantity;
                existingItem.total = existingItem.quantity * existingItem.price;
            } else {
                alert(`Insufficient stock available. Only ${stock - existingItem.quantity} more units can be added.`);
                return;
            }
        } else {
            // Check if requested quantity is available
            if (quantity <= stock) {
                // Add new item with custom quantity
                cart.push({
                    productId: productId,
                    name: name,
                    price: price,
                    unit: products.find(p => p.id === productId)?.unit || 'units',
                    quantity: quantity,
                    total: quantity * price,
                    stock: stock
                });
            } else {
                alert(`Insufficient stock available. Only ${stock} units in stock.`);
                return;
            }
        }

        updateCartDisplay();
    }

    function removeFromCart(productId) {
        cart = cart.filter(item => item.productId !== productId);
        updateCartDisplay();
    }

    function updateQuantity(productId, newQuantity) {
        const item = cart.find(item => item.productId === productId);
        if (item) {
            if (newQuantity <= 0) {
                removeFromCart(productId);
            } else if (newQuantity <= item.stock) {
                item.quantity = newQuantity;
                item.total = item.quantity * item.price;
                updateCartDisplay();
            } else {
                alert('Insufficient stock available');
            }
        }
    }

    function updateCartDisplay() {
        const cartItems = document.getElementById('cart-items');
        const cartSummary = document.getElementById('cart-summary');
        const checkoutBtn = document.getElementById('checkout-btn');

        if (cart.length === 0) {
            cartItems.innerHTML = `
                <div class="empty-cart">
                    <div class="empty-cart-icon">🛒</div>
                    <h4>Cart is Empty</h4>
                    <p>Add products to start a sale</p>
                </div>
            `;
            cartSummary.style.display = 'none';
            checkoutBtn.disabled = true;
            return;
        }

        let cartHtml = '';
        let subtotal = 0;

        cart.forEach(item => {
            subtotal += item.total;
            cartHtml += `
                <div class="cart-item">
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.name}</div>
                        <div class="cart-item-price">Rs. ${item.price} / ${item.unit}</div>
                    </div>
                    <div class="cart-item-controls">
                        <button class="qty-btn" onclick="updateQuantity(${item.productId}, ${item.quantity - 1})">-</button>
                        <input type="number" class="qty-input" value="${item.quantity}"
                               onchange="updateQuantity(${item.productId}, parseInt(this.value))"
                               min="1" max="${item.stock}">
                        <button class="qty-btn" onclick="updateQuantity(${item.productId}, ${item.quantity + 1})">+</button>
                        <button class="remove-btn" onclick="removeFromCart(${item.productId})">×</button>
                    </div>
                </div>
            `;
        });

        cartItems.innerHTML = cartHtml;

        // Update summary
        // Calculate discount based on percentage
        const discountPercentInput = document.getElementById('discount-percent');
        const discountPercent = discountPercentInput ? parseFloat(discountPercentInput.value) || 0 : 0;
        const discount = (subtotal * discountPercent) / 100;

        // Calculate tax based on product tax rates (not hardcoded 17%)
        let productTax = 0;
        cart.forEach(item => {
            const product = products.find(p => p.id === item.productId);
            if (product && product.tax_rate > 0) {
                const itemSubtotal = item.price * item.quantity;
                productTax += (itemSubtotal * product.tax_rate) / 100;
            }
        });

        // Add manual tax if specified
        const manualTaxInput = document.getElementById('manual-tax');
        const manualTax = manualTaxInput ? parseFloat(manualTaxInput.value) || 0 : 0;

        const totalTax = productTax + manualTax;
        const total = subtotal - discount + totalTax;

        document.getElementById('subtotal').textContent = `Rs. ${subtotal.toFixed(0)}`;
        document.getElementById('discount').textContent = `Rs. ${discount.toFixed(0)}`;
        document.getElementById('tax').textContent = `Rs. ${totalTax.toFixed(0)}`;
        document.getElementById('total').textContent = `Rs. ${total.toFixed(0)}`;

        cartSummary.style.display = 'block';
        checkoutBtn.disabled = false;
    }

    async function processCheckout() {
        if (cart.length === 0) {
            alert('Cart is empty');
            return;
        }

        const checkoutBtn = document.getElementById('checkout-btn');
        checkoutBtn.disabled = true;
        checkoutBtn.textContent = 'Processing...';

        try {
            // Step 1: Create transaction
            const customerId = document.getElementById('customer-select').value;
            const transactionData = {
                customer_id: customerId || null,
                notes: 'POS Sale'
            };

            const transactionResponse = await fetch('/inventory/pos/api/transactions/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(transactionData)
            });

            if (!transactionResponse.ok) {
                throw new Error('Failed to create transaction');
            }

            const transactionResult = await transactionResponse.json();
            currentTransaction = transactionResult.transaction;

            // Step 2: Add items to transaction
            for (const item of cart) {
                const itemData = {
                    product_id: item.productId,
                    quantity: item.quantity,
                    discount_percent: 0
                };

                const itemResponse = await fetch(`/inventory/pos/api/transactions/${currentTransaction.id}/items/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(itemData)
                });

                if (!itemResponse.ok) {
                    throw new Error(`Failed to add item: ${item.name}`);
                }
            }

            // Step 3: Process payment
            const total = parseFloat(document.getElementById('total').textContent.replace('Rs. ', ''));
            let paymentData = {};

            if (selectedPaymentMethod === 'cash') {
                paymentData = {
                    cash_amount: total,
                    loan_amount: 0,
                    card_amount: 0
                };
            } else if (selectedPaymentMethod === 'card') {
                paymentData = {
                    cash_amount: 0,
                    loan_amount: 0,
                    card_amount: total
                };
            } else if (selectedPaymentMethod === 'loan') {
                paymentData = {
                    cash_amount: 0,
                    loan_amount: total,
                    card_amount: 0,
                    loan_due_date: document.getElementById('loan-due-date')?.value || new Date(Date.now() + 30*24*60*60*1000).toISOString().split('T')[0],
                    loan_interest_rate: document.getElementById('loan-interest-rate')?.value || 0
                };
            } else if (selectedPaymentMethod === 'mixed') {
                const cashAmount = parseFloat(document.getElementById('cash-amount-input').value) || 0;
                const loanAmount = parseFloat(document.getElementById('loan-amount-input').value) || 0;
                const loanDueDate = document.getElementById('loan-due-date').value;
                const loanInterestRate = parseFloat(document.getElementById('loan-interest-rate').value) || 0;

                paymentData = {
                    cash_amount: cashAmount,
                    loan_amount: loanAmount,
                    card_amount: 0,
                    loan_due_date: loanDueDate,
                    loan_interest_rate: loanInterestRate
                };
            }

            const paymentResponse = await fetch(`/inventory/pos/api/transactions/${currentTransaction.id}/payment/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(paymentData)
            });

            if (!paymentResponse.ok) {
                throw new Error('Payment processing failed');
            }

            // Step 4: Complete transaction
            const completeResponse = await fetch(`/inventory/pos/api/transactions/${currentTransaction.id}/complete/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            if (!completeResponse.ok) {
                throw new Error('Failed to complete transaction');
            }

            const completeResult = await completeResponse.json();

            // Show success message
            document.getElementById('success-message').textContent =
                `Transaction #${completeResult.result.transaction_number} completed successfully!`;
            document.getElementById('success-modal').style.display = 'flex';

            // Reset cart
            cart = [];
            currentTransaction = null;
            updateCartDisplay();

        } catch (error) {
            alert('Error processing sale: ' + error.message);
            console.error('Checkout error:', error);
        } finally {
            checkoutBtn.disabled = false;
            checkoutBtn.textContent = 'Complete Sale';
        }
    }

    function closeSucessModal() {
        document.getElementById('success-modal').style.display = 'none';
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %}