{% extends 'base/app_base.html' %}

{% block title %}{{ category.name }} - Categories{% endblock %}
{% block page_title %}📂 {{ category.name }}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'inventory:category-list' %}" class="breadcrumb-item">Categories</a>
    <span class="breadcrumb-separator">›</span>
    {% for ancestor in ancestors %}
        <a href="{% url 'inventory:category-detail' ancestor.pk %}" class="breadcrumb-item">{{ ancestor.name }}</a>
        <span class="breadcrumb-separator">›</span>
    {% endfor %}
    <span class="breadcrumb-item active">{{ category.name }}</span>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .category-detail-container {
        max-width: 1200px;
        margin: 0 auto;
    }

    .category-hero {
        background: var(--white);
        border-radius: 16px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
        overflow: hidden;
        margin-bottom: 24px;
    }

    .category-hero-header {
        background: var(--primary-gradient);
        color: white;
        padding: 32px;
        position: relative;
        overflow: hidden;
    }

    .category-hero-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--glass-gradient);
        pointer-events: none;
    }

    .hero-content {
        position: relative;
        z-index: 1;
        display: grid;
        grid-template-columns: auto 1fr auto;
        gap: 24px;
        align-items: center;
    }

    .category-icon-large {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .category-info h1 {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
    }

    .category-path {
        opacity: 0.9;
        font-size: 1rem;
        margin-bottom: 12px;
    }

    .category-description {
        opacity: 0.9;
        font-size: 1.1rem;
        line-height: 1.6;
    }

    .category-actions {
        display: flex;
        gap: 12px;
        flex-direction: column;
    }

    .btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
        white-space: nowrap;
    }

    .btn-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .btn-primary:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .btn-secondary {
        background: var(--white);
        color: var(--primary-green);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .btn-secondary:hover {
        background: var(--success-lighter);
        border-color: white;
        transform: translateY(-2px);
    }

    .btn-danger {
        background: rgba(239, 68, 68, 0.2);
        color: white;
        border: 1px solid rgba(239, 68, 68, 0.3);
    }

    .btn-danger:hover {
        background: rgba(239, 68, 68, 0.3);
        transform: translateY(-2px);
    }

    .category-meta-bar {
        padding: 20px 32px;
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 24px;
    }

    .meta-stat {
        text-align: center;
    }

    .meta-stat-value {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-green);
        display: block;
    }

    .meta-stat-label {
        color: var(--gray-600);
        font-size: 0.9rem;
        margin-top: 4px;
    }

    .content-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
    }

    .content-section {
        background: var(--white);
        border-radius: 16px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
        overflow: hidden;
    }

    .section-header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .section-title {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary-green);
        margin: 0;
    }

    .section-action {
        padding: 8px 16px;
        background: var(--primary-green);
        color: white;
        border-radius: 8px;
        text-decoration: none;
        font-size: 0.85rem;
        font-weight: 600;
        transition: all var(--transition-normal);
    }

    .section-action:hover {
        background: var(--primary-green-light);
        transform: translateY(-1px);
    }

    .items-list {
        padding: 0;
    }

    .item-row {
        padding: 16px 24px;
        border-bottom: 1px solid var(--gray-100);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all var(--transition-normal);
    }

    .item-row:last-child {
        border-bottom: none;
    }

    .item-row:hover {
        background: var(--gray-50);
    }

    .item-icon {
        width: 40px;
        height: 40px;
        background: var(--success-lighter);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: var(--primary-green);
    }

    .item-info {
        flex: 1;
    }

    .item-name {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 4px;
    }

    .item-meta {
        font-size: 0.85rem;
        color: var(--gray-600);
    }

    .item-actions {
        display: flex;
        gap: 8px;
    }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 6px;
    }

    .empty-section {
        padding: 40px 24px;
        text-align: center;
        color: var(--gray-500);
    }

    .empty-section-icon {
        font-size: 2.5rem;
        margin-bottom: 12px;
        opacity: 0.5;
    }

    .full-width {
        grid-column: 1 / -1;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .hero-content {
            grid-template-columns: 1fr;
            text-align: center;
            gap: 16px;
        }

        .category-actions {
            flex-direction: row;
            justify-content: center;
        }

        .category-meta-bar {
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            padding: 16px 20px;
        }

        .content-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        div[style*="grid-template-columns: repeat(auto-fit, minmax(400px, 1fr))"] {
            grid-template-columns: 1fr !important;
        }

        .category-detail-container {
            padding: 0 12px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="category-detail-container">
    <!-- Category Hero Section -->
    <div class="category-hero">
        <div class="category-hero-header">
            <div class="hero-content">
                <div class="category-icon-large">
                    {% if category.level == 0 %}📁
                    {% elif category.level == 1 %}📂
                    {% else %}📄{% endif %}
                </div>

                <div class="category-info">
                    <h1>{{ category.name }}</h1>
                    <div class="category-path">{{ category.full_path }}</div>
                    {% if category.description %}
                        <p class="category-description">{{ category.description }}</p>
                    {% endif %}
                </div>

                <div class="category-actions">
                    <a href="{% url 'inventory:category-update' category.pk %}" class="btn btn-primary">
                        ✏️ Edit
                    </a>
                    <a href="{% url 'inventory:category-list' %}" class="btn btn-secondary">
                        📂 All Categories
                    </a>
                    {% if products_count == 0 and subcategories_count == 0 %}
                        <a href="{% url 'inventory:category-delete' category.pk %}" class="btn btn-danger">
                            🗑️ Delete
                        </a>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Meta Statistics -->
        <div class="category-meta-bar">
            <div class="meta-stat">
                <span class="meta-stat-value">{{ total_products_count }}</span>
                <div class="meta-stat-label">Total Products</div>
            </div>
            <div class="meta-stat">
                <span class="meta-stat-value">{{ inventory_products_count }}</span>
                <div class="meta-stat-label">Enhanced Products</div>
            </div>
            <div class="meta-stat">
                <span class="meta-stat-value">{{ subcategories_count }}</span>
                <div class="meta-stat-label">Subcategories</div>
            </div>
        </div>
    </div>

    <!-- Content Grid -->
    <div style="display: grid; grid-template-columns: 1fr; gap: 24px;">
        <!-- Subcategories Section -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">📁 Subcategories</h2>
                {% if category.level < 2 %}
                    <a href="{% url 'inventory:category-create' %}?parent={{ category.pk }}" class="section-action">
                        ➕ Add Subcategory
                    </a>
                {% endif %}
            </div>

            {% if subcategories %}
                <div class="items-list">
                    {% for subcategory in subcategories %}
                        <div class="item-row">
                            <div class="item-icon">
                                {% if subcategory.level == 1 %}📂
                                {% else %}📄{% endif %}
                            </div>
                            <div class="item-info">
                                <div class="item-name">{{ subcategory.name }}</div>
                                <div class="item-meta">
                                    {{ subcategory.products.count }} inventory + {{ subcategory.pos_products.count }} POS products
                                    {% if subcategory.description %}
                                        • {{ subcategory.description|truncatewords:10 }}
                                    {% endif %}
                                </div>
                            </div>
                            <div class="item-actions">
                                <a href="{% url 'inventory:category-detail' subcategory.pk %}"
                                   class="btn btn-secondary btn-sm">View</a>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="empty-section">
                    <div class="empty-section-icon">📁</div>
                    <p>No subcategories yet</p>
                    {% if category.level < 2 %}
                        <a href="{% url 'inventory:category-create' %}?parent={{ category.pk }}" class="btn btn-primary btn-sm">
                            ➕ Add First Subcategory
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Products Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 24px;">

        <!-- Products Section -->
        <div class="content-section">
            <div class="section-header">
                <h2 class="section-title">📦 Products</h2>
                <div style="display: flex; gap: 8px;">
                    <a href="{% url 'inventory:product-list' %}?category={{ category.pk }}" class="section-action">
                        📦 View All
                    </a>
                </div>
            </div>

            {% if inventory_products %}
                <div class="items-list">
                    {% for product in inventory_products %}
                        <div class="item-row">
                            <div class="item-icon">📦</div>
                            <div class="item-info">
                                <div class="item-name">{{ product.name }}</div>
                                <div class="item-meta">
                                    Rs. {{ product.selling_price }}
                                    {% if product.sku %} • SKU: {{ product.sku }}{% endif %}
                                    {% if product.current_stock is not None %} • Stock: {{ product.current_stock }} {{ product.unit_label }}{% endif %}
                                    {% if product.reorder_level and product.current_stock <= product.reorder_level %}
                                        <span style="color: var(--warning); font-weight: 600;"> ⚠️ Low Stock</span>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="item-actions">
                                <a href="{% url 'inventory:product-detail' product.pk %}" class="btn btn-secondary btn-sm" title="View {{ product.name }} details and manage stock">
                                    🔍 View Details
                                </a>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                {% if inventory_products_count > 10 %}
                    <div style="padding: 16px 24px; text-align: center; border-top: 1px solid var(--gray-200);">
                        <a href="{% url 'inventory:product-list' %}?category={{ category.pk }}" class="btn btn-secondary btn-sm">
                            📋 View All {{ inventory_products_count }} Products in {{ category.name }}
                        </a>
                    </div>
                {% endif %}
            {% else %}
                <div class="empty-section">
                    <div class="empty-section-icon">📦</div>
                    <p>No products in "{{ category.name }}" category yet</p>
                    <a href="{% url 'inventory:product-list' %}?category={{ category.pk }}" class="btn btn-primary btn-sm">
                        📋 View All Products
                    </a>
                </div>
            {% endif %}
        </div>
        </div>
    </div>

    <!-- Full Width Sections for Additional Info -->
    {% if ancestors %}
        <div class="content-section full-width" style="margin-top: 24px;">
            <div class="section-header">
                <h2 class="section-title">🗂️ Category Hierarchy</h2>
            </div>
            <div style="padding: 20px 24px;">
                <div style="display: flex; align-items: center; gap: 12px; flex-wrap: wrap;">
                    <a href="{% url 'inventory:category-list' %}" style="text-decoration: none;">
                        <span style="padding: 8px 12px; background: var(--gray-100); border-radius: 8px; font-size: 0.9rem;">
                            🏠 Root
                        </span>
                    </a>
                    {% for ancestor in ancestors %}
                        <span style="color: var(--gray-400);">›</span>
                        <a href="{% url 'inventory:category-detail' ancestor.pk %}" style="text-decoration: none;">
                            <span style="padding: 8px 12px; background: var(--success-lighter); border-radius: 8px; font-size: 0.9rem; color: var(--primary-green);">
                                {{ ancestor.name }}
                            </span>
                        </a>
                    {% endfor %}
                    <span style="color: var(--gray-400);">›</span>
                    <span style="padding: 8px 12px; background: var(--primary-green); color: white; border-radius: 8px; font-size: 0.9rem; font-weight: 600;">
                        {{ category.name }}
                    </span>
                </div>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any interactive features here
    console.log('Category detail page loaded for: {{ category.name }}');
});
</script>
{% endblock %}