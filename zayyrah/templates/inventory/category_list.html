{% extends 'base/app_base.html' %}

{% block title %}Categories - Inventory Management{% endblock %}
{% block page_title %}📂 Categories{% endblock %}
{% block breadcrumb_current %}Categories{% endblock %}

{% block nav_inventory_categories %}active{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="#" class="breadcrumb-item">Inventory</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">Categories</span>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .category-container {
        background: var(--white);
        border-radius: 16px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
        overflow: hidden;
        animation: fadeIn 0.4s ease-out;
    }

    .category-header {
        background: var(--primary-gradient);
        color: white;
        padding: 24px;
        position: relative;
        overflow: hidden;
    }

    .category-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--glass-gradient);
        pointer-events: none;
    }

    .category-header-content {
        position: relative;
        z-index: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .category-title h1 {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
    }

    .category-title p {
        opacity: 0.9;
        margin: 0;
    }

    .category-actions {
        display: flex;
        gap: 12px;
    }

    .btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
    }

    .btn-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.3);
        backdrop-filter: blur(10px);
    }

    .btn-primary:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .btn-secondary {
        background: var(--white);
        color: var(--primary-green);
        border: 2px solid var(--gray-200);
    }

    .btn-secondary:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        transform: translateY(-2px);
    }

    .search-filter-bar {
        padding: 20px 24px;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
    }

    .search-container {
        position: relative;
        max-width: 400px;
    }

    .search-input {
        width: 100%;
        padding: 12px 16px 12px 44px;
        border: 2px solid var(--gray-200);
        border-radius: 12px;
        background: var(--white);
        font-size: 0.95rem;
        transition: all var(--transition-normal);
        outline: none;
    }

    .search-input:focus {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
    }

    .search-icon {
        position: absolute;
        left: 14px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-400);
        font-size: 1.1rem;
    }

    .categories-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        padding: 24px;
    }

    .category-card {
        background: var(--white);
        border: 2px solid var(--gray-200);
        border-radius: 16px;
        padding: 20px;
        transition: all var(--transition-normal);
        position: relative;
        overflow: hidden;
    }

    .category-card:hover {
        border-color: var(--primary-green);
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
    }

    .category-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--primary-gradient);
        border-radius: 16px 16px 0 0;
    }

    .category-level {
        position: absolute;
        top: 16px;
        right: 16px;
        background: var(--success-lighter);
        color: var(--primary-green);
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .category-icon {
        width: 48px;
        height: 48px;
        background: var(--success-lighter);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        margin-bottom: 16px;
    }

    .category-name {
        font-size: 1.3rem;
        font-weight: 700;
        color: var(--primary-green);
        margin-bottom: 8px;
        line-height: 1.3;
    }

    .category-description {
        color: var(--gray-600);
        font-size: 0.9rem;
        margin-bottom: 16px;
        line-height: 1.5;
    }

    .category-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        margin-bottom: 16px;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
        color: var(--gray-600);
        font-size: 0.85rem;
    }

    .category-actions-card {
        display: flex;
        gap: 8px;
        padding-top: 16px;
        border-top: 1px solid var(--gray-200);
    }

    .btn-sm {
        padding: 8px 12px;
        font-size: 0.85rem;
        border-radius: 8px;
        flex: 1;
        text-align: center;
    }

    .btn-view {
        background: var(--success-lighter);
        color: var(--primary-green);
        border: 1px solid var(--success-green);
    }

    .btn-edit {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .btn-view:hover, .btn-edit:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-sm);
    }

    .empty-state {
        text-align: center;
        padding: 60px 24px;
        color: var(--gray-500);
    }

    .empty-state-icon {
        font-size: 4rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .empty-state h3 {
        font-size: 1.5rem;
        color: var(--gray-700);
        margin-bottom: 8px;
    }

    .empty-state p {
        margin-bottom: 24px;
        font-size: 1.1rem;
    }

    .pagination-container {
        padding: 20px 24px;
        border-top: 1px solid var(--gray-200);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 16px;
    }

    .pagination-info {
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .stats-bar {
        padding: 16px 24px;
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        justify-content: between;
        align-items: center;
    }

    .stats-item {
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .level-indent-0 { margin-left: 0; }
    .level-indent-1 { margin-left: 20px; }
    .level-indent-2 { margin-left: 40px; }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .category-header-content {
            flex-direction: column;
            gap: 16px;
            text-align: center;
        }

        .category-actions {
            width: 100%;
            justify-content: center;
        }

        .categories-grid {
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 16px;
        }

        .search-filter-bar {
            padding: 16px;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="category-container">
    <!-- Header -->
    <div class="category-header">
        <div class="category-header-content">
            <div class="category-title">
                <h1>📂 Product Categories</h1>
                <p>Organize your products into categories and subcategories</p>
            </div>
            <div class="category-actions">
                <a href="{% url 'inventory:category-create' %}" class="btn btn-primary">
                    ➕ Add Category
                </a>
            </div>
        </div>
    </div>

    <!-- Search and Filter Bar -->
    <div class="search-filter-bar">
        <form method="get" class="search-form">
            <div class="search-container">
                <input type="text"
                       name="search"
                       value="{{ search_query }}"
                       placeholder="Search categories..."
                       class="search-input"
                       id="categorySearch">
                <span class="search-icon">🔍</span>
            </div>
        </form>
    </div>

    <!-- Stats Bar -->
    {% if total_categories > 0 %}
    <div class="stats-bar">
        <div class="stats-item">
            <strong>{{ total_categories }}</strong> categories found
        </div>
    </div>
    {% endif %}

    <!-- Categories Grid -->
    {% if categories %}
        <div class="categories-grid">
            {% for category in categories %}
                <div class="category-card level-indent-{{ category.level }}">
                    <div class="category-level">Level {{ category.level|add:1 }}</div>

                    <div class="category-icon">
                        {% if category.level == 0 %}📁
                        {% elif category.level == 1 %}📂
                        {% else %}📄{% endif %}
                    </div>

                    <h3 class="category-name">{{ category.name }}</h3>

                    {% if category.description %}
                        <p class="category-description">{{ category.description }}</p>
                    {% endif %}

                    <div class="category-meta">
                        {% if category.parent %}
                            <div class="meta-item">
                                <span>👆</span>
                                <span>Parent: {{ category.parent.name }}</span>
                            </div>
                        {% endif %}

                        <div class="meta-item">
                            <span>📦</span>
                            <span>{{ category.products.count }} products</span>
                        </div>

                        {% if category.subcategories.count > 0 %}
                            <div class="meta-item">
                                <span>📁</span>
                                <span>{{ category.subcategories.count }} subcategories</span>
                            </div>
                        {% endif %}
                    </div>

                    <div class="category-actions-card">
                        <a href="{% url 'inventory:category-detail' category.pk %}" class="btn btn-view btn-sm">
                            👁️ View
                        </a>
                        <a href="{% url 'inventory:category-update' category.pk %}" class="btn btn-edit btn-sm">
                            ✏️ Edit
                        </a>
                    </div>
                </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if is_paginated %}
            <div class="pagination-container">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                       class="btn btn-secondary btn-sm">← Previous</a>
                {% endif %}

                <div class="pagination-info">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </div>

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}"
                       class="btn btn-secondary btn-sm">Next →</a>
                {% endif %}
            </div>
        {% endif %}
    {% else %}
        <!-- Empty State -->
        <div class="empty-state">
            <div class="empty-state-icon">📂</div>
            <h3>No categories found</h3>
            {% if search_query %}
                <p>No categories match your search for "{{ search_query }}"</p>
                <a href="{% url 'inventory:category-list' %}" class="btn btn-secondary">Clear Search</a>
            {% else %}
                <p>Create your first category to organize your products</p>
                <a href="{% url 'inventory:category-create' %}" class="btn btn-primary">➕ Create Category</a>
            {% endif %}
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-submit search form on input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('categorySearch');
    let searchTimeout;

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.form.submit();
            }, 500);
        });
    }
});
</script>
{% endblock %}