{% extends 'base/app_base.html' %}

{% block title %}{{ product.name }} · Product Detail · CHANNAB{% endblock %}

{% block nav_products %}active{% endblock %}
{% block bottom_nav_products %}active{% endblock %}

{% block page_title %}{{ product.name }}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'pos:product-list' %}" class="breadcrumb-item">Products</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">{{ product.name }}</span>
</nav>
{% endblock %}

{% block extra_css %}
<style>
    /* Product Detail Specific Styles using CHANNAB Design System */
    .product-header-card {
        background: var(--white);
        border-radius: 16px;
        padding: 32px;
        margin-bottom: 24px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
    }

    .product-info-grid {
        display: grid;
        grid-template-columns: 300px 1fr auto;
        gap: 32px;
        align-items: start;
    }

    .product-image-container {
        background: var(--gray-100);
        border-radius: 12px;
        aspect-ratio: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px dashed var(--gray-300);
        font-size: 3rem;
        color: var(--gray-400);
    }

    .product-details h1 {
        font-size: 2.25rem;
        font-weight: 700;
        color: var(--primary-green);
        margin-bottom: 16px;
        letter-spacing: -0.025em;
    }

    .product-badges {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 24px;
    }

    .badge {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .badge-primary {
        background: var(--primary-green);
        color: white;
    }

    .badge-secondary {
        background: var(--gray-200);
        color: var(--gray-700);
    }

    .metrics-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 24px;
    }

    .metric-card {
        background: var(--gray-50);
        padding: 20px;
        border-radius: 12px;
        text-align: center;
        border: 1px solid var(--gray-200);
        transition: all var(--transition-normal);
    }

    .metric-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .metric-label {
        font-size: 0.85rem;
        color: var(--gray-500);
        margin-bottom: 8px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .metric-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--primary-green);
        margin-bottom: 4px;
    }

    .metric-unit {
        font-size: 0.8rem;
        color: var(--gray-600);
        font-weight: 500;
    }

    .stock-low { color: var(--warning) !important; }
    .stock-critical { color: var(--danger) !important; }
    .stock-good { color: var(--primary-green-lighter) !important; }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;
        min-width: 180px;
    }

    .btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        text-decoration: none;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all var(--transition-normal);
        border: 2px solid transparent;
        cursor: pointer;
    }

    .btn-primary {
        background: var(--primary-gradient);
        color: white;
        box-shadow: var(--shadow-green);
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-glow);
    }

    .btn-outline {
        background: transparent;
        color: var(--primary-green);
        border-color: var(--primary-green);
    }

    .btn-outline:hover {
        background: var(--primary-green);
        color: white;
        transform: translateY(-2px);
    }

    .btn-ghost {
        background: transparent;
        color: var(--gray-600);
    }

    .btn-ghost:hover {
        background: var(--gray-100);
        color: var(--primary-green);
        transform: translateY(-2px);
    }

    .alert {
        padding: 16px 20px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
        font-weight: 500;
    }

    .alert-warning {
        background: rgba(245, 158, 11, 0.1);
        border: 1px solid var(--warning-light);
        color: var(--warning);
    }

    .alert-danger {
        background: rgba(239, 68, 68, 0.1);
        border: 1px solid var(--danger-light);
        color: var(--danger);
    }

    .content-card {
        background: var(--white);
        border-radius: 16px;
        padding: 0;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
        overflow: hidden;
    }

    .card-header {
        padding: 24px 32px;
        border-bottom: 1px solid var(--gray-200);
        background: var(--gray-50);
    }

    .tabs-nav {
        display: flex;
        border-bottom: 2px solid var(--gray-200);
        background: var(--white);
    }

    .tab-button {
        padding: 16px 24px;
        background: none;
        border: none;
        color: var(--gray-600);
        font-weight: 600;
        cursor: pointer;
        transition: all var(--transition-normal);
        border-bottom: 3px solid transparent;
        position: relative;
    }

    .tab-button.active {
        color: var(--primary-green);
        border-bottom-color: var(--primary-green);
        background: rgba(30, 77, 59, 0.05);
    }

    .tab-button:hover:not(.active) {
        color: var(--primary-green-light);
        background: rgba(30, 77, 59, 0.03);
    }

    .tab-content {
        padding: 32px;
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 32px;
        margin-bottom: 24px;
    }

    .form-section h3 {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--primary-green);
        margin-bottom: 20px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        font-size: 0.95rem;
        transition: all var(--transition-normal);
        background: var(--white);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .summary-card {
        background: var(--success-lighter);
        border: 1px solid var(--success-green);
        border-radius: 12px;
        padding: 24px;
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid rgba(30, 77, 59, 0.1);
    }

    .summary-item:last-child {
        border-bottom: none;
    }

    .stock-entries {
        max-height: 400px;
        overflow-y: auto;
        margin-top: 24px;
    }

    .stock-entry {
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 12px;
        transition: all var(--transition-normal);
    }

    .stock-entry:hover {
        box-shadow: var(--shadow-sm);
        transform: translateY(-1px);
    }

    .entry-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 8px;
    }

    .entry-details {
        font-size: 0.85rem;
        color: var(--gray-600);
        line-height: 1.4;
    }

    .sales-history {
        max-height: 500px;
        overflow-y: auto;
    }

    .sale-item {
        background: var(--white);
        border: 1px solid var(--gray-200);
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 16px;
        transition: all var(--transition-normal);
    }

    .sale-item:hover {
        box-shadow: var(--shadow-sm);
        transform: translateY(-1px);
    }

    .sale-header {
        display: flex;
        justify-content: space-between;
        align-items: start;
        margin-bottom: 12px;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: var(--gray-500);
    }

    .empty-state-icon {
        font-size: 4rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    /* Stock Entry Action Buttons */
    .btn-icon {
        background: none;
        border: none;
        padding: 6px 8px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all var(--transition-normal);
        min-width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .btn-edit {
        background: rgba(59, 130, 246, 0.1);
        color: #3b82f6;
    }

    .btn-edit:hover {
        background: rgba(59, 130, 246, 0.2);
        transform: scale(1.05);
    }

    .btn-delete {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
    }

    .btn-delete:hover {
        background: rgba(239, 68, 68, 0.2);
        transform: scale(1.05);
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: var(--white);
        margin: 5% auto;
        padding: 32px;
        border-radius: 16px;
        width: 90%;
        max-width: 500px;
        box-shadow: var(--shadow-lg);
        animation: modalAppear 0.3s ease-out;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 2px solid var(--gray-200);
    }

    .modal-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-green);
        margin: 0;
    }

    .close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--gray-400);
        transition: color var(--transition-normal);
    }

    .close:hover {
        color: var(--danger);
    }

    @keyframes modalAppear {
        from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        .product-info-grid {
            grid-template-columns: 1fr;
            gap: 24px;
        }

        .action-buttons {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 24px;
        }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .time-filter-btn.active {
        background: var(--primary-green) !important;
        color: var(--white) !important;
        border-color: var(--primary-green) !important;
    }

    @media (max-width: 768px) {
        .product-header-card,
        .content-card .tab-content {
            padding: 20px;
        }

        .metrics-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .tabs-nav {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .tab-button {
            white-space: nowrap;
            padding: 14px 20px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Product Header -->
<div class="product-header-card fade-in">
    <div class="product-info-grid">
        <!-- Product Image -->
        <div class="product-image-container">
            📦
        </div>

        <!-- Product Details -->
        <div class="product-details">
            <h1>{{ product.name }}</h1>

            <div class="product-badges">
                {% if product.sku %}
                    <span class="badge badge-secondary">SKU: {{ product.sku }}</span>
                {% endif %}
                {% if product.category %}
                    <span class="badge badge-primary">{{ product.category.name }}</span>
                {% endif %}
            </div>

            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-label">Selling Price</div>
                    <div class="metric-value">${{ product.selling_price }}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Current Stock</div>
                    <div class="metric-value {% if product.stock_quantity == 0 %}stock-critical{% elif product.stock_quantity < 10 %}stock-low{% else %}stock-good{% endif %}">
                        {{ product.stock_quantity }}
                    </div>
                    <div class="metric-unit">{{ product.unit_label }}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Purchase Price</div>
                    <div class="metric-value">${{ product.purchase_price }}</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">Tax Rate</div>
                    <div class="metric-value">{{ product.tax_rate }}%</div>
                </div>
            </div>

            <!-- Stock Alerts -->
            {% if product.stock_quantity == 0 %}
                <div class="alert alert-danger">
                    <span>⚠️</span>
                    <span><strong>Out of Stock:</strong> This product is currently unavailable.</span>
                </div>
            {% elif product.stock_quantity < 10 %}
                <div class="alert alert-warning">
                    <span>⚠️</span>
                    <span><strong>Low Stock Alert:</strong> Only {{ product.stock_quantity }} {{ product.unit_label|lower }} remaining!</span>
                </div>
            {% endif %}
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
            <a href="{% url 'pos:product-update' product.pk %}" class="btn btn-primary">
                <span>✏️</span>
                Edit Product
            </a>
            <a href="{% url 'pos:product-list' %}" class="btn btn-outline">
                <span>←</span>
                Back to Products
            </a>
            <a href="{% url 'pos:sale-create' %}" class="btn btn-ghost">
                <span>🛒</span>
                Add to Sale
            </a>
        </div>
    </div>
</div>

<!-- Content Tabs -->
<div class="content-card fade-in">
    <div class="tabs-nav">
        <button class="tab-button active" onclick="switchTab(event, 'stock-tab')">Stock Management</button>
        <button class="tab-button" onclick="switchTab(event, 'history-tab')">Sales History</button>
    </div>

    <!-- Stock Management Tab -->
    <div id="stock-tab" class="tab-content active">
        <div class="form-grid">
            <!-- Add Stock Form -->
            <div class="form-section">
                <h3>Add New Stock</h3>
                <form method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="form-label">Quantity to Add</label>
                        <input type="number" class="form-control" name="quantity_added" step="1" min="1" required placeholder="Enter quantity">
                    </div>

                    <div class="form-group">
                        <label class="form-label">Purchase Price</label>
                        <input type="number" class="form-control" name="purchase_price" step="0.01" value="{{ product.purchase_price }}" required>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">Received Date</label>
                            <input type="date" class="form-control" name="added_on" value="{% now 'Y-m-d' %}" required>
                        </div>
                        <div class="form-group">
                            <label class="form-label">Expiry Date</label>
                            <input type="date" class="form-control" name="expiry_date">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" name="notes" rows="3" placeholder="Add any notes about this stock entry"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary" style="width: 100%;">
                        <span>➕</span>
                        Add Stock Entry
                    </button>
                </form>
            </div>

            <!-- Stock Summary -->
            <div class="form-section">
                <h3>Stock Summary</h3>
                <div class="summary-card">
                    <div class="summary-item">
                        <span style="font-weight: 600;">Total Available</span>
                        <span style="font-weight: 700; color: var(--primary-green);">{{ product.stock_quantity }} {{ product.unit_label }}</span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Unit Type</span>
                        <span>{{ product.unit_label }}</span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Last Updated</span>
                        <span>{{ product.updated_at|date:"M d, Y" }}</span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Product Value</span>
                        <span style="font-weight: 700; color: var(--primary-green);">Calculated in stock</span>
                    </div>
                </div>

                <!-- Recent Stock Entries -->
                {% if stock_entries %}
                    <h4 style="font-size: 1.1rem; font-weight: 600; margin: 24px 0 16px; color: var(--primary-green);">Recent Stock Entries</h4>
                    <div class="stock-entries">
                        {% for entry in stock_entries %}
                            <div class="stock-entry">
                                <div class="entry-header">
                                    <div>
                                        <strong style="color: var(--primary-green);">{{ entry.quantity_received }} {{ product.unit_label }}</strong>
                                        <div class="entry-details">
                                            Added on {{ entry.received_date|date:"M d, Y" }} •
                                            Remaining: {{ entry.quantity_available }} •
                                            Batch: {{ entry.batch_number }}
                                        </div>
                                    </div>
                                    <div style="text-align: right;">
                                        <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                                            <div style="font-weight: 700; color: var(--primary-green);">${{ entry.purchase_price }}</div>
                                            <div style="display: flex; gap: 4px;">
                                                <button onclick="editStockEntry({{ entry.id }})" class="btn-icon btn-edit" title="Edit Stock Entry">
                                                    ✏️
                                                </button>
                                                <button onclick="deleteStockEntry({{ entry.id }})" class="btn-icon btn-delete" title="Delete Stock Entry">
                                                    🗑️
                                                </button>
                                            </div>
                                        </div>
                                        {% if entry.expiry_date %}
                                            <div style="font-size: 0.8rem; color: var(--gray-600);">
                                                Expires: {{ entry.expiry_date|date:"M d, Y" }}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                {% if entry.notes %}
                                    <div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid var(--gray-200); font-size: 0.9rem; color: var(--gray-600);">
                                        📝 {{ entry.notes }}
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Sales History Tab -->
    <div id="history-tab" class="tab-content">
        <h3 style="margin-bottom: 24px; color: var(--primary-green);">Sales Analytics & History</h3>

        <!-- Sales Analytics Summary -->
        <div class="form-grid" style="margin-bottom: 32px;">
            <!-- Overall Analytics -->
            <div class="form-section">
                <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 16px; color: var(--primary-green);">Overall Performance</h4>
                <div class="summary-card">
                    <div class="summary-item">
                        <span style="font-weight: 600;">Total Sold</span>
                        <span style="font-weight: 700; color: var(--primary-green);">
                            {% if sales_analytics.total_sold %}{{ sales_analytics.total_sold }} {{ product.unit_label }}{% else %}0 {{ product.unit_label }}{% endif %}
                        </span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Total Revenue</span>
                        <span style="font-weight: 700; color: var(--primary-green);">
                            ${% if sales_analytics.total_revenue %}{{ sales_analytics.total_revenue|floatformat:2 }}{% else %}0.00{% endif %}
                        </span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Total Transactions</span>
                        <span style="font-weight: 700; color: var(--primary-green);">
                            {% if sales_analytics.total_transactions %}{{ sales_analytics.total_transactions }}{% else %}0{% endif %}
                        </span>
                    </div>
                    <div class="summary-item">
                        <span style="font-weight: 600;">Avg per Sale</span>
                        <span style="font-weight: 700; color: var(--primary-green);">
                            {% if sales_analytics.avg_quantity_per_sale %}{{ sales_analytics.avg_quantity_per_sale|floatformat:3 }} {{ product.unit_label }}{% else %}0 {{ product.unit_label }}{% endif %}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Time Period Analytics -->
            <div class="form-section">
                <h4 style="font-size: 1.1rem; font-weight: 600; margin-bottom: 16px; color: var(--primary-green);">Time Period Analysis</h4>
                <div style="display: grid; gap: 16px;">
                    <!-- Today -->
                    <div style="background: var(--white); border: 1px solid var(--gray-200); border-radius: 8px; padding: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; color: var(--gray-700);">📅 Today</div>
                                <div style="font-size: 0.9rem; color: var(--gray-500);">{{ today_sales.transactions }} transaction{{ today_sales.transactions|pluralize }}</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: 700; color: var(--primary-green);">{{ today_sales.quantity|floatformat:3 }} {{ product.unit_label }}</div>
                                <div style="font-size: 0.9rem; color: var(--gray-600);">${{ today_sales.revenue|floatformat:2 }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- This Week -->
                    <div style="background: var(--white); border: 1px solid var(--gray-200); border-radius: 8px; padding: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; color: var(--gray-700);">📊 This Week</div>
                                <div style="font-size: 0.9rem; color: var(--gray-500);">{{ week_sales.transactions }} transaction{{ week_sales.transactions|pluralize }}</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: 700; color: var(--primary-green);">{{ week_sales.quantity|floatformat:3 }} {{ product.unit_label }}</div>
                                <div style="font-size: 0.9rem; color: var(--gray-600);">${{ week_sales.revenue|floatformat:2 }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- This Month -->
                    <div style="background: var(--white); border: 1px solid var(--gray-200); border-radius: 8px; padding: 16px;">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div>
                                <div style="font-weight: 600; color: var(--gray-700);">📈 This Month</div>
                                <div style="font-size: 0.9rem; color: var(--gray-500);">{{ month_sales.transactions }} transaction{{ month_sales.transactions|pluralize }}</div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-weight: 700; color: var(--primary-green);">{{ month_sales.quantity|floatformat:3 }} {{ product.unit_label }}</div>
                                <div style="font-size: 0.9rem; color: var(--gray-600);">${{ month_sales.revenue|floatformat:2 }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales History Controls -->
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px; flex-wrap: wrap; gap: 16px;">
            <h4 style="font-size: 1.1rem; font-weight: 600; color: var(--primary-green); margin: 0;">Sales Transactions</h4>

            <!-- Search Input -->
            <div style="display: flex; gap: 12px; align-items: center;">
                <input type="text"
                       id="salesSearchInput"
                       placeholder="Search transactions..."
                       style="padding: 8px 12px; border: 1px solid var(--gray-300); border-radius: 6px; font-size: 14px; width: 200px;">
                <button onclick="searchSales()" class="btn btn-ghost" style="padding: 8px 12px;">🔍</button>
            </div>
        </div>

        <!-- Time Filter Buttons -->
        <div style="margin-bottom: 24px;">
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                <button onclick="filterSalesByTime('today')" class="btn btn-ghost time-filter-btn" style="padding: 6px 12px; font-size: 14px;">Today</button>
                <button onclick="filterSalesByTime('last_7_days')" class="btn btn-ghost time-filter-btn" style="padding: 6px 12px; font-size: 14px;">Last 7 Days</button>
                <button onclick="filterSalesByTime('last_30_days')" class="btn btn-primary time-filter-btn active" style="padding: 6px 12px; font-size: 14px;">Last 30 Days</button>
                <button onclick="filterSalesByTime('this_month')" class="btn btn-ghost time-filter-btn" style="padding: 6px 12px; font-size: 14px;">This Month</button>
                <button onclick="filterSalesByTime('this_year')" class="btn btn-ghost time-filter-btn" style="padding: 6px 12px; font-size: 14px;">This Year</button>
            </div>
        </div>

        <!-- Dynamic Analytics Summary -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; padding: 20px; background: var(--gray-50); border-radius: 12px;">
            <div style="text-align: center;">
                <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Period Quantity</div>
                <div id="periodQuantitySold" style="font-size: 20px; font-weight: 700; color: var(--primary-green);">-</div>
            </div>
            <div style="text-align: center;">
                <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Period Revenue</div>
                <div id="periodRevenue" style="font-size: 20px; font-weight: 700; color: var(--primary-green);">-</div>
            </div>
            <div style="text-align: center;">
                <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Transactions</div>
                <div id="periodTransactions" style="font-size: 20px; font-weight: 700; color: var(--primary-green);">-</div>
            </div>
            <div style="text-align: center;">
                <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Avg per Sale</div>
                <div id="periodAvgQuantity" style="font-size: 20px; font-weight: 700; color: var(--primary-green);">-</div>
            </div>
        </div>

        <!-- Loading Indicator -->
        <div id="salesLoadingIndicator" style="display: none; text-align: center; padding: 20px;">
            <div style="display: inline-block; animation: spin 1s linear infinite; font-size: 24px;">⏳</div>
            <div style="margin-top: 8px; color: var(--gray-600);">Loading sales data...</div>
        </div>

        <!-- Sales Transactions List -->
        <div id="salesTransactionsList">
            <!-- Dynamic content will be loaded here -->
        </div>

        <!-- Pagination -->
        <div id="salesPagination">
            <!-- Pagination controls will be added here -->
        </div>

        <!-- Lifetime Analytics Summary (Static) -->
        <div style="margin-top: 32px; padding: 20px; background: var(--white); border: 1px solid var(--gray-200); border-radius: 12px;">
            <h5 style="color: var(--primary-green); margin-bottom: 16px;">Lifetime Performance</h5>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 16px;">
                <div style="text-align: center;">
                    <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Total Sold</div>
                    <div id="lifetimeQuantitySold" style="font-size: 18px; font-weight: 600; color: var(--primary-green);">
                        {% if sales_analytics.total_sold %}{{ sales_analytics.total_sold|floatformat:3 }}{% else %}0{% endif %}
                    </div>
                </div>
                <div style="text-align: center;">
                    <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Total Revenue</div>
                    <div id="lifetimeRevenue" style="font-size: 18px; font-weight: 600; color: var(--primary-green);">
                        ${% if sales_analytics.total_revenue %}{{ sales_analytics.total_revenue|floatformat:2 }}{% else %}0.00{% endif %}
                    </div>
                </div>
                <div style="text-align: center;">
                    <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">All Transactions</div>
                    <div id="lifetimeTransactions" style="font-size: 18px; font-weight: 600; color: var(--primary-green);">
                        {% if sales_analytics.total_transactions %}{{ sales_analytics.total_transactions }}{% else %}0{% endif %}
                    </div>
                </div>
                <div style="text-align: center;">
                    <div style="color: var(--gray-600); font-size: 12px; text-transform: uppercase; margin-bottom: 4px;">Avg per Sale</div>
                    <div id="lifetimeAvgQuantity" style="font-size: 18px; font-weight: 600; color: var(--primary-green);">
                        {% if sales_analytics.avg_quantity_per_sale %}{{ sales_analytics.avg_quantity_per_sale|floatformat:2 }}{% else %}0{% endif %}
                    </div>
                </div>
            </div>
        </div>

        {% if not recent_sales %}
            <div style="text-align: center; padding: 40px; color: var(--gray-500); display: none;" id="noSalesMessage">
                <div style="font-size: 48px; margin-bottom: 16px;">📭</div>
                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">No Sales Found</div>
                <div>This product hasn't been sold yet or no sales match your current filters.</div>
            </div>
        {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">📊</div>
                <h4 style="color: var(--gray-600); margin-bottom: 8px;">No Sales History</h4>
                <p style="color: var(--gray-500);">This product hasn't been sold yet. Sales will appear here once customers start purchasing this item.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Edit Stock Entry Modal -->
<div id="editStockModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Edit Stock Entry</h3>
            <span class="close" onclick="closeModal('editStockModal')">&times;</span>
        </div>
        <form id="editStockForm" method="post">
            {% csrf_token %}
            <input type="hidden" name="action" value="edit_stock">
            <input type="hidden" name="stock_id" id="editStockId">

            <div class="form-group">
                <label class="form-label">Quantity Received</label>
                <input type="number" class="form-control" name="quantity_received" id="editQuantityReceived" step="1" min="1" required>
            </div>

            <div class="form-group">
                <label class="form-label">Purchase Price</label>
                <input type="number" class="form-control" name="purchase_price" id="editPurchasePrice" step="0.01" min="0" required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">Received Date</label>
                    <input type="date" class="form-control" name="received_date" id="editReceivedDate" required>
                </div>
                <div class="form-group">
                    <label class="form-label">Expiry Date</label>
                    <input type="date" class="form-control" name="expiry_date" id="editExpiryDate">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">Notes (Optional)</label>
                <textarea class="form-control" name="notes" id="editNotes" rows="3"></textarea>
            </div>

            <div style="display: flex; gap: 12px; justify-content: flex-end; margin-top: 24px;">
                <button type="button" class="btn btn-ghost" onclick="closeModal('editStockModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Stock Entry</button>
            </div>
        </form>
    </div>
</div>

<!-- Delete Stock Entry Modal -->
<div id="deleteStockModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Delete Stock Entry</h3>
            <span class="close" onclick="closeModal('deleteStockModal')">&times;</span>
        </div>
        <div style="text-align: center; padding: 20px 0;">
            <div style="font-size: 3rem; color: var(--danger); margin-bottom: 16px;">⚠️</div>
            <h4 style="color: var(--gray-700); margin-bottom: 12px;">Are you sure?</h4>
            <p style="color: var(--gray-600); margin-bottom: 24px;">
                This will permanently delete this stock entry. This action cannot be undone.
            </p>
            <div id="deleteStockDetails" style="background: var(--gray-50); padding: 16px; border-radius: 8px; margin-bottom: 24px; text-align: left;">
                <!-- Stock details will be populated here -->
            </div>
        </div>
        <form id="deleteStockForm" method="post">
            {% csrf_token %}
            <input type="hidden" name="action" value="delete_stock">
            <input type="hidden" name="stock_id" id="deleteStockId">

            <div style="display: flex; gap: 12px; justify-content: center;">
                <button type="button" class="btn btn-ghost" onclick="closeModal('deleteStockModal')">Cancel</button>
                <button type="submit" class="btn btn-primary" style="background: var(--danger); border-color: var(--danger);">Delete Entry</button>
            </div>
        </form>
    </div>
</div>

<script>
function switchTab(event, tabId) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(content => content.classList.remove('active'));

    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => button.classList.remove('active'));

    // Show selected tab content
    document.getElementById(tabId).classList.add('active');

    // Add active class to clicked tab button
    event.target.classList.add('active');

    // Haptic feedback
    if ('vibrate' in navigator) {
        navigator.vibrate(10);
    }
}

// Add smooth animations to elements
document.addEventListener('DOMContentLoaded', function() {
    // Animate metric cards on hover
    const metricCards = document.querySelectorAll('.metric-card');
    metricCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-4px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Add loading states to form submission
    const stockForm = document.querySelector('form[method="post"]');
    if (stockForm) {
        stockForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<span>⏳</span> Adding Stock...';
            submitBtn.disabled = true;
        });
    }
});

// Stock Entry Management Functions
let stockEntries = {};

// Populate stock entries data from template
{% for entry in stock_entries %}
stockEntries[{{ entry.id }}] = {
    id: {{ entry.id }},
    quantity_received: {{ entry.quantity_received }},
    quantity_available: {{ entry.quantity_available }},
    purchase_price: {{ entry.purchase_price }},
    received_date: '{{ entry.received_date|date:"Y-m-d" }}',
    expiry_date: {% if entry.expiry_date %}'{{ entry.expiry_date|date:"Y-m-d" }}'{% else %}null{% endif %},
    notes: '{{ entry.notes|escapejs }}',
    batch_number: '{{ entry.batch_number|escapejs }}'
};
{% endfor %}

function editStockEntry(stockId) {
    const entry = stockEntries[stockId];
    if (!entry) {
        alert('Stock entry not found');
        return;
    }

    // Populate the edit form
    document.getElementById('editStockId').value = stockId;
    document.getElementById('editQuantityReceived').value = entry.quantity_received;
    document.getElementById('editPurchasePrice').value = entry.purchase_price;
    document.getElementById('editReceivedDate').value = entry.received_date;
    document.getElementById('editExpiryDate').value = entry.expiry_date || '';
    document.getElementById('editNotes').value = entry.notes;

    // Show the modal
    openModal('editStockModal');
}

function deleteStockEntry(stockId) {
    const entry = stockEntries[stockId];
    if (!entry) {
        alert('Stock entry not found');
        return;
    }

    // Populate delete details
    const detailsDiv = document.getElementById('deleteStockDetails');
    detailsDiv.innerHTML = `
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 12px;">
            <div><strong>Batch:</strong> ${entry.batch_number}</div>
            <div><strong>Quantity:</strong> ${entry.quantity_received} {{ product.unit_label }}</div>
            <div><strong>Available:</strong> ${entry.quantity_available} {{ product.unit_label }}</div>
            <div><strong>Price:</strong> $${entry.purchase_price}</div>
            <div><strong>Received:</strong> ${new Date(entry.received_date).toLocaleDateString()}</div>
            ${entry.expiry_date ? `<div><strong>Expires:</strong> ${new Date(entry.expiry_date).toLocaleDateString()}</div>` : ''}
        </div>
        ${entry.notes ? `<div style="margin-top: 12px;"><strong>Notes:</strong> ${entry.notes}</div>` : ''}
    `;

    document.getElementById('deleteStockId').value = stockId;

    // Show the modal
    openModal('deleteStockModal');
}

function openModal(modalId) {
    document.getElementById(modalId).style.display = 'block';
    document.body.style.overflow = 'hidden'; // Prevent background scroll
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
    document.body.style.overflow = 'auto'; // Restore scroll
}

// Close modal when clicking outside of it
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            closeModal(modal.id);
        }
    });
}

// Close modal on Escape key
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        const openModals = document.querySelectorAll('.modal[style*="display: block"]');
        openModals.forEach(modal => closeModal(modal.id));
    }
});

// Sales History Dynamic Loading
let currentSalesPage = 1;
let currentSalesTimeFilter = 'last_30_days';
let currentSalesSearch = '';
let salesLoading = false;

function loadSalesHistory(timeFilter = 'last_30_days', page = 1, search = '', append = false) {
    if (salesLoading) return;

    salesLoading = true;
    currentSalesTimeFilter = timeFilter;
    currentSalesPage = page;
    currentSalesSearch = search;

    // Show loading indicator
    const loadingIndicator = document.getElementById('salesLoadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = 'block';
    }

    // Build API URL
    const params = new URLSearchParams({
        time_filter: timeFilter,
        page: page,
        page_size: 10,
        search: search
    });

    const apiUrl = `/api/v1/inventory/products/{{ product.id }}/sales_history/?${params.toString()}`;

    fetch(apiUrl, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (!append) {
                // Replace sales list
                updateSalesDisplay(data);
            } else {
                // Append to existing sales list
                appendSalesDisplay(data);
            }
            updateSalesAnalytics(data.analytics);
            updatePaginationControls(data.pagination);
        } else {
            console.error('Error loading sales history:', data.error);
            showSalesError('Failed to load sales history');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showSalesError('Network error while loading sales history');
    })
    .finally(() => {
        salesLoading = false;
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    });
}

function updateSalesDisplay(data) {
    const salesContainer = document.getElementById('salesTransactionsList');
    if (!salesContainer) return;

    if (data.sales.length === 0) {
        salesContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--gray-500);">
                <div style="font-size: 48px; margin-bottom: 16px;">📭</div>
                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">No Sales Found</div>
                <div>No sales transactions found for the selected time period.</div>
            </div>
        `;
        return;
    }

    const salesHtml = data.sales.map(sale => `
        <div class="sales-transaction-item" style="background: var(--white); border: 1px solid var(--gray-200); border-radius: 12px; padding: 20px; margin-bottom: 16px;">
            <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 12px;">
                <div>
                    <div style="font-weight: 600; color: var(--primary-green); margin-bottom: 4px;">
                        #${sale.transaction_number}
                    </div>
                    <div style="color: var(--gray-600); font-size: 14px;">
                        ${sale.date} at ${sale.time}
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="font-weight: 600; color: var(--gray-900); font-size: 18px;">
                        $${sale.line_total.toFixed(2)}
                    </div>
                    <div style="color: var(--gray-600); font-size: 14px;">
                        ${sale.quantity.toFixed(3)} units @ $${sale.unit_price.toFixed(2)}
                    </div>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 12px; padding: 12px; background: var(--gray-50); border-radius: 8px;">
                <div>
                    <div style="font-size: 12px; color: var(--gray-500); margin-bottom: 4px;">CUSTOMER</div>
                    <div style="font-weight: 500;">${sale.customer.name}</div>
                    ${sale.customer.mobile_number ? `<div style="font-size: 12px; color: var(--gray-600);">${sale.customer.mobile_number}</div>` : ''}
                </div>
                <div>
                    <div style="font-size: 12px; color: var(--gray-500); margin-bottom: 4px;">PAYMENT</div>
                    <div style="font-weight: 500; text-transform: capitalize;">${sale.payment_method}</div>
                    <div style="font-size: 12px; color: var(--gray-600);">Total: $${sale.transaction_total.toFixed(2)}</div>
                </div>
            </div>

            ${sale.discount_amount > 0 ? `
                <div style="background: rgba(245, 158, 11, 0.1); padding: 8px 12px; border-radius: 6px; margin-bottom: 8px;">
                    <span style="color: var(--warning); font-size: 12px; font-weight: 600;">DISCOUNT APPLIED: $${sale.discount_amount.toFixed(2)}</span>
                </div>
            ` : ''}

            ${sale.notes ? `
                <div style="font-size: 12px; color: var(--gray-600); font-style: italic; margin-top: 8px;">
                    Note: ${sale.notes}
                </div>
            ` : ''}
        </div>
    `).join('');

    salesContainer.innerHTML = salesHtml;
}

function appendSalesDisplay(data) {
    const salesContainer = document.getElementById('salesTransactionsList');
    if (!salesContainer || data.sales.length === 0) return;

    const salesHtml = data.sales.map(sale => `
        <div class="sales-transaction-item" style="background: var(--white); border: 1px solid var(--gray-200); border-radius: 12px; padding: 20px; margin-bottom: 16px;">
            <!-- Same HTML structure as updateSalesDisplay -->
        </div>
    `).join('');

    salesContainer.insertAdjacentHTML('beforeend', salesHtml);
}

function updateSalesAnalytics(analytics) {
    // Update period analytics
    if (analytics.period) {
        const periodStats = analytics.period;
        updateMetricElement('periodQuantitySold', periodStats.total_quantity_sold.toFixed(3));
        updateMetricElement('periodRevenue', `$${periodStats.total_revenue.toFixed(2)}`);
        updateMetricElement('periodTransactions', periodStats.total_transactions);
        updateMetricElement('periodAvgQuantity', periodStats.avg_quantity_per_sale.toFixed(2));
    }

    // Update lifetime analytics
    if (analytics.lifetime) {
        const lifetimeStats = analytics.lifetime;
        updateMetricElement('lifetimeQuantitySold', lifetimeStats.total_quantity_sold.toFixed(3));
        updateMetricElement('lifetimeRevenue', `$${lifetimeStats.total_revenue.toFixed(2)}`);
        updateMetricElement('lifetimeTransactions', lifetimeStats.total_transactions);
        updateMetricElement('lifetimeAvgQuantity', lifetimeStats.avg_quantity_per_sale.toFixed(2));
    }
}

function updateMetricElement(elementId, value) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = value;
        // Add animation
        element.style.transform = 'scale(1.1)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 200);
    }
}

function updatePaginationControls(pagination) {
    const paginationContainer = document.getElementById('salesPagination');
    if (!paginationContainer) return;

    const { current_page, total_pages, has_next, has_previous } = pagination;

    let paginationHtml = '<div style="display: flex; justify-content: center; align-items: center; gap: 12px; margin-top: 24px;">';

    // Previous button
    if (has_previous) {
        paginationHtml += `<button onclick="loadSalesHistory('${currentSalesTimeFilter}', ${current_page - 1}, '${currentSalesSearch}')" class="btn btn-ghost" style="padding: 8px 16px;">← Previous</button>`;
    }

    // Page info
    paginationHtml += `<span style="color: var(--gray-600);">Page ${current_page} of ${total_pages}</span>`;

    // Next button
    if (has_next) {
        paginationHtml += `<button onclick="loadSalesHistory('${currentSalesTimeFilter}', ${current_page + 1}, '${currentSalesSearch}')" class="btn btn-ghost" style="padding: 8px 16px;">Next →</button>`;
    }

    // Load more button (alternative to pagination)
    if (has_next) {
        paginationHtml += `<button onclick="loadMoreSales()" class="btn btn-primary" style="padding: 8px 16px; margin-left: 12px;">Load More</button>`;
    }

    paginationHtml += '</div>';
    paginationContainer.innerHTML = paginationHtml;
}

function loadMoreSales() {
    loadSalesHistory(currentSalesTimeFilter, currentSalesPage + 1, currentSalesSearch, true);
}

function filterSalesByTime(timeFilter) {
    // Update active filter button
    document.querySelectorAll('.time-filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Load new data
    loadSalesHistory(timeFilter, 1, currentSalesSearch);
}

function searchSales() {
    const searchInput = document.getElementById('salesSearchInput');
    const searchTerm = searchInput ? searchInput.value.trim() : '';
    loadSalesHistory(currentSalesTimeFilter, 1, searchTerm);
}

function showSalesError(message) {
    const salesContainer = document.getElementById('salesTransactionsList');
    if (salesContainer) {
        salesContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: var(--danger);">
                <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                <div style="font-size: 18px; font-weight: 600; margin-bottom: 8px;">Error Loading Sales</div>
                <div>${message}</div>
                <button onclick="loadSalesHistory()" class="btn btn-primary" style="margin-top: 16px;">Retry</button>
            </div>
        `;
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize sales history on page load
document.addEventListener('DOMContentLoaded', function() {
    // Load initial sales data when the sales tab is first shown
    const salesTab = document.querySelector('[onclick*="sales-tab"]');
    if (salesTab) {
        salesTab.addEventListener('click', function() {
            // Small delay to ensure tab content is visible
            setTimeout(() => {
                if (document.getElementById('salesTransactionsList').innerHTML.trim() === '') {
                    loadSalesHistory();
                }
            }, 100);
        });
    }

    // Set up search input debouncing
    const searchInput = document.getElementById('salesSearchInput');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                searchSales();
            }, 500); // 500ms delay
        });
    }
});
</script>
{% endblock %}