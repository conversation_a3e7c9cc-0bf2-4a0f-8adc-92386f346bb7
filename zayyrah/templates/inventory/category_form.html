{% extends 'base/app_base.html' %}

{% block title %}{% if is_update %}Edit {{ category.name }}{% else %}Create Category{% endif %} - Categories{% endblock %}
{% block page_title %}{% if is_update %}✏️ Edit Category{% else %}➕ Create Category{% endif %}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'inventory:category-list' %}" class="breadcrumb-item">Categories</a>
    <span class="breadcrumb-separator">›</span>
    {% if is_update %}
        <a href="{% url 'inventory:category-detail' category.pk %}" class="breadcrumb-item">{{ category.name }}</a>
        <span class="breadcrumb-separator">›</span>
        <span class="breadcrumb-item active">Edit</span>
    {% else %}
        <span class="breadcrumb-item active">Create</span>
    {% endif %}
</nav>
{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 600px;
        margin: 0 auto;
        background: var(--white);
        border-radius: 16px;
        border: 1px solid var(--gray-200);
        box-shadow: var(--shadow);
        overflow: hidden;
        animation: fadeIn 0.4s ease-out;
    }

    .form-header {
        background: var(--primary-gradient);
        color: white;
        padding: 32px;
        position: relative;
        overflow: hidden;
        text-align: center;
    }

    .form-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--glass-gradient);
        pointer-events: none;
    }

    .form-header-content {
        position: relative;
        z-index: 1;
    }

    .form-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2.5rem;
        margin: 0 auto 16px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .form-title {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 8px;
        letter-spacing: -0.025em;
    }

    .form-subtitle {
        opacity: 0.9;
        font-size: 1rem;
    }

    .form-body {
        padding: 32px;
    }

    .form-group {
        margin-bottom: 24px;
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 8px;
        font-size: 0.95rem;
    }

    .form-label.required::after {
        content: ' *';
        color: var(--danger);
    }

    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--gray-200);
        border-radius: 12px;
        background: var(--white);
        font-size: 0.95rem;
        transition: all var(--transition-normal);
        outline: none;
        box-sizing: border-box;
    }

    .form-input:focus {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        transform: scale(1.01);
    }

    .form-textarea {
        min-height: 100px;
        resize: vertical;
        font-family: inherit;
    }

    .form-select {
        appearance: none;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
        background-position: right 12px center;
        background-repeat: no-repeat;
        background-size: 16px;
        padding-right: 40px;
    }

    .form-help {
        font-size: 0.85rem;
        color: var(--gray-500);
        margin-top: 6px;
        line-height: 1.4;
    }

    .form-checkbox-group {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 16px;
        background: var(--gray-50);
        border-radius: 12px;
        border: 2px solid var(--gray-200);
        transition: all var(--transition-normal);
    }

    .form-checkbox-group:has(input:checked) {
        background: var(--success-lighter);
        border-color: var(--primary-green);
    }

    .form-checkbox {
        width: 20px;
        height: 20px;
        accent-color: var(--primary-green);
    }

    .form-checkbox-label {
        font-weight: 600;
        color: var(--gray-700);
        margin: 0;
        cursor: pointer;
    }

    .form-actions {
        padding: 24px 32px;
        background: var(--gray-50);
        border-top: 1px solid var(--gray-200);
        display: flex;
        gap: 12px;
        justify-content: space-between;
    }

    .btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        transition: all var(--transition-normal);
        border: none;
        cursor: pointer;
        font-size: 0.95rem;
        flex: 1;
    }

    .btn-primary {
        background: var(--primary-green);
        color: white;
        border: 2px solid var(--primary-green);
    }

    .btn-primary:hover {
        background: var(--primary-green-light);
        border-color: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-lg);
    }

    .btn-secondary {
        background: var(--white);
        color: var(--gray-700);
        border: 2px solid var(--gray-300);
    }

    .btn-secondary:hover {
        background: var(--gray-100);
        border-color: var(--gray-400);
        transform: translateY(-2px);
    }

    .alert {
        padding: 16px;
        border-radius: 12px;
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        gap: 12px;
        font-weight: 500;
    }

    .alert-success {
        background: var(--success-lighter);
        color: var(--primary-green);
        border: 1px solid var(--success-green);
    }

    .alert-error {
        background: #fef2f2;
        color: #dc2626;
        border: 1px solid #fecaca;
    }

    .alert-warning {
        background: #fffbeb;
        color: #d97706;
        border: 1px solid #fed7aa;
    }

    .parent-preview {
        padding: 12px;
        background: var(--success-lighter);
        border: 1px solid var(--success-green);
        border-radius: 8px;
        margin-top: 8px;
        display: none;
    }

    .parent-preview.show {
        display: block;
    }

    .parent-preview-text {
        color: var(--primary-green);
        font-size: 0.9rem;
        font-weight: 600;
    }

    /* Mobile responsiveness */
    @media (max-width: 768px) {
        .form-container {
            margin: 0 16px;
        }

        .form-header {
            padding: 24px 20px;
        }

        .form-body {
            padding: 24px 20px;
        }

        .form-actions {
            padding: 20px;
            flex-direction: column;
        }

        .btn {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="form-container">
    <!-- Form Header -->
    <div class="form-header">
        <div class="form-header-content">
            <div class="form-icon">
                {% if is_update %}✏️{% else %}📂{% endif %}
            </div>
            <h1 class="form-title">
                {% if is_update %}Edit Category{% else %}Create New Category{% endif %}
            </h1>
            <p class="form-subtitle">
                {% if is_update %}
                    Update category information and organization
                {% else %}
                    Add a new category to organize your products
                {% endif %}
            </p>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
        <div style="padding: 24px 32px 0;">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }}">
                    {% if message.tags == 'success' %}✅
                    {% elif message.tags == 'error' %}❌
                    {% elif message.tags == 'warning' %}⚠️
                    {% else %}ℹ️{% endif %}
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Form Body -->
    <form method="post" class="category-form">
        {% csrf_token %}
        <div class="form-body">
            <!-- Category Name -->
            <div class="form-group">
                <label for="name" class="form-label required">Category Name</label>
                <input type="text"
                       id="name"
                       name="name"
                       value="{% if is_update %}{{ category.name }}{% endif %}"
                       class="form-input"
                       placeholder="Enter category name..."
                       required
                       maxlength="120">
                <div class="form-help">
                    Choose a clear, descriptive name for your category
                </div>
            </div>

            <!-- Parent Category -->
            <div class="form-group">
                <label for="parent" class="form-label">Parent Category</label>
                <select id="parent" name="parent" class="form-input form-select">
                    <option value="">None (Root Category)</option>
                    {% for parent_cat in parent_categories %}
                        <option value="{{ parent_cat.id }}"
                                {% if is_update and category.parent.id == parent_cat.id %}selected{% endif %}>
                            {% if parent_cat.level == 0 %}📁 {{ parent_cat.name }}
                            {% else %}📂 &nbsp;&nbsp;&nbsp;&nbsp;{{ parent_cat.name }}{% endif %}
                        </option>
                    {% endfor %}
                </select>
                <div class="form-help">
                    Select a parent category to create a subcategory (maximum 3 levels deep)
                </div>
                <div id="parentPreview" class="parent-preview">
                    <div class="parent-preview-text" id="parentPreviewText"></div>
                </div>
            </div>

            <!-- Description -->
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <textarea id="description"
                          name="description"
                          class="form-input form-textarea"
                          placeholder="Describe what products belong in this category..."
                          rows="4">{% if is_update %}{{ category.description }}{% endif %}</textarea>
                <div class="form-help">
                    Optional description to help identify what products belong in this category
                </div>
            </div>

            <!-- Sort Order -->
            <div class="form-group">
                <label for="sort_order" class="form-label">Sort Order</label>
                <input type="number"
                       id="sort_order"
                       name="sort_order"
                       value="{% if is_update %}{{ category.sort_order }}{% else %}0{% endif %}"
                       class="form-input"
                       min="0"
                       max="9999">
                <div class="form-help">
                    Lower numbers appear first. Use 0 for default ordering.
                </div>
            </div>

            <!-- Active Status (only for edit) -->
            {% if is_update %}
                <div class="form-group">
                    <div class="form-checkbox-group">
                        <input type="checkbox"
                               id="is_active"
                               name="is_active"
                               class="form-checkbox"
                               {% if category.is_active %}checked{% endif %}>
                        <label for="is_active" class="form-checkbox-label">Category is active</label>
                    </div>
                    <div class="form-help">
                        Inactive categories are hidden from the system but not deleted
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{% if is_update %}{% url 'inventory:category-detail' category.pk %}{% else %}{% url 'inventory:category-list' %}{% endif %}"
               class="btn btn-secondary">
                ❌ Cancel
            </a>
            <button type="submit" class="btn btn-primary">
                {% if is_update %}✅ Update Category{% else %}➕ Create Category{% endif %}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const parentSelect = document.getElementById('parent');
    const parentPreview = document.getElementById('parentPreview');
    const parentPreviewText = document.getElementById('parentPreviewText');

    if (parentSelect && parentPreview && parentPreviewText) {
        function updateParentPreview() {
            const selectedOption = parentSelect.options[parentSelect.selectedIndex];

            if (selectedOption.value) {
                const parentName = selectedOption.textContent.trim();
                parentPreviewText.textContent = `This will be a subcategory of: ${parentName}`;
                parentPreview.classList.add('show');
            } else {
                parentPreview.classList.remove('show');
            }
        }

        parentSelect.addEventListener('change', updateParentPreview);

        // Initialize on page load
        updateParentPreview();
    }

    // Form validation
    const form = document.querySelector('.category-form');
    const nameInput = document.getElementById('name');

    if (form && nameInput) {
        form.addEventListener('submit', function(e) {
            const name = nameInput.value.trim();

            if (!name) {
                e.preventDefault();
                alert('Category name is required!');
                nameInput.focus();
                return false;
            }

            if (name.length < 2) {
                e.preventDefault();
                alert('Category name must be at least 2 characters long!');
                nameInput.focus();
                return false;
            }
        });

        // Auto-focus on name input
        nameInput.focus();
    }

    // Real-time character counter for name field
    if (nameInput) {
        const maxLength = 120;

        function updateCharCounter() {
            const remaining = maxLength - nameInput.value.length;
            const color = remaining < 20 ? '#dc2626' : '#6b7280';

            // Find or create counter element
            let counter = document.getElementById('nameCharCounter');
            if (!counter) {
                counter = document.createElement('div');
                counter.id = 'nameCharCounter';
                counter.style.cssText = `
                    font-size: 0.8rem;
                    margin-top: 4px;
                    text-align: right;
                    color: ${color};
                `;
                nameInput.parentNode.appendChild(counter);
            }

            counter.textContent = `${nameInput.value.length}/${maxLength}`;
            counter.style.color = color;
        }

        nameInput.addEventListener('input', updateCharCounter);
        updateCharCounter(); // Initialize
    }
});
</script>
{% endblock %}