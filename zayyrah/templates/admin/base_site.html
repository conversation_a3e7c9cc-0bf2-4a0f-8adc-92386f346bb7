{% extends "admin/base.html" %}
{% load static i18n %}

{% block title %}{% if subtitle %}{{ subtitle }} | {% endif %}{{ title }} | {{ site_title|default:_('Django site admin') }}{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}" style="color: #0da487; text-decoration: none;">
        <span style="background: linear-gradient(135deg, #0da487, #0b9372); -webkit-background-clip: text; -webkit-text-fill-color: transparent; font-weight: 700;">
            Zayyrah POS
        </span>
        - Site Administration
    </a>
</h1>
{% endblock %}

{% block nav-global %}
{% if user.is_active and user.is_staff %}
<div style="background: #0da487; padding: 10px 0; margin-bottom: 20px;">
    <div style="max-width: 1200px; margin: 0 auto; display: flex; justify-content: center; gap: 30px;">
        <a href="{% url 'admin:business_dashboard' %}"
           style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; background: rgba(255,255,255,0.1); transition: all 0.2s;">
            🏢 Business Dashboard
        </a>
        <a href="{% url 'admin:accounts_business_changelist' %}"
           style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; background: rgba(255,255,255,0.1); transition: all 0.2s;">
            📋 Manage Businesses
        </a>
        <a href="{% url 'admin:accounts_user_changelist' %}"
           style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; background: rgba(255,255,255,0.1); transition: all 0.2s;">
            👥 Manage Users
        </a>
        <a href="{% url 'admin:export_business_data' %}"
           style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; background: rgba(255,255,255,0.1); transition: all 0.2s;">
            📊 Export Data
        </a>
        {% if not user.is_superuser %}
        <a href="{% url 'accounts:dashboard' %}"
           style="color: white; text-decoration: none; padding: 8px 16px; border-radius: 5px; background: rgba(255,255,255,0.1); transition: all 0.2s;">
            🏠 Return to App
        </a>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block extrahead %}
{{ block.super }}
<style>
/* Custom admin styling to match your app theme */
:root {
    --primary-color: #0da487;
    --primary-dark: #0b9372;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

#header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-bottom: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

#branding h1 {
    color: white;
    font-weight: 700;
}

#branding h1 a:link, #branding h1 a:visited {
    color: white;
}

.module h2, .module caption, .inline-group h2 {
    background: var(--primary-color);
    color: white;
}

.button, input[type=submit], input[type=button], .submit-row input, a.button {
    background: var(--primary-color);
    border: 1px solid var(--primary-dark);
    color: white;
    border-radius: 5px;
    transition: all 0.2s ease;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(13, 164, 135, 0.3);
}

.object-tools a:link, .object-tools a:visited {
    background: var(--primary-color);
    border-radius: 5px;
    color: white;
}

.object-tools a:hover {
    background: var(--primary-dark);
}

#changelist-filter h2 {
    background: var(--primary-color);
    color: white;
}

#changelist-filter h3 {
    color: var(--primary-color);
    font-weight: 600;
}

.paginator .this-page {
    background: var(--primary-color);
    border: 1px solid var(--primary-color);
}

.calendar table {
    border: 1px solid var(--primary-color);
}

.calendar td a {
    color: var(--primary-color);
}

/* Success messages */
.messagelist .success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Warning messages */
.messagelist .warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

/* Error messages */
.messagelist .error {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* Info messages */
.messagelist .info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Tables */
#result_list th {
    background: #f8f9fa;
    color: var(--primary-color);
    font-weight: 600;
}

#result_list .action-checkbox-column {
    background: #f8f9fa;
}

/* Forms */
.form-row label {
    color: var(--primary-color);
    font-weight: 600;
}

.required label, label.required {
    color: var(--primary-color);
}

.required label:after, label.required:after {
    color: var(--danger-color);
}

/* Breadcrumbs */
.breadcrumbs {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: var(--primary-color);
}

.breadcrumbs a {
    color: var(--primary-color);
}

/* Navigation hover effects */
div[style*="background: #0da487"] a:hover {
    background: rgba(255,255,255,0.2) !important;
    transform: translateY(-1px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    div[style*="background: #0da487"] > div {
        flex-direction: column;
        gap: 10px !important;
    }

    div[style*="background: #0da487"] a {
        text-align: center;
    }
}
</style>
{% endblock %}

{% block usertools %}
{% if has_permission %}
<div id="user-tools">
    {% block welcome-msg %}
        Welcome,
        <strong>{% firstof user.get_short_name user.get_username %}</strong>
        {% if user.is_superuser %}(Site Admin){% endif %}.
    {% endblock %}
    {% block userlinks %}
        {% if site_url %}
            <a href="{{ site_url }}">{% trans 'View site' %}</a> /
        {% endif %}
        {% if user.is_active and user.is_staff %}
            {% url 'django-admindocs-docroot' as docsroot %}
            {% if docsroot %}
                <a href="{{ docsroot }}">{% trans 'Documentation' %}</a> /
            {% endif %}
        {% endif %}
        {% if user.has_usable_password %}
            <a href="{% url 'admin:password_change' %}">{% trans 'Change password' %}</a> /
        {% endif %}
        <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
    {% endblock %}
</div>
{% endif %}
{% endblock %}