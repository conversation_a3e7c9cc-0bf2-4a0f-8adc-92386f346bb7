{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style>
    .dashboard-link {
        background: linear-gradient(135deg, #0da487, #0b9372);
        color: white !important;
        padding: 20px;
        border-radius: 10px;
        text-decoration: none;
        display: block;
        margin: 20px 0;
        text-align: center;
        box-shadow: 0 4px 15px rgba(13, 164, 135, 0.3);
        transition: all 0.3s ease;
    }
    .dashboard-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(13, 164, 135, 0.4);
        color: white !important;
    }
    .dashboard-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 8px;
    }
    .dashboard-subtitle {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .admin-section {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 15px 0;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .section-title {
        color: #0da487;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 2px solid #0da487;
        padding-bottom: 5px;
    }
</style>
{% endblock %}

{% block content %}
<h1>{% trans 'Site administration' %}</h1>

{% if user.is_superuser %}
<a href="{% url 'admin:business_dashboard' %}" class="dashboard-link">
    <div class="dashboard-title">🏢 Business Management Dashboard</div>
    <div class="dashboard-subtitle">
        View analytics, manage businesses, approve registrations, and monitor platform performance
    </div>
</a>
{% endif %}

{% if app_list %}
    {% for app in app_list %}
        <div class="admin-section">
            <h2 class="section-title">
                <a href="{{ app.app_url }}" class="section-title">{{ app.name }}</a>
            </h2>

            {% if app.name == "Accounts" and user.is_superuser %}
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                    <strong>🎯 Business Administration Quick Actions:</strong>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><a href="{% url 'admin:accounts_business_changelist' %}">Manage All Businesses</a></li>
                        <li><a href="{% url 'admin:accounts_business_changelist' %}?status__exact=pending">Review Pending Approvals</a></li>
                        <li><a href="{% url 'admin:accounts_businessadmin_changelist' %}">Manage Business Administrators</a></li>
                        <li><a href="{% url 'admin:accounts_businesssubscription_changelist' %}">View Subscriptions</a></li>
                    </ul>
                </div>
            {% endif %}

            {% if app.models %}
                <dl>
                {% for model in app.models %}
                    <dt>
                        <a href="{{ model.admin_url }}">{{ model.name }}</a>
                    </dt>
                    {% if model.perms.change or model.perms.view %}
                        <dd>
                            {% if model.perms.view %}
                                <a href="{{ model.admin_url }}">
                                    {% if model.perms.change %}{% trans 'Change' %}{% else %}{% trans 'View' %}{% endif %}
                                </a>
                            {% endif %}
                            {% if model.perms.add %}
                                {% if model.perms.change or model.perms.view %} / {% endif %}
                                <a href="{{ model.add_url }}">{% trans 'Add' %}</a>
                            {% endif %}
                        </dd>
                    {% endif %}
                {% endfor %}
                </dl>
            {% endif %}
        </div>
    {% endfor %}
{% else %}
    <p>{% trans "You don't have permission to view or edit anything." %}</p>
{% endif %}

{% if user.is_superuser %}
<div class="admin-section">
    <h2 class="section-title">🔧 System Management</h2>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <strong>📊 Platform Analytics</strong>
            <div style="margin-top: 10px;">
                <a href="{% url 'admin:business_analytics_api' %}?timeframe=30" target="_blank">View API Data</a>
            </div>
        </div>
        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <strong>📥 Export Data</strong>
            <div style="margin-top: 10px;">
                <a href="{% url 'admin:export_business_data' %}">Download CSV</a>
            </div>
        </div>
        <div style="text-align: center; padding: 15px; background: #f8f9fa; border-radius: 5px;">
            <strong>👥 User Management</strong>
            <div style="margin-top: 10px;">
                <a href="{% url 'admin:accounts_user_changelist' %}">Manage Users</a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}