{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Site Admin Dashboard{% endblock %}

{% block extrahead %}
<style>
    .dashboard-container {
        padding: 20px;
        background-color: #f8f9fa;
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }
    .stat-card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        text-align: center;
        border-left: 4px solid #0da487;
    }
    .stat-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #0da487;
        display: block;
    }
    .stat-label {
        color: #666;
        font-size: 0.9rem;
        margin-top: 5px;
    }
    .content-section {
        background: white;
        margin-bottom: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    .section-header {
        background: #0da487;
        color: white;
        padding: 15px 20px;
        border-radius: 10px 10px 0 0;
        font-weight: bold;
        font-size: 1.1rem;
    }
    .section-content {
        padding: 20px;
    }
    .business-table {
        width: 100%;
        border-collapse: collapse;
    }
    .business-table th,
    .business-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
    }
    .business-table th {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    .status-pending { background: #fff3cd; color: #856404; }
    .status-approved { background: #d4edda; color: #155724; }
    .status-suspended { background: #f8d7da; color: #721c24; }
    .status-rejected { background: #e2e3e5; color: #383d41; }
    .action-buttons {
        display: flex;
        gap: 5px;
    }
    .btn {
        padding: 5px 10px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        text-decoration: none;
        font-size: 0.8rem;
    }
    .btn-approve { background: #28a745; color: white; }
    .btn-reject { background: #dc3545; color: white; }
    .btn-suspend { background: #ffc107; color: black; }
    .charts-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-bottom: 30px;
    }
    .chart-container {
        height: 300px;
        padding: 20px;
    }
    .export-buttons {
        margin: 20px 0;
        display: flex;
        gap: 10px;
    }
    .btn-export {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        text-decoration: none;
        border-radius: 5px;
    }
</style>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <h1 style="color: #0da487; margin-bottom: 30px;">
        <strong>Site Admin Dashboard</strong>
        <span style="font-size: 0.6em; color: #666; font-weight: normal;">
            - Business Management Center
        </span>
    </h1>

    <!-- Summary Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">{{ business_stats.total }}</span>
            <div class="stat-label">Total Businesses</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ business_stats.pending }}</span>
            <div class="stat-label">Pending Approval</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ business_stats.approved }}</span>
            <div class="stat-label">Active Businesses</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">${{ revenue_stats.monthly_revenue_sum|floatformat:0|default:"0" }}</span>
            <div class="stat-label">Monthly Revenue</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ recent_businesses }}</span>
            <div class="stat-label">New This Week</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ user_stats.total_users }}</span>
            <div class="stat-label">Total Users</div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="charts-grid">
        <div class="content-section">
            <div class="section-header">Business Status Distribution</div>
            <div class="chart-container">
                <canvas id="statusChart"></canvas>
            </div>
        </div>
        <div class="content-section">
            <div class="section-header">Business Type Distribution</div>
            <div class="chart-container">
                <canvas id="typeChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Export Buttons -->
    <div class="export-buttons">
        <a href="{% url 'admin:export_business_data' %}" class="btn-export">
            📊 Export Business Data (CSV)
        </a>
        <button onclick="refreshDashboard()" class="btn-export">
            🔄 Refresh Dashboard
        </button>
    </div>

    <!-- Pending Businesses -->
    {% if pending_businesses %}
    <div class="content-section">
        <div class="section-header">
            Pending Business Approvals ({{ business_stats.pending }})
        </div>
        <div class="section-content">
            <table class="business-table">
                <thead>
                    <tr>
                        <th>Business Name</th>
                        <th>Owner</th>
                        <th>Type</th>
                        <th>Location</th>
                        <th>Registered</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for business in pending_businesses %}
                    <tr>
                        <td><strong>{{ business.business_name }}</strong></td>
                        <td>{{ business.owner_name }}</td>
                        <td>{{ business.get_business_type_display }}</td>
                        <td>{{ business.city }}, {{ business.state }}</td>
                        <td>{{ business.created_at|date:"M d, Y" }}</td>
                        <td>
                            <div class="action-buttons">
                                <a href="{% url 'admin:approve_business' business.id %}"
                                   class="btn btn-approve"
                                   onclick="return confirm('Approve {{ business.business_name }}?')">
                                    ✓ Approve
                                </a>
                                <a href="{% url 'admin:reject_business' business.id %}"
                                   class="btn btn-reject"
                                   onclick="return confirm('Reject {{ business.business_name }}?')">
                                    ✗ Reject
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Top Performing Businesses -->
    <div class="content-section">
        <div class="section-header">Top Performing Businesses</div>
        <div class="section-content">
            <table class="business-table">
                <thead>
                    <tr>
                        <th>Business Name</th>
                        <th>Owner</th>
                        <th>Type</th>
                        <th>Monthly Revenue</th>
                        <th>Total Transactions</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for business in top_businesses %}
                    <tr>
                        <td>
                            <a href="{% url 'admin:accounts_business_change' business.id %}"
                               style="text-decoration: none; color: #0da487;">
                                <strong>{{ business.business_name }}</strong>
                            </a>
                        </td>
                        <td>{{ business.owner_name }}</td>
                        <td>{{ business.get_business_type_display }}</td>
                        <td>${{ business.monthly_revenue|floatformat:2 }}</td>
                        <td>{{ business.total_transactions }}</td>
                        <td>
                            <span class="status-badge status-{{ business.status }}">
                                {{ business.get_status_display }}
                            </span>
                        </td>
                        <td>
                            {% if business.status == 'approved' %}
                            <a href="{% url 'admin:suspend_business' business.id %}"
                               class="btn btn-suspend"
                               onclick="return confirm('Suspend {{ business.business_name }}?')">
                                ⏸ Suspend
                            </a>
                            {% endif %}
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="7" style="text-align: center; color: #666; padding: 30px;">
                            No active businesses found
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Additional Statistics -->
    <div class="content-section">
        <div class="section-header">Platform Statistics</div>
        <div class="section-content">
            <div class="stats-grid">
                <div style="text-align: center;">
                    <strong style="font-size: 1.2rem; color: #0da487;">{{ active_subscriptions }}</strong>
                    <div>Active Subscriptions</div>
                </div>
                <div style="text-align: center;">
                    <strong style="font-size: 1.2rem; color: #0da487;">{{ this_month_registrations }}</strong>
                    <div>This Month Registrations</div>
                </div>
                <div style="text-align: center;">
                    <strong style="font-size: 1.2rem; color: #0da487;">{{ last_month_registrations }}</strong>
                    <div>Last Month Registrations</div>
                </div>
                <div style="text-align: center;">
                    <strong style="font-size: 1.2rem; color: #0da487;">${{ revenue_stats.avg_monthly_revenue|floatformat:0|default:"0" }}</strong>
                    <div>Average Monthly Revenue</div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Status Distribution Chart
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Pending', 'Approved', 'Suspended', 'Rejected'],
        datasets: [{
            data: [
                {{ business_stats.pending }},
                {{ business_stats.approved }},
                {{ business_stats.suspended }},
                {{ business_stats.rejected }}
            ],
            backgroundColor: ['#ffc107', '#28a745', '#dc3545', '#6c757d']
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Business Type Distribution Chart
const typeCtx = document.getElementById('typeChart').getContext('2d');
new Chart(typeCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for stat in business_type_stats %}
            '{{ stat.business_type|title }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Number of Businesses',
            data: [
                {% for stat in business_type_stats %}
                {{ stat.count }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: '#0da487'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

function refreshDashboard() {
    location.reload();
}
</script>
{% endblock %}