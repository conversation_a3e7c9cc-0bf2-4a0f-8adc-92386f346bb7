<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHANNAB - Employee Management</title>
    <style>
        /* ==== CHANNAB PROFESSIONAL DARK GREEN DESIGN SYSTEM ==== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Professional Dark Green Color System */
            --primary-green: #1e4d3b;
            --primary-green-light: #2d6a4f;
            --primary-green-lighter: #40916c;
            --primary-green-lightest: #52b788;
            --accent-green: #74c69d;
            --success-green: #95d5b2;
            --success-light: #b7e4c7;
            --success-lighter: #d8f3dc;

            /* Neutral Grays */
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Functional Colors */
            --warning: #f59e0b;
            --warning-light: #fbbf24;
            --danger: #ef4444;
            --danger-light: #f87171;
            --info: #3b82f6;
            --info-light: #60a5fa;

            /* Professional Gradients */
            --primary-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 50%, var(--primary-green-lighter) 100%);
            --success-gradient: linear-gradient(135deg, var(--primary-green-lighter) 0%, var(--accent-green) 50%, var(--success-green) 100%);
            --accent-gradient: linear-gradient(135deg, var(--accent-green) 0%, var(--success-green) 50%, var(--success-light) 100%);

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(30, 77, 59, 0.05);
            --shadow: 0 1px 3px 0 rgba(30, 77, 59, 0.1), 0 1px 2px 0 rgba(30, 77, 59, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(30, 77, 59, 0.1), 0 4px 6px -2px rgba(30, 77, 59, 0.05);
            --shadow-green: 0 4px 14px 0 rgba(30, 77, 59, 0.2);

            /* Layout */
            --sidebar-width: 280px;
            --header-height: 70px;
            --bottom-nav-height: 80px;
            --vh: 1vh;
        }

        html {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
        }

        body {
            background: var(--gray-50);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ==== LAYOUT CONTAINER ==== */
        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* ==== PROFESSIONAL SIDEBAR ==== */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--white);
            border-right: 1px solid var(--gray-200);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 200;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: var(--shadow);
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--primary-gradient);
            color: white;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            backdrop-filter: blur(10px);
        }

        .sidebar-logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .sidebar-nav {
            padding: 24px 0;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            padding: 0 24px 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--gray-500);
            letter-spacing: 0.05em;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-700);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-item:hover {
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .nav-item.active {
            background: var(--success-lighter);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
            font-weight: 600;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            right: 24px;
            width: 6px;
            height: 6px;
            background: var(--primary-green);
            border-radius: 50%;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-item-text {
            flex: 1;
            font-size: 0.95rem;
        }

        .nav-item-badge {
            background: var(--primary-green);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            font-weight: 600;
        }

        /* ==== MAIN CONTENT ==== */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* ==== PROFESSIONAL HEADER ==== */
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 16px 24px;
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            display: none;
            width: 44px;
            height: 44px;
            border: 1px solid var(--gray-300);
            border-radius: 12px;
            background: var(--white);
            cursor: pointer;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
            color: var(--primary-green);
        }

        .menu-toggle:hover {
            background: var(--success-lighter);
            border-color: var(--primary-green);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 12px 16px 12px 40px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            background: var(--white);
            font-size: 0.95rem;
            transition: all 0.2s ease;
            outline: none;
            color: var(--gray-700);
        }

        .search-input:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        }

        .search-input::placeholder {
            color: var(--gray-400);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.1rem;
        }

        .header-btn {
            width: 44px;
            height: 44px;
            border: 2px solid var(--gray-200);
            border-radius: 50%;
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
            position: relative;
            color: var(--gray-600);
        }

        .header-btn:hover {
            border-color: var(--primary-green);
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--primary-green);
            color: white;
            font-size: 0.7rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            font-weight: 600;
        }

        /* ==== CONTENT AREA ==== */
        .content-wrapper {
            flex: 1;
            padding: 0 24px 24px 24px;
            margin-bottom: var(--bottom-nav-height);
        }

        /* ==== PROFESSIONAL STATS CARDS ==== */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--white);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-green);
        }

        .stat-card.employees-stat {
            background: var(--primary-gradient);
            color: white;
            border: none;
        }

        .stat-card.attendance-stat {
            background: var(--success-gradient);
            color: white;
            border: none;
        }

        .stat-card.leaves-stat {
            background: var(--accent-gradient);
            color: var(--primary-green);
            border: none;
        }

        .stat-card.payroll-stat {
            background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
            color: white;
            border: none;
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .stat-card.leaves-stat .stat-icon,
        .stat-card.payroll-stat .stat-icon {
            background: rgba(30, 77, 59, 0.15);
        }

        .stat-content {
            flex: 1;
        }

        .stat-title {
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 8px;
        }

        .stat-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .stat-details span {
            display: block;
            margin-bottom: 2px;
        }

        /* ==== QUICK ACTIONS GRID ==== */
        .quick-actions-section {
            margin-bottom: 32px;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
        }

        .quick-action-card {
            background: var(--white);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            display: block;
        }

        .quick-action-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-green);
            text-decoration: none;
            color: inherit;
        }

        .action-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .action-icon {
            width: 56px;
            height: 56px;
            background: var(--success-gradient);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }

        .action-content h3 {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 4px;
        }

        .action-content p {
            color: var(--gray-600);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .action-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-100);
        }

        .action-stats {
            display: flex;
            gap: 16px;
        }

        .action-stat {
            text-align: center;
        }

        .action-stat-number {
            font-size: 1.1rem;
            font-weight: 700;
            color: var(--primary-green);
            display: block;
        }

        .action-stat-label {
            font-size: 0.7rem;
            color: var(--gray-500);
            text-transform: uppercase;
            font-weight: 600;
        }

        .action-arrow {
            width: 32px;
            height: 32px;
            background: var(--success-lighter);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-green);
            font-size: 1rem;
        }

        /* ==== EMPLOYEE FILTER BAR ==== */
        .employee-filter-bar {
            background: var(--white);
            border-radius: 12px;
            padding: 20px 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin: 0 24px 24px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 24px;
            position: sticky;
            top: var(--header-height);
            z-index: 90;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .filter-dropdown {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 8px;
            background: var(--white);
            color: var(--gray-700);
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            width: 180px;
        }

        .filter-dropdown:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        }

        .filter-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-green);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-green-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow-green);
        }

        .btn-secondary {
            background: var(--white);
            border: 1px solid var(--gray-300);
            color: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--success-lighter);
            border-color: var(--primary-green);
            color: var(--primary-green);
        }

        /* ==== RECENT ACTIVITIES ==== */
        .activities-section {
            background: var(--white);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }

        .activities-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 16px;
            border-radius: 12px;
            background: var(--gray-50);
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            background: var(--success-lighter);
        }

        .activity-icon {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            background: var(--success-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .activity-content {
            flex: 1;
        }

        .activity-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 4px;
        }

        .activity-description {
            color: var(--gray-600);
            font-size: 0.9rem;
            margin-bottom: 4px;
        }

        .activity-time {
            color: var(--gray-500);
            font-size: 0.8rem;
        }

        /* ==== EMPTY STATE ==== */
        .empty-state {
            text-align: center;
            padding: 60px 24px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            color: var(--primary-green);
            margin-bottom: 8px;
        }

        /* ==== RESPONSIVE DESIGN ==== */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .main-header {
                padding: 12px 16px;
            }

            .page-title {
                font-size: 1.25rem;
            }

            .search-input {
                width: 100%;
                max-width: 200px;
            }

            .content-wrapper {
                padding: 0 16px 16px 16px;
            }

            .stats-section {
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                margin-bottom: 24px;
            }

            .stat-card {
                padding: 16px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .employee-filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
                padding: 16px;
                margin: 0 16px 16px 16px;
                position: static;
            }

            .filter-controls {
                width: 100%;
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .filter-dropdown {
                width: 100%;
            }

            .filter-actions {
                flex-direction: column;
                gap: 12px;
                width: 100%;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .quick-actions-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .quick-action-card {
                padding: 20px;
            }
        }

        @media (max-width: 480px) {
            .stats-section {
                grid-template-columns: 1fr;
            }
        }

        /* ==== SIDEBAR OVERLAY ==== */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(30, 77, 59, 0.5);
            z-index: 150;
        }

        @media (max-width: 768px) {
            .sidebar-overlay.active {
                display: block;
            }
        }

        /* ==== ANIMATIONS ==== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- App Container -->
    <div class="app-container">
        <!-- Professional Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="sidebar-logo-icon">🌿</div>
                    <h1 class="sidebar-logo-text">CHANNAB</h1>
                </div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="{% url 'accounts:dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">🏠</div>
                        <div class="nav-item-text">Home</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">🏢</div>
                        <div class="nav-item-text">Animals</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">💰</div>
                        <div class="nav-item-text">Expense</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Income</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">🥛</div>
                        <div class="nav-item-text">Milk Rec</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Customer Management</div>
                    <a href="{% url 'customers:list' %}" class="nav-item">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Customers</div>
                    </a>
                    <a href="{% url 'customers:create' %}" class="nav-item">
                        <div class="nav-item-icon">➕</div>
                        <div class="nav-item-text">Add Customer</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Employee Management</div>
                    <a href="{% url 'employees:dashboard' %}" class="nav-item active">
                        <div class="nav-item-icon">👷</div>
                        <div class="nav-item-text">Employee Dashboard</div>
                    </a>
                    <a href="{% url 'employees:employee-list' %}" class="nav-item">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Employee List</div>
                    </a>
                    <a href="{% url 'employees:attendance-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">📅</div>
                        <div class="nav-item-text">Attendance</div>
                        {% if pending_leaves > 0 %}
                        <div class="nav-item-badge">{{ pending_leaves }}</div>
                        {% endif %}
                    </a>
                    <a href="{% url 'employees:leave-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">🏖️</div>
                        <div class="nav-item-text">Leave Management</div>
                    </a>
                    <a href="{% url 'employees:payroll-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">💳</div>
                        <div class="nav-item-text">Payroll</div>
                    </a>
                    <a href="{% url 'employees:reports-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Reports</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Inventory</div>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">📦</div>
                        <div class="nav-item-text">Stock Management</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">⚠️</div>
                        <div class="nav-item-text">Low Stock</div>
                        <div class="nav-item-badge">3</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">⚙️</div>
                        <div class="nav-item-text">General Settings</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">👤</div>
                        <div class="nav-item-text">User Profile</div>
                    </a>
                    <a href="{% url 'accounts:logout' %}" class="nav-item">
                        <div class="nav-item-icon">🚪</div>
                        <div class="nav-item-text">Logout</div>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Professional Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle">☰</button>
                    <h1 class="page-title">Employee Management</h1>
                </div>
                <div class="header-actions">
                    <div class="search-container">
                        <span class="search-icon">🔍</span>
                        <input type="search" class="search-input" placeholder="Search employees..." id="searchInput">
                    </div>
                    <button class="header-btn" id="notificationBtn">
                        🔔
                        {% if pending_leaves > 0 %}
                        <span class="notification-badge">{{ pending_leaves }}</span>
                        {% endif %}
                    </button>
                    <button class="header-btn" id="themeToggle">🌙</button>
                </div>
            </header>

            <!-- Employee Filter Bar -->
            <div class="employee-filter-bar fade-in">
                <div class="filter-controls">
                    <select class="filter-dropdown" id="departmentFilter">
                        <option value="">All Departments</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}">{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                    <select class="filter-dropdown" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="on_leave">On Leave</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button class="btn btn-secondary" id="clearFilters">Clear Filters</button>
                    <a href="{% url 'employees:employee-add' %}" class="btn btn-primary">➕ Add Employee</a>
                </div>
            </div>

            <!-- Content Wrapper -->
            <div class="content-wrapper">
                <!-- Professional Stats Section -->
                <div class="stats-section">
                    <div class="stat-card employees-stat fade-in">
                        <div class="stat-header">
                            <div class="stat-icon">👷</div>
                            <div class="stat-content">
                                <div class="stat-title">TOTAL EMPLOYEES</div>
                                <div class="stat-number">{{ total_employees|default:"0" }}</div>
                                <div class="stat-details">
                                    <span>Active: {{ total_employees|default:"0" }}</span>
                                    <span>Departments: {{ departments.count|default:"0" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card attendance-stat fade-in">
                        <div class="stat-header">
                            <div class="stat-icon">📅</div>
                            <div class="stat-content">
                                <div class="stat-title">TODAY'S ATTENDANCE</div>
                                <div class="stat-number">{{ present_today|default:"0" }}/{{ total_employees|default:"0" }}</div>
                                <div class="stat-details">
                                    <span>Present: {{ present_today|default:"0" }}</span>
                                    <span>Absent: {{ absent_today|default:"0" }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card leaves-stat fade-in">
                        <div class="stat-header">
                            <div class="stat-icon">🏖️</div>
                            <div class="stat-content">
                                <div class="stat-title">PENDING LEAVES</div>
                                <div class="stat-number">{{ pending_leaves|default:"0" }}</div>
                                <div class="stat-details">
                                    <span>Awaiting Approval</span>
                                    <span>This Month</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card payroll-stat fade-in">
                        <div class="stat-header">
                            <div class="stat-icon">💳</div>
                            <div class="stat-content">
                                <div class="stat-title">MONTHLY PAYROLL</div>
                                <div class="stat-number">Rs. 0</div>
                                <div class="stat-details">
                                    <span>Current Month</span>
                                    <span>Not Processed</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Section -->
                <div class="quick-actions-section">
                    <h2 class="section-title">
                        <span>⚡</span>
                        Quick Actions
                    </h2>
                    <div class="quick-actions-grid">
                        <a href="{% url 'employees:employee-list' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">👥</div>
                                <div class="action-content">
                                    <h3>Manage Employees</h3>
                                    <p>View, add, edit employee profiles and information</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">{{ total_employees|default:"0" }}</span>
                                        <span class="action-stat-label">Total</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">{{ departments.count|default:"0" }}</span>
                                        <span class="action-stat-label">Departments</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>

                        <a href="{% url 'employees:attendance-dashboard' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">📅</div>
                                <div class="action-content">
                                    <h3>Attendance Tracking</h3>
                                    <p>Mark attendance, view reports and manage schedules</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">{{ present_percentage|default:"0" }}%</span>
                                        <span class="action-stat-label">Present</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">{{ late_today|default:"0" }}</span>
                                        <span class="action-stat-label">Late</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>

                        <a href="{% url 'employees:leave-dashboard' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">🏖️</div>
                                <div class="action-content">
                                    <h3>Leave Management</h3>
                                    <p>Process leave requests and manage leave policies</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">{{ pending_leaves|default:"0" }}</span>
                                        <span class="action-stat-label">Pending</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">0</span>
                                        <span class="action-stat-label">Approved</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>

                        <a href="{% url 'employees:payroll-dashboard' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">💳</div>
                                <div class="action-content">
                                    <h3>Payroll Management</h3>
                                    <p>Process salaries, manage salary structures and bonuses</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">Rs. 0</span>
                                        <span class="action-stat-label">This Month</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">0</span>
                                        <span class="action-stat-label">Processed</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>

                        <a href="{% url 'employees:reports-dashboard' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">📊</div>
                                <div class="action-content">
                                    <h3>Reports & Analytics</h3>
                                    <p>Generate detailed reports and analyze employee data</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">5</span>
                                        <span class="action-stat-label">Reports</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">Live</span>
                                        <span class="action-stat-label">Data</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>

                        <a href="{% url 'employees:performance-dashboard' %}" class="quick-action-card">
                            <div class="action-header">
                                <div class="action-icon">⭐</div>
                                <div class="action-content">
                                    <h3>Performance Reviews</h3>
                                    <p>Conduct reviews, set goals and track performance</p>
                                </div>
                            </div>
                            <div class="action-footer">
                                <div class="action-stats">
                                    <div class="action-stat">
                                        <span class="action-stat-number">0</span>
                                        <span class="action-stat-label">Due</span>
                                    </div>
                                    <div class="action-stat">
                                        <span class="action-stat-number">0</span>
                                        <span class="action-stat-label">Completed</span>
                                    </div>
                                </div>
                                <div class="action-arrow">→</div>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Recent Activities Section -->
                <div class="activities-section">
                    <div class="activities-header">
                        <h2 class="section-title">
                            <span>🕒</span>
                            Recent Activities
                        </h2>
                        <a href="#" class="btn btn-secondary">View All</a>
                    </div>
                    <div class="activity-list">
                        {% if recent_activities %}
                            {% for activity in recent_activities %}
                            <div class="activity-item">
                                <div class="activity-icon">{{ activity.icon|default:"📝" }}</div>
                                <div class="activity-content">
                                    <div class="activity-title">{{ activity.title }}</div>
                                    <div class="activity-description">{{ activity.description }}</div>
                                    <div class="activity-time">{{ activity.timestamp|timesince }} ago</div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                        <div class="empty-state">
                            <i class="fas fa-history">🕒</i>
                            <h3>No Recent Activities</h3>
                            <p>Employee activities will appear here as they happen.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Professional Employee Management System
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            setupEventListeners();
            setupMobileOptimizations();
            setupNavigationSystem();
        }

        function setupNavigationSystem() {
            // Mobile menu toggle
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    sidebarOverlay.classList.toggle('active');
                    hapticFeedback();
                });
            }

            // Close sidebar when clicking overlay
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                });
            }

            // Close sidebar when clicking nav item on mobile
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('active');
                        sidebarOverlay.classList.remove('active');
                    }
                });
            });
        }

        function setupEventListeners() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', handleSearch);
            }

            // Theme toggle
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }

            // Clear filters
            const clearFilters = document.getElementById('clearFilters');
            if (clearFilters) {
                clearFilters.addEventListener('click', clearAllFilters);
            }

            // Department filter
            const departmentFilter = document.getElementById('departmentFilter');
            if (departmentFilter) {
                departmentFilter.addEventListener('change', function() {
                    applyFilters();
                    hapticFeedback();
                });
            }

            // Status filter
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    applyFilters();
                    hapticFeedback();
                });
            }

            // Notification button
            const notificationBtn = document.getElementById('notificationBtn');
            if (notificationBtn) {
                notificationBtn.addEventListener('click', function() {
                    alert('Notifications: {{ pending_leaves|default:"0" }} pending leave requests');
                    hapticFeedback();
                });
            }

            // Add haptic feedback to interactive elements
            document.querySelectorAll('.stat-card, .quick-action-card, .btn, .nav-item').forEach(element => {
                element.addEventListener('click', hapticFeedback);
            });
        }

        function setupMobileOptimizations() {
            // Dynamic viewport height
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }
            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Touch feedback
            document.querySelectorAll('.stat-card, .quick-action-card, .btn, .nav-item').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                }, { passive: true });
            });

            // Header scroll effect
            window.addEventListener('scroll', () => {
                const header = document.querySelector('.main-header');
                if (window.scrollY > 50) {
                    header.style.boxShadow = '0 2px 10px rgba(30, 77, 59, 0.1)';
                } else {
                    header.style.boxShadow = 'none';
                }
            }, { passive: true });

            // Close sidebar on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                }
            });
        }

        function handleSearch(event) {
            const query = event.target.value.toLowerCase().trim();
            // In a real app, this would filter the employee data
            console.log('Searching for:', query);
            if (query) {
                showNotification(`Searching for: ${query}`);
            }
        }

        function applyFilters() {
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let message = 'Filters applied';
            if (departmentFilter) {
                message += ` - Department: ${departmentFilter}`;
            }
            if (statusFilter) {
                message += ` - Status: ${statusFilter}`;
            }

            showNotification(message);
        }

        function clearAllFilters() {
            const searchInput = document.getElementById('searchInput');
            const departmentFilter = document.getElementById('departmentFilter');
            const statusFilter = document.getElementById('statusFilter');

            if (searchInput) searchInput.value = '';
            if (departmentFilter) departmentFilter.value = '';
            if (statusFilter) statusFilter.value = '';

            showNotification('All filters cleared');
            hapticFeedback();
        }

        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.getElementById('themeToggle');

            body.classList.toggle('dark-theme');
            const isDark = body.classList.contains('dark-theme');

            themeToggle.textContent = isDark ? '☀️' : '🌙';
            localStorage.setItem('channab-theme', isDark ? 'dark' : 'light');

            hapticFeedback();
        }

        function showNotification(message) {
            // Create temporary notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: var(--primary-green);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                font-size: 0.9rem;
                z-index: 1000;
                box-shadow: var(--shadow-green);
                animation: fadeIn 0.3s ease;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function hapticFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate(10);
            }
        }

        // Initialize theme from localStorage
        const savedTheme = localStorage.getItem('channab-theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            document.getElementById('themeToggle').textContent = '☀️';
        }
    </script>
</body>
</html>