{% extends 'base/app_base.html' %}
{% load static %}

{% block title %}
    {% if employee %}Edit Employee{% else %}Add New Employee{% endif %} - CHANNAB POS
{% endblock %}

{% block extra_css %}
<style>
    :root {
        --primary-color: #0da487;
        --primary-dark: #0b8a73;
        --primary-light: #e8f5f3;
        --text-primary: #2d3748;
        --text-secondary: #718096;
        --border-color: #e2e8f0;
        --bg-light: #f7fafc;
        --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .form-container {
        background: white;
        border-radius: 12px;
        box-shadow: var(--shadow-md);
        overflow: hidden;
    }

    .form-header {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        padding: 2rem;
        text-align: center;
    }

    .form-header h1 {
        margin: 0;
        font-size: 2rem;
        font-weight: 600;
    }

    .form-header p {
        margin: 0.5rem 0 0 0;
        opacity: 0.9;
    }

    .form-content {
        padding: 2rem;
    }

    .form-section {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: var(--bg-light);
        border-radius: 8px;
        border-left: 4px solid var(--primary-color);
    }

    .form-section h3 {
        margin: 0 0 1rem 0;
        color: var(--text-primary);
        font-size: 1.2rem;
        font-weight: 600;
    }

    .form-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-bottom: 1rem;
    }

    .form-group {
        display: flex;
        flex-direction: column;
    }

    .form-group label {
        font-weight: 500;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 0.75rem;
        border: 2px solid var(--border-color);
        border-radius: 6px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 100px;
    }

    .required {
        color: #e53e3e;
    }

    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 1rem;
        padding-top: 2rem;
        border-top: 1px solid var(--border-color);
        margin-top: 2rem;
    }

    .btn {
        padding: 0.75rem 2rem;
        border-radius: 6px;
        border: none;
        font-size: 1rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
    }

    .btn-primary {
        background: var(--primary-color);
        color: white;
    }

    .btn-primary:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
    }

    .btn-secondary {
        background: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-1px);
    }

    .alert {
        padding: 1rem;
        border-radius: 6px;
        margin-bottom: 1rem;
    }

    .alert-error {
        background: #fed7d7;
        border: 1px solid #fc8181;
        color: #c53030;
    }

    .errorlist {
        list-style: none;
        padding: 0;
        margin: 0.5rem 0 0 0;
    }

    .errorlist li {
        color: #e53e3e;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .help-text {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    @media (max-width: 768px) {
        .form-content {
            padding: 1rem;
        }

        .form-section {
            padding: 1rem;
        }

        .form-row {
            grid-template-columns: 1fr;
        }

        .form-actions {
            flex-direction: column;
        }

        .btn {
            width: 100%;
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="form-container">
        <div class="form-header">
            <h1>
                <i class="fas fa-user-plus me-2"></i>
                {% if employee %}Edit Employee{% else %}Add New Employee{% endif %}
            </h1>
            <p>{% if employee %}Update employee information{% else %}Enter employee details to add to the system{% endif %}</p>
        </div>

        <div class="form-content">
            {% if form.non_field_errors %}
                <div class="alert alert-error">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}

                <!-- Personal Information Section -->
                <div class="form-section">
                    <h3><i class="fas fa-user me-2"></i>Personal Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.first_name.id_for_label }}">
                                First Name <span class="required">*</span>
                            </label>
                            {{ form.first_name }}
                            {% if form.first_name.errors %}
                                <ul class="errorlist">
                                    {% for error in form.first_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.last_name.id_for_label }}">
                                Last Name <span class="required">*</span>
                            </label>
                            {{ form.last_name }}
                            {% if form.last_name.errors %}
                                <ul class="errorlist">
                                    {% for error in form.last_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.email.id_for_label }}">
                                Email Address
                            </label>
                            {{ form.email }}
                            {% if form.email.errors %}
                                <ul class="errorlist">
                                    {% for error in form.email.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.phone.id_for_label }}">
                                Phone Number <span class="required">*</span>
                            </label>
                            {{ form.phone }}
                            {% if form.phone.errors %}
                                <ul class="errorlist">
                                    {% for error in form.phone.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.date_of_birth.id_for_label }}">
                                Date of Birth
                            </label>
                            {{ form.date_of_birth }}
                            {% if form.date_of_birth.errors %}
                                <ul class="errorlist">
                                    {% for error in form.date_of_birth.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.gender.id_for_label }}">
                                Gender
                            </label>
                            {{ form.gender }}
                            {% if form.gender.errors %}
                                <ul class="errorlist">
                                    {% for error in form.gender.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.marital_status.id_for_label }}">
                                Marital Status
                            </label>
                            {{ form.marital_status }}
                            {% if form.marital_status.errors %}
                                <ul class="errorlist">
                                    {% for error in form.marital_status.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.address.id_for_label }}">
                                Address
                            </label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <ul class="errorlist">
                                    {% for error in form.address.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                </div>

                <!-- Employment Information Section -->
                <div class="form-section">
                    <h3><i class="fas fa-briefcase me-2"></i>Employment Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.employee_id.id_for_label }}">
                                Employee ID <span class="required">*</span>
                            </label>
                            {{ form.employee_id }}
                            {% if form.employee_id.errors %}
                                <ul class="errorlist">
                                    {% for error in form.employee_id.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.department.id_for_label }}">
                                Department <span class="required">*</span>
                            </label>
                            {{ form.department }}
                            {% if form.department.errors %}
                                <ul class="errorlist">
                                    {% for error in form.department.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.position.id_for_label }}">
                                Position <span class="required">*</span>
                            </label>
                            {{ form.position }}
                            {% if form.position.errors %}
                                <ul class="errorlist">
                                    {% for error in form.position.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.employment_status.id_for_label }}">
                                Employment Status <span class="required">*</span>
                            </label>
                            {{ form.employment_status }}
                            {% if form.employment_status.errors %}
                                <ul class="errorlist">
                                    {% for error in form.employment_status.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.hire_date.id_for_label }}">
                                Date of Joining <span class="required">*</span>
                            </label>
                            {{ form.hire_date }}
                            {% if form.hire_date.errors %}
                                <ul class="errorlist">
                                    {% for error in form.hire_date.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                </div>

                <!-- Emergency Contact Section -->
                <div class="form-section">
                    <h3><i class="fas fa-phone me-2"></i>Emergency Contact</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.emergency_contact_name.id_for_label }}">
                                Emergency Contact Name
                            </label>
                            {{ form.emergency_contact_name }}
                            {% if form.emergency_contact_name.errors %}
                                <ul class="errorlist">
                                    {% for error in form.emergency_contact_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.emergency_contact_phone.id_for_label }}">
                                Emergency Contact Phone
                            </label>
                            {{ form.emergency_contact_phone }}
                            {% if form.emergency_contact_phone.errors %}
                                <ul class="errorlist">
                                    {% for error in form.emergency_contact_phone.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Banking Information Section -->
                <div class="form-section">
                    <h3><i class="fas fa-university me-2"></i>Banking Information</h3>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.bank_name.id_for_label }}">
                                Bank Name
                            </label>
                            {{ form.bank_name }}
                            {% if form.bank_name.errors %}
                                <ul class="errorlist">
                                    {% for error in form.bank_name.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.account_number.id_for_label }}">
                                Account Number
                            </label>
                            {{ form.account_number }}
                            {% if form.account_number.errors %}
                                <ul class="errorlist">
                                    {% for error in form.account_number.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="{{ form.routing_number.id_for_label }}">
                                Routing Number
                            </label>
                            {{ form.routing_number }}
                            {% if form.routing_number.errors %}
                                <ul class="errorlist">
                                    {% for error in form.routing_number.errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <a href="{% url 'employees:employee-list' %}" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>
                        {% if employee %}Update Employee{% else %}Add Employee{% endif %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-generate employee ID if empty
    document.addEventListener('DOMContentLoaded', function() {
        const employeeIdField = document.getElementById('{{ form.employee_id.id_for_label }}');
        if (employeeIdField && !employeeIdField.value) {
            // Generate a simple employee ID based on current timestamp
            const now = new Date();
            const employeeId = 'EMP' + now.getFullYear().toString().substr(-2) +
                             (now.getMonth() + 1).toString().padStart(2, '0') +
                             now.getDate().toString().padStart(2, '0') +
                             Math.floor(Math.random() * 100).toString().padStart(2, '0');
            employeeIdField.value = employeeId;
        }

        // Date field formatting
        const dateFields = document.querySelectorAll('input[type="date"]');
        dateFields.forEach(field => {
            if (!field.value) {
                field.style.color = '#999';
            }
            field.addEventListener('change', function() {
                this.style.color = this.value ? '#333' : '#999';
            });
        });
    });
</script>
{% endblock %}