{% extends 'base/app_base.html' %}
{% load static %}

{% block nav_attendance %}active{% endblock %}

{% block title %}Attendance Dashboard - Zayyrah{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{% static 'employees/css/employees.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
{% endblock %}

{% block content %}
<div class="attendance-dashboard">
    <div class="dashboard-header">
        <div class="dashboard-title-group">
            <h1><i class="fas fa-calendar-check"></i> Attendance Dashboard</h1>
            <p class="dashboard-subtitle">
                Managing {{ total_employees }} active employee{{ total_employees|pluralize }}
                <span class="dot-separator">•</span>
                {{ today|date:"F j, Y" }}
            </p>
        </div>
        <div class="dashboard-actions">
            <div class="date-selector">
                <input
                    type="text"
                    id="dateRange"
                    placeholder="Select date range"
                    class="date-picker"
                    data-default-start="{{ today|date:'Y-m-d' }}"
                    data-default-end="{{ today|date:'Y-m-d' }}"
                >
            </div>
            <button type="button" class="btn btn-primary" onclick="openModal('bulkAttendanceModal')">
                <i class="fas fa-users"></i> Bulk Entry
            </button>
            <button type="button" class="btn btn-secondary" onclick="exportAttendance()">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>

    <!-- Attendance Summary Cards -->
    <div class="attendance-summary">
        <div class="summary-card present">
            <div class="summary-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="summary-content">
                <h3>{{ present_count }}</h3>
                <p>Present Today</p>
                <span class="summary-percentage">{{ present_percentage|floatformat:1 }}%</span>
            </div>
        </div>
        <div class="summary-card absent">
            <div class="summary-icon">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="summary-content">
                <h3>{{ absent_count }}</h3>
                <p>Absent Today</p>
                <span class="summary-percentage">{{ absent_percentage|floatformat:1 }}%</span>
            </div>
        </div>
        <div class="summary-card late">
            <div class="summary-icon">
                <i class="fas fa-user-clock"></i>
            </div>
            <div class="summary-content">
                <h3>{{ late_count }}</h3>
                <p>Late Today</p>
                <span class="summary-percentage">{{ late_percentage|floatformat:1 }}%</span>
            </div>
        </div>
        <div class="summary-card leave">
            <div class="summary-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="summary-content">
                <h3>{{ on_leave_count }}</h3>
                <p>On Leave</p>
                <span class="summary-percentage">{{ leave_percentage|floatformat:1 }}%</span>
            </div>
        </div>
    </div>

    <!-- Attendance Chart -->
    <div class="chart-section">
        <div class="chart-header">
            <h2>Attendance Trend</h2>
            <div class="chart-controls">
                <select id="chartPeriod" onchange="updateChart()">
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                </select>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="attendanceChart"></canvas>
        </div>
    </div>

    <!-- Today's Attendance Table -->
    <div class="attendance-table-section">
        <div class="table-header">
            <h2>Today's Attendance</h2>
            <div class="table-actions">
                <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input type="text" id="attendanceSearch" placeholder="Search employees..." onkeyup="filterAttendanceTable()">
                </div>
                <div class="filter-buttons">
                    <button class="filter-btn active" data-status="all" onclick="filterByStatus('all')">All</button>
                    <button class="filter-btn" data-status="present" onclick="filterByStatus('present')">Present</button>
                    <button class="filter-btn" data-status="absent" onclick="filterByStatus('absent')">Absent</button>
                    <button class="filter-btn" data-status="late" onclick="filterByStatus('late')">Late</button>
                    <button class="filter-btn" data-status="on_leave" onclick="filterByStatus('on_leave')">On Leave</button>
                </div>
            </div>
        </div>

        <div class="attendance-table-container">
            <table class="attendance-table" id="attendanceTable">
                <thead>
                    <tr>
                        <th>Employee</th>
                        <th>Department</th>
                        <th>Status</th>
                        <th>Check In</th>
                        <th>Check Out</th>
                        <th>Hours Worked</th>
                        <th>Overtime</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attendance in todays_attendance %}
                    <tr
                        data-attendance-id="{{ attendance.id }}"
                        data-employee-id="{{ attendance.employee.id }}"
                        data-status="{{ attendance.status }}"
                        data-department="{{ attendance.employee.department_id }}"
                        data-department-name="{{ attendance.employee.department.name|default_if_none:''|escape }}"
                        data-search-index="{{ attendance.employee.full_name|default_if_none:''|lower }} {{ attendance.employee.employee_id|default_if_none:''|lower }} {{ attendance.employee.department.name|default_if_none:''|lower }}"
                        data-employee-name="{{ attendance.employee.full_name|escape }}"
                        data-employee-code="{{ attendance.employee.employee_id }}"
                        data-check-in="{{ attendance.check_in|time:'H:i' }}"
                        data-check-out="{{ attendance.check_out|time:'H:i' }}"
                        data-hours-worked="{{ attendance.hours_worked|default_if_none:'' }}"
                        data-overtime-hours="{{ attendance.overtime_hours|default_if_none:'' }}"
                        data-remarks="{{ attendance.remarks|default_if_none:''|escape }}"
                        data-attendance-date="{{ attendance.date|date:'F j, Y' }}"
                    >
                        <td data-field="employee">
                            <div class="employee-cell">
                                <img src="{% static 'images/default-avatar.png' %}" alt="{{ attendance.employee.full_name }}" class="employee-avatar-small">
                                <div>
                                    <strong>{{ attendance.employee.full_name }}</strong>
                                    <small>{{ attendance.employee.employee_id }}</small>
                                </div>
                            </div>
                        </td>
                        <td data-field="department">{{ attendance.employee.department.name|default:'-' }}</td>
                        <td data-field="status">
                            <span class="status-badge status-{{ attendance.status }}" data-status-badge>
                                {{ attendance.get_status_display }}
                            </span>
                        </td>
                        <td data-field="check-in">
                            {% if attendance.check_in %}
                                <span class="time">{{ attendance.check_in|time:"H:i" }}</span>
                            {% else %}
                                <span class="no-data">-</span>
                            {% endif %}
                        </td>
                        <td data-field="check-out">
                            {% if attendance.check_out %}
                                <span class="time">{{ attendance.check_out|time:"H:i" }}</span>
                            {% else %}
                                <span class="no-data">-</span>
                            {% endif %}
                        </td>
                        <td data-field="hours-worked">
                            {% if attendance.hours_worked %}
                                <span class="hours">{{ attendance.hours_worked|floatformat:2 }}h</span>
                            {% else %}
                                <span class="no-data">-</span>
                            {% endif %}
                        </td>
                        <td data-field="overtime">
                            {% if attendance.overtime_hours %}
                                <span class="overtime">{{ attendance.overtime_hours|floatformat:2 }}h</span>
                            {% else %}
                                <span class="no-data">-</span>
                            {% endif %}
                        </td>
                        <td data-field="actions">
                            <div class="action-buttons">
                                <button type="button" class="action-btn" onclick="editAttendance({{ attendance.id }})" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="action-btn" onclick="viewAttendanceHistory({{ attendance.employee.id }})" title="History">
                                    <i class="fas fa-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="8" class="no-data-row">
                            <div class="empty-state">
                                <i class="fas fa-calendar-times"></i>
                                <p>No attendance records for today</p>
                                <button type="button" class="btn btn-primary" onclick="openModal('bulkAttendanceModal')">
                                    <i class="fas fa-plus"></i> Add Attendance
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                    {% if todays_attendance %}
                    <tr id="attendanceNoResults" class="no-results-row" style="display: none;">
                        <td colspan="8" class="no-data-row">
                            <div class="empty-state compact">
                                <i class="fas fa-search"></i>
                                <p>No matching records found</p>
                                <span>Try adjusting the status filter or search term.</span>
                            </div>
                        </td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Department-wise Attendance -->
    <div class="department-attendance">
        <div class="section-heading">
            <div>
                <h2>Department-wise Attendance</h2>
                <p>Snapshot of today's presence by team</p>
            </div>
            <button type="button" class="btn btn-link" onclick="window.location.href='{% url 'employees:attendance-list' %}'">
                View full attendance list
            </button>
        </div>

        {% if department_attendance %}
        <div class="department-grid">
            {% for dept_data in department_attendance %}
            <div class="department-card">
                <div class="department-header">
                    <h3>{{ dept_data.department.name }}</h3>
                    <span class="employee-count">{{ dept_data.total_employees }} employee{{ dept_data.total_employees|pluralize }}</span>
                </div>
                <div class="department-stats">
                    <div class="stat-item">
                        <span class="stat-value present">{{ dept_data.present }}</span>
                        <span class="stat-label">Present</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value absent">{{ dept_data.absent }}</span>
                        <span class="stat-label">Absent</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value late">{{ dept_data.late }}</span>
                        <span class="stat-label">Late</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-value leave">{{ dept_data.on_leave }}</span>
                        <span class="stat-label">On Leave</span>
                    </div>
                </div>
                <div class="attendance-bar">
                    <div class="bar-segment present" style="width: {{ dept_data.present_percentage }}%"></div>
                    <div class="bar-segment absent" style="width: {{ dept_data.absent_percentage }}%"></div>
                    <div class="bar-segment late" style="width: {{ dept_data.late_percentage }}%"></div>
                    <div class="bar-segment leave" style="width: {{ dept_data.leave_percentage }}%"></div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-state compact">
            <i class="fas fa-sitemap"></i>
            <p>No departments with attendance data yet</p>
            <span>Attendance records will appear here once marked for your teams.</span>
        </div>
        {% endif %}
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="modal large-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-calendar-plus"></i> Bulk Attendance Entry</h2>
            <span class="close" onclick="closeModal('bulkAttendanceModal')">&times;</span>
        </div>
        <form class="bulk-attendance-form">
            {% csrf_token %}
            <div class="modal-body">
                <div class="bulk-attendance-toolbar">
                    <div class="form-group">
                        <label for="bulk_attendance_date">Date</label>
                        <input type="date" id="bulk_attendance_date" name="attendance_date" value="{{ today }}" required>
                    </div>
                    <div class="form-group">
                        <label for="bulkStatusFilter">Quick Filter</label>
                        <input type="text" id="bulkStatusFilter" placeholder="Search employees..." class="bulk-search">
                    </div>
                </div>
                {% if has_more_employees %}
                <div class="modal-note">
                    Showing the first {{ modal_employee_limit }} employees. Use the attendance list to manage the full roster.
                </div>
                {% else %}
                <div class="modal-note">
                    Quickly mark attendance for your active team members.
                </div>
                {% endif %}

                {% if employees %}
                <div class="attendance-list" id="bulkAttendanceList">
                    <div class="attendance-header">
                        <span>Employee</span>
                        <span>Status</span>
                        <span>Check In</span>
                        <span>Check Out</span>
                        <span>Notes</span>
                    </div>
                    {% for employee in employees %}
                    <div class="attendance-row" data-employee-id="{{ employee.id }}" data-search-index="{{ employee.full_name|default_if_none:''|lower }} {{ employee.employee_id|default_if_none:''|lower }} {{ employee.department.name|default_if_none:''|lower }}">
                        <div class="employee-info">
                            <img src="{% static 'images/default-avatar.png' %}" alt="{{ employee.full_name }}" class="employee-avatar">
                            <div>
                                <strong>{{ employee.full_name }}</strong>
                                <small>{{ employee.employee_id }}</small>
                                <small class="muted">{{ employee.department.name|default:'No Department' }}</small>
                            </div>
                        </div>
                        <select name="status_{{ employee.id }}" class="status-select">
                            <option value="present" selected>Present</option>
                            <option value="absent">Absent</option>
                            <option value="late">Late</option>
                            <option value="half_day">Half Day</option>
                            <option value="on_leave">On Leave</option>
                        </select>
                        <input type="time" name="check_in_{{ employee.id }}" class="time-input">
                        <input type="time" name="check_out_{{ employee.id }}" class="time-input">
                        <input type="text" name="notes_{{ employee.id }}" placeholder="Notes" class="notes-input">
                    </div>
                    {% endfor %}
                    <div class="bulk-empty-placeholder" id="bulkAttendanceEmpty" style="display: none;">
                        <i class="fas fa-search"></i>
                        <p>No employees match this filter</p>
                        <span>Try a different name, ID or department.</span>
                    </div>
                </div>
                {% else %}
                <div class="empty-state compact">
                    <i class="fas fa-user-slash"></i>
                    <p>No active employees found</p>
                    <span>Add employees to start tracking attendance.</span>
                </div>
                {% endif %}
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('bulkAttendanceModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Attendance</button>
            </div>
        </form>
    </div>
</div>

<!-- Edit Attendance Modal -->
<div id="editAttendanceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-edit"></i> Edit Attendance</h2>
            <span class="close" onclick="closeModal('editAttendanceModal')">&times;</span>
        </div>
        <form class="edit-attendance-form">
            <div class="employee-info-header">
                <img src="{% static 'images/default-avatar.png' %}" alt="Employee" class="employee-avatar">
                <div>
                    <h3 id="editEmployeeName"></h3>
                    <p id="editEmployeeId"></p>
                    <p id="editAttendanceDate"></p>
                </div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="edit_status">Status</label>
                    <select id="edit_status" name="status" required>
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="half_day">Half Day</option>
                        <option value="on_leave">On Leave</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="edit_check_in">Check In</label>
                    <input type="time" id="edit_check_in" name="check_in">
                </div>
                <div class="form-group">
                    <label for="edit_check_out">Check Out</label>
                    <input type="time" id="edit_check_out" name="check_out">
                </div>
                <div class="form-group">
                    <label for="edit_hours_worked">Hours Worked</label>
                    <input type="number" id="edit_hours_worked" name="hours_worked" step="0.1" min="0" max="24" readonly>
                </div>
                <div class="form-group full-width">
                    <label for="edit_remarks">Remarks</label>
                    <textarea id="edit_remarks" name="remarks" rows="3" placeholder="Any additional notes..."></textarea>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('editAttendanceModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Update Attendance</button>
            </div>
        </form>
    </div>
</div>

{{ attendance_trend|json_script:"attendance-trend-data" }}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'employees/js/attendance-dashboard.js' %}"></script>
{% endblock %}
