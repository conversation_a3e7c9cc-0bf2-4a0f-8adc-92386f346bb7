<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHANNAB - Employee List</title>
    <style>
        /* ==== CHANNAB PROFESSIONAL DARK GREEN DESIGN SYSTEM ==== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Professional Dark Green Color System */
            --primary-green: #1e4d3b;
            --primary-green-light: #2d6a4f;
            --primary-green-lighter: #40916c;
            --primary-green-lightest: #52b788;
            --accent-green: #74c69d;
            --success-green: #95d5b2;
            --success-light: #b7e4c7;
            --success-lighter: #d8f3dc;

            /* Neutral Grays */
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Functional Colors */
            --warning: #f59e0b;
            --warning-light: #fbbf24;
            --danger: #ef4444;
            --danger-light: #f87171;
            --info: #3b82f6;
            --info-light: #60a5fa;

            /* Professional Gradients */
            --primary-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 50%, var(--primary-green-lighter) 100%);
            --success-gradient: linear-gradient(135deg, var(--primary-green-lighter) 0%, var(--accent-green) 50%, var(--success-green) 100%);
            --accent-gradient: linear-gradient(135deg, var(--accent-green) 0%, var(--success-green) 50%, var(--success-light) 100%);

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(30, 77, 59, 0.05);
            --shadow: 0 1px 3px 0 rgba(30, 77, 59, 0.1), 0 1px 2px 0 rgba(30, 77, 59, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(30, 77, 59, 0.1), 0 4px 6px -2px rgba(30, 77, 59, 0.05);
            --shadow-green: 0 4px 14px 0 rgba(30, 77, 59, 0.2);

            /* Layout */
            --sidebar-width: 280px;
            --header-height: 70px;
            --bottom-nav-height: 80px;
            --vh: 1vh;
        }

        html {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
        }

        body {
            background: var(--gray-50);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ==== LAYOUT CONTAINER ==== */
        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* ==== PROFESSIONAL SIDEBAR ==== */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--white);
            border-right: 1px solid var(--gray-200);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 200;
            transition: transform 0.3s ease;
            overflow-y: auto;
            box-shadow: var(--shadow);
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--primary-gradient);
            color: white;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-logo-icon {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            backdrop-filter: blur(10px);
        }

        .sidebar-logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            letter-spacing: -0.025em;
        }

        .sidebar-nav {
            padding: 24px 0;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            padding: 0 24px 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--gray-500);
            letter-spacing: 0.05em;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            color: var(--gray-700);
            text-decoration: none;
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-item:hover {
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .nav-item.active {
            background: var(--success-lighter);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
            font-weight: 600;
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            right: 24px;
            width: 6px;
            height: 6px;
            background: var(--primary-green);
            border-radius: 50%;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .nav-item-text {
            flex: 1;
            font-size: 0.95rem;
        }

        .nav-item-badge {
            background: var(--primary-green);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            font-weight: 600;
        }

        /* ==== MAIN CONTENT ==== */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        /* ==== PROFESSIONAL HEADER ==== */
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 16px 24px;
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .menu-toggle {
            display: none;
            width: 44px;
            height: 44px;
            border: 1px solid var(--gray-300);
            border-radius: 12px;
            background: var(--white);
            cursor: pointer;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
            color: var(--primary-green);
        }

        .menu-toggle:hover {
            background: var(--success-lighter);
            border-color: var(--primary-green);
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 12px 16px 12px 40px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            background: var(--white);
            font-size: 0.95rem;
            transition: all 0.2s ease;
            outline: none;
            color: var(--gray-700);
        }

        .search-input:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        }

        .search-input::placeholder {
            color: var(--gray-400);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.1rem;
        }

        .header-btn {
            width: 44px;
            height: 44px;
            border: 2px solid var(--gray-200);
            border-radius: 50%;
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
            position: relative;
            color: var(--gray-600);
        }

        .header-btn:hover {
            border-color: var(--primary-green);
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        /* ==== EMPLOYEE FILTER BAR ==== */
        .employee-filter-bar {
            background: var(--white);
            border-radius: 12px;
            padding: 20px 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin: 24px 24px 0 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 24px;
            position: sticky;
            top: var(--header-height);
            z-index: 90;
        }

        .filter-controls {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .filter-dropdown {
            padding: 12px 16px;
            border: 2px solid var(--gray-200);
            border-radius: 8px;
            background: var(--white);
            color: var(--gray-700);
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            outline: none;
            width: 180px;
        }

        .filter-dropdown:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        }

        .filter-actions {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .btn-primary {
            background: var(--primary-green);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-green-light);
            transform: translateY(-1px);
            box-shadow: var(--shadow-green);
        }

        .btn-secondary {
            background: var(--white);
            border: 1px solid var(--gray-300);
            color: var(--gray-600);
        }

        .btn-secondary:hover {
            background: var(--success-lighter);
            border-color: var(--primary-green);
            color: var(--primary-green);
        }

        /* ==== CONTENT AREA ==== */
        .content-wrapper {
            flex: 1;
            padding: 24px;
            margin-bottom: var(--bottom-nav-height);
        }

        /* ==== EMPLOYEE GRID ==== */
        .employees-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 24px;
        }

        .employee-card {
            background: var(--white);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .employee-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-green);
        }

        .employee-header {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
        }

        .employee-avatar {
            width: 60px;
            height: 60px;
            background: var(--success-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 700;
            text-transform: uppercase;
        }

        .employee-info {
            flex: 1;
        }

        .employee-name {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-green);
            margin-bottom: 4px;
        }

        .employee-id {
            font-size: 0.9rem;
            color: var(--gray-500);
            font-weight: 500;
        }

        .employee-department {
            font-size: 0.85rem;
            color: var(--primary-green-light);
            font-weight: 500;
            margin-top: 2px;
        }

        .employee-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }

        .detail-item {
            display: flex;
            flex-direction: column;
        }

        .detail-label {
            font-size: 0.75rem;
            color: var(--gray-500);
            text-transform: uppercase;
            font-weight: 600;
            letter-spacing: 0.05em;
            margin-bottom: 4px;
        }

        .detail-value {
            font-size: 0.9rem;
            color: var(--gray-700);
            font-weight: 500;
        }

        .employee-status {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-active {
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .status-inactive {
            background: var(--gray-100);
            color: var(--gray-600);
        }

        .status-on-leave {
            background: #fef3c7;
            color: #92400e;
        }

        .employee-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid var(--gray-100);
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            transition: all 0.2s ease;
            color: var(--gray-600);
        }

        .action-btn:hover {
            border-color: var(--primary-green);
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .view-profile-btn {
            padding: 8px 16px;
            background: var(--success-gradient);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .view-profile-btn:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-green);
        }

        /* ==== PAGINATION ==== */
        .pagination-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 40px;
            gap: 16px;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .page-link {
            padding: 8px 12px;
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            background: var(--white);
            color: var(--gray-700);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .page-link:hover {
            border-color: var(--primary-green);
            background: var(--success-lighter);
            color: var(--primary-green);
        }

        .page-link.current {
            background: var(--primary-green);
            color: white;
            border-color: var(--primary-green);
        }

        /* ==== EMPTY STATE ==== */
        .empty-state {
            text-align: center;
            padding: 60px 24px;
            color: var(--gray-500);
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-state h3 {
            color: var(--primary-green);
            margin-bottom: 8px;
        }

        /* ==== RESPONSIVE DESIGN ==== */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .main-header {
                padding: 12px 16px;
            }

            .page-title {
                font-size: 1.25rem;
            }

            .search-input {
                width: 100%;
                max-width: 200px;
            }

            .content-wrapper {
                padding: 16px;
            }

            .employee-filter-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 16px;
                padding: 16px;
                margin: 16px 16px 0 16px;
                position: static;
            }

            .filter-controls {
                width: 100%;
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .filter-dropdown {
                width: 100%;
            }

            .filter-actions {
                flex-direction: column;
                gap: 12px;
                width: 100%;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .employees-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .employee-card {
                padding: 20px;
            }
        }

        /* ==== SIDEBAR OVERLAY ==== */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(30, 77, 59, 0.5);
            z-index: 150;
        }

        @media (max-width: 768px) {
            .sidebar-overlay.active {
                display: block;
            }
        }

        /* ==== ANIMATIONS ==== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
    </style>
</head>
<body>
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- App Container -->
    <div class="app-container">
        <!-- Professional Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="sidebar-logo-icon">🌿</div>
                    <h1 class="sidebar-logo-text">CHANNAB</h1>
                </div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="{% url 'accounts:dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">🏠</div>
                        <div class="nav-item-text">Home</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">🏢</div>
                        <div class="nav-item-text">Animals</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">💰</div>
                        <div class="nav-item-text">Expense</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Income</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">🥛</div>
                        <div class="nav-item-text">Milk Rec</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Customer Management</div>
                    <a href="{% url 'customers:list' %}" class="nav-item">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Customers</div>
                    </a>
                    <a href="{% url 'customers:create' %}" class="nav-item">
                        <div class="nav-item-icon">➕</div>
                        <div class="nav-item-text">Add Customer</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Employee Management</div>
                    <a href="{% url 'employees:dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">👷</div>
                        <div class="nav-item-text">Employee Dashboard</div>
                    </a>
                    <a href="{% url 'employees:employee-list' %}" class="nav-item active">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Employee List</div>
                    </a>
                    <a href="{% url 'employees:attendance-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">📅</div>
                        <div class="nav-item-text">Attendance</div>
                    </a>
                    <a href="{% url 'employees:leave-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">🏖️</div>
                        <div class="nav-item-text">Leave Management</div>
                    </a>
                    <a href="{% url 'employees:payroll-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">💳</div>
                        <div class="nav-item-text">Payroll</div>
                    </a>
                    <a href="{% url 'employees:reports-dashboard' %}" class="nav-item">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Reports</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Inventory</div>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">📦</div>
                        <div class="nav-item-text">Stock Management</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">⚠️</div>
                        <div class="nav-item-text">Low Stock</div>
                        <div class="nav-item-badge">3</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">⚙️</div>
                        <div class="nav-item-text">General Settings</div>
                    </a>
                    <a href="#" class="nav-item">
                        <div class="nav-item-icon">👤</div>
                        <div class="nav-item-text">User Profile</div>
                    </a>
                    <a href="{% url 'accounts:logout' %}" class="nav-item">
                        <div class="nav-item-icon">🚪</div>
                        <div class="nav-item-text">Logout</div>
                    </a>
                </div>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Professional Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle">☰</button>
                    <h1 class="page-title">Employee List</h1>
                </div>
                <div class="header-actions">
                    <div class="search-container">
                        <span class="search-icon">🔍</span>
                        <input type="search" class="search-input" placeholder="Search employees..." id="searchInput" value="{{ request.GET.search }}">
                    </div>
                    <button class="header-btn" id="notificationBtn">
                        🔔
                    </button>
                    <button class="header-btn" id="themeToggle">🌙</button>
                </div>
            </header>

            <!-- Employee Filter Bar -->
            <div class="employee-filter-bar fade-in">
                <div class="filter-controls">
                    <select class="filter-dropdown" id="departmentFilter">
                        <option value="">All Departments</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                    <select class="filter-dropdown" id="statusFilter">
                        <option value="">All Status</option>
                        <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                        <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                        <option value="on_leave" {% if request.GET.status == 'on_leave' %}selected{% endif %}>On Leave</option>
                    </select>
                </div>
                <div class="filter-actions">
                    <button class="btn btn-secondary" id="clearFilters">Clear Filters</button>
                    <a href="{% url 'employees:employee-add' %}" class="btn btn-primary">➕ Add Employee</a>
                </div>
            </div>

            <!-- Content Wrapper -->
            <div class="content-wrapper">
                {% if employees %}
                    <!-- Employee Grid -->
                    <div class="employees-grid">
                        {% for employee in employees %}
                        <div class="employee-card fade-in" onclick="window.location.href='{% url 'employees:employee-detail' employee.pk %}'">
                            <div class="employee-header">
                                <div class="employee-avatar">
                                    {{ employee.first_name.0 }}{{ employee.last_name.0 }}
                                </div>
                                <div class="employee-info">
                                    <div class="employee-name">{{ employee.full_name }}</div>
                                    <div class="employee-id">{{ employee.employee_id }}</div>
                                    <div class="employee-department">{{ employee.department.name }}</div>
                                </div>
                            </div>

                            <div class="employee-details">
                                <div class="detail-item">
                                    <div class="detail-label">Position</div>
                                    <div class="detail-value">{{ employee.position.title }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Hire Date</div>
                                    <div class="detail-value">{{ employee.hire_date|date:"M Y" }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Email</div>
                                    <div class="detail-value" style="font-size: 0.8rem;">{{ employee.email|truncatechars:20 }}</div>
                                </div>
                                <div class="detail-item">
                                    <div class="detail-label">Status</div>
                                    <div class="detail-value">
                                        <span class="employee-status status-{{ employee.employment_status }}">
                                            {% if employee.employment_status == 'active' %}
                                                🟢 Active
                                            {% elif employee.employment_status == 'on_leave' %}
                                                🟡 On Leave
                                            {% else %}
                                                🔴 {{ employee.get_employment_status_display }}
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="employee-actions">
                                <div class="action-buttons">
                                    <button class="action-btn" title="Edit Employee" onclick="event.stopPropagation(); window.location.href='{% url 'employees:employee-edit' employee.pk %}'">✏️</button>
                                    <button class="action-btn" title="Mark Attendance" onclick="event.stopPropagation(); window.location.href='{% url 'employees:mark-attendance' employee.pk %}'">📅</button>
                                    <button class="action-btn" title="Salary Management" onclick="event.stopPropagation(); window.location.href='{% url 'employees:salary-management' employee.pk %}'">💰</button>
                                </div>
                                <a href="{% url 'employees:employee-detail' employee.pk %}" class="view-profile-btn" onclick="event.stopPropagation()">
                                    View Profile
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="pagination-wrapper">
                        <div class="pagination">
                            {% if page_obj.has_previous %}
                                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.previous_page_number }}" class="page-link">‹ Previous</a>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <span class="page-link current">{{ num }}</span>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ num }}" class="page-link">{{ num }}</a>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <a href="?{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.status %}status={{ request.GET.status }}&{% endif %}page={{ page_obj.next_page_number }}" class="page-link">Next ›</a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                {% else %}
                    <!-- Empty State -->
                    <div class="empty-state">
                        <i>👥</i>
                        <h3>No Employees Found</h3>
                        <p>{% if request.GET.search or request.GET.department or request.GET.status %}No employees match your search criteria.{% else %}Start by adding your first employee.{% endif %}</p>
                        <a href="{% url 'employees:employee-add' %}" class="btn btn-primary" style="margin-top: 20px;">➕ Add First Employee</a>
                    </div>
                {% endif %}
            </div>
        </main>
    </div>

    <script>
        // Professional Employee List System
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            setupEventListeners();
            setupMobileOptimizations();
            setupNavigationSystem();
            setupFiltering();
        }

        function setupNavigationSystem() {
            // Mobile menu toggle
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    sidebarOverlay.classList.toggle('active');
                    hapticFeedback();
                });
            }

            // Close sidebar when clicking overlay
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                });
            }

            // Close sidebar when clicking nav item on mobile
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('active');
                        sidebarOverlay.classList.remove('active');
                    }
                });
            });
        }

        function setupEventListeners() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('input', debounce(handleSearch, 500));
            }

            // Theme toggle
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', toggleTheme);
            }

            // Clear filters
            const clearFilters = document.getElementById('clearFilters');
            if (clearFilters) {
                clearFilters.addEventListener('click', clearAllFilters);
            }

            // Add haptic feedback to interactive elements
            document.querySelectorAll('.employee-card, .btn, .nav-item, .action-btn').forEach(element => {
                element.addEventListener('click', hapticFeedback);
            });
        }

        function setupFiltering() {
            // Department filter
            const departmentFilter = document.getElementById('departmentFilter');
            if (departmentFilter) {
                departmentFilter.addEventListener('change', function() {
                    applyFilters();
                    hapticFeedback();
                });
            }

            // Status filter
            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) {
                statusFilter.addEventListener('change', function() {
                    applyFilters();
                    hapticFeedback();
                });
            }
        }

        function setupMobileOptimizations() {
            // Dynamic viewport height
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }
            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Touch feedback
            document.querySelectorAll('.employee-card, .btn, .nav-item, .action-btn').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                }, { passive: true });
            });

            // Header scroll effect
            window.addEventListener('scroll', () => {
                const header = document.querySelector('.main-header');
                if (window.scrollY > 50) {
                    header.style.boxShadow = '0 2px 10px rgba(30, 77, 59, 0.1)';
                } else {
                    header.style.boxShadow = 'none';
                }
            }, { passive: true });

            // Close sidebar on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                }
            });
        }

        function handleSearch(event) {
            const query = event.target.value.trim();
            applyFilters();
        }

        function applyFilters() {
            const searchValue = document.getElementById('searchInput').value.trim();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            // Build URL with filters
            const url = new URL(window.location);

            if (searchValue) {
                url.searchParams.set('search', searchValue);
            } else {
                url.searchParams.delete('search');
            }

            if (departmentFilter) {
                url.searchParams.set('department', departmentFilter);
            } else {
                url.searchParams.delete('department');
            }

            if (statusFilter) {
                url.searchParams.set('status', statusFilter);
            } else {
                url.searchParams.delete('status');
            }

            // Reset page to 1 when filtering
            url.searchParams.delete('page');

            // Navigate to filtered URL
            window.location.href = url.toString();
        }

        function clearAllFilters() {
            const searchInput = document.getElementById('searchInput');
            const departmentFilter = document.getElementById('departmentFilter');
            const statusFilter = document.getElementById('statusFilter');

            if (searchInput) searchInput.value = '';
            if (departmentFilter) departmentFilter.value = '';
            if (statusFilter) statusFilter.value = '';

            // Navigate to clean URL
            window.location.href = window.location.pathname;
            hapticFeedback();
        }

        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.getElementById('themeToggle');

            body.classList.toggle('dark-theme');
            const isDark = body.classList.contains('dark-theme');

            themeToggle.textContent = isDark ? '☀️' : '🌙';
            localStorage.setItem('channab-theme', isDark ? 'dark' : 'light');

            hapticFeedback();
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        function hapticFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate(10);
            }
        }

        // Initialize theme from localStorage
        const savedTheme = localStorage.getItem('channab-theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            document.getElementById('themeToggle').textContent = '☀️';
        }
    </script>
</body>
</html>