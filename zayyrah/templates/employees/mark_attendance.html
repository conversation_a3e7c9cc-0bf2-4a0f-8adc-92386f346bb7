{% extends 'base/app_base.html' %}

{% block title %}Mark Attendance - {{ employee.full_name }} - CHANNAB{% endblock %}

{% block nav_attendance %}active{% endblock %}
{% block bottom_nav_employees %}active{% endblock %}

{% block page_title %}Mark Attendance{% endblock %}
{% block breadcrumb_current %}Mark Attendance{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'employees:employee-list' %}" class="breadcrumb-item">Employees</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'employees:employee-detail' employee.pk %}" class="breadcrumb-item">{{ employee.full_name }}</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">Mark Attendance</span>
</nav>
{% endblock %}

{% block content %}
<!-- Employee Info Header -->
<div class="employee-attendance-header fade-in">
    <div class="employee-card">
        <div class="employee-info">
            <div class="employee-avatar">
                {{ employee.first_name.0 }}{{ employee.last_name.0 }}
            </div>
            <div class="employee-details">
                <h2>{{ employee.full_name }}</h2>
                <p class="employee-id">{{ employee.employee_id }}</p>
                <p class="employee-position">{{ employee.position.title }}</p>
                <p class="employee-department">{{ employee.department.name }}</p>
            </div>
        </div>
        <div class="date-info">
            <div class="current-date">
                <span class="date-label">Date</span>
                <span class="date-value">{{ today|date:"F d, Y" }}</span>
                <span class="day-name">{{ today|date:"l" }}</span>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Form -->
<div class="attendance-form-container">
    <div class="form-card">
        <form method="post" class="attendance-form">
            {% csrf_token %}

            <div class="form-header">
                <h3>📅 Attendance Details</h3>
                <p>Mark attendance for {{ employee.full_name }} on {{ today|date:"F d, Y" }}</p>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label for="attendance_date">Date</label>
                    <input type="date" id="attendance_date" name="date" value="{{ today|date:'Y-m-d' }}" required>
                </div>

                <div class="form-group">
                    <label for="status">Attendance Status</label>
                    <select id="status" name="status" required class="status-select">
                        <option value="">Select Status</option>
                        <option value="present" selected>✅ Present</option>
                        <option value="absent">❌ Absent</option>
                        <option value="late">⏰ Late</option>
                        <option value="half_day">🕐 Half Day</option>
                        <option value="on_leave">🏖️ On Leave</option>
                        <option value="holiday">🎉 Holiday</option>
                        <option value="weekend">📅 Weekend</option>
                    </select>
                </div>

                <div class="form-group time-group">
                    <label for="check_in">Check In Time</label>
                    <input type="time" id="check_in" name="check_in" class="time-input">
                    <small class="help-text">Leave empty if absent</small>
                </div>

                <div class="form-group time-group">
                    <label for="check_out">Check Out Time</label>
                    <input type="time" id="check_out" name="check_out" class="time-input">
                    <small class="help-text">Leave empty if not checked out yet</small>
                </div>

                <div class="form-group hours-group">
                    <label for="hours_worked">Hours Worked</label>
                    <input type="number" id="hours_worked" name="hours_worked" step="0.25" min="0" max="24" class="hours-input">
                    <small class="help-text">Will be calculated automatically if times are provided</small>
                </div>

                <div class="form-group hours-group">
                    <label for="overtime_hours">Overtime Hours</label>
                    <input type="number" id="overtime_hours" name="overtime_hours" step="0.25" min="0" max="12" class="hours-input">
                    <small class="help-text">Additional hours beyond regular schedule</small>
                </div>

                <div class="form-group full-width">
                    <label for="remarks">Remarks/Notes</label>
                    <textarea id="remarks" name="remarks" rows="3" placeholder="Any additional notes about attendance..." class="remarks-input"></textarea>
                </div>
            </div>

            <div class="form-actions">
                <a href="{% url 'employees:employee-detail' employee.pk %}" class="btn btn-secondary">
                    ← Back to Profile
                </a>
                <button type="submit" class="btn btn-primary">
                    💾 Save Attendance
                </button>
            </div>
        </form>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-card">
        <h4>⚡ Quick Actions</h4>
        <div class="quick-buttons">
            <button type="button" class="quick-btn present" onclick="setQuickAttendance('present')">
                <span class="quick-icon">✅</span>
                <span>Mark Present</span>
                <small>9:00 AM - 5:00 PM</small>
            </button>
            <button type="button" class="quick-btn late" onclick="setQuickAttendance('late')">
                <span class="quick-icon">⏰</span>
                <span>Mark Late</span>
                <small>After 9:15 AM</small>
            </button>
            <button type="button" class="quick-btn absent" onclick="setQuickAttendance('absent')">
                <span class="quick-icon">❌</span>
                <span>Mark Absent</span>
                <small>No times needed</small>
            </button>
            <button type="button" class="quick-btn leave" onclick="setQuickAttendance('on_leave')">
                <span class="quick-icon">🏖️</span>
                <span>On Leave</span>
                <small>Approved absence</small>
            </button>
        </div>
    </div>

    <!-- Recent Attendance History -->
    <div class="history-card">
        <h4>📊 Recent Attendance</h4>
        <div class="attendance-history">
            {% for i in "12345"|make_list %}
            <div class="history-item">
                <div class="history-date">Dec {{ i|add:"0" }}</div>
                <div class="history-status present">Present</div>
                <div class="history-time">9:00 - 17:30</div>
            </div>
            {% endfor %}
        </div>
        <a href="{% url 'employees:employee-detail' employee.pk %}" class="view-all-link">
            View Full History →
        </a>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Mark Attendance Page Styles */
.employee-attendance-header {
    margin-bottom: 32px;
}

.employee-card {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
}

.employee-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.employee-avatar {
    width: 80px;
    height: 80px;
    background: var(--success-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
    text-transform: uppercase;
}

.employee-details h2 {
    color: var(--primary-green);
    font-size: 1.5rem;
    margin-bottom: 4px;
}

.employee-id {
    color: var(--gray-500);
    font-weight: 600;
    margin-bottom: 2px;
}

.employee-position {
    color: var(--primary-green-light);
    font-weight: 600;
    margin-bottom: 2px;
}

.employee-department {
    color: var(--gray-600);
}

.date-info {
    text-align: right;
}

.date-label {
    display: block;
    color: var(--gray-500);
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.date-value {
    display: block;
    color: var(--primary-green);
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 2px;
}

.day-name {
    color: var(--gray-600);
    font-size: 0.9rem;
}

/* Form Container */
.attendance-form-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
    align-items: start;
}

.form-card {
    background: var(--white);
    border-radius: 16px;
    padding: 32px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.form-header {
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--gray-200);
}

.form-header h3 {
    color: var(--primary-green);
    font-size: 1.3rem;
    margin-bottom: 8px;
}

.form-header p {
    color: var(--gray-600);
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: all 0.2s ease;
    outline: none;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
}

.status-select {
    background: var(--white);
    cursor: pointer;
}

.time-input {
    font-family: var(--font-mono);
}

.help-text {
    color: var(--gray-500);
    font-size: 0.8rem;
    margin-top: 4px;
}

.form-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    padding-top: 24px;
    border-top: 1px solid var(--gray-200);
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-green);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-green-light);
    transform: translateY(-1px);
    box-shadow: var(--shadow-green);
}

.btn-secondary {
    background: var(--white);
    border: 1px solid var(--gray-300);
    color: var(--gray-600);
}

.btn-secondary:hover {
    background: var(--success-lighter);
    border-color: var(--primary-green);
    color: var(--primary-green);
    text-decoration: none;
}

/* Sidebar Cards */
.quick-actions-card,
.history-card {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    margin-bottom: 24px;
}

.quick-actions-card h4,
.history-card h4 {
    color: var(--primary-green);
    font-size: 1.1rem;
    margin-bottom: 20px;
}

.quick-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.quick-btn {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.quick-btn:hover {
    background: var(--success-lighter);
    border-color: var(--primary-green);
}

.quick-icon {
    font-size: 1.2rem;
    width: 24px;
}

.quick-btn span:not(.quick-icon) {
    flex: 1;
}

.quick-btn small {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.quick-btn.present:hover {
    background: rgba(34, 197, 94, 0.1);
    border-color: #22c55e;
}

.quick-btn.late:hover {
    background: rgba(245, 158, 11, 0.1);
    border-color: #f59e0b;
}

.quick-btn.absent:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: #ef4444;
}

.quick-btn.leave:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* Attendance History */
.attendance-history {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.history-item {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 12px;
    align-items: center;
    padding: 12px;
    background: var(--gray-50);
    border-radius: 6px;
}

.history-date {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.history-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.history-status.present {
    background: var(--success-lighter);
    color: var(--primary-green);
}

.history-time {
    color: var(--gray-600);
    font-size: 0.8rem;
    font-family: var(--font-mono);
}

.view-all-link {
    display: block;
    text-align: center;
    margin-top: 16px;
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.view-all-link:hover {
    color: var(--primary-green-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .employee-card {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .employee-info {
        flex-direction: column;
        text-align: center;
    }

    .attendance-form-container {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .form-card {
        padding: 24px;
    }

    .form-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-actions {
        flex-direction: column;
        gap: 12px;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .quick-buttons {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .quick-btn {
        flex-direction: column;
        text-align: center;
        padding: 12px;
    }

    .quick-btn small {
        display: none;
    }
}

@media (max-width: 480px) {
    .employee-avatar {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .employee-details h2 {
        font-size: 1.2rem;
    }

    .quick-buttons {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    setupAttendanceForm();
});

function setupAttendanceForm() {
    const statusSelect = document.getElementById('status');
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    const hoursInput = document.getElementById('hours_worked');

    // Status change handler
    statusSelect.addEventListener('change', function() {
        const status = this.value;
        toggleTimeInputs(status);
    });

    // Time calculation
    function calculateHours() {
        const checkIn = checkInInput.value;
        const checkOut = checkOutInput.value;

        if (checkIn && checkOut) {
            const start = new Date(`2000-01-01T${checkIn}`);
            const end = new Date(`2000-01-01T${checkOut}`);

            if (end > start) {
                const diffMs = end - start;
                const diffHours = diffMs / (1000 * 60 * 60);
                hoursInput.value = diffHours.toFixed(2);
            }
        }
    }

    checkInInput.addEventListener('change', calculateHours);
    checkOutInput.addEventListener('change', calculateHours);

    // Form submission
    document.querySelector('.attendance-form').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Basic validation
        if (!formData.get('status')) {
            alert('Please select an attendance status');
            return;
        }

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '⏳ Saving...';
        submitBtn.disabled = true;

        // Simulate form submission (replace with actual AJAX call)
        setTimeout(() => {
            alert('Attendance marked successfully!');
            // Redirect or update UI as needed
            window.location.href = '{% url "employees:employee-detail" employee.pk %}';
        }, 1000);
    });
}

function toggleTimeInputs(status) {
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    const hoursInput = document.getElementById('hours_worked');
    const timeGroups = document.querySelectorAll('.time-group, .hours-group');

    if (status === 'absent' || status === 'on_leave' || status === 'holiday') {
        // Hide time inputs for non-working statuses
        timeGroups.forEach(group => {
            group.style.opacity = '0.5';
            group.querySelectorAll('input').forEach(input => {
                input.disabled = true;
                input.value = '';
            });
        });
    } else {
        // Show time inputs for working statuses
        timeGroups.forEach(group => {
            group.style.opacity = '1';
            group.querySelectorAll('input').forEach(input => {
                input.disabled = false;
            });
        });
    }
}

function setQuickAttendance(status) {
    const statusSelect = document.getElementById('status');
    const checkInInput = document.getElementById('check_in');
    const checkOutInput = document.getElementById('check_out');
    const hoursInput = document.getElementById('hours_worked');

    statusSelect.value = status;
    toggleTimeInputs(status);

    // Set default times based on status
    switch (status) {
        case 'present':
            checkInInput.value = '09:00';
            checkOutInput.value = '17:00';
            hoursInput.value = '8.00';
            break;
        case 'late':
            checkInInput.value = '09:30';
            checkOutInput.value = '17:00';
            hoursInput.value = '7.50';
            break;
        case 'absent':
        case 'on_leave':
            checkInInput.value = '';
            checkOutInput.value = '';
            hoursInput.value = '';
            break;
    }

    // Visual feedback
    document.querySelectorAll('.quick-btn').forEach(btn => btn.classList.remove('selected'));
    event.target.closest('.quick-btn').classList.add('selected');
}

// Add selected state styling
document.head.insertAdjacentHTML('beforeend', `
<style>
.quick-btn.selected {
    background: var(--primary-green) !important;
    color: white !important;
    border-color: var(--primary-green) !important;
}
.quick-btn.selected small {
    color: rgba(255, 255, 255, 0.8) !important;
}
</style>
`);
</script>
{% endblock %}