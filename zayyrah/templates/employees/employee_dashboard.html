{% extends 'base/app_base.html' %}
{% load static %}

{% block title %}Employee Management - Zayyrah{% endblock %}

{% block extra_head %}
<link rel="stylesheet" href="{% static 'employees/css/employees.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
{% endblock %}

{% block content %}
<div class="employee-dashboard">
    <div class="dashboard-header">
        <h1><i class="fas fa-users"></i> Employee Management</h1>
        <div class="dashboard-actions">
            <button class="btn btn-primary" onclick="openModal('addEmployeeModal')">
                <i class="fas fa-plus"></i> Add Employee
            </button>
            <button class="btn btn-secondary" onclick="openModal('bulkAttendanceModal')">
                <i class="fas fa-calendar-check"></i> Bulk Attendance
            </button>
            <button class="btn btn-accent" onclick="openModal('payrollModal')">
                <i class="fas fa-money-bills"></i> Process Payroll
            </button>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-content">
                <h3>{{ total_employees }}</h3>
                <p>Total Employees</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-check"></i>
            </div>
            <div class="stat-content">
                <h3>{{ present_today }}</h3>
                <p>Present Today</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-user-times"></i>
            </div>
            <div class="stat-content">
                <h3>{{ absent_today }}</h3>
                <p>Absent Today</p>
            </div>
        </div>
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stat-content">
                <h3>{{ pending_leaves }}</h3>
                <p>Pending Leave Requests</p>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions-section">
        <h2>Quick Actions</h2>
        <div class="quick-actions-grid">
            <a href="#employees" class="quick-action-card">
                <i class="fas fa-users"></i>
                <h3>Manage Employees</h3>
                <p>View, add, edit employee profiles</p>
            </a>
            <a href="#attendance" class="quick-action-card">
                <i class="fas fa-calendar-check"></i>
                <h3>Attendance</h3>
                <p>Track daily attendance and hours</p>
            </a>
            <a href="#leaves" class="quick-action-card">
                <i class="fas fa-calendar-times"></i>
                <h3>Leave Management</h3>
                <p>Handle leave requests and policies</p>
            </a>
            <a href="#payroll" class="quick-action-card">
                <i class="fas fa-money-bills"></i>
                <h3>Payroll</h3>
                <p>Process salaries and transactions</p>
            </a>
            <a href="#reports" class="quick-action-card">
                <i class="fas fa-chart-bar"></i>
                <h3>Reports</h3>
                <p>Generate detailed reports</p>
            </a>
            <a href="#performance" class="quick-action-card">
                <i class="fas fa-star"></i>
                <h3>Performance</h3>
                <p>Employee performance reviews</p>
            </a>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="recent-activities">
        <h2>Recent Activities</h2>
        <div class="activity-list">
            {% for activity in recent_activities %}
            <div class="activity-item">
                <div class="activity-icon">
                    <i class="{{ activity.icon }}"></i>
                </div>
                <div class="activity-content">
                    <h4>{{ activity.title }}</h4>
                    <p>{{ activity.description }}</p>
                    <span class="activity-time">{{ activity.timestamp|timesince }} ago</span>
                </div>
            </div>
            {% empty %}
            <div class="empty-state">
                <i class="fas fa-history"></i>
                <p>No recent activities</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Add Employee Modal -->
<div id="addEmployeeModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-user-plus"></i> Add New Employee</h2>
            <span class="close" onclick="closeModal('addEmployeeModal')">&times;</span>
        </div>
        <form class="employee-form" id="addEmployeeForm">
            <div class="form-tabs">
                <div class="tab active" onclick="switchTab(event, 'personal-tab')">Personal Info</div>
                <div class="tab" onclick="switchTab(event, 'employment-tab')">Employment</div>
                <div class="tab" onclick="switchTab(event, 'salary-tab')">Salary</div>
            </div>

            <div id="personal-tab" class="tab-content active">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="employee_id">Employee ID</label>
                        <input type="text" id="employee_id" name="employee_id" required>
                    </div>
                    <div class="form-group">
                        <label for="first_name">First Name</label>
                        <input type="text" id="first_name" name="first_name" required>
                    </div>
                    <div class="form-group">
                        <label for="last_name">Last Name</label>
                        <input type="text" id="last_name" name="last_name" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                    <div class="form-group">
                        <label for="date_of_birth">Date of Birth</label>
                        <input type="date" id="date_of_birth" name="date_of_birth" required>
                    </div>
                    <div class="form-group">
                        <label for="gender">Gender</label>
                        <select id="gender" name="gender" required>
                            <option value="">Select Gender</option>
                            <option value="M">Male</option>
                            <option value="F">Female</option>
                            <option value="O">Other</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="marital_status">Marital Status</label>
                        <select id="marital_status" name="marital_status" required>
                            <option value="">Select Status</option>
                            <option value="single">Single</option>
                            <option value="married">Married</option>
                            <option value="divorced">Divorced</option>
                            <option value="widowed">Widowed</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="address">Address</label>
                        <textarea id="address" name="address" rows="3" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="emergency_contact_name">Emergency Contact Name</label>
                        <input type="text" id="emergency_contact_name" name="emergency_contact_name" required>
                    </div>
                    <div class="form-group">
                        <label for="emergency_contact_phone">Emergency Contact Phone</label>
                        <input type="tel" id="emergency_contact_phone" name="emergency_contact_phone" required>
                    </div>
                </div>
            </div>

            <div id="employment-tab" class="tab-content">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="department">Department</label>
                        <select id="department" name="department" required>
                            <option value="">Select Department</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}">{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="position">Position</label>
                        <select id="position" name="position" required>
                            <option value="">Select Position</option>
                            {% for pos in positions %}
                            <option value="{{ pos.id }}" data-department="{{ pos.department.id }}">{{ pos.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="hire_date">Hire Date</label>
                        <input type="date" id="hire_date" name="hire_date" required>
                    </div>
                    <div class="form-group">
                        <label for="employment_status">Employment Status</label>
                        <select id="employment_status" name="employment_status" required>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="on_leave">On Leave</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="bank_name">Bank Name</label>
                        <input type="text" id="bank_name" name="bank_name">
                    </div>
                    <div class="form-group">
                        <label for="account_number">Account Number</label>
                        <input type="text" id="account_number" name="account_number">
                    </div>
                </div>
            </div>

            <div id="salary-tab" class="tab-content">
                <div class="salary-components">
                    <h3>Salary Components</h3>
                    <div id="salary-components-list">
                        <!-- Dynamic salary components will be added here -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addSalaryComponent()">
                        <i class="fas fa-plus"></i> Add Component
                    </button>
                </div>
            </div>

            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('addEmployeeModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Employee</button>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Attendance Modal -->
<div id="bulkAttendanceModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-calendar-check"></i> Bulk Attendance Entry</h2>
            <span class="close" onclick="closeModal('bulkAttendanceModal')">&times;</span>
        </div>
        <form class="bulk-attendance-form">
            <div class="form-group">
                <label for="attendance_date">Date</label>
                <input type="date" id="attendance_date" name="attendance_date" value="{{ today }}" required>
            </div>
            <div class="attendance-list">
                <div class="attendance-header">
                    <span>Employee</span>
                    <span>Status</span>
                    <span>Check In</span>
                    <span>Check Out</span>
                    <span>Notes</span>
                </div>
                {% for employee in employees %}
                <div class="attendance-row">
                    <div class="employee-info">
                        <img src="{% static 'images/default-avatar.png' %}" alt="{{ employee.full_name }}" class="employee-avatar">
                        <div>
                            <strong>{{ employee.full_name }}</strong>
                            <small>{{ employee.employee_id }}</small>
                        </div>
                    </div>
                    <select name="status_{{ employee.id }}" class="status-select">
                        <option value="present">Present</option>
                        <option value="absent">Absent</option>
                        <option value="late">Late</option>
                        <option value="half_day">Half Day</option>
                        <option value="on_leave">On Leave</option>
                    </select>
                    <input type="time" name="check_in_{{ employee.id }}" class="time-input">
                    <input type="time" name="check_out_{{ employee.id }}" class="time-input">
                    <input type="text" name="notes_{{ employee.id }}" placeholder="Notes" class="notes-input">
                </div>
                {% endfor %}
            </div>
            <div class="form-actions">
                <button type="button" class="btn btn-secondary" onclick="closeModal('bulkAttendanceModal')">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Attendance</button>
            </div>
        </form>
    </div>
</div>

<script src="{% static 'employees/js/employees.js' %}"></script>
{% endblock %}