{% extends 'base/app_base.html' %}

{% block title %}{{ employee.full_name }} - Employee Details - CHANNAB{% endblock %}

{% block nav_employees_list %}active{% endblock %}
{% block bottom_nav_employees %}active{% endblock %}

{% block page_title %}{{ employee.full_name }}{% endblock %}
{% block breadcrumb_current %}Employee Details{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'employees:employee-list' %}" class="breadcrumb-item">Employees</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">{{ employee.full_name }}</span>
</nav>
{% endblock %}

{% block content %}
<!-- Employee Profile Header -->
<div class="employee-profile-header fade-in">
    <div class="profile-card">
        <div class="profile-main">
            <div class="profile-avatar">
                {{ employee.first_name.0 }}{{ employee.last_name.0 }}
            </div>
            <div class="profile-info">
                <h1 class="profile-name">{{ employee.full_name }}</h1>
                <p class="profile-id">{{ employee.employee_id }}</p>
                <p class="profile-position">{{ employee.position.title }}</p>
                <p class="profile-department">{{ employee.department.name }}</p>
                <div class="profile-status">
                    <span class="status-badge status-{{ employee.employment_status }}">
                        {% if employee.employment_status == 'active' %}
                            🟢 Active
                        {% elif employee.employment_status == 'on_leave' %}
                            🟡 On Leave
                        {% else %}
                            🔴 {{ employee.get_employment_status_display }}
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
        <div class="profile-actions">
            <a href="{% url 'employees:employee-edit' employee.pk %}" class="action-btn primary">✏️ Edit Employee</a>
            <a href="{% url 'employees:mark-attendance' employee.pk %}" class="action-btn secondary">📅 Mark Attendance</a>
            <a href="{% url 'employees:salary-management' employee.pk %}" class="action-btn success">💰 Salary</a>
        </div>
    </div>
</div>

<!-- Employee Information Tabs -->
<div class="employee-tabs-container">
    <div class="tabs-header">
        <button class="tab-btn active" data-tab="personal">Personal Info</button>
        <button class="tab-btn" data-tab="attendance">Attendance</button>
        <button class="tab-btn" data-tab="leaves">Leaves</button>
        <button class="tab-btn" data-tab="salary">Salary</button>
        <button class="tab-btn" data-tab="documents">Documents</button>
    </div>

    <!-- Personal Information Tab -->
    <div class="tab-content active" id="personal">
        <div class="info-grid">
            <div class="info-card">
                <h3>Basic Information</h3>
                <div class="info-items">
                    <div class="info-item">
                        <span class="info-label">Full Name</span>
                        <span class="info-value">{{ employee.full_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Employee ID</span>
                        <span class="info-value">{{ employee.employee_id }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Email</span>
                        <span class="info-value">{{ employee.email }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Phone</span>
                        <span class="info-value">{{ employee.phone }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Date of Birth</span>
                        <span class="info-value">{{ employee.date_of_birth|date:"F d, Y" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Gender</span>
                        <span class="info-value">{{ employee.get_gender_display }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Marital Status</span>
                        <span class="info-value">{{ employee.get_marital_status_display }}</span>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <h3>Employment Details</h3>
                <div class="info-items">
                    <div class="info-item">
                        <span class="info-label">Department</span>
                        <span class="info-value">{{ employee.department.name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Position</span>
                        <span class="info-value">{{ employee.position.title }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Hire Date</span>
                        <span class="info-value">{{ employee.hire_date|date:"F d, Y" }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Employment Status</span>
                        <span class="info-value">{{ employee.get_employment_status_display }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Current Salary</span>
                        <span class="info-value">Rs. {{ employee.current_salary|floatformat:2 }}</span>
                    </div>
                </div>
            </div>

            <div class="info-card">
                <h3>Contact Information</h3>
                <div class="info-items">
                    <div class="info-item">
                        <span class="info-label">Address</span>
                        <span class="info-value">{{ employee.address }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Emergency Contact</span>
                        <span class="info-value">{{ employee.emergency_contact_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Emergency Phone</span>
                        <span class="info-value">{{ employee.emergency_contact_phone }}</span>
                    </div>
                </div>
            </div>

            {% if employee.bank_name %}
            <div class="info-card">
                <h3>Banking Information</h3>
                <div class="info-items">
                    <div class="info-item">
                        <span class="info-label">Bank Name</span>
                        <span class="info-value">{{ employee.bank_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Account Number</span>
                        <span class="info-value">****{{ employee.account_number|slice:"-4:" }}</span>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Attendance Tab -->
    <div class="tab-content" id="attendance">
        <div class="attendance-summary">
            <h3>Recent Attendance (Last 30 Days)</h3>
            {% if recent_attendance %}
            <div class="attendance-grid">
                {% for attendance in recent_attendance %}
                <div class="attendance-item">
                    <div class="attendance-date">{{ attendance.date|date:"M d" }}</div>
                    <div class="attendance-status status-{{ attendance.status }}">
                        {% if attendance.status == 'present' %}
                            ✓ Present
                        {% elif attendance.status == 'late' %}
                            ⏰ Late
                        {% elif attendance.status == 'absent' %}
                            ✗ Absent
                        {% elif attendance.status == 'on_leave' %}
                            🏖️ On Leave
                        {% else %}
                            {{ attendance.get_status_display }}
                        {% endif %}
                    </div>
                    {% if attendance.check_in %}
                    <div class="attendance-time">{{ attendance.check_in }} - {{ attendance.check_out|default:"--" }}</div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <p>No attendance records found.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Leaves Tab -->
    <div class="tab-content" id="leaves">
        <div class="leaves-summary">
            <h3>Leave Requests</h3>
            {% if leave_requests %}
            <div class="leaves-list">
                {% for leave in leave_requests %}
                <div class="leave-item">
                    <div class="leave-header">
                        <h4>{{ leave.leave_type.name }}</h4>
                        <span class="leave-status status-{{ leave.status }}">{{ leave.get_status_display }}</span>
                    </div>
                    <div class="leave-details">
                        <p><strong>Duration:</strong> {{ leave.start_date|date:"M d" }} - {{ leave.end_date|date:"M d, Y" }} ({{ leave.days_requested }} days)</p>
                        <p><strong>Reason:</strong> {{ leave.reason }}</p>
                        {% if leave.approved_by %}
                        <p><strong>Approved by:</strong> {{ leave.approved_by.mobile_number }} on {{ leave.approval_date|date:"M d, Y" }}</p>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <p>No leave requests found.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Salary Tab -->
    <div class="tab-content" id="salary">
        <div class="salary-summary">
            <h3>Salary History</h3>
            {% if salary_structures %}
            <div class="salary-list">
                {% for structure in salary_structures %}
                <div class="salary-item">
                    <div class="salary-header">
                        <h4>Rs. {{ structure.total_salary|floatformat:2 }}</h4>
                        <span class="salary-date">Effective: {{ structure.effective_date|date:"M d, Y" }}</span>
                    </div>
                    <div class="salary-components">
                        {% for component in structure.components.all %}
                        <div class="component-item">
                            <span class="component-name">{{ component.salary_component.name }}</span>
                            <span class="component-amount">Rs. {{ component.amount|floatformat:2 }}</span>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <p>No salary structures found.</p>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Documents Tab -->
    <div class="tab-content" id="documents">
        <div class="documents-summary">
            <h3>Employee Documents</h3>
            {% if documents %}
            <div class="documents-list">
                {% for document in documents %}
                <div class="document-item">
                    <div class="document-icon">📄</div>
                    <div class="document-info">
                        <h4>{{ document.title }}</h4>
                        <p>{{ document.get_document_type_display }}</p>
                        <p class="document-date">Uploaded: {{ document.uploaded_at|date:"M d, Y" }}</p>
                    </div>
                    <div class="document-actions">
                        <a href="{{ document.file.url }}" class="btn-download" target="_blank">Download</a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <p>No documents uploaded.</p>
                <a href="{% url 'employees:document-upload' employee.pk %}" class="btn btn-primary">Upload Document</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Employee Detail Page Styles */
.employee-profile-header {
    margin-bottom: 32px;
}

.profile-card {
    background: var(--white);
    border-radius: 16px;
    padding: 32px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 32px;
}

.profile-main {
    display: flex;
    align-items: center;
    gap: 24px;
}

.profile-avatar {
    width: 100px;
    height: 100px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    font-weight: 700;
    text-transform: uppercase;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-green);
    margin-bottom: 8px;
}

.profile-id {
    color: var(--gray-500);
    font-weight: 600;
    margin-bottom: 4px;
}

.profile-position {
    color: var(--primary-green-light);
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.profile-department {
    color: var(--gray-600);
    margin-bottom: 12px;
}

.profile-actions {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.action-btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.action-btn.primary {
    background: var(--primary-green);
    color: white;
}

.action-btn.primary:hover {
    background: var(--primary-green-light);
    transform: translateY(-1px);
}

.action-btn.secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.action-btn.secondary:hover {
    background: var(--success-lighter);
    border-color: var(--primary-green);
    color: var(--primary-green);
}

.action-btn.success {
    background: var(--success-gradient);
    color: white;
}

.action-btn.success:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-green);
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-active {
    background: var(--success-lighter);
    color: var(--primary-green);
}

.status-inactive {
    background: var(--gray-100);
    color: var(--gray-600);
}

.status-on-leave {
    background: #fef3c7;
    color: #92400e;
}

/* Tabs System */
.employee-tabs-container {
    background: var(--white);
    border-radius: 16px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.tabs-header {
    display: flex;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
}

.tab-btn {
    flex: 1;
    padding: 16px 24px;
    background: transparent;
    border: none;
    cursor: pointer;
    font-weight: 600;
    color: var(--gray-600);
    transition: all 0.2s ease;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: var(--success-lighter);
    color: var(--primary-green);
}

.tab-btn.active {
    background: var(--white);
    color: var(--primary-green);
    border-bottom-color: var(--primary-green);
}

.tab-content {
    display: none;
    padding: 32px;
}

.tab-content.active {
    display: block;
}

/* Info Grid */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.info-card {
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 24px;
    background: var(--gray-50);
}

.info-card h3 {
    color: var(--primary-green);
    font-size: 1.2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.info-items {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: start;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--gray-200);
}

.info-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.info-label {
    font-weight: 600;
    color: var(--gray-600);
    font-size: 0.9rem;
    flex: 1;
}

.info-value {
    color: var(--gray-800);
    font-weight: 500;
    text-align: right;
    flex: 1;
}

/* Attendance Grid */
.attendance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

.attendance-item {
    background: var(--gray-50);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--gray-200);
    text-align: center;
}

.attendance-date {
    font-weight: 600;
    color: var(--primary-green);
    margin-bottom: 8px;
}

.attendance-status {
    font-weight: 600;
    margin-bottom: 4px;
}

.attendance-status.status-present {
    color: var(--primary-green);
}

.attendance-status.status-late {
    color: var(--warning);
}

.attendance-status.status-absent {
    color: var(--danger);
}

.attendance-status.status-on_leave {
    color: var(--info);
}

.attendance-time {
    font-size: 0.8rem;
    color: var(--gray-600);
}

/* Leaves and Salary Lists */
.leaves-list, .salary-list, .documents-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.leave-item, .salary-item, .document-item {
    background: var(--gray-50);
    padding: 20px;
    border-radius: 12px;
    border: 1px solid var(--gray-200);
}

.leave-header, .salary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.leave-header h4, .salary-header h4 {
    color: var(--primary-green);
    margin: 0;
}

.leave-status {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.leave-status.status-pending {
    background: #fef3c7;
    color: #92400e;
}

.leave-status.status-approved {
    background: var(--success-lighter);
    color: var(--primary-green);
}

.leave-status.status-rejected {
    background: #fee2e2;
    color: #dc2626;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 16px;
}

.document-icon {
    font-size: 2rem;
    color: var(--primary-green);
}

.document-info {
    flex: 1;
}

.document-info h4 {
    color: var(--primary-green);
    margin-bottom: 4px;
}

.document-date {
    color: var(--gray-500);
    font-size: 0.8rem;
}

.btn-download {
    padding: 8px 16px;
    background: var(--primary-green);
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
}

.btn-download:hover {
    background: var(--primary-green-light);
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: var(--gray-500);
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-card {
        flex-direction: column;
        align-items: stretch;
        gap: 24px;
    }

    .profile-main {
        flex-direction: column;
        text-align: center;
    }

    .profile-actions {
        flex-direction: row;
        justify-content: center;
    }

    .tabs-header {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1;
        min-width: 120px;
    }

    .tab-content {
        padding: 20px;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: start;
        gap: 4px;
    }

    .info-value {
        text-align: left;
    }

    .attendance-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab switching functionality
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and contents
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            this.classList.add('active');
            document.getElementById(targetTab).classList.add('active');
        });
    });
});
</script>
{% endblock %}