{% extends 'base/app_base.html' %}

{% block title %}{% if object %}Edit {{ object.name|default:object.mobile_number|default:'Customer' }}{% else %}Add Customer{% endif %} - CHANNAB{% endblock %}

{% block nav_customers %}active{% endblock %}
{% block bottom_nav_customers %}active{% endblock %}

{% block page_title %}{% if object %}Edit Customer{% else %}Add Customer{% endif %}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'customers:list' %}" class="breadcrumb-item">Customers</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">{% if object %}Edit{% else %}Add{% endif %}</span>
</nav>
{% endblock %}

{% block header_actions %}
<a href="{% url 'customers:list' %}" class="header-btn" title="Back to List">
    ←
</a>
{% endblock %}

{% block extra_css %}
<style>
    /* ==== ULTRA-MODERN CUSTOMER FORM STYLES ==== */

    /* Global mobile overflow prevention */
    @media (max-width: 768px) {
        html, body {
            overflow-x: hidden !important;
            max-width: 100vw !important;
        }

        .main-content, .content-wrapper {
            max-width: 100% !important;
            overflow-x: hidden !important;
            box-sizing: border-box !important;
        }
    }

    /* Form Container */
    .customer-form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .form-intro {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        text-align: center;
    }

    .form-intro-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        color: var(--primary-green);
    }

    .form-intro-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 8px;
    }

    .form-intro-description {
        color: var(--gray-600);
        font-size: 0.95rem;
        line-height: 1.5;
    }

    /* Form Sections */
    .form-section {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        margin-bottom: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--gray-200);
    }

    .section-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: var(--primary-green);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--gray-800);
        margin: 0;
    }

    .section-description {
        font-size: 0.85rem;
        color: var(--gray-600);
        margin: 0;
    }

    /* Form Grid */
    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .form-grid.single-column {
        grid-template-columns: 1fr;
    }

    .form-group {
        position: relative;
    }

    .form-group.full-width {
        grid-column: 1 / -1;
    }

    /* Form Labels */
    .form-label {
        display: block;
        font-size: 0.85rem;
        font-weight: 600;
        color: var(--gray-700);
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .form-label.required::after {
        content: ' *';
        color: #dc3545;
    }

    /* Form Inputs */
    .form-input {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--gray-300);
        border-radius: 12px;
        font-size: 0.9rem;
        background: var(--white);
        transition: all var(--transition-normal);
        box-sizing: border-box;
    }

    .form-input:focus {
        outline: none;
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        background: var(--success-lighter);
    }

    .form-input::placeholder {
        color: var(--gray-400);
    }

    /* Currency Input Container */
    .currency-container {
        position: relative;
        display: flex;
        align-items: center;
    }

    .currency-prefix {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--gray-500);
        font-size: 0.9rem;
        font-weight: 600;
        pointer-events: none;
        z-index: 1;
    }

    .currency-input {
        padding-left: 45px !important;
        width: 100%;
    }

    /* Select Inputs */
    .form-select {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--gray-300);
        border-radius: 12px;
        font-size: 0.9rem;
        background: var(--white);
        cursor: pointer;
        transition: all var(--transition-normal);
        box-sizing: border-box;
    }

    .form-select:focus {
        outline: none;
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
    }

    /* Textarea */
    .form-textarea {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid var(--gray-300);
        border-radius: 12px;
        font-size: 0.9rem;
        background: var(--white);
        transition: all var(--transition-normal);
        resize: vertical;
        min-height: 100px;
        box-sizing: border-box;
    }

    .form-textarea:focus {
        outline: none;
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
        background: var(--success-lighter);
    }

    /* Clearance Date Fields */
    .clearance-fields {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-top: 12px;
    }

    .clearance-field {
        display: none;
    }

    .clearance-field.active {
        display: block;
    }

    /* Error Handling */
    .form-error {
        color: #dc3545;
        font-size: 0.8rem;
        margin-top: 6px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .form-error::before {
        content: '⚠️';
        font-size: 0.9rem;
    }

    .form-input.error {
        border-color: #dc3545;
        background: #fdf2f2;
    }

    /* Help Text */
    .form-help {
        font-size: 0.8rem;
        color: var(--gray-500);
        margin-top: 4px;
        line-height: 1.4;
    }

    /* Form Actions */
    .form-actions {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        display: flex;
        gap: 16px;
        justify-content: flex-end;
    }

    .form-button {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        text-decoration: none;
        transition: all var(--transition-normal);
        cursor: pointer;
        border: none;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-button.primary {
        background: var(--primary-green);
        color: white;
    }

    .form-button.primary:hover {
        background: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    .form-button.secondary {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .form-button.secondary:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
    }

    /* Mobile Responsive Design */
    @media (max-width: 768px) {
        .main-content {
            padding: 8px !important;
        }

        .customer-form-container {
            max-width: 100%;
        }

        .form-intro, .form-section, .form-actions {
            padding: 16px;
            border-radius: 12px;
            margin-bottom: 16px;
        }

        .form-intro-icon {
            font-size: 2.5rem;
            margin-bottom: 12px;
        }

        .form-intro-title {
            font-size: 1.3rem;
            margin-bottom: 6px;
        }

        .form-intro-description {
            font-size: 0.9rem;
        }

        .section-header {
            margin-bottom: 16px;
            padding-bottom: 8px;
        }

        .section-icon {
            width: 32px;
            height: 32px;
            font-size: 1rem;
        }

        .section-title {
            font-size: 1rem;
        }

        .section-description {
            font-size: 0.8rem;
        }

        .form-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .clearance-fields {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .form-actions {
            flex-direction: column;
            gap: 12px;
        }

        .form-button {
            padding: 10px 20px;
            font-size: 0.85rem;
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .main-content {
            padding: 6px !important;
        }

        .form-intro, .form-section, .form-actions {
            padding: 12px;
            margin-bottom: 12px;
        }

        .form-intro-icon {
            font-size: 2rem;
            margin-bottom: 8px;
        }

        .form-intro-title {
            font-size: 1.1rem;
        }

        .section-header {
            margin-bottom: 12px;
        }

        .section-icon {
            width: 28px;
            height: 28px;
            font-size: 0.9rem;
        }

        .section-title {
            font-size: 0.9rem;
        }

        .form-input, .form-select, .form-textarea {
            padding: 10px 12px;
            font-size: 0.85rem;
        }

        .form-label {
            font-size: 0.8rem;
            margin-bottom: 6px;
        }

        .form-button {
            padding: 8px 16px;
            font-size: 0.8rem;
        }
    }

    /* Dark Theme Support */
    .dark-theme .form-intro,
    .dark-theme .form-section,
    .dark-theme .form-actions {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }

    .dark-theme .section-title,
    .dark-theme .form-intro-title {
        color: var(--gray-100);
    }

    .dark-theme .form-input,
    .dark-theme .form-select,
    .dark-theme .form-textarea {
        background: var(--gray-700);
        border-color: var(--gray-600);
        color: var(--gray-100);
    }

    .dark-theme .form-input:focus,
    .dark-theme .form-select:focus,
    .dark-theme .form-textarea:focus {
        background: rgba(30, 77, 59, 0.2);
        border-color: var(--accent-green);
    }
</style>
{% endblock %}

{% block content %}
<div class="customer-form-container fade-in">
    <!-- Form Introduction -->
    <div class="form-intro">
        <div class="form-intro-icon">{% if object %}✏️{% else %}👤{% endif %}</div>
        <h1 class="form-intro-title">
            {% if object %}
                Edit {{ object.name|default:object.mobile_number|default:'Customer' }}
            {% else %}
                Add New Customer
            {% endif %}
        </h1>
        <p class="form-intro-description">
            {% if object %}
                Update customer information and financial settings
            {% else %}
                Add as little as a name or mobile number. You can always fill in more details later.
            {% endif %}
        </p>
    </div>

    <form method="post" novalidate>
        {% csrf_token %}

        <!-- Basic Information Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">📞</div>
                <div>
                    <h2 class="section-title">Basic Information</h2>
                    <p class="section-description">Customer contact details and identification</p>
                </div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label" for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                    <input type="text"
                           name="{{ form.name.name }}"
                           id="{{ form.name.id_for_label }}"
                           value="{{ form.name.value|default:'' }}"
                           placeholder="Customer name"
                           class="form-input {% if form.name.errors %}error{% endif %}"
                           autocomplete="name">
                    {% if form.name.errors %}
                        {% for error in form.name.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-group">
                    <label class="form-label" for="{{ form.mobile_number.id_for_label }}">{{ form.mobile_number.label }}</label>
                    <input type="text"
                           name="{{ form.mobile_number.name }}"
                           id="{{ form.mobile_number.id_for_label }}"
                           value="{{ form.mobile_number.value|default:'' }}"
                           placeholder="03XXXXXXXXX"
                           class="form-input {% if form.mobile_number.errors %}error{% endif %}"
                           inputmode="numeric"
                           autocomplete="tel">
                    {% if form.mobile_number.errors %}
                        {% for error in form.mobile_number.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-group full-width">
                    <label class="form-label" for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                    <input type="email"
                           name="{{ form.email.name }}"
                           id="{{ form.email.id_for_label }}"
                           value="{{ form.email.value|default:'' }}"
                           placeholder="<EMAIL>"
                           class="form-input {% if form.email.errors %}error{% endif %}"
                           autocomplete="email">
                    {% if form.email.errors %}
                        {% for error in form.email.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Financial Information Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">💰</div>
                <div>
                    <h2 class="section-title">Financial Settings</h2>
                    <p class="section-description">Credit limits and opening balance information</p>
                </div>
            </div>

            <div class="form-grid">
                <div class="form-group">
                    <label class="form-label" for="{{ form.opening_balance.id_for_label }}">{{ form.opening_balance.label }}</label>
                    <div class="currency-container">
                        <span class="currency-prefix">Rs.</span>
                        <input type="number"
                               name="{{ form.opening_balance.name }}"
                               id="{{ form.opening_balance.id_for_label }}"
                               value="{{ form.opening_balance.value|default:'0.00' }}"
                               placeholder="0.00"
                               step="0.01"
                               min="0"
                               max="999999999.99"
                               class="form-input currency-input {% if form.opening_balance.errors %}error{% endif %}">
                    </div>
                    {% if form.opening_balance.help_text %}
                        <div class="form-help">{{ form.opening_balance.help_text }}</div>
                    {% endif %}
                    {% if form.opening_balance.errors %}
                        {% for error in form.opening_balance.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="form-group">
                    <label class="form-label" for="{{ form.credit_limit.id_for_label }}">{{ form.credit_limit.label }}</label>
                    <div class="currency-container">
                        <span class="currency-prefix">Rs.</span>
                        <input type="number"
                               name="{{ form.credit_limit.name }}"
                               id="{{ form.credit_limit.id_for_label }}"
                               value="{{ form.credit_limit.value|default:'0.00' }}"
                               placeholder="0.00"
                               step="0.01"
                               min="0"
                               max="999999999.99"
                               class="form-input currency-input {% if form.credit_limit.errors %}error{% endif %}">
                    </div>
                    {% if form.credit_limit.help_text %}
                        <div class="form-help">{{ form.credit_limit.help_text }}</div>
                    {% endif %}
                    {% if form.credit_limit.errors %}
                        {% for error in form.credit_limit.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Clearance Schedule Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">📅</div>
                <div>
                    <h2 class="section-title">Clearance Schedule</h2>
                    <p class="section-description">Set up payment clearance dates and schedule</p>
                </div>
            </div>

            <div class="form-grid single-column">
                <div class="form-group">
                    <label class="form-label" for="{{ form.clearance_type.id_for_label }}">{{ form.clearance_type.label }}</label>
                    <select name="{{ form.clearance_type.name }}"
                            id="{{ form.clearance_type.id_for_label }}"
                            class="form-select clearance-type-select {% if form.clearance_type.errors %}error{% endif %}"
                            onchange="toggleClearanceFields(this.value)">
                        {% for value, label in form.clearance_type.field.choices %}
                            <option value="{{ value }}" {% if form.clearance_type.value == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    {% if form.clearance_type.help_text %}
                        <div class="form-help">{{ form.clearance_type.help_text }}</div>
                    {% endif %}
                    {% if form.clearance_type.errors %}
                        {% for error in form.clearance_type.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>

                <div class="clearance-fields">
                    <div class="clearance-field" id="fixed-date-field">
                        <label class="form-label" for="{{ form.clearance_day_of_month.id_for_label }}">{{ form.clearance_day_of_month.label }}</label>
                        <input type="number"
                               name="{{ form.clearance_day_of_month.name }}"
                               id="{{ form.clearance_day_of_month.id_for_label }}"
                               value="{{ form.clearance_day_of_month.value|default:'' }}"
                               placeholder="Day (1-31)"
                               min="1"
                               max="31"
                               class="form-input clearance-day-input {% if form.clearance_day_of_month.errors %}error{% endif %}">
                        {% if form.clearance_day_of_month.help_text %}
                            <div class="form-help">{{ form.clearance_day_of_month.help_text }}</div>
                        {% endif %}
                        {% if form.clearance_day_of_month.errors %}
                            {% for error in form.clearance_day_of_month.errors %}
                                <div class="form-error">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="clearance-field" id="weekly-field">
                        <label class="form-label" for="{{ form.clearance_day_of_week.id_for_label }}">{{ form.clearance_day_of_week.label }}</label>
                        <select name="{{ form.clearance_day_of_week.name }}"
                                id="{{ form.clearance_day_of_week.id_for_label }}"
                                class="form-select clearance-day-select {% if form.clearance_day_of_week.errors %}error{% endif %}">
                            <option value="">Select day</option>
                            {% for value, label in form.clearance_day_of_week.field.choices %}
                                <option value="{{ value }}" {% if form.clearance_day_of_week.value == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                        {% if form.clearance_day_of_week.help_text %}
                            <div class="form-help">{{ form.clearance_day_of_week.help_text }}</div>
                        {% endif %}
                        {% if form.clearance_day_of_week.errors %}
                            {% for error in form.clearance_day_of_week.errors %}
                                <div class="form-error">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>

                    <div class="clearance-field" id="custom-date-field">
                        <label class="form-label" for="{{ form.clearance_custom_date.id_for_label }}">{{ form.clearance_custom_date.label }}</label>
                        <input type="date"
                               name="{{ form.clearance_custom_date.name }}"
                               id="{{ form.clearance_custom_date.id_for_label }}"
                               value="{{ form.clearance_custom_date.value|default:'' }}"
                               class="form-input clearance-date-input {% if form.clearance_custom_date.errors %}error{% endif %}">
                        {% if form.clearance_custom_date.help_text %}
                            <div class="form-help">{{ form.clearance_custom_date.help_text }}</div>
                        {% endif %}
                        {% if form.clearance_custom_date.errors %}
                            {% for error in form.clearance_custom_date.errors %}
                                <div class="form-error">{{ error }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="form-section">
            <div class="section-header">
                <div class="section-icon">📝</div>
                <div>
                    <h2 class="section-title">Additional Notes</h2>
                    <p class="section-description">Any additional information about this customer</p>
                </div>
            </div>

            <div class="form-grid single-column">
                <div class="form-group">
                    <label class="form-label" for="{{ form.notes.id_for_label }}">{{ form.notes.label }}</label>
                    <textarea name="{{ form.notes.name }}"
                              id="{{ form.notes.id_for_label }}"
                              rows="4"
                              placeholder="Additional context about this customer"
                              class="form-textarea {% if form.notes.errors %}error{% endif %}">{{ form.notes.value|default:'' }}</textarea>
                    {% if form.notes.errors %}
                        {% for error in form.notes.errors %}
                            <div class="form-error">{{ error }}</div>
                        {% endfor %}
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{% url 'customers:list' %}" class="form-button secondary">
                ← Cancel
            </a>
            <button type="submit" class="form-button primary">
                💾 {% if object %}Update Customer{% else %}Save Customer{% endif %}
            </button>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        setupCustomerForm();
    });

    function setupCustomerForm() {
        // Initialize clearance fields visibility
        const clearanceType = document.querySelector('.clearance-type-select');
        if (clearanceType) {
            toggleClearanceFields(clearanceType.value);
        }

        // Add currency validation to currency inputs
        document.querySelectorAll('.currency-input').forEach(input => {
            input.addEventListener('blur', function() {
                // Format to 2 decimal places on blur only
                const value = parseFloat(this.value);
                if (!isNaN(value) && value > 0) {
                    this.value = value.toFixed(2);
                }
            });

            // Allow typing large numbers without interference
            input.addEventListener('input', function() {
                // Remove any non-numeric characters except decimal point
                this.value = this.value.replace(/[^0-9.]/g, '');
            });
        });

        // Form validation feedback
        const form = document.querySelector('form');
        if (form) {
            form.addEventListener('submit', function(e) {
                const requiredFields = form.querySelectorAll('[required]');
                let isValid = true;

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('error');
                        isValid = false;
                    } else {
                        field.classList.remove('error');
                    }
                });

                if (!isValid) {
                    e.preventDefault();
                    showNotification('Please fill in all required fields', 'error');
                    hapticFeedback();
                }
            });
        }
    }

    function toggleClearanceFields(clearanceType) {
        // Hide all clearance fields
        document.querySelectorAll('.clearance-field').forEach(field => {
            field.classList.remove('active');
        });

        // Show the relevant field based on clearance type
        if (clearanceType === 'fixed_date') {
            document.getElementById('fixed-date-field').classList.add('active');
        } else if (clearanceType === 'weekly') {
            document.getElementById('weekly-field').classList.add('active');
        } else if (clearanceType === 'custom') {
            document.getElementById('custom-date-field').classList.add('active');
        }

        hapticFeedback();
    }
</script>
{% endblock %}