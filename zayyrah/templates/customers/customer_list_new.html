{% extends 'base/app_base.html' %}

{% block title %}Customers - CHANNAB{% endblock %}

{% block nav_customers %}active{% endblock %}
{% block bottom_nav_customers %}active{% endblock %}

{% block page_title %}Customers{% endblock %}
{% block breadcrumb_current %}Customers{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">Customers</span>
</nav>
{% endblock %}

{% block header_search %}
<div class="search-container">
    <form method="get" action="" style="display: inline;">
        <input type="search" name="q" value="{{ search_term }}" class="search-input" placeholder="Search customers..." id="searchInput">
    </form>
    <span class="search-icon">🔍</span>
</div>
{% endblock %}

{% block filter_bar %}
<!-- Customer Filter Bar - Top Position -->
<div class="customer-filter-bar fade-in">
    <div class="filter-controls">
        <div class="filter-dropdown-container">
            <select class="filter-dropdown" id="dateFilter">
                <option value="today">Today</option>
                <option value="7days">7 Days</option>
                <option value="month" selected>This Month</option>
                <option value="year">This Year</option>
                <option value="custom">Custom Range</option>
            </select>
            <div class="custom-date-range" id="customDateRange" style="display: none;">
                <input type="date" class="date-input" id="startDate" placeholder="Start Date">
                <span class="date-separator">to</span>
                <input type="date" class="date-input" id="endDate" placeholder="End Date">
                <button class="apply-range-btn" id="applyRange">Apply</button>
            </div>
        </div>
    </div>
    <div class="filter-actions">
        <button class="clear-filter-btn" id="clearFilters">Clear Filter</button>
        <a href="{% url 'customers:create' %}" class="add-customer-btn">➕ Add Customer</a>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* ==== CUSTOMER-SPECIFIC STYLES ==== */

    /* Customer Filter Bar */
    .customer-filter-bar {
        background: var(--white);
        border-radius: 12px;
        padding: 20px 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        margin: 0 24px 24px 24px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 24px;
        position: sticky;
        top: var(--header-height);
        z-index: 90;
    }

    .filter-controls {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .filter-dropdown-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .filter-dropdown {
        padding: 12px 16px;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        background: var(--white);
        color: var(--gray-700);
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all var(--transition-normal);
        outline: none;
        width: 180px;
        max-width: 180px;
    }

    .filter-dropdown:focus {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
    }

    .filter-dropdown:hover {
        border-color: var(--primary-green-lighter);
    }

    .custom-date-range {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        flex-wrap: wrap;
    }

    .date-input {
        padding: 10px 12px;
        border: 2px solid var(--gray-200);
        border-radius: 8px;
        background: var(--white);
        color: var(--gray-700);
        font-size: 0.9rem;
        transition: all var(--transition-normal);
        outline: none;
        min-width: 140px;
    }

    .date-input:focus {
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
    }

    .date-separator {
        color: var(--gray-500);
        font-weight: 500;
        font-size: 0.9rem;
    }

    .apply-range-btn {
        padding: 10px 16px;
        background: var(--primary-green-lighter);
        color: var(--primary-green);
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all var(--transition-normal);
    }

    .apply-range-btn:hover {
        background: var(--primary-green);
        color: white;
    }

    .filter-actions {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .clear-filter-btn {
        padding: 12px 20px;
        background: var(--white);
        border: 1px solid var(--gray-300);
        border-radius: 8px;
        color: var(--gray-600);
        font-size: 0.9rem;
        cursor: pointer;
        transition: all var(--transition-normal);
        font-weight: 500;
    }

    .clear-filter-btn:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
    }

    .add-customer-btn {
        padding: 12px 24px;
        background: var(--primary-green);
        color: white;
        border: none;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all var(--transition-normal);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        white-space: nowrap;
    }

    .add-customer-btn:hover {
        background: var(--primary-green-light);
        transform: translateY(-1px);
        box-shadow: var(--shadow-green);
    }

    /* Stats Section */
    .stats-section {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 24px;
        margin-bottom: 32px;
    }

    .stat-card {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        position: relative;
        overflow: hidden;
        cursor: pointer;
        transition: all var(--transition-slow);
    }

    .stat-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-green);
    }

    .stat-card.customers-stat {
        background: var(--primary-gradient);
        color: white;
        border: none;
    }

    .stat-card.sales-stat {
        background: var(--success-gradient);
        color: white;
        border: none;
    }

    .stat-card.balance-stat {
        background: var(--accent-gradient);
        color: var(--primary-green);
        border: none;
    }

    .stat-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 16px;
    }

    .stat-icon {
        width: 48px;
        height: 48px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        backdrop-filter: blur(10px);
    }

    .stat-card.balance-stat .stat-icon {
        background: rgba(30, 77, 59, 0.15);
    }

    .stat-content {
        flex: 1;
    }

    .stat-title {
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        opacity: 0.9;
        margin-bottom: 4px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        line-height: 1;
        margin-bottom: 8px;
    }

    .stat-details {
        font-size: 0.9rem;
        opacity: 0.8;
    }

    .stat-details span {
        display: block;
        margin-bottom: 2px;
    }

    /* Desktop Table View */
    .desktop-view {
        display: block;
    }

    .table-container {
        background: var(--white);
        border-radius: 12px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        overflow: hidden;
    }

    .customers-table {
        width: 100%;
        border-collapse: collapse;
    }

    .customers-table thead {
        background: var(--gray-50);
    }

    .customers-table th {
        padding: 16px 20px;
        text-align: left;
        font-weight: 600;
        color: var(--primary-green);
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        border-bottom: 1px solid var(--gray-200);
    }

    .customers-table td {
        padding: 16px 20px;
        border-bottom: 1px solid var(--gray-100);
        vertical-align: middle;
    }

    .customer-row {
        transition: background-color var(--transition-normal);
    }

    .customer-row:hover {
        background: var(--success-lighter);
    }

    .customer-cell {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .customer-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1rem;
    }

    .customer-name {
        font-weight: 600;
        color: var(--gray-800);
    }

    .contact-info .phone {
        font-weight: 500;
        color: var(--gray-700);
        margin-bottom: 4px;
    }

    .contact-info .balance {
        font-size: 0.9rem;
        color: var(--gray-500);
    }

    .milk-info .amount,
    .balance-info .amount {
        font-weight: 600;
        color: var(--primary-green);
    }

    .status-badge {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
    }

    .status-badge.active {
        background: var(--success-light);
        color: var(--primary-green);
    }

    .action-buttons-table {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid var(--gray-300);
        background: var(--white);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        transition: all var(--transition-normal);
        text-decoration: none;
        color: var(--gray-600);
    }

    .action-btn:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
    }

    .action-btn.delete-btn:hover {
        background: #fef2f2;
        border-color: var(--danger);
        color: var(--danger);
    }

    /* Mobile Card View */
    .mobile-view {
        display: none;
    }

    .customer-cards {
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .customer-card-mobile {
        background: var(--white);
        border-radius: 12px;
        padding: 16px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        transition: all var(--transition-slow);
    }

    .customer-card-mobile:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    .customer-info-mobile {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;
    }

    .customer-avatar-mobile {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.1rem;
        flex-shrink: 0;
    }

    .customer-details-mobile {
        flex: 1;
        min-width: 0;
    }

    .customer-name-mobile {
        font-size: 1rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 2px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .customer-phone-mobile {
        font-size: 0.85rem;
        color: var(--gray-600);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .card-actions-mobile {
        display: flex;
        gap: 8px;
        flex-shrink: 0;
    }

    .mobile-action-btn {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        border: 1px solid var(--gray-300);
        background: var(--white);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        transition: all var(--transition-normal);
        text-decoration: none;
        color: var(--gray-600);
    }

    .mobile-action-btn:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
        transform: scale(1.05);
    }

    .card-stats-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        padding: 12px;
        background: var(--gray-50);
        border-radius: 8px;
    }

    .stat-item-mobile {
        text-align: center;
    }

    .stat-label-mobile {
        display: block;
        font-size: 0.7rem;
        color: var(--gray-500);
        text-transform: uppercase;
        font-weight: 600;
        margin-bottom: 4px;
        letter-spacing: 0.05em;
    }

    .stat-value-mobile {
        display: block;
        font-weight: 600;
        color: var(--gray-800);
        font-size: 0.9rem;
    }

    .stat-value-mobile.sales-amount {
        color: var(--primary-green-lighter);
    }

    .stat-value-mobile.balance-amount {
        color: var(--primary-green);
    }

    /* Empty State */
    .empty-state-channab {
        text-align: center;
        padding: 80px 24px;
        background: var(--white);
        border-radius: 16px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .empty-icon {
        font-size: 4rem;
        margin-bottom: 24px;
        opacity: 0.5;
        color: var(--primary-green);
    }

    .empty-state-channab h2 {
        font-size: 1.5rem;
        color: var(--primary-green);
        margin-bottom: 12px;
    }

    .empty-state-channab p {
        color: var(--gray-600);
        margin-bottom: 32px;
        font-size: 1rem;
    }

    .add-first-customer-btn {
        padding: 14px 28px;
        background: var(--primary-green);
        color: white;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all var(--transition-normal);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-first-customer-btn:hover {
        background: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .customer-filter-bar {
            flex-direction: column;
            align-items: stretch;
            gap: 16px;
            padding: 16px;
            margin: 0 16px 16px 16px;
            position: static;
        }

        .filter-controls {
            width: 100%;
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
        }

        .filter-dropdown {
            width: 100%;
            max-width: unset;
        }

        .custom-date-range {
            flex-direction: column;
            gap: 12px;
            align-items: stretch;
        }

        .date-input {
            min-width: unset;
            width: 100%;
        }

        .filter-actions {
            flex-direction: column;
            gap: 12px;
            width: 100%;
        }

        .clear-filter-btn,
        .add-customer-btn {
            width: 100%;
            justify-content: center;
        }

        .stats-section {
            grid-template-columns: 1fr 1fr;
            grid-template-areas:
                "customers sales"
                "balance balance";
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card.customers-stat {
            grid-area: customers;
        }

        .stat-card.sales-stat {
            grid-area: sales;
        }

        .stat-card.balance-stat {
            grid-area: balance;
        }

        .stat-card {
            padding: 16px;
        }

        .stat-number {
            font-size: 1.5rem;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
        }

        .stat-title {
            font-size: 0.7rem;
        }

        .stat-details {
            font-size: 0.8rem;
        }

        .desktop-view {
            display: none;
        }

        .mobile-view {
            display: block;
        }

        .customer-card-mobile {
            padding: 14px;
        }

        .customer-avatar-mobile {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .customer-name-mobile {
            font-size: 0.95rem;
        }

        .customer-phone-mobile {
            font-size: 0.8rem;
        }

        .mobile-action-btn {
            width: 32px;
            height: 32px;
            font-size: 0.9rem;
        }

        .card-stats-row {
            padding: 10px;
            gap: 12px;
        }
    }

    @media (max-width: 480px) {
        .customer-card-mobile {
            padding: 12px;
        }

        .customer-info-mobile {
            gap: 8px;
        }

        .customer-avatar-mobile {
            width: 36px;
            height: 36px;
            font-size: 0.9rem;
        }

        .customer-name-mobile {
            font-size: 0.9rem;
        }

        .customer-phone-mobile {
            font-size: 0.75rem;
        }

        .mobile-action-btn {
            width: 28px;
            height: 28px;
            font-size: 0.8rem;
        }

        .card-stats-row {
            padding: 8px;
            gap: 8px;
        }

        .stat-label-mobile {
            font-size: 0.65rem;
        }

        .stat-value-mobile {
            font-size: 0.85rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Professional Stats Section -->
<div class="stats-section">
    <div class="stat-card customers-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
                <div class="stat-title">TOTAL CUSTOMERS</div>
                <div class="stat-number" id="totalCustomers">{{ customers.count|default:"0" }}</div>
                <div class="stat-details">
                    <span>Registered: {{ customers.count|default:"0" }}</span>
                    <span>Status: Active</span>
                </div>
            </div>
        </div>
    </div>

    <div class="stat-card sales-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
                <div class="stat-title">TOTAL SALES</div>
                <div class="stat-number">Rs. 26875</div>
                <div class="stat-details">
                    <span>Total Amount: Rs. 26875</span>
                    <span>Pending: Rs. 0</span>
                </div>
            </div>
        </div>
    </div>

    <div class="stat-card balance-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">⚠️</div>
            <div class="stat-content">
                <div class="stat-title">OUTSTANDING BALANCE</div>
                <div class="stat-number">Rs. -12845</div>
                <div class="stat-details">
                    <span>Pending Payment</span>
                    <span>Service in Demand</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customers Section -->
{% if customers %}
    <div id="customersSection" class="fade-in">
        <!-- Desktop Table View -->
        <div class="desktop-view">
            <div class="table-container">
                <table class="customers-table">
                    <thead>
                        <tr>
                            <th>Customer</th>
                            <th>Contact Info</th>
                            <th>Total Milk</th>
                            <th>Outstanding Balance</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="customersTableBody">
                        {% for customer in customers %}
                            <tr class="customer-row" data-name="{{ customer.name|default:'Unnamed customer' }}" data-phone="{{ customer.mobile_number|default:'N/A' }}">
                                <td class="customer-cell">
                                    <div class="customer-avatar">{{ customer.name|default:"U"|first|upper }}</div>
                                    <div class="customer-info">
                                        <div class="customer-name">{{ customer.name|default:'Unnamed customer' }}</div>
                                    </div>
                                </td>
                                <td class="contact-cell">
                                    <div class="contact-info">
                                        <div class="phone">{{ customer.mobile_number|default:"N/A" }}</div>
                                        <div class="balance">Rs. 0</div>
                                    </div>
                                </td>
                                <td class="milk-cell">
                                    <div class="milk-info">
                                        <div class="amount">0L</div>
                                    </div>
                                </td>
                                <td class="balance-cell">
                                    <div class="balance-info">
                                        <div class="amount">Rs. 0</div>
                                    </div>
                                </td>
                                <td class="status-cell">
                                    <span class="status-badge active">Active</span>
                                </td>
                                <td class="actions-cell">
                                    <div class="action-buttons-table">
                                        <a href="{% url 'customers:detail' customer.pk %}" class="action-btn view-btn">👁️</a>
                                        <a href="{% url 'customers:update' customer.pk %}" class="action-btn edit-btn">✏️</a>
                                        <button class="action-btn delete-btn" onclick="deleteCustomer('{{ customer.name|default:'customer' }}')">🗑️</button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Mobile Card View -->
        <div class="mobile-view">
            <div class="customer-cards" id="customerCardsMobile">
                {% for customer in customers %}
                    <div class="customer-card-mobile" data-name="{{ customer.name|default:'Unnamed customer' }}" data-phone="{{ customer.mobile_number|default:'N/A' }}">
                        <!-- First Row: Avatar + Name/Phone + Actions -->
                        <div class="card-header">
                            <div class="customer-info-mobile">
                                <div class="customer-avatar-mobile">{{ customer.name|default:"U"|first|upper }}</div>
                                <div class="customer-details-mobile">
                                    <div class="customer-name-mobile">{{ customer.name|default:'Unnamed customer' }}</div>
                                    <div class="customer-phone-mobile">{{ customer.mobile_number|default:"N/A" }}</div>
                                </div>
                            </div>
                            <div class="card-actions-mobile">
                                <a href="{% url 'customers:detail' customer.pk %}" class="mobile-action-btn view-btn-mobile">👁️</a>
                                <a href="{% url 'customers:update' customer.pk %}" class="mobile-action-btn edit-btn-mobile">✏️</a>
                                <button class="mobile-action-btn delete-btn-mobile" onclick="deleteCustomer('{{ customer.name|default:'customer' }}')">🗑️</button>
                            </div>
                        </div>

                        <!-- Second Row: Total Sales + Outstanding Balance -->
                        <div class="card-stats-row">
                            <div class="stat-item-mobile">
                                <span class="stat-label-mobile">Total Sales</span>
                                <span class="stat-value-mobile sales-amount">Rs. 0</span>
                            </div>
                            <div class="stat-item-mobile">
                                <span class="stat-label-mobile">Outstanding Balance</span>
                                <span class="stat-value-mobile balance-amount">Rs. 0</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% else %}
    <!-- Empty State -->
    <div class="empty-state-channab fade-in">
        <div class="empty-icon">👥</div>
        <h2>No customers yet</h2>
        <p>Add your first customer with just a name or mobile number. We'll fill in the details later.</p>
        <a href="{% url 'customers:create' %}" class="add-first-customer-btn">➕ Create customer</a>
    </div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    // Customer-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        setupCustomerFilters();
        setupCustomerSearch();
    });

    function setupCustomerFilters() {
        // Date filter dropdown
        const dateFilter = document.getElementById('dateFilter');
        const customDateRange = document.getElementById('customDateRange');

        if (dateFilter) {
            dateFilter.addEventListener('change', function() {
                if (this.value === 'custom') {
                    customDateRange.style.display = 'flex';
                } else {
                    customDateRange.style.display = 'none';
                    applyDateFilter(this.value);
                }
                hapticFeedback();
            });
        }

        // Apply custom date range
        const applyRange = document.getElementById('applyRange');
        if (applyRange) {
            applyRange.addEventListener('click', function() {
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                if (startDate && endDate) {
                    applyCustomDateRange(startDate, endDate);
                } else {
                    showNotification('Please select both start and end dates', 'warning');
                }
                hapticFeedback();
            });
        }

        // Clear filters
        const clearFilters = document.getElementById('clearFilters');
        if (clearFilters) {
            clearFilters.addEventListener('click', function() {
                clearAllFilters();
                hapticFeedback();
            });
        }
    }

    function setupCustomerSearch() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', handleCustomerSearch);
        }
    }

    function handleCustomerSearch(event) {
        const query = event.target.value.toLowerCase().trim();
        const rows = document.querySelectorAll('.customer-row');
        const cards = document.querySelectorAll('.customer-card-mobile');
        let visibleCount = 0;

        // Filter desktop table
        rows.forEach(row => {
            const name = row.getAttribute('data-name').toLowerCase();
            const phone = row.getAttribute('data-phone').toLowerCase();
            const isVisible = name.includes(query) || phone.includes(query);
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) visibleCount++;
        });

        // Filter mobile cards
        cards.forEach(card => {
            const name = card.getAttribute('data-name').toLowerCase();
            const phone = card.getAttribute('data-phone').toLowerCase();
            const isVisible = name.includes(query) || phone.includes(query);
            card.style.display = isVisible ? '' : 'none';
        });

        // Update stats
        updateSearchStats(visibleCount, query);
    }

    function updateSearchStats(count, query) {
        const totalCustomersEl = document.getElementById('totalCustomers');
        if (query) {
            totalCustomersEl.textContent = count;
        } else {
            totalCustomersEl.textContent = '{{ customers.count|default:"0" }}';
        }
    }

    function clearAllFilters() {
        const searchInput = document.getElementById('searchInput');
        const dateFilter = document.getElementById('dateFilter');
        const customDateRange = document.getElementById('customDateRange');

        if (searchInput) {
            searchInput.value = '';
            handleCustomerSearch({ target: { value: '' } });
        }

        if (dateFilter) {
            dateFilter.value = 'month';
            customDateRange.style.display = 'none';
            applyDateFilter('month');
        }
    }

    function applyDateFilter(period) {
        const filterPeriods = {
            'today': 'Today',
            '7days': 'Last 7 Days',
            'month': 'This Month',
            'year': 'This Year'
        };

        showNotification(`Showing customers for: ${filterPeriods[period]}`, 'success');
    }

    function applyCustomDateRange(startDate, endDate) {
        showNotification(`Showing customers from ${startDate} to ${endDate}`, 'success');
    }

    function deleteCustomer(name) {
        if (confirm(`Delete customer: ${name}?`)) {
            showNotification(`Customer ${name} would be deleted`, 'info');
            hapticFeedback();
        }
    }
</script>
{% endblock %}
