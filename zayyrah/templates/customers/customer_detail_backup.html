{% extends 'base/app_base.html' %}
{% load static %}

{% block title %}{{ customer }} · Customers · Zayyrah{% endblock %}
{% block body_class %}dashboard-page customers-page{% endblock %}
{% block extra_head %}
<link rel="stylesheet" href="{% static 'customers/css/customers.css' %}">
{% endblock %}

{% block content %}
<div class="customers-shell">
    <div class="collection-header">
        <div class="collection-title">
            <h1>{{ customer.name|default:customer.mobile_number|default:'Customer' }}</h1>
            <p>Created {{ customer.created_at|date:'M j, Y' }} · Last updated {{ customer.updated_at|date:'M j, Y' }}</p>
        </div>
        <div class="collection-actions">
            <a href="{% url 'customers:update' customer.pk %}" class="secondary-button">Edit details</a>
            <a href="{% url 'customers:list' %}" class="secondary-button">Back to list</a>
        </div>
    </div>

    <section class="customer-detail-card">
        <dl class="detail-meta">
            <div>
                <dt>Mobile</dt>
                <dd>{% if customer.mobile_number %}{{ customer.mobile_number }}{% else %}<span class="muted">Not provided</span>{% endif %}</dd>
            </div>
            <div>
                <dt>Email</dt>
                <dd>{% if customer.email %}{{ customer.email }}{% else %}<span class="muted">Not provided</span>{% endif %}</dd>
            </div>
            <div>
                <dt>Notes</dt>
                <dd>{% if customer.notes %}{{ customer.notes|linebreaksbr }}{% else %}<span class="muted">No notes yet</span>{% endif %}</dd>
            </div>
        </dl>
    </section>
</div>
{% endblock %}
