{% extends 'base/app_base.html' %}

{% block title %}{{ customer.name|default:customer.mobile_number|default:'Customer' }} - CHANNAB{% endblock %}

{% block nav_customers %}active{% endblock %}
{% block bottom_nav_customers %}active{% endblock %}

{% block page_title %}{{ customer.name|default:customer.mobile_number|default:'Customer' }}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'customers:list' %}" class="breadcrumb-item">Customers</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">{{ customer.name|default:customer.mobile_number|default:'Customer' }}</span>
</nav>
{% endblock %}

{% block header_actions %}
<a href="{% url 'customers:update' customer.pk %}" class="header-btn" title="Edit Customer">
    ✏️
</a>
<a href="{% url 'customers:list' %}" class="header-btn" title="Back to List">
    ←
</a>
{% endblock %}

{% block extra_css %}
<style>
    /* ==== ULTRA-MODERN CUSTOMER DETAIL STYLES ==== */

    /* Customer Profile Header */
    .customer-profile-header {
        background: var(--white);
        border-radius: 20px;
        padding: 32px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
        margin-bottom: 24px;
        position: relative;
        overflow: hidden;
    }

    .customer-profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: var(--primary-gradient);
    }

    .profile-main {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 24px;
    }

    .customer-avatar-large {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 2rem;
        flex-shrink: 0;
        border: 4px solid white;
        box-shadow: var(--shadow-lg);
    }

    .profile-info {
        flex: 1;
    }

    .customer-name-large {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 8px;
        line-height: 1.2;
    }

    .customer-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .meta-icon {
        font-size: 1rem;
        color: var(--primary-green);
    }

    .profile-actions {
        display: flex;
        gap: 12px;
        flex-shrink: 0;
    }

    .profile-action-btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        text-decoration: none;
        transition: all var(--transition-normal);
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        border: none;
    }

    .profile-action-btn.primary {
        background: var(--primary-green);
        color: white;
    }

    .profile-action-btn.primary:hover {
        background: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    .profile-action-btn.secondary {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .profile-action-btn.secondary:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
    }

    /* Quick Stats Cards */
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 32px;
    }

    .stat-card-small {
        background: var(--white);
        border-radius: 16px;
        padding: 20px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        transition: all var(--transition-normal);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .stat-card-small:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-green);
    }

    .stat-card-small.milk-stat {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: none;
    }

    .stat-card-small.sales-stat {
        background: var(--success-gradient);
        color: white;
        border: none;
    }

    .stat-card-small.balance-stat {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        border: none;
    }

    .stat-card-small.visits-stat {
        background: var(--primary-gradient);
        color: white;
        border: none;
    }

    .stat-header-small {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    .stat-icon-small {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stat-trend {
        font-size: 0.8rem;
        padding: 2px 8px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .stat-number-small {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 4px;
    }

    .stat-label-small {
        font-size: 0.85rem;
        opacity: 0.8;
        font-weight: 500;
    }

    /* Customer Details Grid */
    .details-grid {
        display: grid;
        grid-template-columns: 2fr 1fr;
        gap: 24px;
        margin-bottom: 32px;
    }

    .detail-section {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .section-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--gray-200);
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-icon {
        font-size: 1.2rem;
        color: var(--primary-green);
    }

    .contact-info-grid {
        display: grid;
        gap: 16px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: var(--gray-50);
        border-radius: 12px;
        transition: all var(--transition-normal);
    }

    .info-item:hover {
        background: var(--success-lighter);
    }

    .info-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: var(--primary-green);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.8rem;
        color: var(--gray-500);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
    }

    .info-value {
        font-size: 0.95rem;
        color: var(--gray-800);
        font-weight: 500;
    }

    .info-value.empty {
        color: var(--gray-400);
        font-style: italic;
    }

    .notes-section {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 16px;
        margin-top: 8px;
    }

    .notes-content {
        color: var(--gray-700);
        line-height: 1.6;
        font-size: 0.95rem;
    }

    .notes-empty {
        color: var(--gray-400);
        font-style: italic;
        text-align: center;
        padding: 20px;
    }

    /* Activity Timeline */
    .activity-timeline {
        position: relative;
    }

    .timeline-item {
        display: flex;
        gap: 16px;
        margin-bottom: 20px;
        position: relative;
    }

    .timeline-item:not(:last-child)::after {
        content: '';
        position: absolute;
        left: 20px;
        top: 40px;
        bottom: -20px;
        width: 2px;
        background: var(--gray-200);
    }

    .timeline-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--primary-green);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        flex-shrink: 0;
        position: relative;
        z-index: 1;
    }

    .timeline-content {
        flex: 1;
        background: var(--gray-50);
        border-radius: 12px;
        padding: 16px;
    }

    .timeline-title {
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 4px;
    }

    .timeline-time {
        font-size: 0.8rem;
        color: var(--gray-500);
        margin-bottom: 8px;
    }

    .timeline-description {
        font-size: 0.9rem;
        color: var(--gray-600);
        line-height: 1.5;
    }

    /* Action Buttons */
    .customer-actions {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        margin-bottom: 24px;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }

    .action-card {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        transition: all var(--transition-normal);
        cursor: pointer;
        border: 1px solid var(--gray-200);
        text-decoration: none;
        color: inherit;
    }

    .action-card:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .action-icon {
        font-size: 1.5rem;
        margin-bottom: 8px;
        color: var(--primary-green);
    }

    .action-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 4px;
    }

    .action-description {
        font-size: 0.8rem;
        color: var(--gray-600);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .customer-profile-header {
            padding: 20px;
        }

        .profile-main {
            flex-direction: column;
            text-align: center;
            gap: 16px;
        }

        .customer-avatar-large {
            width: 64px;
            height: 64px;
            font-size: 1.5rem;
        }

        .customer-name-large {
            font-size: 1.5rem;
        }

        .customer-meta {
            justify-content: center;
            gap: 16px;
        }

        .profile-actions {
            justify-content: center;
            flex-wrap: wrap;
        }

        .quick-stats {
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .details-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .detail-section {
            padding: 16px;
        }

        .actions-grid {
            grid-template-columns: repeat(2, 1fr);
        }

        .stat-card-small {
            padding: 16px;
        }

        .stat-number-small {
            font-size: 1.5rem;
        }
    }

    @media (max-width: 480px) {
        .customer-profile-header {
            padding: 16px;
        }

        .customer-name-large {
            font-size: 1.3rem;
        }

        .quick-stats {
            grid-template-columns: 1fr;
        }

        .customer-meta {
            flex-direction: column;
            gap: 8px;
        }

        .profile-actions {
            flex-direction: column;
        }

        .profile-action-btn {
            justify-content: center;
        }

        .actions-grid {
            grid-template-columns: 1fr;
        }
    }

    /* Dark Theme Support */
    .dark-theme .customer-profile-header,
    .dark-theme .detail-section,
    .dark-theme .customer-actions {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }

    .dark-theme .customer-name-large,
    .dark-theme .section-title,
    .dark-theme .info-value,
    .dark-theme .timeline-title,
    .dark-theme .action-title {
        color: var(--gray-100);
    }

    .dark-theme .info-item,
    .dark-theme .timeline-content,
    .dark-theme .action-card,
    .dark-theme .notes-section {
        background: var(--gray-700);
        border-color: var(--gray-600);
    }

    .dark-theme .action-card:hover {
        background: rgba(30, 77, 59, 0.2);
        border-color: var(--accent-green);
    }
</style>
{% endblock %}

{% block content %}
<!-- Ultra-Modern Customer Profile Header -->
<div class="customer-profile-header fade-in">
    <div class="profile-main">
        <div class="customer-avatar-large">
            {{ customer.name|default:customer.mobile_number|default:"C"|first|upper }}
        </div>
        <div class="profile-info">
            <h1 class="customer-name-large">{{ customer.name|default:customer.mobile_number|default:'Unnamed Customer' }}</h1>
            <div class="customer-meta">
                <div class="meta-item">
                    <span class="meta-icon">📅</span>
                    <span>Created {{ customer.created_at|date:'M j, Y' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-icon">📝</span>
                    <span>Updated {{ customer.updated_at|date:'M j, Y' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-icon">✅</span>
                    <span>Active Customer</span>
                </div>
            </div>
        </div>
        <div class="profile-actions">
            <a href="{% url 'customers:update' customer.pk %}" class="profile-action-btn primary">
                ✏️ Edit Details
            </a>
            <button class="profile-action-btn secondary" onclick="shareCustomer()">
                📤 Share
            </button>
        </div>
    </div>
</div>

<!-- Quick Stats Section -->
<div class="quick-stats fade-in">
    <div class="stat-card-small milk-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">🥛</div>
            <div class="stat-trend">+12%</div>
        </div>
        <div class="stat-number-small">0L</div>
        <div class="stat-label-small">Total Milk Purchased</div>
    </div>

    <div class="stat-card-small sales-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">💰</div>
            <div class="stat-trend">+8%</div>
        </div>
        <div class="stat-number-small">Rs. 0</div>
        <div class="stat-label-small">Total Sales</div>
    </div>

    <div class="stat-card-small balance-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">⚖️</div>
            <div class="stat-trend">-5%</div>
        </div>
        <div class="stat-number-small">Rs. 0</div>
        <div class="stat-label-small">Outstanding Balance</div>
    </div>

    <div class="stat-card-small visits-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">📊</div>
            <div class="stat-trend">+15%</div>
        </div>
        <div class="stat-number-small">0</div>
        <div class="stat-label-small">Total Transactions</div>
    </div>
</div>

<!-- Customer Details Grid -->
<div class="details-grid fade-in">
    <!-- Contact Information -->
    <div class="detail-section">
        <div class="section-header">
            <h2 class="section-title">
                <span class="section-icon">📞</span>
                Contact Information
            </h2>
        </div>

        <div class="contact-info-grid">
            <div class="info-item">
                <div class="info-icon">📱</div>
                <div class="info-content">
                    <div class="info-label">Mobile Number</div>
                    <div class="info-value {% if not customer.mobile_number %}empty{% endif %}">
                        {% if customer.mobile_number %}
                            {{ customer.mobile_number }}
                        {% else %}
                            Not provided
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">📧</div>
                <div class="info-content">
                    <div class="info-label">Email Address</div>
                    <div class="info-value {% if not customer.email %}empty{% endif %}">
                        {% if customer.email %}
                            {{ customer.email }}
                        {% else %}
                            Not provided
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="info-item">
                <div class="info-icon">🏠</div>
                <div class="info-content">
                    <div class="info-label">Address</div>
                    <div class="info-value empty">Not provided</div>
                </div>
            </div>
        </div>

        <!-- Notes Section -->
        <div class="section-header" style="margin-top: 24px;">
            <h3 class="section-title">
                <span class="section-icon">📝</span>
                Notes
            </h3>
        </div>

        <div class="notes-section">
            {% if customer.notes %}
                <div class="notes-content">{{ customer.notes|linebreaksbr }}</div>
            {% else %}
                <div class="notes-empty">No notes added yet</div>
            {% endif %}
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="detail-section">
        <div class="section-header">
            <h2 class="section-title">
                <span class="section-icon">📈</span>
                Recent Activity
            </h2>
        </div>

        <div class="activity-timeline">
            <div class="timeline-item">
                <div class="timeline-icon">👤</div>
                <div class="timeline-content">
                    <div class="timeline-title">Customer Created</div>
                    <div class="timeline-time">{{ customer.created_at|date:'M j, Y g:i A' }}</div>
                    <div class="timeline-description">Customer profile was created in the system</div>
                </div>
            </div>

            {% if customer.updated_at != customer.created_at %}
            <div class="timeline-item">
                <div class="timeline-icon">📝</div>
                <div class="timeline-content">
                    <div class="timeline-title">Profile Updated</div>
                    <div class="timeline-time">{{ customer.updated_at|date:'M j, Y g:i A' }}</div>
                    <div class="timeline-description">Customer information was last updated</div>
                </div>
            </div>
            {% endif %}

            <div class="timeline-item">
                <div class="timeline-icon">💰</div>
                <div class="timeline-content">
                    <div class="timeline-title">First Purchase</div>
                    <div class="timeline-time">Coming soon</div>
                    <div class="timeline-description">Waiting for first transaction</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Actions -->
<div class="customer-actions fade-in">
    <div class="section-header">
        <h2 class="section-title">
            <span class="section-icon">⚡</span>
            Quick Actions
        </h2>
    </div>

    <div class="actions-grid">
        <a href="{% url 'customers:update' customer.pk %}" class="action-card">
            <div class="action-icon">✏️</div>
            <div class="action-title">Edit Details</div>
            <div class="action-description">Update customer information</div>
        </a>

        <div class="action-card" onclick="addTransaction()">
            <div class="action-icon">💳</div>
            <div class="action-title">New Sale</div>
            <div class="action-description">Record a new transaction</div>
        </div>

        <div class="action-card" onclick="viewTransactions()">
            <div class="action-icon">📊</div>
            <div class="action-title">View History</div>
            <div class="action-description">See all transactions</div>
        </div>

        <div class="action-card" onclick="sendMessage()">
            <div class="action-icon">💬</div>
            <div class="action-title">Send Message</div>
            <div class="action-description">Contact customer</div>
        </div>

        <div class="action-card" onclick="generateReport()">
            <div class="action-icon">📋</div>
            <div class="action-title">Generate Report</div>
            <div class="action-description">Customer summary report</div>
        </div>

        <div class="action-card" onclick="archiveCustomer()">
            <div class="action-icon">📦</div>
            <div class="action-title">Archive</div>
            <div class="action-description">Archive customer profile</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Customer detail functionality
    document.addEventListener('DOMContentLoaded', function() {
        setupCustomerDetailPage();
    });

    function setupCustomerDetailPage() {
        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card-small').forEach(card => {
            card.addEventListener('click', function() {
                const type = this.classList.contains('milk-stat') ? 'milk' :
                           this.classList.contains('sales-stat') ? 'sales' :
                           this.classList.contains('balance-stat') ? 'balance' : 'visits';

                showNotification(`Opening ${type} details...`, 'info');
                hapticFeedback();
            });
        });

        // Contact info click actions
        document.querySelectorAll('.info-item').forEach(item => {
            item.addEventListener('click', function() {
                const icon = this.querySelector('.info-icon').textContent;
                if (icon === '📱') {
                    const phone = this.querySelector('.info-value').textContent.trim();
                    if (phone && phone !== 'Not provided') {
                        if (confirm(`Call ${phone}?`)) {
                            window.open(`tel:${phone}`);
                        }
                    }
                } else if (icon === '📧') {
                    const email = this.querySelector('.info-value').textContent.trim();
                    if (email && email !== 'Not provided') {
                        if (confirm(`Send email to ${email}?`)) {
                            window.open(`mailto:${email}`);
                        }
                    }
                }
                hapticFeedback();
            });
        });
    }

    function shareCustomer() {
        const customerName = '{{ customer.name|default:customer.mobile_number|default:"Customer" }}';
        const shareData = {
            title: `${customerName} - Customer Profile`,
            text: `Customer profile for ${customerName}`,
            url: window.location.href
        };

        if (navigator.share) {
            navigator.share(shareData).then(() => {
                showNotification('Customer profile shared successfully!', 'success');
            }).catch(console.error);
        } else {
            // Fallback to copy link
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('Customer profile link copied to clipboard!', 'success');
            });
        }
        hapticFeedback();
    }

    function addTransaction() {
        showNotification('Opening new transaction form...', 'info');
        // Navigate to POS or transaction form
        setTimeout(() => {
            window.location.href = '{% url "pos:index" %}';
        }, 1000);
        hapticFeedback();
    }

    function viewTransactions() {
        showNotification('Loading transaction history...', 'info');
        // Navigate to transaction history
        hapticFeedback();
    }

    function sendMessage() {
        const phone = '{{ customer.mobile_number }}';
        if (phone) {
            const message = encodeURIComponent('Hello {{ customer.name|default:"there" }}, this is CHANNAB Farm.');
            if (confirm('Send WhatsApp message?')) {
                window.open(`https://wa.me/${phone}?text=${message}`);
            }
        } else {
            showNotification('No phone number available', 'warning');
        }
        hapticFeedback();
    }

    function generateReport() {
        showNotification('Generating customer report...', 'info');
        // Generate and download report
        setTimeout(() => {
            showNotification('Report generated successfully!', 'success');
        }, 2000);
        hapticFeedback();
    }

    function archiveCustomer() {
        const customerName = '{{ customer.name|default:customer.mobile_number|default:"this customer" }}';
        if (confirm(`Archive ${customerName}? This will hide them from the active customer list.`)) {
            showNotification(`${customerName} has been archived`, 'success');
            setTimeout(() => {
                window.location.href = '{% url "customers:list" %}';
            }, 1500);
        }
        hapticFeedback();
    }
</script>
{% endblock %}
