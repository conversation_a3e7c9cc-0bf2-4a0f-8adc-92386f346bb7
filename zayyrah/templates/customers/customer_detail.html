{% extends 'base/app_base.html' %}

{% block title %}{{ customer.name|default:customer.mobile_number|default:'Customer' }} - CHANNAB{% endblock %}

{% block nav_customers %}active{% endblock %}
{% block bottom_nav_customers %}active{% endblock %}

{% block page_title %}{{ customer.name|default:customer.mobile_number|default:'Customer' }}{% endblock %}

{% block breadcrumb %}
<nav class="breadcrumb">
    <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
    <span class="breadcrumb-separator">›</span>
    <a href="{% url 'customers:list' %}" class="breadcrumb-item">Customers</a>
    <span class="breadcrumb-separator">›</span>
    <span class="breadcrumb-item active">{{ customer.name|default:customer.mobile_number|default:'Customer' }}</span>
</nav>
{% endblock %}

{% block header_actions %}
<a href="{% url 'customers:update' customer.pk %}" class="header-btn" title="Edit Customer">
    ✏️
</a>
<a href="{% url 'customers:list' %}" class="header-btn" title="Back to List">
    ←
</a>
{% endblock %}

{% load static %}

{% block extra_css %}
<meta name="csrf-token" content="{{ csrf_token }}">
<style>
    /* ==== ULTRA-MODERN CUSTOMER DETAIL STYLES ==== */

    /* Global mobile overflow prevention */
    @media (max-width: 768px) {
        html, body {
            overflow-x: hidden !important;
            max-width: 100vw !important;
        }

        .main-content, .content-wrapper {
            max-width: 100% !important;
            overflow-x: hidden !important;
            box-sizing: border-box !important;
        }
    }

    /* Customer Profile Header */
    .customer-profile-header {
        background: var(--white);
        border-radius: 20px;
        padding: 32px;
        box-shadow: var(--shadow-lg);
        border: 1px solid var(--gray-200);
        margin-bottom: 24px;
        position: relative;
        overflow: hidden;
    }

    .customer-profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 6px;
        background: var(--primary-gradient);
    }

    .profile-main {
        display: flex;
        align-items: center;
        gap: 24px;
        margin-bottom: 24px;
    }

    .customer-avatar-large {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: var(--success-gradient);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 700;
        font-size: 2rem;
        flex-shrink: 0;
        border: 4px solid white;
        box-shadow: var(--shadow-lg);
    }

    .profile-info {
        flex: 1;
    }

    .customer-name-large {
        font-size: 2rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 8px;
        line-height: 1.2;
    }

    .customer-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 24px;
        color: var(--gray-600);
        font-size: 0.9rem;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .meta-icon {
        font-size: 1rem;
        color: var(--primary-green);
    }

    .profile-actions {
        display: flex;
        gap: 12px;
        flex-shrink: 0;
    }

    .profile-action-btn {
        padding: 12px 20px;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        text-decoration: none;
        transition: all var(--transition-normal);
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        border: none;
    }

    .profile-action-btn.primary {
        background: var(--primary-green);
        color: white;
    }

    .profile-action-btn.primary:hover {
        background: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    .profile-action-btn.secondary {
        background: var(--gray-100);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .profile-action-btn.secondary:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        color: var(--primary-green);
    }

    /* Quick Stats Cards */
    .quick-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 32px;
    }

    .stat-card-small {
        background: var(--white);
        border-radius: 16px;
        padding: 20px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        transition: all var(--transition-normal);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .stat-card-small:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-green);
    }

    .stat-card-small.milk-stat {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border: none;
    }

    .stat-card-small.sales-stat {
        background: var(--success-gradient);
        color: white;
        border: none;
    }

    .stat-card-small.balance-stat {
        background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
        border: none;
    }

    .stat-card-small.visits-stat {
        background: var(--primary-gradient);
        color: white;
        border: none;
    }

    .stat-header-small {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
    }

    .stat-icon-small {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stat-trend {
        font-size: 0.8rem;
        padding: 2px 8px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
    }

    .stat-number-small {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 4px;
    }

    .stat-label-small {
        font-size: 0.85rem;
        opacity: 0.8;
        font-weight: 500;
    }

    /* Tab Navigation */
    .tab-navigation {
        display: flex;
        background: var(--white);
        border-radius: 16px;
        padding: 8px;
        margin-bottom: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        gap: 8px;
    }

    .tab-button {
        flex: 1;
        padding: 12px 20px;
        border: none;
        background: transparent;
        border-radius: 12px;
        font-weight: 600;
        font-size: 0.9rem;
        color: var(--gray-600);
        cursor: pointer;
        transition: all var(--transition-normal);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .tab-button.active {
        background: var(--primary-green);
        color: white;
        box-shadow: var(--shadow-green);
    }

    .tab-button:hover:not(.active) {
        background: var(--gray-100);
        color: var(--primary-green);
    }

    .tab-icon {
        font-size: 1rem;
    }

    /* Tab Content */
    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
        animation: fadeInUp 0.3s ease-out;
    }

    /* Customer Details Grid */
    .details-grid {
        display: grid;
        grid-template-columns: 1fr;
        gap: 24px;
        margin-bottom: 32px;
    }

    .detail-section {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .section-header {
        display: flex;
        align-items: center;
        justify-content: between;
        margin-bottom: 20px;
        padding-bottom: 12px;
        border-bottom: 1px solid var(--gray-200);
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .section-icon {
        font-size: 1.2rem;
        color: var(--primary-green);
    }

    .contact-info-grid {
        display: grid;
        gap: 16px;
    }

    .info-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background: var(--gray-50);
        border-radius: 12px;
        transition: all var(--transition-normal);
    }

    .info-item:hover {
        background: var(--success-lighter);
    }

    .info-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        background: var(--primary-green);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-size: 0.8rem;
        color: var(--gray-500);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
    }

    .info-value {
        font-size: 0.95rem;
        color: var(--gray-800);
        font-weight: 500;
    }

    .info-value.empty {
        color: var(--gray-400);
        font-style: italic;
    }

    .notes-section {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 16px;
        margin-top: 8px;
    }

    .notes-content {
        color: var(--gray-700);
        line-height: 1.6;
        font-size: 0.95rem;
    }

    .notes-empty {
        color: var(--gray-400);
        font-style: italic;
        text-align: center;
        padding: 20px;
    }

    /* Sales List */
    .sales-list {
        background: var(--white);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
    }

    .sales-header {
        padding: 20px 24px;
        background: var(--gray-50);
        border-bottom: 1px solid var(--gray-200);
        display: flex;
        align-items: center;
        justify-content: between;
    }

    .sales-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--gray-800);
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .sales-empty {
        padding: 60px 20px;
        text-align: center;
        color: var(--gray-500);
    }

    .sales-empty-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .sales-empty-text {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .sales-empty-description {
        font-size: 0.9rem;
        margin-bottom: 24px;
    }

    .sales-item {
        padding: 16px 24px;
        border-bottom: 1px solid var(--gray-100);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all var(--transition-normal);
        cursor: pointer;
    }

    .sales-item:hover {
        background: var(--gray-50);
    }

    .sales-item:last-child {
        border-bottom: none;
    }

    .sale-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: var(--success-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .sale-details {
        flex: 1;
    }

    .sale-date {
        font-size: 0.8rem;
        color: var(--gray-500);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 4px;
    }

    .sale-amount {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 2px;
    }

    .sale-items {
        font-size: 0.85rem;
        color: var(--gray-600);
    }

    .sale-status {
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .sale-status.paid {
        background: var(--success-lighter);
        color: var(--primary-green);
    }

    .sale-status.pending {
        background: #fff3cd;
        color: #856404;
    }

    /* Action Buttons */
    .customer-actions {
        background: var(--white);
        border-radius: 16px;
        padding: 24px;
        box-shadow: var(--shadow);
        border: 1px solid var(--gray-200);
        margin-bottom: 24px;
    }

    .actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 12px;
    }

    .action-card {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 16px;
        text-align: center;
        transition: all var(--transition-normal);
        cursor: pointer;
        border: 1px solid var(--gray-200);
        text-decoration: none;
        color: inherit;
    }

    .action-card:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
        transform: translateY(-2px);
        box-shadow: var(--shadow);
    }

    .action-icon {
        font-size: 1.5rem;
        margin-bottom: 8px;
        color: var(--primary-green);
    }

    .action-title {
        font-size: 0.9rem;
        font-weight: 600;
        color: var(--gray-800);
        margin-bottom: 4px;
    }

    .action-description {
        font-size: 0.8rem;
        color: var(--gray-600);
    }

    /* Mobile Responsive Design - No Horizontal Scroll */
    @media (max-width: 768px) {
        /* Ensure no overflow */
        body, html {
            overflow-x: hidden;
            max-width: 100vw;
        }

        .main-content {
            padding: 8px !important;
            max-width: 100%;
            box-sizing: border-box;
        }

        .customer-profile-header {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 12px;
            position: relative;
            max-width: 100%;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .profile-main {
            display: block;
            text-align: left;
            margin-bottom: 8px;
        }

        .customer-avatar-large {
            width: 44px;
            height: 44px;
            font-size: 1.1rem;
            border-radius: 8px;
            border: 2px solid white;
            float: left;
            margin-right: 12px;
            margin-bottom: 8px;
        }

        .profile-info {
            overflow: hidden;
            padding-top: 2px;
        }

        .customer-name-large {
            font-size: 1rem;
            margin-bottom: 4px;
            line-height: 1.2;
            word-break: break-word;
        }

        .customer-meta {
            display: block;
            font-size: 0.75rem;
            line-height: 1.3;
            clear: both;
            margin-top: 8px;
        }

        .meta-item {
            display: inline-block;
            margin-right: 12px;
            margin-bottom: 2px;
            gap: 3px;
        }

        .profile-actions {
            position: absolute;
            top: 6px;
            right: 6px;
            display: flex;
            gap: 3px;
            max-width: 80px;
        }

        .profile-action-btn {
            padding: 4px 6px;
            font-size: 0.65rem;
            min-width: 32px;
            max-width: 36px;
            white-space: nowrap;
            overflow: hidden;
            border-radius: 6px;
        }

        .profile-action-btn span {
            display: none;
        }

        /* Ultra Compact Stats - Single Row on Mobile */
        .quick-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6px;
            margin-bottom: 12px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .stat-card-small {
            padding: 6px;
            border-radius: 6px;
            min-height: unset;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .stat-header-small {
            margin-bottom: 4px;
            flex-direction: column;
            align-items: flex-start;
            gap: 2px;
        }

        .stat-icon-small {
            width: 24px;
            height: 24px;
            font-size: 0.9rem;
            margin-bottom: 2px;
        }

        .stat-trend {
            font-size: 0.6rem;
            padding: 1px 4px;
            align-self: flex-end;
        }

        .stat-number-small {
            font-size: 1.1rem;
            margin-bottom: 2px;
            line-height: 1;
        }

        .stat-label-small {
            font-size: 0.65rem;
            line-height: 1.1;
        }

        /* Tab Navigation Mobile */
        .tab-navigation {
            padding: 4px;
            margin-bottom: 12px;
            border-radius: 8px;
            gap: 4px;
        }

        .tab-button {
            padding: 8px 12px;
            font-size: 0.8rem;
            border-radius: 6px;
            gap: 4px;
        }

        .tab-icon {
            font-size: 0.9rem;
        }

        /* Stack all sections vertically */
        .details-grid {
            display: block;
            width: 100%;
            max-width: 100%;
        }

        .detail-section {
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 12px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .section-header {
            margin-bottom: 12px;
            padding-bottom: 6px;
        }

        .section-title {
            font-size: 0.9rem;
        }

        .info-item {
            padding: 6px;
            border-radius: 6px;
            gap: 6px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .info-icon {
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
            flex-shrink: 0;
        }

        .info-label {
            font-size: 0.6rem;
        }

        .info-value {
            font-size: 0.75rem;
            word-break: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }

        .timeline-item {
            gap: 8px;
            margin-bottom: 12px;
        }

        .timeline-icon {
            width: 28px;
            height: 28px;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .timeline-content {
            padding: 8px;
            border-radius: 6px;
        }

        .timeline-title {
            font-size: 0.8rem;
        }

        .timeline-time {
            font-size: 0.65rem;
        }

        .timeline-description {
            font-size: 0.75rem;
        }

        .customer-actions {
            padding: 10px;
            margin-bottom: 12px;
            border-radius: 8px;
            width: 100%;
            max-width: 100%;
            box-sizing: border-box;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 4px;
            width: 100%;
            max-width: 100%;
        }

        .action-card {
            padding: 6px 2px;
            border-radius: 6px;
            min-height: 55px;
            max-height: 65px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            box-sizing: border-box;
            overflow: hidden;
        }

        .action-icon {
            font-size: 1.1rem;
            margin-bottom: 4px;
        }

        .action-title {
            font-size: 0.65rem;
            margin-bottom: 0;
            text-align: center;
            line-height: 1.1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
        }

        .action-description {
            display: none;
        }

        .notes-section {
            padding: 8px;
            border-radius: 6px;
        }

        .notes-content {
            font-size: 0.8rem;
        }

        /* Sales List Mobile */
        .sales-list {
            border-radius: 8px;
        }

        .sales-header {
            padding: 12px;
        }

        .sales-title {
            font-size: 0.9rem;
        }

        .sales-empty {
            padding: 40px 12px;
        }

        .sales-empty-icon {
            font-size: 2rem;
            margin-bottom: 12px;
        }

        .sales-empty-text {
            font-size: 0.9rem;
            margin-bottom: 6px;
        }

        .sales-empty-description {
            font-size: 0.8rem;
            margin-bottom: 16px;
        }

        .sales-item {
            padding: 12px;
            gap: 12px;
        }

        .sale-icon {
            width: 36px;
            height: 36px;
            font-size: 1rem;
            border-radius: 8px;
        }

        .sale-date {
            font-size: 0.7rem;
            margin-bottom: 3px;
        }

        .sale-amount {
            font-size: 0.9rem;
            margin-bottom: 2px;
        }

        .sale-items {
            font-size: 0.75rem;
        }

        .sale-status {
            padding: 3px 8px;
            font-size: 0.65rem;
        }
    }

    /* Additional mobile fixes for extreme small screens */
    @media (max-width: 480px) {
        /* Ensure complete containment */
        * {
            max-width: 100% !important;
            box-sizing: border-box !important;
        }

        .main-content {
            padding: 6px !important;
        }

        .customer-profile-header {
            padding: 8px;
        }

        .customer-avatar-large {
            width: 40px;
            height: 40px;
            font-size: 1rem;
            margin-right: 10px;
        }

        .customer-name-large {
            font-size: 0.95rem;
        }

        .customer-meta {
            font-size: 0.7rem;
        }

        .meta-item {
            margin-right: 8px;
        }

        .profile-action-btn {
            padding: 4px 6px;
            font-size: 0.65rem;
        }

        .quick-stats {
            grid-template-columns: 1fr 1fr;
            gap: 6px;
        }

        .stat-card-small {
            padding: 6px;
        }

        .stat-icon-small {
            width: 20px;
            height: 20px;
            font-size: 0.8rem;
        }

        .stat-number-small {
            font-size: 1rem;
        }

        .stat-label-small {
            font-size: 0.6rem;
        }

        .stat-trend {
            font-size: 0.55rem;
        }

        .detail-section {
            padding: 10px;
        }

        .section-title {
            font-size: 0.85rem;
        }

        .info-item {
            padding: 6px;
        }

        .info-icon {
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
        }

        .info-label {
            font-size: 0.6rem;
        }

        .info-value {
            font-size: 0.75rem;
        }

        .timeline-icon {
            width: 24px;
            height: 24px;
            font-size: 0.75rem;
        }

        .timeline-content {
            padding: 6px;
        }

        .timeline-title {
            font-size: 0.75rem;
        }

        .timeline-time {
            font-size: 0.6rem;
        }

        .timeline-description {
            font-size: 0.7rem;
        }

        .customer-actions {
            padding: 10px;
        }

        .actions-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 4px;
        }

        .action-card {
            padding: 6px 2px;
            min-height: 50px;
        }

        .action-icon {
            font-size: 1rem;
            margin-bottom: 3px;
        }

        .action-title {
            font-size: 0.65rem;
        }

        .notes-section {
            padding: 6px;
        }

        .notes-content {
            font-size: 0.75rem;
        }
    }

    /* Dark Theme Support */
    .dark-theme .customer-profile-header,
    .dark-theme .detail-section,
    .dark-theme .customer-actions {
        background: var(--gray-800);
        border-color: var(--gray-700);
    }

    .dark-theme .customer-name-large,
    .dark-theme .section-title,
    .dark-theme .info-value,
    .dark-theme .timeline-title,
    .dark-theme .action-title {
        color: var(--gray-100);
    }

    .dark-theme .info-item,
    .dark-theme .timeline-content,
    .dark-theme .action-card,
    .dark-theme .notes-section {
        background: var(--gray-700);
        border-color: var(--gray-600);
    }

    .dark-theme .action-card:hover {
        background: rgba(30, 77, 59, 0.2);
        border-color: var(--accent-green);
    }

    /* Transaction Detail Actions */
    .sale-status-container {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;
    }

    .sale-actions {
        display: flex;
        gap: 8px;
    }

    .view-detail-btn, .print-invoice-btn {
        background: #f1f5f9;
        border: 1px solid #e2e8f0;
        border-radius: 6px;
        padding: 6px 8px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
    }

    .view-detail-btn:hover {
        background: #e0f2fe;
        border-color: #0284c7;
        color: #0284c7;
    }

    .print-invoice-btn:hover {
        background: #f3e8ff;
        border-color: #7c3aed;
        color: #7c3aed;
    }

    /* Transaction Detail Modal */
    .transaction-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        display: none;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .transaction-modal-content {
        background: white;
        border-radius: 16px;
        max-width: 600px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .modal-header {
        padding: 24px;
        border-bottom: 1px solid #e2e8f0;
        background: #f8fafc;
        border-radius: 16px 16px 0 0;
        position: relative;
    }

    .modal-title {
        font-size: 1.4rem;
        font-weight: 600;
        color: #374151;
        margin: 0;
    }

    .modal-close {
        position: absolute;
        top: 20px;
        right: 20px;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #64748b;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: all 0.2s;
    }

    .modal-close:hover {
        background: #e2e8f0;
        color: #374151;
    }

    .modal-body {
        padding: 24px;
    }

    .transaction-info {
        background: #f8fafc;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 24px;
        border: 1px solid #e2e8f0;
    }

    .transaction-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
    }

    .info-item {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }

    .info-label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #64748b;
        text-transform: uppercase;
        letter-spacing: 0.025em;
    }

    .info-value {
        font-size: 1rem;
        font-weight: 600;
        color: #374151;
    }

    .transaction-number {
        font-family: 'Courier New', monospace;
        background: #e2e8f0;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 0.9rem;
    }

    .items-section {
        margin-bottom: 24px;
    }

    .section-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 16px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .items-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
    }

    .items-table th {
        background: #f8fafc;
        padding: 12px;
        text-align: left;
        font-weight: 600;
        color: #374151;
        border-bottom: 1px solid #e2e8f0;
        font-size: 0.875rem;
    }

    .items-table td {
        padding: 12px;
        border-bottom: 1px solid #f1f5f9;
        color: #64748b;
        font-size: 0.875rem;
    }

    .items-table tr:last-child td {
        border-bottom: none;
    }

    .price-breakdown {
        background: #f8fafc;
        border-radius: 12px;
        padding: 20px;
        border: 1px solid #e2e8f0;
    }

    .breakdown-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #e2e8f0;
    }

    .breakdown-row:last-child {
        border-bottom: none;
        font-weight: 700;
        font-size: 1.1rem;
        color: #0da487;
        padding-top: 12px;
        margin-top: 8px;
        border-top: 2px solid #e2e8f0;
    }

    .breakdown-label {
        color: #64748b;
        font-weight: 500;
    }

    .breakdown-value {
        color: #374151;
        font-weight: 600;
    }

    .modal-actions {
        padding: 20px 24px;
        border-top: 1px solid #e2e8f0;
        background: #f8fafc;
        display: flex;
        gap: 12px;
        justify-content: flex-end;
    }

    .modal-btn {
        padding: 10px 20px;
        border-radius: 8px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
        font-size: 0.9rem;
    }

    .modal-btn-primary {
        background: #0da487;
        color: white;
    }

    .modal-btn-primary:hover {
        background: #0891b2;
    }

    .modal-btn-secondary {
        background: #6b7280;
        color: white;
    }

    .modal-btn-secondary:hover {
        background: #4b5563;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .transaction-modal {
            padding: 10px;
        }

        .transaction-modal-content {
            max-height: 95vh;
        }

        .modal-header, .modal-body, .modal-actions {
            padding: 16px;
        }

        .transaction-info-grid {
            grid-template-columns: 1fr;
        }

        .sale-status-container {
            flex-direction: column;
            align-items: flex-start;
            gap: 8px;
        }

        .items-table {
            font-size: 0.8rem;
        }

        .items-table th, .items-table td {
            padding: 8px;
        }
    }

    /* Payment Form Styles */
    .payment-form {
        background: var(--gray-50);
        border-radius: 12px;
        padding: 20px;
        margin-top: 16px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-bottom: 16px;
    }

    .form-group {
        display: flex;
        flex-direction: column;
        gap: 6px;
    }

    .form-group label {
        font-weight: 600;
        color: var(--gray-700);
        font-size: 0.9rem;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        padding: 10px 12px;
        border: 1px solid var(--gray-300);
        border-radius: 8px;
        font-size: 0.9rem;
        transition: all var(--transition-normal);
        background: white;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: var(--primary-green);
        box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    }

    .form-actions {
        display: flex;
        gap: 12px;
        margin-top: 20px;
    }

    .btn-primary,
    .btn-secondary {
        padding: 12px 20px;
        border-radius: 8px;
        font-weight: 600;
        font-size: 0.9rem;
        cursor: pointer;
        transition: all var(--transition-normal);
        border: none;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .btn-primary {
        background: var(--primary-green);
        color: white;
    }

    .btn-primary:hover {
        background: var(--primary-green-light);
        transform: translateY(-2px);
        box-shadow: var(--shadow-green);
    }

    .btn-secondary {
        background: var(--gray-200);
        color: var(--gray-700);
        border: 1px solid var(--gray-300);
    }

    .btn-secondary:hover {
        background: var(--gray-300);
        border-color: var(--gray-400);
    }

    .btn-icon-only {
        background: var(--gray-100);
        border: 1px solid var(--gray-300);
        border-radius: 8px;
        padding: 8px;
        cursor: pointer;
        transition: all var(--transition-normal);
        font-size: 1rem;
    }

    .btn-icon-only:hover {
        background: var(--success-lighter);
        border-color: var(--primary-green);
    }

    /* Payments List Styles */
    .payments-list {
        background: var(--white);
        border-radius: 12px;
        overflow: hidden;
        margin-top: 16px;
    }

    .loading-state {
        padding: 40px 20px;
        text-align: center;
        color: var(--gray-500);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }

    .loading-spinner {
        font-size: 2rem;
        animation: pulse 2s ease-in-out infinite;
    }

    .payment-item {
        padding: 16px 20px;
        border-bottom: 1px solid var(--gray-100);
        display: flex;
        align-items: center;
        gap: 16px;
        transition: all var(--transition-normal);
    }

    .payment-item:hover {
        background: var(--gray-50);
    }

    .payment-item:last-child {
        border-bottom: none;
    }

    .payment-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: var(--primary-gradient);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        flex-shrink: 0;
    }

    .payment-details {
        flex: 1;
    }

    .payment-amount {
        font-size: 1.1rem;
        font-weight: 700;
        color: var(--gray-800);
        margin-bottom: 4px;
    }

    .payment-date {
        font-size: 0.8rem;
        color: var(--gray-500);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.05em;
        margin-bottom: 2px;
    }

    .payment-method {
        font-size: 0.85rem;
        color: var(--gray-600);
        background: var(--gray-100);
        padding: 2px 8px;
        border-radius: 12px;
        display: inline-block;
    }

    .payment-note {
        font-size: 0.8rem;
        color: var(--gray-600);
        margin-top: 4px;
        font-style: italic;
    }

    .payment-actions {
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity var(--transition-normal);
    }

    .payment-item:hover .payment-actions {
        opacity: 1;
    }

    .payment-edit-btn,
    .payment-delete-btn {
        background: var(--gray-100);
        border: 1px solid var(--gray-300);
        border-radius: 6px;
        padding: 6px 8px;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all var(--transition-normal);
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 32px;
        height: 32px;
    }

    .payment-edit-btn:hover {
        background: var(--primary-green);
        border-color: var(--primary-green);
        color: white;
    }

    .payment-delete-btn:hover {
        background: #ef4444;
        border-color: #ef4444;
        color: white;
    }

    .empty-payments {
        padding: 60px 20px;
        text-align: center;
        color: var(--gray-500);
    }

    .empty-payments-icon {
        font-size: 3rem;
        margin-bottom: 16px;
        opacity: 0.5;
    }

    .empty-payments-text {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 8px;
    }

    .empty-payments-description {
        font-size: 0.9rem;
        margin-bottom: 24px;
    }

    .section-actions {
        display: flex;
        gap: 8px;
    }

    /* Mobile Responsive for Payment Forms */
    @media (max-width: 768px) {
        .form-grid {
            grid-template-columns: 1fr;
            gap: 12px;
        }

        .form-actions {
            flex-direction: column;
            gap: 8px;
        }

        .btn-primary,
        .btn-secondary {
            justify-content: center;
        }

        .payment-item {
            padding: 12px 16px;
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;
        }

        .payment-icon {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }

        .payment-actions {
            opacity: 1;
            align-self: stretch;
            justify-content: flex-end;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Ultra-Modern Customer Profile Header -->
<div class="customer-profile-header fade-in">
    <div class="profile-main">
        <div class="customer-avatar-large">
            {{ customer.name|default:customer.mobile_number|default:"C"|first|upper }}
        </div>
        <div class="profile-info">
            <h1 class="customer-name-large">{{ customer.name|default:customer.mobile_number|default:'Unnamed Customer' }}</h1>
            <div class="customer-meta">
                <div class="meta-item">
                    <span class="meta-icon">📅</span>
                    <span>Created {{ customer.created_at|date:'M j, Y' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-icon">📝</span>
                    <span>Updated {{ customer.updated_at|date:'M j, Y' }}</span>
                </div>
                <div class="meta-item">
                    <span class="meta-icon">✅</span>
                    <span>Active Customer</span>
                </div>
            </div>
        </div>
        <div class="profile-actions">
            <a href="{% url 'customers:update' customer.pk %}" class="profile-action-btn primary">
                ✏️<span> Edit</span>
            </a>
            <button class="profile-action-btn secondary" onclick="shareCustomer()">
                📤<span> Share</span>
            </button>
        </div>
    </div>
</div>

<!-- Quick Stats Section -->
<div class="quick-stats fade-in">
    <div class="stat-card-small milk-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">🥛</div>
            <div class="stat-trend">+12%</div>
        </div>
        <div class="stat-number-small">0L</div>
        <div class="stat-label-small">Total Milk Purchased</div>
    </div>

    <div class="stat-card-small sales-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">💰</div>
            <div class="stat-trend">+8%</div>
        </div>
        <div class="stat-number-small">Rs. {{ total_sales_amount|floatformat:0|default:"0" }}</div>
        <div class="stat-label-small">Total Sales</div>
    </div>

    <div class="stat-card-small balance-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">⚖️</div>
            <div class="stat-trend">-5%</div>
        </div>
        <div class="stat-number-small">Rs. {{ pending_amount|floatformat:0|default:"0" }}</div>
        <div class="stat-label-small">Outstanding Balance</div>
    </div>

    <div class="stat-card-small visits-stat">
        <div class="stat-header-small">
            <div class="stat-icon-small">📊</div>
            <div class="stat-trend">+15%</div>
        </div>
        <div class="stat-number-small">{{ total_sales_count|default:"0" }}</div>
        <div class="stat-label-small">Total Transactions</div>
    </div>
</div>

<!-- Tab Navigation -->
<div class="tab-navigation fade-in">
    <button class="tab-button active" onclick="switchTab('info')" id="tab-info">
        <span class="tab-icon">👤</span>
        <span>Customer Info</span>
    </button>
    <button class="tab-button" onclick="switchTab('sales')" id="tab-sales">
        <span class="tab-icon">💰</span>
        <span>Sales</span>
    </button>
    <button class="tab-button" onclick="switchTab('payments')" id="tab-payments">
        <span class="tab-icon">💳</span>
        <span>Payments</span>
    </button>
</div>

<!-- Tab Content: Customer Info -->
<div class="tab-content active fade-in" id="content-info">
    <div class="details-grid">
        <!-- Contact Information -->
        <div class="detail-section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="section-icon">📞</span>
                    Contact Information
                </h2>
            </div>

            <div class="contact-info-grid">
                <div class="info-item">
                    <div class="info-icon">📱</div>
                    <div class="info-content">
                        <div class="info-label">Mobile Number</div>
                        <div class="info-value {% if not customer.mobile_number %}empty{% endif %}">
                            {% if customer.mobile_number %}
                                {{ customer.mobile_number }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">📧</div>
                    <div class="info-content">
                        <div class="info-label">Email Address</div>
                        <div class="info-value {% if not customer.email %}empty{% endif %}">
                            {% if customer.email %}
                                {{ customer.email }}
                            {% else %}
                                Not provided
                            {% endif %}
                        </div>
                    </div>
                </div>

                <div class="info-item">
                    <div class="info-icon">🏠</div>
                    <div class="info-content">
                        <div class="info-label">Address</div>
                        <div class="info-value empty">Not provided</div>
                    </div>
                </div>
            </div>

            <!-- Notes Section -->
            <div class="section-header" style="margin-top: 24px;">
                <h3 class="section-title">
                    <span class="section-icon">📝</span>
                    Notes
                </h3>
            </div>

            <div class="notes-section">
                {% if customer.notes %}
                    <div class="notes-content">{{ customer.notes|linebreaksbr }}</div>
                {% else %}
                    <div class="notes-empty">No notes added yet</div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tab Content: Sales -->
<div class="tab-content fade-in" id="content-sales">
    <div class="sales-list">
        <div class="sales-header">
            <h2 class="sales-title">
                <span class="section-icon">💰</span>
                Sales History
            </h2>
        </div>

        {% if has_sales %}
            <!-- New POS Transactions -->
            {% if has_new_pos %}
            <div style="margin-bottom: 20px;">
                <h3 style="color: #0da487; font-size: 1.1rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                    <span>💳</span> Recent POS Transactions ({{ new_pos_count }})
                </h3>
                {% for transaction in pos_transactions %}
                <div class="sales-item">
                    <div class="sale-icon" style="background: #0da487; color: white;">
                        💳
                    </div>
                    <div class="sale-details">
                        <div class="sale-date">{{ transaction.created_at|date:"M j, Y • g:i A" }}</div>
                        <div class="sale-amount">Rs. {{ transaction.total_amount|floatformat:0 }}</div>
                        <div class="sale-items">
                            Transaction #{{ transaction.transaction_number|slice:"-8:" }} - {{ transaction.total_item_count }} item{{ transaction.total_item_count|pluralize }}
                            {% if transaction.items.all %}
                                <br><small style="color: #64748b;">
                                {% for item in transaction.items.all|slice:":2" %}
                                    {{ item.product.pos_name }}{% if not forloop.last %}, {% endif %}
                                {% endfor %}
                                {% if transaction.items.all|length > 2 %}
                                    + {{ transaction.items.all|length|add:"-2" }} more
                                {% endif %}
                                </small>
                            {% endif %}
                        </div>
                    </div>
                    <div class="sale-status-container">
                        <div class="sale-status {% if transaction.status == 'completed' %}paid{% else %}pending{% endif %}">
                            {% if transaction.status == 'completed' %}
                                {% if transaction.payment_status == 'paid' %}
                                    Paid ({{ transaction.get_payment_method_display|default:'Cash' }})
                                {% else %}
                                    {{ transaction.get_payment_status_display }}
                                {% endif %}
                            {% else %}
                                {{ transaction.get_status_display|title }}
                            {% endif %}
                        </div>
                        <div class="sale-actions">
                            <button onclick="viewTransactionDetail({{ transaction.id }})" class="view-detail-btn" title="View Details">
                                👁️
                            </button>
                            {% if transaction.status == 'completed' %}
                                <button onclick="printInvoice({{ transaction.id }})" class="print-invoice-btn" title="Print Invoice">
                                    🖨️
                                </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            <!-- Legacy Sales (Old POS & Manual) -->
            {% if sales %}
            <div style="margin-bottom: 20px;">
                <h3 style="color: #6b7280; font-size: 1.1rem; margin-bottom: 15px; display: flex; align-items: center; gap: 8px;">
                    <span>📊</span> Legacy Sales ({{ old_sales_count }})
                </h3>
                {% for sale in sales %}
                <div class="sales-item">
                    <div class="sale-icon">
                        {% if sale.sale_type == 'manual' %}
                            📝
                        {% else %}
                            🛒
                        {% endif %}
                    </div>
                    <div class="sale-details">
                        <div class="sale-date">{{ sale.created_at|date:"M j, Y • g:i A" }}</div>
                        <div class="sale-amount">Rs. {{ sale.total|floatformat:0 }}</div>
                        <div class="sale-items">
                            {% if sale.sale_type == 'manual' %}
                                Manual Sale{% if sale.manual_description %} - {{ sale.manual_description|truncatechars:50 }}{% endif %}
                            {% else %}
                                Old POS Sale - {{ sale.items.count }} item{{ sale.items.count|pluralize }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="sale-status {% if sale.is_fully_paid %}paid{% else %}pending{% endif %}">
                        {% if sale.is_fully_paid %}
                            Paid
                        {% else %}
                            Pending Rs. {{ sale.remaining_balance|floatformat:0 }}
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% if total_sales_count > 10 %}
            <div class="sales-item" style="text-align: center; padding: 20px; background: var(--gray-50);">
                <div style="color: var(--gray-600);">
                    Showing latest transactions from {{ total_sales_count }} total
                </div>
            </div>
            {% endif %}
        {% else %}
            <!-- Empty State -->
            <div class="sales-empty">
                <div class="sales-empty-icon">📊</div>
                <div class="sales-empty-text">No transactions yet</div>
                <div class="sales-empty-description">This customer hasn't made any purchases yet. Create their first sale using the POS system.</div>
                <a href="{% url 'inventory:pos-interface' %}" class="profile-action-btn primary">
                    💳 Start POS Sale
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Tab Content: Payments -->
<div class="tab-content fade-in" id="content-payments">
    <div class="payments-section">
        <!-- Add Payment Form -->
        <div class="detail-section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="section-icon">💳</span>
                    Add New Payment
                </h2>
            </div>

            <form id="payment-form" class="payment-form">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="payment-amount">Amount (PKR) *</label>
                        <input type="number" id="payment-amount" name="amount" step="0.01" min="0.01" placeholder="0.00" required>
                    </div>

                    <div class="form-group">
                        <label for="payment-type">Payment Method</label>
                        <select id="payment-type" name="payment_type">
                            <option value="cash">Cash</option>
                            <option value="card">Card</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="cheque">Cheque</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="payment-date">Payment Date & Time</label>
                        <input type="datetime-local" id="payment-date" name="payment_date">
                    </div>

                    <div class="form-group">
                        <label for="reference-number">Reference Number</label>
                        <input type="text" id="reference-number" name="reference_number" placeholder="Optional reference number">
                    </div>
                </div>

                <div class="form-group">
                    <label for="payment-note">Note (Optional)</label>
                    <textarea id="payment-note" name="note" rows="3" placeholder="Add any notes about this payment..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn-primary">
                        <span class="btn-icon">💾</span>
                        Record Payment
                    </button>
                    <button type="reset" class="btn-secondary">
                        <span class="btn-icon">🔄</span>
                        Reset
                    </button>
                </div>
            </form>
        </div>

        <!-- Payment History -->
        <div class="detail-section">
            <div class="section-header">
                <h2 class="section-title">
                    <span class="section-icon">📊</span>
                    Payment History
                </h2>
                <div class="section-actions">
                    <button onclick="refreshPayments()" class="btn-icon-only" title="Refresh">
                        🔄
                    </button>
                </div>
            </div>

            <div id="payments-list" class="payments-list">
                <!-- Payments will be loaded here -->
                <div class="loading-state">
                    <div class="loading-spinner">⏳</div>
                    <div>Loading payments...</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Actions -->
<div class="customer-actions fade-in">
    <div class="section-header">
        <h2 class="section-title">
            <span class="section-icon">⚡</span>
            Quick Actions
        </h2>
    </div>

    <div class="actions-grid">
        <a href="{% url 'customers:update' customer.pk %}" class="action-card">
            <div class="action-icon">✏️</div>
            <div class="action-title">Edit Details</div>
            <div class="action-description">Update customer information</div>
        </a>

        <div class="action-card" onclick="addTransaction()">
            <div class="action-icon">💳</div>
            <div class="action-title">New Sale</div>
            <div class="action-description">Record a new transaction</div>
        </div>

        <div class="action-card" onclick="viewTransactions()">
            <div class="action-icon">📊</div>
            <div class="action-title">View History</div>
            <div class="action-description">See all transactions</div>
        </div>

        <div class="action-card" onclick="sendMessage()">
            <div class="action-icon">💬</div>
            <div class="action-title">Send Message</div>
            <div class="action-description">Contact customer</div>
        </div>

        <div class="action-card" onclick="generateReport()">
            <div class="action-icon">📋</div>
            <div class="action-title">Generate Report</div>
            <div class="action-description">Customer summary report</div>
        </div>

        <div class="action-card" onclick="archiveCustomer()">
            <div class="action-icon">📦</div>
            <div class="action-title">Archive</div>
            <div class="action-description">Archive customer profile</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Customer detail functionality
    document.addEventListener('DOMContentLoaded', function() {
        setupCustomerDetailPage();
    });

    function setupCustomerDetailPage() {
        // Initialize tab functionality
        setupTabs();

        // Add hover effects to stat cards
        document.querySelectorAll('.stat-card-small').forEach(card => {
            card.addEventListener('click', function() {
                const type = this.classList.contains('milk-stat') ? 'milk' :
                           this.classList.contains('sales-stat') ? 'sales' :
                           this.classList.contains('balance-stat') ? 'balance' : 'visits';

                // If sales stat clicked, switch to sales tab
                if (type === 'sales') {
                    switchTab('sales');
                } else {
                    showNotification(`Opening ${type} details...`, 'info');
                }
                hapticFeedback();
            });
        });

        // Contact info click actions
        document.querySelectorAll('.info-item').forEach(item => {
            item.addEventListener('click', function() {
                const icon = this.querySelector('.info-icon').textContent;
                if (icon === '📱') {
                    const phone = this.querySelector('.info-value').textContent.trim();
                    if (phone && phone !== 'Not provided') {
                        if (confirm(`Call ${phone}?`)) {
                            window.open(`tel:${phone}`);
                        }
                    }
                } else if (icon === '📧') {
                    const email = this.querySelector('.info-value').textContent.trim();
                    if (email && email !== 'Not provided') {
                        if (confirm(`Send email to ${email}?`)) {
                            window.open(`mailto:${email}`);
                        }
                    }
                }
                hapticFeedback();
            });
        });
    }

    function setupTabs() {
        // Tab switching functionality is handled by switchTab function
        // Set initial state
        const initialTab = 'info';
        switchTab(initialTab);
    }

    function switchTab(tabName) {
        // Remove active class from all tabs and content
        document.querySelectorAll('.tab-button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });

        // Add active class to selected tab and content
        const selectedTabButton = document.getElementById(`tab-${tabName}`);
        const selectedTabContent = document.getElementById(`content-${tabName}`);

        if (selectedTabButton && selectedTabContent) {
            selectedTabButton.classList.add('active');
            selectedTabContent.classList.add('active');

            // Initialize payment tab if needed
            if (tabName === 'payments') {
                initializePaymentTab();
            }

            // Add haptic feedback
            hapticFeedback();

            // Show notification for better UX
            const tabLabels = {
                'info': 'Customer Information',
                'sales': 'Sales History',
                'payments': 'Payment Management'
            };
            const tabLabel = tabLabels[tabName] || 'Unknown Tab';
            showNotification(`Switched to ${tabLabel}`, 'info');
        }
    }

    function shareCustomer() {
        const customerName = '{{ customer.name|default:customer.mobile_number|default:"Customer" }}';
        const shareData = {
            title: `${customerName} - Customer Profile`,
            text: `Customer profile for ${customerName}`,
            url: window.location.href
        };

        if (navigator.share) {
            navigator.share(shareData).then(() => {
                showNotification('Customer profile shared successfully!', 'success');
            }).catch(console.error);
        } else {
            // Fallback to copy link
            navigator.clipboard.writeText(window.location.href).then(() => {
                showNotification('Customer profile link copied to clipboard!', 'success');
            });
        }
        hapticFeedback();
    }

    function addTransaction() {
        showNotification('Opening new transaction form...', 'info');
        // Navigate to POS or transaction form
        setTimeout(() => {
            window.location.href = '{% url "pos:sale-create" %}';
        }, 1000);
        hapticFeedback();
    }

    function viewTransactions() {
        showNotification('Loading transaction history...', 'info');
        // Navigate to transaction history
        hapticFeedback();
    }

    function sendMessage() {
        const phone = '{{ customer.mobile_number }}';
        if (phone) {
            const message = encodeURIComponent('Hello {{ customer.name|default:"there" }}, this is CHANNAB Farm.');
            if (confirm('Send WhatsApp message?')) {
                window.open(`https://wa.me/${phone}?text=${message}`);
            }
        } else {
            showNotification('No phone number available', 'warning');
        }
        hapticFeedback();
    }

    function generateReport() {
        showNotification('Generating customer report...', 'info');
        // Generate and download report
        setTimeout(() => {
            showNotification('Report generated successfully!', 'success');
        }, 2000);
        hapticFeedback();
    }

    function archiveCustomer() {
        const customerName = '{{ customer.name|default:customer.mobile_number|default:"this customer" }}';
        if (confirm(`Archive ${customerName}? This will hide them from the active customer list.`)) {
            showNotification(`${customerName} has been archived`, 'success');
            setTimeout(() => {
                window.location.href = '{% url "customers:list" %}';
            }, 1500);
        }
        hapticFeedback();
    }

    // Transaction Detail Functions
    function viewTransactionDetail(transactionId) {
        // Show modal
        document.getElementById('transaction-modal').style.display = 'flex';

        // Load transaction details via API
        fetch(`/inventory/pos/api/transactions/${transactionId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayTransactionDetail(data.transaction);
                } else {
                    document.getElementById('modal-content-body').innerHTML =
                        '<p style="color: #ef4444; text-align: center; padding: 40px;">Error loading transaction details: ' + data.error + '</p>';
                }
            })
            .catch(error => {
                document.getElementById('modal-content-body').innerHTML =
                    '<p style="color: #ef4444; text-align: center; padding: 40px;">Error loading transaction details.</p>';
                console.error('Error:', error);
            });
    }

    function displayTransactionDetail(transaction) {
        const modalBody = document.getElementById('modal-content-body');

        // Calculate totals for verification
        let calculatedSubtotal = 0;
        transaction.items.forEach(item => {
            calculatedSubtotal += parseFloat(item.line_total);
        });

        let itemsHtml = '';
        transaction.items.forEach(item => {
            itemsHtml += `
                <tr>
                    <td style="font-weight: 500;">${item.product_name}</td>
                    <td style="text-align: center;">${parseFloat(item.quantity).toFixed(item.quantity % 1 === 0 ? 0 : 2)}</td>
                    <td style="text-align: right;">Rs. ${parseFloat(item.unit_price).toFixed(0)}</td>
                    <td style="text-align: center;">${parseFloat(item.discount_percent).toFixed(1)}%</td>
                    <td style="text-align: right; font-weight: 600;">Rs. ${parseFloat(item.line_total).toFixed(0)}</td>
                </tr>
            `;
        });

        modalBody.innerHTML = `
            <div class="transaction-info">
                <div class="transaction-info-grid">
                    <div class="info-item">
                        <div class="info-label">Transaction Number</div>
                        <div class="info-value">
                            <span class="transaction-number">${transaction.transaction_number}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Status</div>
                        <div class="info-value" style="text-transform: capitalize;">${transaction.status}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Payment Status</div>
                        <div class="info-value" style="text-transform: capitalize;">${transaction.payment_status || 'Pending'}</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Payment Method</div>
                        <div class="info-value" style="text-transform: capitalize;">${transaction.payment_method || 'Not specified'}</div>
                    </div>
                </div>
            </div>

            <div class="items-section">
                <h3 class="section-title">
                    <span>🛍️</span>
                    Items Purchased (${transaction.items.length})
                </h3>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Product</th>
                            <th style="text-align: center;">Quantity</th>
                            <th style="text-align: right;">Unit Price</th>
                            <th style="text-align: center;">Discount</th>
                            <th style="text-align: right;">Line Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>
            </div>

            <div class="price-breakdown">
                <h3 class="section-title">
                    <span>💰</span>
                    Price Breakdown
                </h3>
                <div class="breakdown-row">
                    <span class="breakdown-label">Subtotal (Items):</span>
                    <span class="breakdown-value">Rs. ${calculatedSubtotal.toFixed(0)}</span>
                </div>
                <div class="breakdown-row">
                    <span class="breakdown-label">Transaction Subtotal:</span>
                    <span class="breakdown-value">Rs. ${parseFloat(transaction.subtotal).toFixed(0)}</span>
                </div>
                <div class="breakdown-row">
                    <span class="breakdown-label">Discount:</span>
                    <span class="breakdown-value">Rs. ${parseFloat(transaction.discount_total).toFixed(0)}</span>
                </div>
                <div class="breakdown-row">
                    <span class="breakdown-label">Tax (17%):</span>
                    <span class="breakdown-value">Rs. ${parseFloat(transaction.tax_total).toFixed(0)}</span>
                </div>
                <div class="breakdown-row">
                    <span class="breakdown-label">Total Amount:</span>
                    <span class="breakdown-value">Rs. ${parseFloat(transaction.total_amount).toFixed(0)}</span>
                </div>
                ${calculatedSubtotal !== parseFloat(transaction.subtotal) ? `
                <div style="margin-top: 16px; padding: 12px; background: #fef3c7; border-radius: 8px; border: 1px solid #f59e0b;">
                    <div style="color: #92400e; font-weight: 600; margin-bottom: 4px;">⚠️ Calculation Notice:</div>
                    <div style="color: #92400e; font-size: 0.875rem;">
                        Items total (Rs. ${calculatedSubtotal.toFixed(0)}) differs from transaction subtotal (Rs. ${parseFloat(transaction.subtotal).toFixed(0)})
                    </div>
                </div>
                ` : ''}
            </div>
        `;

        // Update modal title
        document.getElementById('modal-title').textContent = `Transaction Details - #${transaction.transaction_number.slice(-8)}`;
    }

    function closeTransactionModal() {
        document.getElementById('transaction-modal').style.display = 'none';
    }

    function printInvoice(transactionId) {
        // Create a printable invoice
        fetch(`/inventory/pos/api/transactions/${transactionId}/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    generatePrintableInvoice(data.transaction);
                } else {
                    alert('Error loading transaction for printing: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error loading transaction for printing.');
                console.error('Error:', error);
            });
    }

    function generatePrintableInvoice(transaction) {
        const printWindow = window.open('', '_blank');

        let itemsHtml = '';
        let subtotal = 0;
        transaction.items.forEach(item => {
            const lineTotal = parseFloat(item.line_total);
            subtotal += lineTotal;
            itemsHtml += `
                <tr>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd;">${item.product_name}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: center;">${parseFloat(item.quantity).toFixed(item.quantity % 1 === 0 ? 0 : 2)}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">Rs. ${parseFloat(item.unit_price).toFixed(0)}</td>
                    <td style="padding: 8px; border-bottom: 1px solid #ddd; text-align: right;">Rs. ${lineTotal.toFixed(0)}</td>
                </tr>
            `;
        });

        const invoiceHtml = `
            <!DOCTYPE html>
            <html>
            <head>
                <title>Invoice - ${transaction.transaction_number}</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 20px; font-size: 14px; }
                    .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #0da487; padding-bottom: 20px; }
                    .company-name { font-size: 24px; font-weight: bold; color: #0da487; margin-bottom: 5px; }
                    .invoice-title { font-size: 18px; margin-top: 15px; }
                    .invoice-info { display: flex; justify-content: space-between; margin-bottom: 20px; }
                    .customer-info, .transaction-info { width: 48%; }
                    .info-title { font-weight: bold; color: #374151; margin-bottom: 10px; }
                    table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    th { background: #f8fafc; padding: 12px; text-align: left; border-bottom: 2px solid #e2e8f0; font-weight: bold; }
                    td { padding: 8px; border-bottom: 1px solid #ddd; }
                    .totals { margin-top: 20px; text-align: right; }
                    .total-row { display: flex; justify-content: space-between; padding: 5px 0; }
                    .final-total { font-weight: bold; font-size: 18px; color: #0da487; border-top: 2px solid #0da487; padding-top: 10px; margin-top: 10px; }
                    .footer { margin-top: 40px; text-align: center; color: #64748b; font-size: 12px; }
                    @media print { body { margin: 0; } }
                </style>
            </head>
            <body>
                <div class="header">
                    <div class="company-name">CHANNAB POS</div>
                    <div>Point of Sale System</div>
                    <div class="invoice-title">SALES INVOICE</div>
                </div>

                <div class="invoice-info">
                    <div class="customer-info">
                        <div class="info-title">Customer Information:</div>
                        <div><strong>Name:</strong> {{ customer.display_name }}</div>
                        <div><strong>Mobile:</strong> {{ customer.mobile_number|default:"N/A" }}</div>
                    </div>
                    <div class="transaction-info">
                        <div class="info-title">Transaction Details:</div>
                        <div><strong>Invoice #:</strong> ${transaction.transaction_number}</div>
                        <div><strong>Date:</strong> ${new Date().toLocaleDateString()}</div>
                        <div><strong>Status:</strong> ${transaction.status}</div>
                    </div>
                </div>

                <table>
                    <thead>
                        <tr>
                            <th>Item</th>
                            <th style="text-align: center;">Qty</th>
                            <th style="text-align: right;">Unit Price</th>
                            <th style="text-align: right;">Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${itemsHtml}
                    </tbody>
                </table>

                <div class="totals">
                    <div class="total-row">
                        <span>Subtotal:</span>
                        <span>Rs. ${parseFloat(transaction.subtotal).toFixed(0)}</span>
                    </div>
                    <div class="total-row">
                        <span>Discount:</span>
                        <span>Rs. ${parseFloat(transaction.discount_total).toFixed(0)}</span>
                    </div>
                    <div class="total-row">
                        <span>Tax:</span>
                        <span>Rs. ${parseFloat(transaction.tax_total).toFixed(0)}</span>
                    </div>
                    <div class="total-row final-total">
                        <span>Total Amount:</span>
                        <span>Rs. ${parseFloat(transaction.total_amount).toFixed(0)}</span>
                    </div>
                </div>

                <div class="footer">
                    <p>Thank you for your business!</p>
                    <p>Generated on ${new Date().toLocaleString()}</p>
                </div>
            </body>
            </html>
        `;

        printWindow.document.write(invoiceHtml);
        printWindow.document.close();

        // Auto-print after a short delay
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }

    // Close modal when clicking outside
    document.addEventListener('click', function(e) {
        const modal = document.getElementById('transaction-modal');
        if (e.target === modal) {
            closeTransactionModal();
        }
    });

    // Payment functionality
    let paymentsLoaded = false;

    function initializePaymentTab() {
        // Set default date and time to now
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        document.getElementById('payment-date').value = now.toISOString().slice(0, 16);

        // Setup payment form
        setupPaymentForm();

        // Load payments if not already loaded
        if (!paymentsLoaded) {
            loadPayments();
        }
    }

    function setupPaymentForm() {
        const paymentForm = document.getElementById('payment-form');
        if (paymentForm) {
            paymentForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitPayment();
            });

            paymentForm.addEventListener('reset', function(e) {
                // Reset to current date/time
                setTimeout(() => {
                    const now = new Date();
                    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                    document.getElementById('payment-date').value = now.toISOString().slice(0, 16);
                }, 100);
            });
        }
    }

    function submitPayment() {
        const form = document.getElementById('payment-form');
        const formData = new FormData(form);

        const paymentData = {
            amount: formData.get('amount'),
            payment_type: formData.get('payment_type'),
            payment_date: formData.get('payment_date'),
            note: formData.get('note'),
            reference_number: formData.get('reference_number')
        };

        // Validate amount
        if (!paymentData.amount || parseFloat(paymentData.amount) <= 0) {
            showNotification('Please enter a valid amount', 'error');
            return;
        }

        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="btn-icon">⏳</span>Recording...';
        submitBtn.disabled = true;

        // Submit payment
        fetch(`/api/v1/customers/{{ customer.id }}/payments/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(paymentData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Payment recorded successfully!', 'success');
                form.reset();

                // Reset date to current time
                const now = new Date();
                now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
                document.getElementById('payment-date').value = now.toISOString().slice(0, 16);

                // Reload payments
                loadPayments();
                hapticFeedback();
            } else {
                showNotification(data.message || 'Error recording payment', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error recording payment', 'error');
        })
        .finally(() => {
            // Restore button state
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
        });
    }

    function loadPayments() {
        const paymentsList = document.getElementById('payments-list');

        // Show loading state
        paymentsList.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner">⏳</div>
                <div>Loading payments...</div>
            </div>
        `;

        fetch(`/api/v1/customers/{{ customer.id }}/payments/`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayPayments(data.data);
                    paymentsLoaded = true;
                } else {
                    paymentsList.innerHTML = `
                        <div class="empty-payments">
                            <div class="empty-payments-icon">❌</div>
                            <div class="empty-payments-text">Error loading payments</div>
                            <div class="empty-payments-description">${data.message || 'Unknown error'}</div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error loading payments:', error);
                paymentsList.innerHTML = `
                    <div class="empty-payments">
                        <div class="empty-payments-icon">❌</div>
                        <div class="empty-payments-text">Error loading payments</div>
                        <div class="empty-payments-description">Please try again later</div>
                    </div>
                `;
            });
    }

    function displayPayments(paymentData) {
        const paymentsList = document.getElementById('payments-list');
        const payments = paymentData.payments;

        if (payments.length === 0) {
            paymentsList.innerHTML = `
                <div class="empty-payments">
                    <div class="empty-payments-icon">💳</div>
                    <div class="empty-payments-text">No payments recorded</div>
                    <div class="empty-payments-description">Record the first payment using the form above</div>
                </div>
            `;
            return;
        }

        let paymentsHtml = '';
        payments.forEach(payment => {
            const paymentDate = new Date(payment.payment_date);
            const formattedDate = paymentDate.toLocaleDateString();
            const formattedTime = paymentDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            // Payment method icon
            const paymentIcon = getPaymentIcon(payment.payment_type);

            paymentsHtml += `
                <div class="payment-item" data-payment-id="${payment.id}">
                    <div class="payment-icon">
                        ${paymentIcon}
                    </div>
                    <div class="payment-details">
                        <div class="payment-amount">${payment.formatted_amount}</div>
                        <div class="payment-date">${formattedDate} • ${formattedTime}</div>
                        <div class="payment-method">${payment.payment_type_display}</div>
                        ${payment.note ? `<div class="payment-note">"${payment.note}"</div>` : ''}
                        ${payment.reference_number ? `<div class="payment-note">Ref: ${payment.reference_number}</div>` : ''}
                    </div>
                    <div class="payment-actions">
                        <button onclick="editPayment(${payment.id})" class="payment-edit-btn" title="Edit Payment">
                            ✏️
                        </button>
                        <button onclick="deletePayment(${payment.id})" class="payment-delete-btn" title="Delete Payment">
                            🗑️
                        </button>
                    </div>
                </div>
            `;
        });

        // Add summary if there are payments
        const summary = paymentData.summary;
        const summaryHtml = `
            <div style="background: var(--gray-50); padding: 16px 20px; border-top: 2px solid var(--primary-green); margin-top: 16px;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-weight: 600; color: var(--gray-700);">Total Payments:</span>
                    <span style="font-weight: 700; font-size: 1.1rem; color: var(--primary-green);">
                        Rs. ${summary.total_payments_amount.toFixed(2)}
                    </span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 4px;">
                    <span style="font-size: 0.9rem; color: var(--gray-600);">Number of payments:</span>
                    <span style="font-weight: 600; color: var(--gray-700);">${summary.total_payments_count}</span>
                </div>
            </div>
        `;

        paymentsList.innerHTML = paymentsHtml + summaryHtml;
    }

    function getPaymentIcon(paymentType) {
        const icons = {
            'cash': '💵',
            'card': '💳',
            'bank_transfer': '🏦',
            'mobile_money': '📱',
            'cheque': '📝',
            'other': '💰'
        };
        return icons[paymentType] || '💰';
    }

    function refreshPayments() {
        paymentsLoaded = false;
        loadPayments();
        showNotification('Payments refreshed', 'info');
        hapticFeedback();
    }

    function editPayment(paymentId) {
        // For now, show notification - you could implement an edit modal
        showNotification('Edit payment functionality coming soon!', 'info');
        hapticFeedback();
    }

    function deletePayment(paymentId) {
        if (confirm('Are you sure you want to delete this payment? This action cannot be undone.')) {
            fetch(`/api/v1/customers/{{ customer.id }}/payments/${paymentId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Payment deleted successfully', 'success');
                    loadPayments();
                    hapticFeedback();
                } else {
                    showNotification(data.message || 'Error deleting payment', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error deleting payment', 'error');
            });
        }
    }

    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '';
    }
</script>

<!-- Transaction Detail Modal -->
<div id="transaction-modal" class="transaction-modal">
    <div class="transaction-modal-content">
        <div class="modal-header">
            <h2 id="modal-title" class="modal-title">Transaction Details</h2>
            <button class="modal-close" onclick="closeTransactionModal()">×</button>
        </div>
        <div class="modal-body" id="modal-content-body">
            <!-- Content will be loaded here -->
        </div>
        <div class="modal-actions">
            <button class="modal-btn modal-btn-secondary" onclick="closeTransactionModal()">Close</button>
            <button class="modal-btn modal-btn-primary" onclick="printInvoice(currentTransactionId)">Print Invoice</button>
        </div>
    </div>
</div>

{% endblock %}