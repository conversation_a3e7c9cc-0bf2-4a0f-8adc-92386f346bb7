{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>{% block title %}Zayyrah{% endblock %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap">
    <link rel="stylesheet" href="{% static 'accounts/css/auth.css' %}">
    {% block extra_head %}{% endblock %}
</head>
<body class="site-body {% block body_class %}{% endblock %}">
<div class="site-wrapper">
    <header class="site-header">
        <a class="brand" href="{% if user.is_authenticated %}{% if user.is_superuser %}/admin/business-dashboard/{% else %}{% url 'accounts:dashboard' %}{% endif %}{% else %}{% url 'accounts:login' %}{% endif %}" aria-label="Zayyrah">
            <span class="brand-mark">Z</span>
            <span>Zayyrah</span>
        </a>
        <nav class="primary-nav">
            {% if user.is_authenticated %}
                {% if user.is_superuser %}
                    <a href="/admin/business-dashboard/">Site Admin</a>
                    <a href="/admin/">Django Admin</a>
                {% else %}
                    <a href="{% url 'accounts:dashboard' %}">Dashboard</a>
                    <a href="{% url 'pos:product-list' %}">Products</a>
                    <a href="{% url 'inventory:category-list' %}">Inventory</a>
                    <a href="{% url 'customers:list' %}">Customers</a>
                    <a href="{% url 'pos:sale-create' %}">POS</a>
                    <a href="{% url 'employees:dashboard' %}">Employees</a>
                {% endif %}
                <a href="{% url 'accounts:logout' %}" class="cta">Logout</a>
            {% else %}
                <a href="{% url 'accounts:login' %}">Login</a>
                <a href="{% url 'accounts:signup' %}" class="cta">Create account</a>
            {% endif %}
        </nav>
    </header>

    {% if messages %}
        <div class="flash-region" role="status" aria-live="polite">
            {% for message in messages %}
                <div class="flash-message {% if message.tags %}{{ message.tags }}{% endif %}">{{ message }}</div>
            {% endfor %}
        </div>
    {% endif %}

    <main class="page-shell">
        {% block content %}{% endblock %}
    </main>
</div>

{% with current_url=request.resolver_match.url_name current_ns=request.resolver_match.namespace %}
<nav class="mobile-nav" aria-label="Primary navigation">
    <ul>
        {% if user.is_authenticated %}
            {% if user.is_superuser %}
            <li>
                <a href="/admin/business-dashboard/"
                   class="mobile-nav-link">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M12 3L2 12h3v8h6v-6h2v6h6v-8h3L12 3z"/>
                        </svg>
                    </span>
                    <span class="mobile-nav-label">🏢 Site Admin</span>
                </a>
            </li>
            {% else %}
            <li>
                <a href="{% url 'accounts:dashboard' %}"
                   class="mobile-nav-link {% if current_ns == 'accounts' and current_url == 'dashboard' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M4.4 10.3 11.2 4a1 1 0 0 1 1.6 0l6.8 6.3a1 1 0 0 1 .3.7V19a1 1 0 0 1-1 1h-4.8a1 1 0 0 1-1-1v-3.4h-2.4V19a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1v-7.9a1 1 0 0 1 .3-.8z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">🏠 Home</span>
                </a>
            </li>
            {% endif %}
            {% if not user.is_superuser %}
            <li>
                <a href="{% url 'pos:product-list' %}"
                   class="mobile-nav-link {% if current_ns == 'pos' and 'product' in request.resolver_match.url_name %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M4 6V4h16v2H4zm0 5v-2h16v2H4zm0 5v-2h16v2H4zm2-8h2v2H6V8zm0 5h2v2H6v-2zm0 5h2v2H6v-2z"/>
                        </svg>
                    </span>
                    <span class="mobile-nav-label">📦 Products</span>
                </a>
            </li>
            <li>
                <a href="{% url 'inventory:category-list' %}"
                   class="mobile-nav-link {% if current_ns == 'inventory' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M10 2v2H8a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V6a2 2 0 0 0-2-2h-2V2H10zm4 4v2H10V6h4zM8 8h8v12H8V8z"/>
                        </svg>
                    </span>
                    <span class="mobile-nav-label">📊 Inventory</span>
                </a>
            </li>
            <li>
                <a href="{% url 'pos:sale-create' %}"
                   class="mobile-nav-link {% if current_ns == 'pos' and 'sale' in request.resolver_match.url_name %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M5.5 5h13a1 1 0 0 1 1 1v3.1l.8 5a2.6 2.6 0 0 1-2.6 3h-2.1a3 3 0 1 1-5.6 0H8a3 3 0 1 1-5.6-.7l.8-5.3V6a1 1 0 0 1 1-1zm1 2v2h11V7zm-.7 6.3-.5 3.2a1 1 0 0 0 1 1.2 1 1 0 0 0 .9-1l.4-3.4zm11.4 0-.4 3.4a1 1 0 0 0 .9 1 1 1 0 0 0 1-1.2l-.5-3.2z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">🛒 POS</span>
                </a>
            </li>
            <li>
                <a href="{% url 'customers:list' %}"
                   class="mobile-nav-link {% if current_ns == 'customers' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M12 4a4 4 0 1 1 0 8 4 4 0 0 1 0-8zm7 13.2c0 1.6-1.5 2.8-3 2.8H8c-1.5 0-3-1.2-3-2.8 0-2.7 3.3-4.7 7-4.7s7 2 7 4.7z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">👥 Customers</span>
                </a>
            </li>
            <li>
                <a href="{% url 'employees:dashboard' %}"
                   class="mobile-nav-link {% if current_ns == 'employees' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M16 2C17.1 2 18 2.9 18 4V6.2C19.2 6.7 20 7.8 20 9.2V18.8C20 20.2 19.2 21.3 18 21.8V22C18 23.1 17.1 24 16 24H8C6.9 24 6 23.1 6 22V21.8C4.8 21.3 4 20.2 4 18.8V9.2C4 7.8 4.8 6.7 6 6.2V4C6 2.9 6.9 2 8 2H16ZM12 7C10.9 7 10 7.9 10 9S10.9 11 12 11 14 10.1 14 9 13.1 7 12 7ZM16 15C16 13.3 14.7 12 13 12H11C9.3 12 8 13.3 8 15V16H16V15Z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">👷 Employees</span>
                </a>
            </li>
            {% endif %}
        {% else %}
            <li>
                <a href="{% url 'accounts:login' %}"
                   class="mobile-nav-link {% if current_url == 'login' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M12 5a1 1 0 0 1 1 1v3h5a1 1 0 0 1 0 2h-5v3a1 1 0 0 1-2 0v-3H6a1 1 0 0 1 0-2h5V6a1 1 0 0 1 1-1zM5 18h14a1 1 0 0 1 0 2H5a1 1 0 1 1 0-2z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">Login</span>
                </a>
            </li>
            <li>
                <a href="{% url 'accounts:signup' %}"
                   class="mobile-nav-link {% if current_url == 'signup' %}active{% endif %}">
                    <span class="mobile-nav-icon" aria-hidden="true">
                        <svg viewBox="0 0 24 24" focusable="false">
                            <path d="M12 4a4 4 0 1 1 0 8 4 4 0 0 1 0-8zm7 13.2c0 1.6-1.5 2.8-3 2.8H8c-1.5 0-3-1.2-3-2.8 0-2.7 3.3-4.7 7-4.7s7 2 7 4.7zm-8.5-6.6a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" />
                        </svg>
                    </span>
                    <span class="mobile-nav-label">Sign up</span>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endwith %}
</body>
</html>
