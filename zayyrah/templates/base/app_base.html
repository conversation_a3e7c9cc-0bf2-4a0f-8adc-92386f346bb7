<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CHANNAB{% endblock %} - Farm Management System</title>
    <style>
        /* ==== CHANNAB ULTRA-MODERN DESIGN SYSTEM ==== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Ultra-Modern Professional Green Color System */
            --primary-green: #1e4d3b;
            --primary-green-light: #2d6a4f;
            --primary-green-lighter: #40916c;
            --primary-green-lightest: #52b788;
            --accent-green: #74c69d;
            --success-green: #95d5b2;
            --success-light: #b7e4c7;
            --success-lighter: #d8f3dc;

            /* Modern Neutral Palette */
            --white: #ffffff;
            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            /* Functional Colors */
            --warning: #f59e0b;
            --warning-light: #fbbf24;
            --danger: #ef4444;
            --danger-light: #f87171;
            --info: #3b82f6;
            --info-light: #60a5fa;

            /* Professional Gradients */
            --primary-gradient: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-light) 50%, var(--primary-green-lighter) 100%);
            --success-gradient: linear-gradient(135deg, var(--primary-green-lighter) 0%, var(--accent-green) 50%, var(--success-green) 100%);
            --accent-gradient: linear-gradient(135deg, var(--accent-green) 0%, var(--success-green) 50%, var(--success-light) 100%);
            --glass-gradient: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

            /* Ultra-Modern Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(30, 77, 59, 0.05);
            --shadow: 0 1px 3px 0 rgba(30, 77, 59, 0.1), 0 1px 2px 0 rgba(30, 77, 59, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(30, 77, 59, 0.1), 0 4px 6px -2px rgba(30, 77, 59, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(30, 77, 59, 0.1), 0 10px 10px -5px rgba(30, 77, 59, 0.04);
            --shadow-green: 0 4px 14px 0 rgba(30, 77, 59, 0.2);
            --shadow-glow: 0 0 20px rgba(30, 77, 59, 0.15);

            /* Layout Variables */
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
            --header-height: 70px;
            --bottom-nav-height: 80px;
            --vh: 1vh;

            /* Typography */
            --font-primary: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

            /* Animations */
            --transition-fast: 0.15s ease;
            --transition-normal: 0.2s ease;
            --transition-slow: 0.3s ease;
            --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        html {
            font-family: var(--font-primary);
            line-height: 1.6;
            color: var(--gray-700);
            scroll-behavior: smooth;
        }

        body {
            background: var(--gray-50);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ==== ULTRA-MODERN APP LAYOUT ==== */
        .app-container {
            display: flex;
            min-height: 100vh;
            position: relative;
        }

        /* ==== PROFESSIONAL SIDEBAR SYSTEM ==== */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--white);
            border-right: 1px solid var(--gray-200);
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            z-index: 200;
            transition: all var(--transition-slow);
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(20px);
        }

        .sidebar.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-header {
            padding: 24px;
            border-bottom: 1px solid var(--gray-200);
            background: var(--primary-gradient);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--glass-gradient);
            pointer-events: none;
        }

        .sidebar-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }

        .sidebar-logo-icon {
            width: 44px;
            height: 44px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all var(--transition-normal);
        }

        .sidebar-logo-text {
            font-size: 1.6rem;
            font-weight: 700;
            letter-spacing: -0.025em;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .sidebar-logo-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .sidebar-toggle {
            position: absolute;
            top: 50%;
            right: -15px;
            transform: translateY(-50%);
            width: 30px;
            height: 30px;
            background: var(--white);
            border: 1px solid var(--gray-200);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: var(--shadow);
            font-size: 0.8rem;
            color: var(--gray-600);
            transition: all var(--transition-normal);
            z-index: 10;
        }

        .sidebar-toggle:hover {
            background: var(--primary-green);
            color: white;
            transform: translateY(-50%) scale(1.1);
        }

        /* ==== NAVIGATION SYSTEM ==== */
        .sidebar-nav {
            padding: 24px 0;
        }

        .nav-section {
            margin-bottom: 32px;
        }

        .nav-section-title {
            padding: 0 24px 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: var(--gray-500);
            letter-spacing: 0.05em;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .nav-section-title {
            opacity: 0;
            padding: 0 12px 12px;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 14px 24px;
            color: var(--gray-700);
            text-decoration: none;
            transition: all var(--transition-normal);
            border-left: 3px solid transparent;
            position: relative;
            margin: 2px 12px;
            border-radius: 12px;
        }

        .sidebar.collapsed .nav-item {
            padding: 14px 12px;
            justify-content: center;
            margin: 2px 8px;
        }

        .nav-item:hover {
            background: var(--success-lighter);
            color: var(--primary-green);
            transform: translateX(4px);
        }

        .nav-item.active {
            background: var(--success-lighter);
            color: var(--primary-green);
            border-left-color: var(--primary-green);
            font-weight: 600;
            box-shadow: var(--shadow-sm);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            right: 12px;
            width: 6px;
            height: 6px;
            background: var(--primary-green);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .sidebar.collapsed .nav-item.active::after {
            display: none;
        }

        .nav-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .nav-item-icon {
            margin-right: 0;
        }

        .nav-item-text {
            flex: 1;
            font-size: 0.95rem;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .nav-item-text {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        .nav-item-badge {
            background: var(--primary-green);
            color: white;
            font-size: 0.7rem;
            padding: 2px 6px;
            border-radius: 10px;
            min-width: 18px;
            text-align: center;
            font-weight: 600;
            transition: all var(--transition-normal);
        }

        .sidebar.collapsed .nav-item-badge {
            opacity: 0;
            width: 0;
            overflow: hidden;
        }

        /* ==== MAIN CONTENT AREA ==== */
        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            transition: all var(--transition-slow);
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* ==== ULTRA-MODERN HEADER ==== */
        .main-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 16px 24px;
            height: var(--header-height);
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-sm);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .menu-toggle {
            display: none;
            width: 44px;
            height: 44px;
            border: 1px solid var(--gray-300);
            border-radius: 12px;
            background: var(--white);
            cursor: pointer;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all var(--transition-normal);
            color: var(--primary-green);
        }

        .menu-toggle:hover {
            background: var(--success-lighter);
            border-color: var(--primary-green);
            transform: scale(1.05);
        }

        .page-title {
            font-size: 1.6rem;
            font-weight: 700;
            color: var(--primary-green);
            letter-spacing: -0.025em;
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--gray-500);
            font-size: 0.9rem;
        }

        .breadcrumb-item {
            color: var(--gray-500);
            text-decoration: none;
            transition: all var(--transition-fast);
        }

        .breadcrumb-item:hover {
            color: var(--primary-green);
        }

        .breadcrumb-item.active {
            color: var(--primary-green);
            font-weight: 600;
        }

        .breadcrumb-separator {
            color: var(--gray-400);
            font-size: 0.8rem;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 320px;
            padding: 12px 16px 12px 44px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            background: var(--white);
            font-size: 0.95rem;
            transition: all var(--transition-normal);
            outline: none;
            color: var(--gray-700);
        }

        .search-input:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(30, 77, 59, 0.1);
            transform: scale(1.02);
        }

        .search-input::placeholder {
            color: var(--gray-400);
        }

        .search-icon {
            position: absolute;
            left: 14px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.1rem;
            transition: all var(--transition-normal);
        }

        .search-input:focus + .search-icon {
            color: var(--primary-green);
        }

        .header-btn {
            width: 44px;
            height: 44px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all var(--transition-normal);
            position: relative;
            color: var(--gray-600);
        }

        .header-btn:hover {
            border-color: var(--primary-green);
            background: var(--success-lighter);
            color: var(--primary-green);
            transform: scale(1.05);
        }

        .notification-badge {
            position: absolute;
            top: -4px;
            right: -4px;
            background: var(--primary-green);
            color: white;
            font-size: 0.7rem;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        /* ==== CONTENT WRAPPER ==== */
        .content-wrapper {
            flex: 1;
            padding: 0 24px 24px 24px;
            margin-bottom: var(--bottom-nav-height);
        }

        /* ==== MOBILE BOTTOM NAVIGATION ==== */
        .bottom-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--white);
            border-top: 1px solid var(--gray-200);
            height: var(--bottom-nav-height);
            z-index: 100;
            padding: 8px 0;
            backdrop-filter: blur(20px);
            box-shadow: 0 -2px 10px rgba(30, 77, 59, 0.1);
        }

        .bottom-nav-items {
            display: flex;
            height: 100%;
        }

        .bottom-nav-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: var(--gray-500);
            transition: all var(--transition-normal);
            padding: 8px;
            position: relative;
        }

        .bottom-nav-item.active {
            color: var(--primary-green);
        }

        .bottom-nav-item.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 32px;
            height: 3px;
            background: var(--primary-green);
            border-radius: 0 0 2px 2px;
        }

        .bottom-nav-icon {
            font-size: 1.5rem;
            margin-bottom: 4px;
            transition: all var(--transition-normal);
        }

        .bottom-nav-item.active .bottom-nav-icon {
            transform: scale(1.1);
        }

        .bottom-nav-text {
            font-size: 0.7rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* ==== ULTRA-MODERN RESPONSIVE DESIGN ==== */
        @media (max-width: 1024px) {
            .search-input {
                width: 250px;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar.collapsed + .main-content {
                margin-left: 0;
            }

            .menu-toggle {
                display: flex;
            }

            .main-header {
                padding: 12px 16px;
            }

            .page-title {
                font-size: 1.3rem;
            }

            .search-input {
                width: 100%;
                max-width: 200px;
            }

            .content-wrapper {
                padding: 0 16px 16px 16px;
            }

            .bottom-nav {
                display: block;
            }
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 1.1rem;
            }

            .content-wrapper {
                padding: 0 12px 12px 12px;
            }

            .search-input {
                max-width: 150px;
            }

            .header-actions {
                gap: 12px;
            }
        }

        /* ==== ULTRA-MODERN ANIMATIONS ==== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(100%); }
            to { opacity: 1; transform: translateX(0); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: var(--shadow); }
            50% { box-shadow: var(--shadow-glow); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* ==== PROFESSIONAL DARK THEME ==== */
        .dark-theme {
            background: var(--gray-900);
            color: var(--gray-100);
        }

        .dark-theme .sidebar,
        .dark-theme .main-header,
        .dark-theme .bottom-nav {
            background: var(--gray-800);
            border-color: var(--gray-700);
        }

        .dark-theme .sidebar-header {
            background: var(--primary-gradient);
        }

        .dark-theme .nav-item {
            color: var(--gray-300);
        }

        .dark-theme .nav-item:hover {
            background: rgba(30, 77, 59, 0.2);
            color: var(--accent-green);
        }

        .dark-theme .nav-item.active {
            background: rgba(30, 77, 59, 0.2);
            color: var(--accent-green);
            border-left-color: var(--accent-green);
        }

        .dark-theme .search-input {
            background: var(--gray-700);
            border-color: var(--gray-600);
            color: var(--gray-100);
        }

        .dark-theme .search-input::placeholder {
            color: var(--gray-400);
        }

        .dark-theme .header-btn {
            background: var(--gray-700);
            border-color: var(--gray-600);
            color: var(--gray-100);
        }

        .dark-theme .header-btn:hover {
            border-color: var(--accent-green);
            background: rgba(30, 77, 59, 0.2);
            color: var(--accent-green);
        }

        .dark-theme .bottom-nav-item {
            color: var(--gray-400);
        }

        .dark-theme .bottom-nav-item.active {
            color: var(--accent-green);
        }

        /* ==== SIDEBAR OVERLAY ==== */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(30, 77, 59, 0.5);
            z-index: 150;
            backdrop-filter: blur(4px);
        }

        @media (max-width: 768px) {
            .sidebar-overlay.active {
                display: block;
                animation: fadeIn 0.3s ease;
            }
        }

        /* ==== UTILITY CLASSES ==== */
        .hidden { display: none !important; }
        .visible { display: block !important; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .font-bold { font-weight: 700; }
        .font-semibold { font-weight: 600; }
        .text-primary { color: var(--primary-green); }
        .text-success { color: var(--success-green); }
        .text-warning { color: var(--warning); }
        .text-danger { color: var(--danger); }
        .bg-primary { background: var(--primary-green); }
        .bg-success { background: var(--success-green); }
        .bg-warning { background: var(--warning); }
        .bg-danger { background: var(--danger); }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar Overlay -->
    <div class="sidebar-overlay" id="sidebarOverlay"></div>

    <!-- App Container -->
    <div class="app-container">
        <!-- Ultra-Modern Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="sidebar-logo">
                    <div class="sidebar-logo-icon">🌿</div>
                    <h1 class="sidebar-logo-text">CHANNAB</h1>
                </div>
                <button class="sidebar-toggle" id="sidebarToggle">
                    <span>⮃</span>
                </button>
            </div>

            <nav class="sidebar-nav">
                {% block sidebar_nav %}
                <div class="nav-section">
                    <div class="nav-section-title">Main</div>
                    <a href="{% url 'accounts:dashboard' %}" class="nav-item {% block nav_home %}{% endblock %}">
                        <div class="nav-item-icon">🏠</div>
                        <div class="nav-item-text">Dashboard</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Customer Management</div>
                    <a href="{% url 'customers:list' %}" class="nav-item {% block nav_customers %}{% endblock %}">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Customers</div>
                    </a>
                    <a href="{% url 'customers:create' %}" class="nav-item {% block nav_add_customer %}{% endblock %}">
                        <div class="nav-item-icon">➕</div>
                        <div class="nav-item-text">Add Customer</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Employee Management</div>
                    <a href="{% url 'employees:dashboard' %}" class="nav-item {% block nav_employees_dashboard %}{% endblock %}">
                        <div class="nav-item-icon">👷</div>
                        <div class="nav-item-text">Employee Dashboard</div>
                    </a>
                    <a href="{% url 'employees:employee-list' %}" class="nav-item {% block nav_employees_list %}{% endblock %}">
                        <div class="nav-item-icon">👥</div>
                        <div class="nav-item-text">Employee List</div>
                    </a>
                    <a href="{% url 'employees:attendance-dashboard' %}" class="nav-item {% block nav_attendance %}{% endblock %}">
                        <div class="nav-item-icon">📅</div>
                        <div class="nav-item-text">Attendance</div>
                    </a>
                    <a href="{% url 'employees:leave-dashboard' %}" class="nav-item {% block nav_leaves %}{% endblock %}">
                        <div class="nav-item-icon">🏖️</div>
                        <div class="nav-item-text">Leave Management</div>
                    </a>
                    <a href="{% url 'employees:payroll-dashboard' %}" class="nav-item {% block nav_payroll %}{% endblock %}">
                        <div class="nav-item-icon">💳</div>
                        <div class="nav-item-text">Payroll</div>
                    </a>
                    <a href="{% url 'employees:reports-dashboard' %}" class="nav-item {% block nav_employee_reports %}{% endblock %}">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Employee Reports</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Point of Sale</div>
                    <a href="{% url 'inventory:pos-interface' %}" class="nav-item {% block nav_pos %}{% endblock %}">
                        <div class="nav-item-icon">💳</div>
                        <div class="nav-item-text">POS System</div>
                    </a>
                    <a href="{% url 'inventory:pos-transactions' %}" class="nav-item {% block nav_pos_transactions %}{% endblock %}">
                        <div class="nav-item-icon">📋</div>
                        <div class="nav-item-text">Transactions</div>
                    </a>
                    <a href="#" class="nav-item" onclick="alert('Coming soon!')">
                        <div class="nav-item-icon">📊</div>
                        <div class="nav-item-text">Sales Reports</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Product Management</div>
                    <a href="{% url 'inventory:product-list' %}" class="nav-item {% block nav_products %}{% endblock %}">
                        <div class="nav-item-icon">📦</div>
                        <div class="nav-item-text">Products</div>
                    </a>
                    <a href="{% url 'inventory:category-list' %}" class="nav-item {% block nav_add_product %}{% endblock %}">
                        <div class="nav-item-icon">📁</div>
                        <div class="nav-item-text">Categories</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Inventory Management</div>
                    <a href="{% url 'inventory:category-list' %}" class="nav-item {% block nav_inventory_categories %}{% endblock %}">
                        <div class="nav-item-icon">📂</div>
                        <div class="nav-item-text">Categories</div>
                    </a>
                    <a href="{% url 'inventory:category-create' %}" class="nav-item {% block nav_inventory_add_category %}{% endblock %}">
                        <div class="nav-item-icon">➕</div>
                        <div class="nav-item-text">Add Category</div>
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">Settings</div>
                    <a href="{% url 'accounts:logout' %}" class="nav-item {% block nav_logout %}{% endblock %}">
                        <div class="nav-item-icon">🚪</div>
                        <div class="nav-item-text">Logout</div>
                    </a>
                </div>
                {% endblock %}
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Ultra-Modern Header -->
            <header class="main-header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle">☰</button>
                    <div>
                        <h1 class="page-title">{% block page_title %}Dashboard{% endblock %}</h1>
                        {% block breadcrumb %}
                        <nav class="breadcrumb">
                            <a href="{% url 'accounts:dashboard' %}" class="breadcrumb-item">Home</a>
                            <span class="breadcrumb-separator">›</span>
                            <span class="breadcrumb-item active">{% block breadcrumb_current %}Dashboard{% endblock %}</span>
                        </nav>
                        {% endblock %}
                    </div>
                </div>
                <div class="header-actions">
                    {% block header_search %}
                    <div class="search-container">
                        <input type="search" class="search-input" placeholder="Search..." id="globalSearch">
                        <span class="search-icon">🔍</span>
                    </div>
                    {% endblock %}
                    <button class="header-btn" id="notificationBtn">
                        🔔
                        <span class="notification-badge">3</span>
                    </button>
                    <button class="header-btn" id="themeToggle">🌙</button>
                    {% block header_actions %}{% endblock %}
                </div>
            </header>

            {% block filter_bar %}{% endblock %}

            <!-- Content Wrapper -->
            <div class="content-wrapper">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="bottom-nav">
        <div class="bottom-nav-items">
            {% block bottom_nav %}
            <a href="{% url 'accounts:dashboard' %}" class="bottom-nav-item {% block bottom_nav_home %}{% endblock %}">
                <div class="bottom-nav-icon">🏠</div>
                <div class="bottom-nav-text">Home</div>
            </a>
            <a href="{% url 'inventory:product-list' %}" class="bottom-nav-item {% block bottom_nav_products %}{% endblock %}">
                <div class="bottom-nav-icon">📦</div>
                <div class="bottom-nav-text">Products</div>
            </a>
            <a href="{% url 'customers:list' %}" class="bottom-nav-item {% block bottom_nav_customers %}{% endblock %}">
                <div class="bottom-nav-icon">👥</div>
                <div class="bottom-nav-text">Customers</div>
            </a>
            <a href="{% url 'employees:dashboard' %}" class="bottom-nav-item {% block bottom_nav_employees %}{% endblock %}">
                <div class="bottom-nav-icon">👷</div>
                <div class="bottom-nav-text">Employees</div>
            </a>
            {% endblock %}
        </div>
    </nav>

    <script>
        // Ultra-Modern CHANNAB App System
        document.addEventListener('DOMContentLoaded', function() {
            initializeChannabApp();
        });

        function initializeChannabApp() {
            setupNavigationSystem();
            setupMobileOptimizations();
            setupThemeSystem();
            setupSearchSystem();
            setupNotifications();
        }

        function setupNavigationSystem() {
            // Mobile menu toggle
            const menuToggle = document.getElementById('menuToggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebarOverlay');
            const sidebarToggle = document.getElementById('sidebarToggle');

            if (menuToggle) {
                menuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('active');
                    sidebarOverlay.classList.toggle('active');
                    hapticFeedback();
                });
            }

            // Sidebar collapse toggle (desktop)
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    localStorage.setItem('channab-sidebar-collapsed', sidebar.classList.contains('collapsed'));
                    hapticFeedback();
                });
            }

            // Close sidebar when clicking overlay
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                });
            }

            // Close sidebar when clicking nav item on mobile
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        sidebar.classList.remove('active');
                        sidebarOverlay.classList.remove('active');
                    }
                });
            });

            // Bottom navigation active state
            const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
            bottomNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    bottomNavItems.forEach(navItem => navItem.classList.remove('active'));
                    this.classList.add('active');
                    hapticFeedback();
                });
            });

            // Restore sidebar state
            const isCollapsed = localStorage.getItem('channab-sidebar-collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
            }
        }

        function setupMobileOptimizations() {
            // Dynamic viewport height
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }
            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Touch feedback for interactive elements
            document.querySelectorAll('.nav-item, .bottom-nav-item, .header-btn, .sidebar-toggle').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = this.style.transform + ' scale(0.95)';
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.style.transform = this.style.transform.replace(' scale(0.95)', '');
                }, { passive: true });
            });

            // Header scroll effect
            let lastScrollY = window.scrollY;
            window.addEventListener('scroll', () => {
                const header = document.querySelector('.main-header');
                if (window.scrollY > 50) {
                    header.style.boxShadow = 'var(--shadow-lg)';
                } else {
                    header.style.boxShadow = 'var(--shadow-sm)';
                }

                // Hide/show header on scroll (mobile)
                if (window.innerWidth <= 768) {
                    if (window.scrollY > lastScrollY && window.scrollY > 100) {
                        header.style.transform = 'translateY(-100%)';
                    } else {
                        header.style.transform = 'translateY(0)';
                    }
                }
                lastScrollY = window.scrollY;
            }, { passive: true });

            // Close sidebar on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    const sidebar = document.getElementById('sidebar');
                    const sidebarOverlay = document.getElementById('sidebarOverlay');
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                }
            });
        }

        function setupThemeSystem() {
            const themeToggle = document.getElementById('themeToggle');
            const body = document.body;

            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    body.classList.toggle('dark-theme');
                    const isDark = body.classList.contains('dark-theme');

                    themeToggle.textContent = isDark ? '☀️' : '🌙';
                    localStorage.setItem('channab-theme', isDark ? 'dark' : 'light');

                    hapticFeedback();

                    // Theme transition effect
                    body.style.transition = 'background-color 0.3s ease, color 0.3s ease';
                    setTimeout(() => {
                        body.style.transition = '';
                    }, 300);
                });
            }

            // Initialize theme from localStorage
            const savedTheme = localStorage.getItem('channab-theme');
            if (savedTheme === 'dark') {
                body.classList.add('dark-theme');
                if (themeToggle) themeToggle.textContent = '☀️';
            }
        }

        function setupSearchSystem() {
            const globalSearch = document.getElementById('globalSearch');

            if (globalSearch) {
                globalSearch.addEventListener('input', function() {
                    const query = this.value.toLowerCase().trim();
                    // Implement global search functionality
                    console.log('Global search:', query);
                });

                // Search shortcuts
                document.addEventListener('keydown', function(e) {
                    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                        e.preventDefault();
                        globalSearch.focus();
                    }
                });
            }
        }

        function setupNotifications() {
            const notificationBtn = document.getElementById('notificationBtn');

            if (notificationBtn) {
                notificationBtn.addEventListener('click', function() {
                    // Implement notification system
                    showNotification('You have 3 new notifications', 'info');
                    hapticFeedback();
                });
            }
        }

        function hapticFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate(10);
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 90px;
                right: 20px;
                background: var(--primary-green);
                color: white;
                padding: 12px 20px;
                border-radius: 12px;
                font-size: 0.9rem;
                z-index: 1000;
                box-shadow: var(--shadow-green);
                animation: slideIn 0.3s ease;
                max-width: 300px;
            `;

            if (type === 'success') notification.style.background = 'var(--success-green)';
            if (type === 'warning') notification.style.background = 'var(--warning)';
            if (type === 'danger') notification.style.background = 'var(--danger)';

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideOut 0.3s ease';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 3000);
        }

        // Prevent sidebar from staying open on orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                const sidebar = document.getElementById('sidebar');
                const sidebarOverlay = document.getElementById('sidebarOverlay');
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('active');
                    sidebarOverlay.classList.remove('active');
                }
            }, 100);
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>