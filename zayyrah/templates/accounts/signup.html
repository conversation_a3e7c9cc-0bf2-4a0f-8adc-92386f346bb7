{% extends 'base.html' %}

{% block title %}Create Account · Zayyrah{% endblock %}
{% block body_class %}auth-page signup-page{% endblock %}

{% block content %}
<section class="auth-layout">
    <article class="auth-illustration">
        <span class="highlight-pill">Join the Zayyrah community</span>
        <h2>Set up your next-generation retail hub in minutes.</h2>
        <p>Create a customer or business profile to unlock tailored dashboards, lightning-fast order management, and insights that help you grow smarter.</p>
        <ul class="feature-list">
            <li><span class="feature-icon">✓</span><span>Mobile-first design that works beautifully on phones and tablets.</span></li>
            <li><span class="feature-icon">✓</span><span>Business accounts capture shop details for branded receipts.</span></li>
            <li><span class="feature-icon">✓</span><span>Secure authentication with instant dashboard access after signup.</span></li>
        </ul>
    </article>

    <div class="auth-card">
        <header>
            <h2>Create your account</h2>
            <p>We’ll help you get started. Switch between customer and business anytime.</p>
        </header>

        <form method="post" class="auth-form" novalidate>
            {% csrf_token %}

            {% if form.non_field_errors %}
                <div class="non-field-errors">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="form-group">
                <label for="id_mobile_number">Mobile number</label>
                {{ form.mobile_number }}
                {% if form.mobile_number.errors %}
                    <ul class="errorlist">
                        {% for error in form.mobile_number.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="id_account_type">Account type</label>
                {{ form.account_type }}
                {% if form.account_type.errors %}
                    <ul class="errorlist">
                        {% for error in form.account_type.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group shop-name-group" id="shop-name-group">
                <label for="id_shop_name">Shop name</label>
                {{ form.shop_name }}
                <p class="password-hint">Visible on receipts and customer notifications.</p>
                {% if form.shop_name.errors %}
                    <ul class="errorlist">
                        {% for error in form.shop_name.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="id_password1">Password</label>
                {{ form.password1 }}
                <p class="password-hint">Use a mix of numbers and letters for extra security.</p>
                {% if form.password1.errors %}
                    <ul class="errorlist">
                        {% for error in form.password1.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="id_password2">Confirm password</label>
                {{ form.password2 }}
                {% if form.password2.errors %}
                    <ul class="errorlist">
                        {% for error in form.password2.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-footer">
                <button type="submit" class="primary-button">Create account</button>
                <p class="account-switch">Already have an account? <a href="{% url 'accounts:login' %}">Log in instead</a></p>
            </div>
        </form>
    </div>
</section>

<script>
    const accountTypeField = document.getElementById('id_account_type');
    const shopNameGroup = document.getElementById('shop-name-group');

    function toggleShopName() {
        if (!accountTypeField || !shopNameGroup) return;
        const isBusiness = accountTypeField.value === 'business';
        shopNameGroup.dataset.hidden = isBusiness ? 'false' : 'true';
    }

    toggleShopName();
    accountTypeField && accountTypeField.addEventListener('change', toggleShopName);
</script>
{% endblock %}
