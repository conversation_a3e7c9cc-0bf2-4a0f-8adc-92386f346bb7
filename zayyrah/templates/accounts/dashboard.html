{% extends 'base/app_base.html' %}

{% block title %}Dashboard - CHANNAB{% endblock %}

{% block nav_home %}active{% endblock %}
{% block bottom_nav_home %}active{% endblock %}

{% block page_title %}Dashboard{% endblock %}
{% block breadcrumb_current %}Dashboard{% endblock %}

{% block content %}
<!-- Dashboard Stats Cards -->
<div class="stats-section">
    <div class="stat-card primary-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">👷</div>
            <div class="stat-content">
                <div class="stat-title">TOTAL EMPLOYEES</div>
                <div class="stat-number">{{ total_employees|default:"0" }}</div>
                <div class="stat-details">
                    <span>Active workforce</span>
                    <span>{{ departments_count|default:"0" }} departments</span>
                </div>
            </div>
        </div>
    </div>

    <div class="stat-card success-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">👥</div>
            <div class="stat-content">
                <div class="stat-title">TOTAL CUSTOMERS</div>
                <div class="stat-number">{{ total_customers|default:"0" }}</div>
                <div class="stat-details">
                    <span>Registered customers</span>
                    <span>Active accounts</span>
                </div>
            </div>
        </div>
    </div>

    <div class="stat-card warning-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">💰</div>
            <div class="stat-content">
                <div class="stat-title">MONTHLY REVENUE</div>
                <div class="stat-number">Rs. {{ monthly_revenue|default:"0" }}</div>
                <div class="stat-details">
                    <span>This month</span>
                    <span>{{ sales_count|default:"0" }} sales</span>
                </div>
            </div>
        </div>
    </div>

    <div class="stat-card info-stat fade-in">
        <div class="stat-header">
            <div class="stat-icon">📦</div>
            <div class="stat-content">
                <div class="stat-title">INVENTORY ITEMS</div>
                <div class="stat-number">{{ inventory_count|default:"0" }}</div>
                <div class="stat-details">
                    <span>Products in stock</span>
                    <span>{{ low_stock_items|default:"0" }} low stock</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Welcome Section -->
<div class="welcome-section">
    <div class="welcome-card fade-in">
        <div class="welcome-header">
            <div class="welcome-avatar">
                {{ request.user.mobile_number.0|upper }}
            </div>
            <div class="welcome-info">
                <h2>Welcome back, {{ request.user.mobile_number }}!</h2>
                <p>You're signed in as a <strong>{{ request.user.get_account_type_display }}</strong> account.</p>
                {% if request.user.account_type == 'business' and request.user.shop_name %}
                <p>Your shop: <strong>{{ request.user.shop_name }}</strong></p>
                {% endif %}
            </div>
        </div>

        <div class="welcome-actions">
            <a href="{% url 'customers:list' %}" class="action-card">
                <div class="action-icon">👥</div>
                <div class="action-text">
                    <h3>Manage Customers</h3>
                    <p>View and manage customer accounts</p>
                </div>
            </a>

            <a href="{% url 'employees:dashboard' %}" class="action-card">
                <div class="action-icon">👷</div>
                <div class="action-text">
                    <h3>Employee Management</h3>
                    <p>Track attendance and payroll</p>
                </div>
            </a>

            <a href="{% url 'pos:sale-create' %}" class="action-card">
                <div class="action-icon">💳</div>
                <div class="action-text">
                    <h3>Point of Sale</h3>
                    <p>Process sales and transactions</p>
                </div>
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Dashboard-specific styles */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: var(--white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-green);
}

.stat-card.primary-stat {
    background: var(--primary-gradient);
    color: white;
    border: none;
}

.stat-card.success-stat {
    background: var(--success-gradient);
    color: white;
    border: none;
}

.stat-card.warning-stat {
    background: linear-gradient(135deg, var(--warning) 0%, var(--warning-light) 100%);
    color: white;
    border: none;
}

.stat-card.info-stat {
    background: linear-gradient(135deg, var(--info) 0%, var(--info-light) 100%);
    color: white;
    border: none;
}

.stat-header {
    display: flex;
    align-items: center;
    gap: 16px;
}

.stat-icon {
    width: 56px;
    height: 56px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    backdrop-filter: blur(10px);
}

.stat-content {
    flex: 1;
}

.stat-title {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    opacity: 0.9;
    margin-bottom: 8px;
}

.stat-number {
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1;
    margin-bottom: 8px;
}

.stat-details {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stat-details span {
    display: block;
    margin-bottom: 2px;
}

.welcome-section {
    margin-bottom: 32px;
}

.welcome-card {
    background: var(--white);
    border-radius: 16px;
    padding: 32px;
    box-shadow: var(--shadow);
    border: 1px solid var(--gray-200);
}

.welcome-header {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-bottom: 32px;
}

.welcome-avatar {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
}

.welcome-info h2 {
    color: var(--primary-green);
    font-size: 1.8rem;
    margin-bottom: 8px;
}

.welcome-info p {
    color: var(--gray-600);
    margin-bottom: 4px;
}

.welcome-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: var(--gray-50);
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    border: 1px solid var(--gray-200);
}

.action-card:hover {
    background: var(--success-lighter);
    border-color: var(--primary-green);
    transform: translateY(-2px);
    text-decoration: none;
}

.action-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-green);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.action-text h3 {
    color: var(--primary-green);
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.action-text p {
    color: var(--gray-600);
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .welcome-card {
        padding: 24px;
    }

    .welcome-header {
        flex-direction: column;
        text-align: center;
        gap: 16px;
    }

    .welcome-actions {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-section {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}
