{% extends 'base.html' %}

{% block title %}Login · Zayyrah{% endblock %}
{% block body_class %}auth-page login-page{% endblock %}

{% block content %}
<section class="auth-layout">
    <article class="auth-illustration" aria-hidden="false">
        <span class="highlight-pill">Built for modern businesses</span>
        <h2>Effortless selling starts with a quick login.</h2>
        <p>Access your personalised dashboard, keep tabs on customers, and manage every order in one place. Your business, running at full speed.</p>
        <ul class="feature-list">
            <li><span class="feature-icon">1</span><span>Secure mobile-first sign in keeps your data protected.</span></li>
            <li><span class="feature-icon">2</span><span>Real-time insights tailored to your store.</span></li>
            <li><span class="feature-icon">3</span><span>Optimised to work beautifully on any device.</span></li>
        </ul>
    </article>

    <div class="auth-card" role="form">
        <header>
            <h2>Welcome back</h2>
            <p>Enter the mobile number and password linked to your Zayyrah account.</p>
        </header>

        <form method="post" class="auth-form" novalidate>
            {% csrf_token %}
            {% if form.non_field_errors %}
                <div class="non-field-errors">
                    {% for error in form.non_field_errors %}
                        <div>{{ error }}</div>
                    {% endfor %}
                </div>
            {% endif %}

            {% for hidden_field in form.hidden_fields %}
                {{ hidden_field }}
            {% endfor %}

            <div class="form-group">
                <label for="id_username">Mobile number</label>
                {{ form.username }}
                {% if form.username.errors %}
                    <ul class="errorlist">
                        {% for error in form.username.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-group">
                <label for="id_password">Password</label>
                {{ form.password }}
                {% if form.password.errors %}
                    <ul class="errorlist">
                        {% for error in form.password.errors %}
                            <li>{{ error }}</li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>

            <div class="form-footer">
                <button type="submit" class="primary-button">Log in</button>
                <div class="auth-links">
                    <p class="account-switch">New to Zayyrah? <a href="{% url 'accounts:signup' %}">Create a free account</a></p>
                    <p class="forgot-password-link"><a href="#" onclick="showPasswordResetForm()">Forgot your password?</a></p>
                </div>
            </div>
        </form>

        <!-- Password Reset Form -->
        <div id="password-reset-form" class="auth-form" style="display: none;">
            <header>
                <h2>Reset Password</h2>
                <p>Enter your mobile number to receive an OTP for password reset.</p>
            </header>

            <form id="reset-request-form" novalidate>
                <div class="form-group">
                    <label for="reset-mobile">Mobile number</label>
                    <input type="tel" id="reset-mobile" name="mobile_number" required placeholder="Enter your mobile number">
                    <div class="error-message" id="mobile-error"></div>
                </div>

                <div class="form-footer">
                    <button type="submit" class="primary-button">Send OTP</button>
                    <p class="account-switch"><a href="#" onclick="showLoginForm()">Back to login</a></p>
                </div>
            </form>

            <!-- OTP Verification Form -->
            <form id="otp-verification-form" style="display: none;" novalidate>
                <div class="form-group">
                    <label for="otp-code">Enter OTP</label>
                    <input type="text" id="otp-code" name="otp_code" maxlength="6" required placeholder="6-digit OTP">
                    <div class="error-message" id="otp-error"></div>
                </div>

                <div class="form-group">
                    <label for="new-password">New Password</label>
                    <input type="password" id="new-password" name="new_password" required placeholder="Enter new password">
                    <div class="error-message" id="password-error"></div>
                </div>

                <div class="form-group">
                    <label for="confirm-password">Confirm Password</label>
                    <input type="password" id="confirm-password" name="confirm_password" required placeholder="Confirm new password">
                    <div class="error-message" id="confirm-error"></div>
                </div>

                <div class="form-footer">
                    <button type="submit" class="primary-button">Reset Password</button>
                    <p class="account-switch"><a href="#" onclick="showLoginForm()">Back to login</a></p>
                </div>
            </form>
        </div>
    </div>
</section>

<script>
function showPasswordResetForm() {
    console.log('Showing password reset form');
    document.querySelector('.auth-form').style.display = 'none';
    document.getElementById('password-reset-form').style.display = 'block';
    document.getElementById('reset-request-form').style.display = 'block';
    document.getElementById('otp-verification-form').style.display = 'none';
}

function showLoginForm() {
    console.log('Showing login form');
    document.querySelector('.auth-form').style.display = 'block';
    document.getElementById('password-reset-form').style.display = 'none';
}

function showOTPForm() {
    console.log('Showing OTP form');
    document.getElementById('reset-request-form').style.display = 'none';
    document.getElementById('otp-verification-form').style.display = 'block';
}

function clearErrors() {
    document.querySelectorAll('.error-message').forEach(el => el.textContent = '');
}

function showError(elementId, message) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = message;
        element.style.color = 'red';
    }
}

function showSuccess(message) {
    alert(message); // You can replace this with a better notification system
}

// Wait for DOM to be ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, setting up event listeners');

    // Handle password reset request
    const resetRequestForm = document.getElementById('reset-request-form');
    if (resetRequestForm) {
        resetRequestForm.addEventListener('submit', async function(e) {
            console.log('Reset request form submitted');
            e.preventDefault();
            clearErrors();

            const mobileNumber = document.getElementById('reset-mobile').value;

            if (!mobileNumber) {
                showError('mobile-error', 'Mobile number is required');
                return;
            }

            try {
                const response = await fetch('/api/v1/accounts/password-reset/request/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        mobile_number: mobileNumber
                    })
                });

                const data = await response.json();
                console.log('Reset request response:', data);

                if (data.success) {
                    showSuccess('OTP sent successfully! Check the terminal for your OTP code.');
                    showOTPForm();
                } else {
                    if (data.errors && data.errors.mobile_number) {
                        showError('mobile-error', data.errors.mobile_number[0]);
                    } else {
                        showError('mobile-error', data.message || 'Failed to send OTP');
                    }
                }
            } catch (error) {
                console.error('Network error:', error);
                showError('mobile-error', 'Network error. Please try again.');
            }
        });
    }

    // Handle OTP verification and password reset
    const otpVerificationForm = document.getElementById('otp-verification-form');
    if (otpVerificationForm) {
        otpVerificationForm.addEventListener('submit', async function(e) {
            console.log('OTP verification form submitted');
            e.preventDefault();
            clearErrors();

            const mobileNumber = document.getElementById('reset-mobile').value;
            const otpCode = document.getElementById('otp-code').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;

            console.log('Form data:', { mobileNumber, otpCode: '***', newPassword: '***', confirmPassword: '***' });

            // Client-side validation
            if (!otpCode) {
                showError('otp-error', 'OTP is required');
                return;
            }

            if (!newPassword) {
                showError('password-error', 'New password is required');
                return;
            }

            if (newPassword !== confirmPassword) {
                showError('confirm-error', 'Passwords do not match');
                return;
            }

            try {
                const response = await fetch('/api/v1/accounts/password-reset/confirm/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                    },
                    body: JSON.stringify({
                        mobile_number: mobileNumber,
                        otp_code: otpCode,
                        new_password: newPassword,
                        confirm_password: confirmPassword
                    })
                });

                const data = await response.json();
                console.log('Password reset response:', data);

                if (data.success) {
                    showSuccess('Password reset successful! You can now login with your new password.');
                    showLoginForm();
                    // Clear the form
                    document.getElementById('reset-mobile').value = '';
                    document.getElementById('otp-code').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';
                } else {
                    if (data.errors) {
                        if (data.errors.otp_code) {
                            showError('otp-error', data.errors.otp_code[0]);
                        }
                        if (data.errors.new_password) {
                            showError('password-error', data.errors.new_password[0]);
                        }
                        if (data.errors.mobile_number) {
                            showError('otp-error', data.errors.mobile_number[0]);
                        }
                        if (data.errors.non_field_errors) {
                            showError('otp-error', data.errors.non_field_errors[0]);
                        }
                    } else {
                        showError('otp-error', data.message || 'Failed to reset password');
                    }
                }
            } catch (error) {
                console.error('Network error:', error);
                showError('otp-error', 'Network error. Please try again.');
            }
        });
    }
});
</script>
{% endblock %}
