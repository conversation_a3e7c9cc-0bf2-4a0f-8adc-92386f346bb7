import json
from decimal import Decimal
from json import JSONDecodeError

from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import redirect, render
from django.urls import reverse, reverse_lazy
from django.views import View
from django.views.generic import DetailView, ListView
from django.views.generic.edit import CreateView, FormMixin, UpdateView
from django.utils.text import slugify

from customers.models import Customer

from .forms import ProductForm, ProductStockEntryForm, SaleCheckoutForm
from .models import Sale, SaleItem
from inventory.models import EnhancedProduct


class OwnerQuerysetMixin(LoginRequiredMixin):
    def get_queryset(self):
        return super().get_queryset().filter(owner=self.request.user)


class ProductListView(OwnerQuerysetMixin, ListView):
    template_name = 'pos/product_list.html'
    model = EnhancedProduct
    context_object_name = 'products'
    paginate_by = 12

    def get_queryset(self):
        return super().get_queryset().select_related('category').prefetch_related('stock_entries')


class ProductCreateView(LoginRequiredMixin, CreateView):
    template_name = 'inventory/product_form.html'
    model = EnhancedProduct
    form_class = ProductForm
    success_url = reverse_lazy('pos:product-list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['owner'] = self.request.user
        print(f"[DEBUG] ProductCreateView.get_form_kwargs: owner={self.request.user}")
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from inventory.models import Category
        categories = Category.objects.filter(owner=self.request.user, is_active=True).order_by('name')
        context['categories'] = categories
        print(f"[DEBUG] ProductCreateView.get_context_data: Found {categories.count()} categories for user {self.request.user}")
        return context

    def post(self, request, *args, **kwargs):
        print(f"[DEBUG] ProductCreateView.post: Received POST request")
        print(f"[DEBUG] POST data: {dict(request.POST)}")
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        print(f"[DEBUG] ProductCreateView.form_valid: Form is valid")
        print(f"[DEBUG] Form cleaned_data: {form.cleaned_data}")
        form.instance.owner = self.request.user
        print(f"[DEBUG] Set owner to: {self.request.user}")

        try:
            result = super().form_valid(form)
            print(f"[DEBUG] Product created successfully: {form.instance}")
            messages.success(self.request, 'Product created.')
            return result
        except Exception as e:
            print(f"[DEBUG] Error saving product: {e}")
            raise

    def form_invalid(self, form):
        print(f"[DEBUG] ProductCreateView.form_invalid: Form has errors")
        print(f"[DEBUG] Form errors: {form.errors}")
        print(f"[DEBUG] Form non_field_errors: {form.non_field_errors()}")
        return super().form_invalid(form)


class ProductUpdateView(OwnerQuerysetMixin, UpdateView):
    template_name = 'inventory/product_form.html'
    model = EnhancedProduct
    form_class = ProductForm

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['owner'] = self.request.user
        print(f"[DEBUG] ProductUpdateView.get_form_kwargs: owner={self.request.user}, instance={kwargs.get('instance')}")
        return kwargs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        from inventory.models import Category
        categories = Category.objects.filter(owner=self.request.user, is_active=True).order_by('name')
        context['categories'] = categories
        print(f"[DEBUG] ProductUpdateView.get_context_data: Found {categories.count()} categories for user {self.request.user}")
        print(f"[DEBUG] Current product: {self.object}")
        return context

    def post(self, request, *args, **kwargs):
        print(f"[DEBUG] ProductUpdateView.post: Received POST request for product {kwargs.get('pk')}")
        print(f"[DEBUG] POST data: {dict(request.POST)}")
        return super().post(request, *args, **kwargs)

    def form_valid(self, form):
        print(f"[DEBUG] ProductUpdateView.form_valid: Form is valid")
        print(f"[DEBUG] Form cleaned_data: {form.cleaned_data}")
        print(f"[DEBUG] Product being updated: {self.object}")

        try:
            result = super().form_valid(form)
            print(f"[DEBUG] Product updated successfully: {form.instance}")
            messages.success(self.request, 'Product updated.')
            return result
        except Exception as e:
            print(f"[DEBUG] Error updating product: {e}")
            raise

    def form_invalid(self, form):
        print(f"[DEBUG] ProductUpdateView.form_invalid: Form has errors")
        print(f"[DEBUG] Form errors: {form.errors}")
        print(f"[DEBUG] Form non_field_errors: {form.non_field_errors()}")
        return super().form_invalid(form)

    def get_success_url(self):
        print(f"[DEBUG] ProductUpdateView.get_success_url: Redirecting to product list")
        return reverse('pos:product-list')


class ProductDetailView(OwnerQuerysetMixin, FormMixin, DetailView):
    template_name = 'pos/product_detail.html'
    model = EnhancedProduct
    context_object_name = 'product'
    form_class = ProductStockEntryForm

    def get_form_kwargs(self):
        kwargs = FormMixin.get_form_kwargs(self)
        product = getattr(self, 'object', None) or self.get_object()
        kwargs['product'] = product
        return kwargs

    def get_success_url(self):
        return reverse('pos:product-detail', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        product = self.object
        stock_form = kwargs.get('form') or self.get_form()
        context.update(
            {
                'stock_form': stock_form,
                'stock_entries': product.stock_entries.order_by('-added_on', '-created_at'),
                'sale_items': product.sale_items.select_related('sale', 'sale__customer').order_by('-sale__created_at'),
            }
        )
        return context

    def post(self, request, *args, **kwargs):
        self.object = self.get_object()
        form = self.get_form()
        if form.is_valid():
            stock_entry = form.save(commit=False)
            stock_entry.owner = request.user
            stock_entry.product = self.object
            stock_entry.quantity_remaining = stock_entry.quantity_added
            stock_entry.save()
            messages.success(request, 'Stock entry recorded.')
            return redirect(self.get_success_url())
        return self.render_to_response(self.get_context_data(form=form))


class SaleListView(OwnerQuerysetMixin, ListView):
    template_name = 'pos/sale_list.html'
    model = Sale
    context_object_name = 'sales'
    paginate_by = 20


class SaleDetailView(OwnerQuerysetMixin, DetailView):
    template_name = 'pos/sale_detail.html'
    model = Sale
    context_object_name = 'sale'


class SaleCreateView(LoginRequiredMixin, View):
    template_name = 'pos/sale_checkout.html'

    def get(self, request):
        checkout_form = SaleCheckoutForm(owner=request.user)
        return self.render(checkout_form)

    def post(self, request):
        checkout_form = SaleCheckoutForm(request.POST, owner=request.user)
        if checkout_form.is_valid():
            sale = self._create_sale(request, checkout_form)
            if sale:
                messages.success(request, f'Sale #{sale.pk} recorded successfully.')
                return redirect('pos:sale-detail', pk=sale.pk)
        return self.render(checkout_form)

    def render(self, checkout_form):
        products = EnhancedProduct.objects.filter(owner=self.request.user, pos_enabled=True).select_related('category').order_by('name')
        customers_qs = Customer.objects.filter(owner=self.request.user).order_by('-updated_at')
        recent_customers = customers_qs[:12]
        categories = []
        seen = set()
        catalog_entries = []
        for product in products:
            label = product.category.name if product.category else 'Uncategorised'
            slug = slugify(label) or 'uncategorised'
            if label not in seen:
                categories.append({'label': label, 'id': product.category_id, 'slug': slug})
                seen.add(label)
            catalog_entries.append({'product': product, 'category_label': label, 'category_slug': slug})
        product_payload = [
            {
                'id': entry['product'].pk,
                'name': entry['product'].name,
                'price': str(entry['product'].selling_price),
                'tax_rate': str(entry['product'].tax_rate),
                'unit': entry['product'].unit_label,
                'stock': entry['product'].stock_quantity,
                'category': entry['category_label'],
                'category_id': entry['product'].category_id,
            }
            for entry in catalog_entries
        ]
        customers_payload = [
            {
                'id': customer.pk,
                'name': customer.name or 'Customer',
                'mobile_number': customer.mobile_number or '',
            }
            for customer in customers_qs
        ]
        context = {
            'checkout_form': checkout_form,
            'recent_customers': recent_customers,
            'has_products': bool(catalog_entries),
            'products_payload': json.dumps(product_payload),
            'customers_payload': json.dumps(customers_payload),
            'catalog_entries': catalog_entries,
            'categories': categories,
        }
        return render(self.request, self.template_name, context)

    def _create_sale(self, request, checkout_form):
        cart_payload = request.POST.get('cart_payload', '').strip()
        if not cart_payload:
            messages.error(request, 'Add at least one product to complete the sale.')
            return None

        try:
            raw_items = json.loads(cart_payload)
        except JSONDecodeError:
            messages.error(request, 'We could not read the cart items. Try again.')
            return None

        if not isinstance(raw_items, list) or not raw_items:
            messages.error(request, 'Add at least one product to complete the sale.')
            return None

        aggregated = {}
        for entry in raw_items:
            if not isinstance(entry, dict):
                continue
            product_id = entry.get('product_id')
            quantity = entry.get('quantity')
            try:
                quantity = int(quantity)
            except (TypeError, ValueError):
                continue
            if quantity <= 0 or not product_id:
                continue
            aggregated.setdefault(int(product_id), 0)
            aggregated[int(product_id)] += quantity

        if not aggregated:
            messages.error(request, 'Add at least one product to complete the sale.')
            return None

        clean_items = []
        for product_id, quantity in aggregated.items():
            try:
                product = EnhancedProduct.objects.get(owner=request.user, pk=product_id, pos_enabled=True)
            except EnhancedProduct.DoesNotExist:
                messages.error(request, 'One or more products could not be found.')
                return None
            current_stock = product.current_stock if hasattr(product, 'current_stock') else 0
            if current_stock < quantity:
                messages.error(request, f'Not enough stock available for {product.name}.')
                return None
            clean_items.append((product, quantity))

        discount_total = checkout_form.cleaned_data.get('discount_total') or Decimal('0.00')
        customer = checkout_form.cleaned_data.get('customer') if checkout_form.cleaned_data else None
        notes = checkout_form.cleaned_data.get('notes', '')
        payment_method = checkout_form.cleaned_data.get('payment_method') or Sale.PaymentMethod.CASH

        subtotal = Decimal('0.00')
        tax_total = Decimal('0.00')
        sale_items = []

        for product, quantity in clean_items:
            line_subtotal = product.selling_price * quantity
            line_tax = (product.tax_rate / Decimal('100')) * line_subtotal
            subtotal += line_subtotal
            tax_total += line_tax
            sale_items.append((product, quantity, product.selling_price, product.tax_rate, line_subtotal))

        total = subtotal + tax_total - discount_total
        if total < 0:
            total = Decimal('0.00')

        try:
            with transaction.atomic():
                sale = Sale.objects.create(
                    owner=request.user,
                    customer=customer if customer and customer.owner == request.user else None,
                    sale_type=Sale.SaleType.POS,  # Mark as POS transaction
                    subtotal=subtotal,
                    tax_total=tax_total,
                    discount_total=discount_total,
                    total=total,
                    payment_method=payment_method,
                    amount_paid=total,  # POS sales are fully paid by default
                    cash_amount=total if payment_method == Sale.PaymentMethod.CASH else Decimal('0.00'),
                    credit_amount=total if payment_method == Sale.PaymentMethod.CREDIT else Decimal('0.00'),
                    notes=notes,
                )

                for product, quantity, price, tax_rate, _ in sale_items:
                    self._consume_stock(product, quantity)
                    SaleItem.objects.create(
                        sale=sale,
                        product=product,
                        quantity=quantity,
                        price=price,
                        tax_rate=tax_rate,
                    )
        except ValueError:
            messages.error(request, 'Unable to allocate stock for one of the products. Please refresh and try again.')
            return None

        return sale

    @staticmethod
    def _consume_stock(product, quantity):
        product.allocate_stock(quantity)


class SaleEntrySelectionView(LoginRequiredMixin, View):
    """
    View for choosing between Manual Sale Entry and POS Transaction
    """
    template_name = 'pos/sale_entry_selection.html'

    def get(self, request):
        return render(request, self.template_name)


class ManualSaleCreateView(LoginRequiredMixin, View):
    """
    View for creating manual sales with direct total entry
    """
    template_name = 'pos/manual_sale_form.html'

    def get(self, request):
        return render(request, self.template_name)

    def post(self, request):
        # Extract form data
        customer_id = request.POST.get('customer')
        total = request.POST.get('total')
        payment_method = request.POST.get('payment_method', 'cash')
        amount_paid = request.POST.get('amount_paid')
        cash_amount = request.POST.get('cash_amount')
        credit_amount = request.POST.get('credit_amount')
        manual_description = request.POST.get('manual_description', '')
        manual_category = request.POST.get('manual_category', '')
        notes = request.POST.get('notes', '')

        # Validate required fields
        if not total:
            messages.error(request, 'Total amount is required.')
            return render(request, self.template_name)

        try:
            total_amount = Decimal(total)
            if total_amount <= 0:
                raise ValueError("Amount must be positive")
        except (ValueError, TypeError):
            messages.error(request, 'Please enter a valid total amount.')
            return render(request, self.template_name)

        # Get customer if provided
        customer = None
        if customer_id:
            try:
                customer = Customer.objects.get(pk=customer_id, owner=request.user)
            except Customer.DoesNotExist:
                messages.error(request, 'Selected customer not found.')
                return render(request, self.template_name)

        # Calculate payment amounts
        try:
            if payment_method == 'cash':
                paid_amount = total_amount
                cash_amt = total_amount
                credit_amt = Decimal('0.00')
            elif payment_method == 'credit':
                paid_amount = Decimal('0.00')
                cash_amt = Decimal('0.00')
                credit_amt = total_amount
            elif payment_method == 'mixed':
                cash_amt = Decimal(cash_amount) if cash_amount else Decimal('0.00')
                credit_amt = Decimal(credit_amount) if credit_amount else Decimal('0.00')
                paid_amount = cash_amt

                # Validate mixed payment amounts
                if cash_amt + credit_amt != total_amount:
                    messages.error(request, 'Cash + Credit amounts must equal total amount.')
                    return render(request, self.template_name)
            else:
                messages.error(request, 'Invalid payment method.')
                return render(request, self.template_name)

            # Override with custom amount paid if provided
            if amount_paid:
                paid_amount = Decimal(amount_paid)

        except (ValueError, TypeError):
            messages.error(request, 'Please enter valid payment amounts.')
            return render(request, self.template_name)

        # Create the manual sale
        try:
            with transaction.atomic():
                sale = Sale.objects.create(
                    owner=request.user,
                    customer=customer,
                    sale_type=Sale.SaleType.MANUAL,
                    total=total_amount,
                    subtotal=total_amount,  # For manual sales, total = subtotal
                    tax_total=Decimal('0.00'),
                    discount_total=Decimal('0.00'),
                    payment_method=payment_method,
                    amount_paid=paid_amount,
                    cash_amount=cash_amt,
                    credit_amount=credit_amt,
                    manual_description=manual_description,
                    manual_category=manual_category,
                    notes=notes,
                )

                # Update customer balance if credit involved
                if customer and credit_amt > 0:
                    # TODO: Implement customer balance tracking
                    pass

                messages.success(request, f'Manual sale #{sale.pk} recorded successfully!')
                return redirect('pos:sale-detail', pk=sale.pk)

        except Exception as e:
            messages.error(request, f'Error creating sale: {str(e)}')
            return render(request, self.template_name)


class CustomerSearchAPIView(LoginRequiredMixin, View):
    """
    API endpoint for searching customers in manual sale entry
    """
    def get(self, request):
        query = request.GET.get('q', '').strip()

        if len(query) < 2:
            return JsonResponse({'customers': []})

        # Search customers by name or mobile number
        customers = Customer.objects.filter(
            owner=request.user
        ).filter(
            Q(name__icontains=query) | Q(mobile_number__icontains=query)
        ).order_by('-updated_at')[:10]

        results = []
        for customer in customers:
            results.append({
                'id': customer.pk,
                'name': customer.name or 'Unnamed Customer',
                'mobile_number': customer.mobile_number or '',
                'display_name': f"{customer.name or 'Unnamed'} ({customer.mobile_number or 'No Mobile'})"
            })

        return JsonResponse({'customers': results})
