from rest_framework import status, generics, filters
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count
from django.utils import timezone
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from .models import ProductStockEntry, Sale, SaleItem
from inventory.models import EnhancedProduct
from .serializers import (
    ProductListSerializer, ProductDetailSerializer, ProductCreateSerializer, ProductUpdateSerializer,
    ProductStockEntrySerializer, SaleListSerializer, SaleDetailSerializer, SaleCreateSerializer,
    DashboardSerializer, ManualSaleListSerializer, ManualSaleDetailSerializer,
    ManualSaleCreateSerializer, ManualSaleUpdateSerializer
)


class IsOwnerPermission:
    """Custom permission to only allow owners of an object to access it."""
    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return obj.owner == request.user


# Product Category Views
class ProductCategoryListCreateView(generics.ListCreateAPIView):
    """List categories and create new category"""
    permission_classes = [IsAuthenticated]
    ordering = ['name']

    def get_queryset(self):
        return ProductCategory.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ProductCategorySerializer
        return ProductCategoryListSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            category = serializer.save()
            return Response({
                'success': True,
                'message': 'Category created successfully',
                'data': ProductCategorySerializer(category).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': serializer.data
        })


# Product Views
class ProductListCreateView(generics.ListCreateAPIView):
    """
    DEPRECATED: Use /inventory/pos/api/products/ instead
    This endpoint is kept for backward compatibility only
    """
    """List products with filtering and create new product"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'sku']
    filterset_fields = ['category']
    ordering_fields = ['name', 'created_at', 'selling_price']
    ordering = ['name']

    def get_queryset(self):
        queryset = EnhancedProduct.objects.filter(owner=self.request.user, pos_enabled=True).select_related('category')

        # Filter for low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock and low_stock.lower() == 'true':
            # For EnhancedProduct, we need to filter by reorder_level since there's no stock_quantity field
            queryset = queryset.filter(reorder_level__gt=0)  # Show products that have reorder levels set

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ProductCreateSerializer
        return ProductListSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            product = serializer.save()
            return Response({
                'success': True,
                'message': 'Product created successfully',
                'data': ProductDetailSerializer(product).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })


class ProductDetailView(generics.RetrieveUpdateAPIView):
    """
    DEPRECATED: Use inventory management system instead
    This endpoint is kept for backward compatibility only
    """
    """Get and update specific product"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]

    def get_queryset(self):
        return EnhancedProduct.objects.filter(owner=self.request.user, pos_enabled=True).select_related('category')

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ProductUpdateSerializer
        return ProductDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Product updated successfully',
                'data': ProductDetailSerializer(instance).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


class ProductStockView(generics.CreateAPIView):
    """Add stock entry to a product"""
    serializer_class = ProductStockEntrySerializer
    permission_classes = [IsAuthenticated]

    def create(self, request, *args, **kwargs):
        try:
            product = EnhancedProduct.objects.get(id=kwargs['product_id'], owner=request.user)
        except EnhancedProduct.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Product not found'
            }, status=status.HTTP_404_NOT_FOUND)

        serializer = self.get_serializer(data=request.data, context={'request': request, 'product': product})
        if serializer.is_valid():
            stock_entry = serializer.save()
            return Response({
                'success': True,
                'message': 'Stock entry added successfully',
                'data': ProductStockEntrySerializer(stock_entry).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)


# Sale Views
class SaleListCreateView(generics.ListCreateAPIView):
    """List sales and create new sale"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['customer', 'payment_method']
    ordering_fields = ['created_at', 'total']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Sale.objects.filter(owner=self.request.user).select_related('customer')

        # Date filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return SaleCreateSerializer
        return SaleListSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            sale = serializer.save()
            return Response({
                'success': True,
                'message': 'Sale created successfully',
                'data': SaleDetailSerializer(sale).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Sale creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)
            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'success': True,
            'data': {
                'count': queryset.count(),
                'results': serializer.data
            }
        })


class SaleDetailView(generics.RetrieveAPIView):
    """Get specific sale details"""
    serializer_class = SaleDetailSerializer
    permission_classes = [IsAuthenticated, IsOwnerPermission]

    def get_queryset(self):
        return Sale.objects.filter(owner=self.request.user).select_related('customer').prefetch_related('items__product')

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def dashboard_view(request):
    """Dashboard analytics and summary"""
    period = request.query_params.get('period', 'today')
    user = request.user

    # Calculate date range
    today = timezone.now().date()
    if period == 'today':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today - timedelta(days=30)
        end_date = today
    elif period == 'year':
        start_date = today - timedelta(days=365)
        end_date = today
    else:
        start_date = today
        end_date = today

    # Sales data
    sales_qs = Sale.objects.filter(
        owner=user,
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    sales_count = sales_qs.count()
    sales_total = sales_qs.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
    average_sale = sales_total / sales_count if sales_count > 0 else Decimal('0.00')

    # Product data
    products_qs = EnhancedProduct.objects.filter(owner=user, pos_enabled=True)
    products_count = products_qs.count()
    # For EnhancedProduct, we'll get approximate counts - proper implementation would require stock calculations
    low_stock_count = 0  # TODO: Implement with actual stock calculation
    out_of_stock_count = 0  # TODO: Implement with actual stock calculation

    # Customer data
    customers_qs = user.customers.all()
    customers_count = customers_qs.count()
    new_customers_today = customers_qs.filter(created_at__date=today).count()

    # Revenue by payment method
    cash_revenue = sales_qs.filter(payment_method='cash').aggregate(total=Sum('total'))['total'] or Decimal('0.00')
    credit_revenue = sales_qs.filter(payment_method='credit').aggregate(total=Sum('total'))['total'] or Decimal('0.00')

    # Top products
    top_products = (
        SaleItem.objects.filter(
            sale__owner=user,
            sale__created_at__date__gte=start_date,
            sale__created_at__date__lte=end_date
        )
        .values('product__id', 'product__name')
        .annotate(
            quantity_sold=Sum('quantity'),
            revenue=Sum('price') * Sum('quantity')
        )
        .order_by('-revenue')[:5]
    )

    dashboard_data = {
        'period': period,
        'sales': {
            'total_count': sales_count,
            'total_amount': str(sales_total),
            'average_sale': str(average_sale.quantize(Decimal('0.01')))
        },
        'products': {
            'total_count': products_count,
            'low_stock_count': low_stock_count,
            'out_of_stock_count': out_of_stock_count
        },
        'customers': {
            'total_count': customers_count,
            'new_today': new_customers_today
        },
        'revenue': {
            'cash': str(cash_revenue),
            'credit': str(credit_revenue)
        },
        'top_products': [
            {
                'id': item['product__id'],
                'name': item['product__name'],
                'quantity_sold': item['quantity_sold'],
                'revenue': str(item['revenue'])
            }
            for item in top_products
        ]
    }

    return Response({
        'success': True,
        'data': dashboard_data
    })


# Manual Sales Views
class ManualSaleListCreateView(generics.ListCreateAPIView):
    """List manual sales and create new manual sale"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['manual_description', 'manual_category', 'notes']
    filterset_fields = ['customer', 'payment_method', 'payment_status', 'manual_category']
    ordering_fields = ['created_at', 'total', 'amount_paid']
    ordering = ['-created_at']

    def get_queryset(self):
        queryset = Sale.objects.filter(
            owner=self.request.user,
            sale_type=Sale.SaleType.MANUAL
        ).select_related('customer')

        # Date filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        # Payment status filtering
        payment_status = self.request.query_params.get('payment_status')
        if payment_status:
            queryset = queryset.filter(payment_status=payment_status)

        # Amount filtering
        min_amount = self.request.query_params.get('min_amount')
        max_amount = self.request.query_params.get('max_amount')

        if min_amount:
            try:
                queryset = queryset.filter(total__gte=Decimal(min_amount))
            except (ValueError, TypeError):
                pass

        if max_amount:
            try:
                queryset = queryset.filter(total__lte=Decimal(max_amount))
            except (ValueError, TypeError):
                pass

        return queryset

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return ManualSaleCreateSerializer
        return ManualSaleListSerializer

    def create(self, request, *args, **kwargs):
        # Debug logging
        print(f"DEBUG CREATE MANUAL SALE:")
        print(f"  Request method: {request.method}")
        print(f"  Request path: {request.path}")
        print(f"  Request user: {request.user}")
        print(f"  Request data: {request.data}")
        print(f"  Request data type: {type(request.data)}")
        print(f"  Request content type: {request.content_type}")

        serializer = self.get_serializer(data=request.data)
        print(f"  Serializer data: {serializer.initial_data}")

        if serializer.is_valid():
            print(f"  Serializer is valid: {serializer.validated_data}")
            manual_sale = serializer.save()
            print(f"  Manual sale created with ID: {manual_sale.id}")
            return Response({
                'success': True,
                'message': 'Manual sale created successfully',
                'data': ManualSaleDetailSerializer(manual_sale).data
            }, status=status.HTTP_201_CREATED)

        print(f"  Serializer errors: {serializer.errors}")
        return Response({
            'success': False,
            'message': 'Manual sale creation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)

            # Add summary statistics
            total_sales = queryset.count()
            total_amount = queryset.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
            total_paid = queryset.aggregate(paid=Sum('amount_paid'))['paid'] or Decimal('0.00')
            pending_amount = total_amount - total_paid

            result.data['summary'] = {
                'total_sales': total_sales,
                'total_amount': str(total_amount),
                'total_paid': str(total_paid),
                'pending_amount': str(pending_amount)
            }

            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)

        # Add summary statistics
        total_sales = queryset.count()
        total_amount = queryset.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
        total_paid = queryset.aggregate(paid=Sum('amount_paid'))['paid'] or Decimal('0.00')
        pending_amount = total_amount - total_paid

        return Response({
            'success': True,
            'data': {
                'count': total_sales,
                'results': serializer.data,
                'summary': {
                    'total_sales': total_sales,
                    'total_amount': str(total_amount),
                    'total_paid': str(total_paid),
                    'pending_amount': str(pending_amount)
                }
            }
        })


class ManualSaleDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete specific manual sale"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]

    def get_queryset(self):
        return Sale.objects.filter(
            owner=self.request.user,
            sale_type=Sale.SaleType.MANUAL
        ).select_related('customer')

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return ManualSaleUpdateSerializer
        return ManualSaleDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            updated_sale = serializer.save()
            return Response({
                'success': True,
                'message': 'Manual sale updated successfully',
                'data': ManualSaleDetailSerializer(updated_sale).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({
            'success': True,
            'message': 'Manual sale deleted successfully'
        }, status=status.HTTP_200_OK)


class CustomerManualSalesView(generics.ListAPIView):
    """List manual sales for a specific customer"""
    serializer_class = ManualSaleListSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'payment_status', 'manual_category']
    ordering_fields = ['created_at', 'total', 'amount_paid']
    ordering = ['-created_at']

    def get_queryset(self):
        customer_id = self.kwargs['customer_id']
        queryset = Sale.objects.filter(
            owner=self.request.user,
            customer_id=customer_id,
            sale_type=Sale.SaleType.MANUAL
        ).select_related('customer')

        # Date filtering
        date_from = self.request.query_params.get('date_from')
        date_to = self.request.query_params.get('date_to')

        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)

        return queryset

    def list(self, request, *args, **kwargs):
        # Verify customer belongs to user
        try:
            from customers.models import Customer
            customer = Customer.objects.get(id=self.kwargs['customer_id'], owner=request.user)
        except Customer.DoesNotExist:
            return Response({
                'success': False,
                'message': 'Customer not found'
            }, status=status.HTTP_404_NOT_FOUND)

        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)

        if page is not None:
            serializer = self.get_serializer(page, many=True)
            result = self.get_paginated_response(serializer.data)

            # Add customer summary
            total_sales = queryset.count()
            total_amount = queryset.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
            total_paid = queryset.aggregate(paid=Sum('amount_paid'))['paid'] or Decimal('0.00')

            result.data['customer'] = {
                'id': customer.id,
                'name': customer.name,
                'mobile_number': customer.mobile_number
            }
            result.data['summary'] = {
                'total_sales': total_sales,
                'total_amount': str(total_amount),
                'total_paid': str(total_paid),
                'outstanding_balance': str(total_amount - total_paid)
            }

            return Response({
                'success': True,
                'data': result.data
            })

        serializer = self.get_serializer(queryset, many=True)

        # Add customer summary
        total_sales = queryset.count()
        total_amount = queryset.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
        total_paid = queryset.aggregate(paid=Sum('amount_paid'))['paid'] or Decimal('0.00')

        return Response({
            'success': True,
            'data': {
                'count': total_sales,
                'results': serializer.data,
                'customer': {
                    'id': customer.id,
                    'name': customer.name,
                    'mobile_number': customer.mobile_number
                },
                'summary': {
                    'total_sales': total_sales,
                    'total_amount': str(total_amount),
                    'total_paid': str(total_paid),
                    'outstanding_balance': str(total_amount - total_paid)
                }
            }
        })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def manual_sales_analytics_view(request):
    """Analytics and statistics for manual sales"""
    period = request.query_params.get('period', 'month')
    user = request.user

    # Calculate date range
    today = timezone.now().date()
    if period == 'today':
        start_date = today
        end_date = today
    elif period == 'week':
        start_date = today - timedelta(days=7)
        end_date = today
    elif period == 'month':
        start_date = today - timedelta(days=30)
        end_date = today
    elif period == 'year':
        start_date = today - timedelta(days=365)
        end_date = today
    else:
        start_date = today - timedelta(days=30)
        end_date = today

    # Manual sales data
    manual_sales_qs = Sale.objects.filter(
        owner=user,
        sale_type=Sale.SaleType.MANUAL,
        created_at__date__gte=start_date,
        created_at__date__lte=end_date
    )

    total_manual_sales = manual_sales_qs.count()
    total_manual_amount = manual_sales_qs.aggregate(total=Sum('total'))['total'] or Decimal('0.00')
    total_paid_amount = manual_sales_qs.aggregate(paid=Sum('amount_paid'))['paid'] or Decimal('0.00')
    pending_amount = total_manual_amount - total_paid_amount

    # Payment method breakdown
    cash_sales = manual_sales_qs.filter(payment_method=Sale.PaymentMethod.CASH)
    credit_sales = manual_sales_qs.filter(payment_method=Sale.PaymentMethod.CREDIT)
    mixed_sales = manual_sales_qs.filter(payment_method=Sale.PaymentMethod.MIXED)

    # Payment status breakdown
    full_paid = manual_sales_qs.filter(payment_status=Sale.PaymentStatus.FULL).count()
    partial_paid = manual_sales_qs.filter(payment_status=Sale.PaymentStatus.PARTIAL).count()
    pending_payment = manual_sales_qs.filter(payment_status=Sale.PaymentStatus.PENDING).count()

    # Top categories
    top_categories = (
        manual_sales_qs.exclude(manual_category='')
        .values('manual_category')
        .annotate(
            total_sales=Count('id'),
            total_revenue=Sum('total'),
            total_paid=Sum('amount_paid')
        )
        .order_by('-total_revenue')[:10]
    )

    # Top customers by manual sales
    top_customers = (
        manual_sales_qs.filter(customer__isnull=False)
        .values('customer__id', 'customer__name', 'customer__mobile_number')
        .annotate(
            total_sales=Count('id'),
            total_amount=Sum('total'),
            total_paid=Sum('amount_paid')
        )
        .order_by('-total_amount')[:10]
    )

    analytics_data = {
        'period': period,
        'date_range': {
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        },
        'summary': {
            'total_manual_sales': total_manual_sales,
            'total_amount': str(total_manual_amount),
            'total_paid': str(total_paid_amount),
            'pending_amount': str(pending_amount),
            'average_sale': str((total_manual_amount / total_manual_sales).quantize(Decimal('0.01'))) if total_manual_sales > 0 else '0.00'
        },
        'payment_methods': {
            'cash': {
                'count': cash_sales.count(),
                'amount': str(cash_sales.aggregate(total=Sum('total'))['total'] or Decimal('0.00'))
            },
            'credit': {
                'count': credit_sales.count(),
                'amount': str(credit_sales.aggregate(total=Sum('total'))['total'] or Decimal('0.00'))
            },
            'mixed': {
                'count': mixed_sales.count(),
                'amount': str(mixed_sales.aggregate(total=Sum('total'))['total'] or Decimal('0.00'))
            }
        },
        'payment_status': {
            'fully_paid': full_paid,
            'partially_paid': partial_paid,
            'pending': pending_payment
        },
        'top_categories': [
            {
                'category': item['manual_category'],
                'total_sales': item['total_sales'],
                'total_revenue': str(item['total_revenue']),
                'total_paid': str(item['total_paid'])
            }
            for item in top_categories
        ],
        'top_customers': [
            {
                'id': item['customer__id'],
                'name': item['customer__name'] or 'Customer',
                'mobile_number': item['customer__mobile_number'] or '',
                'total_sales': item['total_sales'],
                'total_amount': str(item['total_amount']),
                'total_paid': str(item['total_paid']),
                'outstanding': str(item['total_amount'] - item['total_paid'])
            }
            for item in top_customers
        ]
    }

    return Response({
        'success': True,
        'data': analytics_data
    })