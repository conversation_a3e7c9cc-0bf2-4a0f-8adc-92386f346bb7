# Generated by Django 5.2.6 on 2025-09-18 14:37

from decimal import Decimal
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('pos', '0003_sale_payment_method'),
    ]

    operations = [
        migrations.AddField(
            model_name='sale',
            name='amount_paid',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount actually paid by customer', max_digits=10),
        ),
        migrations.AddField(
            model_name='sale',
            name='cash_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Cash portion of payment', max_digits=10),
        ),
        migrations.AddField(
            model_name='sale',
            name='credit_amount',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Amount added to customer credit', max_digits=10),
        ),
        migrations.AddField(
            model_name='sale',
            name='manual_category',
            field=models.CharField(blank=True, help_text='Category for manual sales', max_length=100),
        ),
        migrations.AddField(
            model_name='sale',
            name='manual_description',
            field=models.TextField(blank=True, help_text='Description for manual sales (what was sold)'),
        ),
        migrations.AddField(
            model_name='sale',
            name='payment_status',
            field=models.CharField(choices=[('full', 'Fully Paid'), ('partial', 'Partially Paid'), ('pending', 'Payment Pending'), ('credit', 'On Credit')], default='full', help_text='Payment completion status', max_length=10),
        ),
        migrations.AddField(
            model_name='sale',
            name='sale_type',
            field=models.CharField(choices=[('manual', 'Manual Entry'), ('pos', 'POS Transaction')], default='pos', help_text='How this sale was recorded', max_length=10),
        ),
        migrations.AlterField(
            model_name='sale',
            name='payment_method',
            field=models.CharField(choices=[('cash', 'Cash'), ('credit', 'Credit'), ('mixed', 'Cash + Credit')], default='cash', max_length=16),
        ),
    ]
