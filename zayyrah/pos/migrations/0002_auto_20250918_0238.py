# Generated by Django 3.2.16 on 2025-09-18 02:38

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


def seed_initial_stock_entries(apps, schema_editor):
    Product = apps.get_model('pos', 'Product')
    ProductStockEntry = apps.get_model('pos', 'ProductStockEntry')

    today = django.utils.timezone.localdate()
    for product in Product.objects.all():
        quantity = getattr(product, 'stock_quantity', 0) or 0
        if quantity <= 0:
            continue
        ProductStockEntry.objects.create(
            owner=product.owner,
            product=product,
            quantity_added=quantity,
            quantity_remaining=quantity,
            purchase_price=getattr(product, 'purchase_price', Decimal('0.00')) or Decimal('0.00'),
            added_on=today,
            notes='Migrated opening stock',
        )


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('pos', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='product',
            old_name='price',
            new_name='selling_price',
        ),
        migrations.AddField(
            model_name='product',
            name='purchase_price',
            field=models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))]),
        ),
        migrations.AddField(
            model_name='product',
            name='unit',
            field=models.CharField(choices=[('each', 'Each'), ('kg', 'Kilogram'), ('g', 'Gram'), ('l', 'Liter'), ('ml', 'Milliliter'), ('m', 'Meter'), ('cm', 'Centimeter'), ('box', 'Box'), ('pack', 'Pack'), ('custom', 'Custom')], default='each', max_length=24),
        ),
        migrations.AddField(
            model_name='product',
            name='unit_custom_label',
            field=models.CharField(blank=True, max_length=32),
        ),
        migrations.CreateModel(
            name='ProductStockEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_added', models.PositiveIntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('quantity_remaining', models.PositiveIntegerField(default=0)),
                ('purchase_price', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, validators=[django.core.validators.MinValueValidator(Decimal('0.00'))])),
                ('added_on', models.DateField(default=django.utils.timezone.localdate)),
                ('expiry_date', models.DateField(blank=True, null=True)),
                ('notes', models.CharField(blank=True, max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_stock_entries', to=settings.AUTH_USER_MODEL)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_entries', to='pos.product')),
            ],
            options={
                'ordering': ['-added_on', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=120)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('owner', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_categories', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['name'],
                'unique_together': {('owner', 'name')},
            },
        ),
        migrations.AddField(
            model_name='product',
            name='category',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='products', to='pos.productcategory'),
        ),
        migrations.RunPython(seed_initial_stock_entries, migrations.RunPython.noop),
    ]
