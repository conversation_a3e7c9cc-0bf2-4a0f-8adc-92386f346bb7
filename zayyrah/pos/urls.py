from django.urls import path

from .views import (
    ProductCreateView,
    ProductDetailView,
    ProductListView,
    ProductUpdateView,
    SaleCreateView,
    SaleDetailView,
    SaleListView,
    SaleEntrySelectionView,
    ManualSaleCreateView,
    CustomerSearchAPIView,
)

app_name = 'pos'

urlpatterns = [
    # Sales Entry Selection
    path('', SaleEntrySelectionView.as_view(), name='sale-entry-selection'),

    # Manual Sale Entry
    path('manual/', ManualSaleCreateView.as_view(), name='manual-sale-create'),

    # POS Transaction (existing)
    path('pos/', SaleCreateView.as_view(), name='sale-create'),

    # Sales Management
    path('sales/', SaleListView.as_view(), name='sale-list'),
    path('sales/<int:pk>/', SaleDetailView.as_view(), name='sale-detail'),

    # Products Management
    path('products/', ProductListView.as_view(), name='product-list'),
    path('products/add/', ProductCreateView.as_view(), name='product-create'),
    path('products/<int:pk>/', ProductDetailView.as_view(), name='product-detail'),
    path('products/<int:pk>/edit/', ProductUpdateView.as_view(), name='product-update'),

    # API Endpoints
    path('api/customers/search/', CustomerSearchAPIView.as_view(), name='customer-search-api'),
]
