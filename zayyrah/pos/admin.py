from django.contrib import admin

from .models import ProductStockEntry, Sale, SaleItem
from inventory.models import EnhancedProduct


class SaleItemInline(admin.TabularInline):
    model = SaleItem
    extra = 0


# ProductAdmin removed - using inventory.EnhancedProduct admin instead




@admin.register(ProductStockEntry)
class ProductStockEntryAdmin(admin.ModelAdmin):
    list_display = ('product', 'quantity_added', 'quantity_remaining', 'purchase_price', 'added_on', 'expiry_date')
    list_filter = ('added_on', 'expiry_date', 'owner')
    search_fields = ('product__name', 'notes')
    autocomplete_fields = ('product',)


@admin.register(Sale)
class SaleAdmin(admin.ModelAdmin):
    list_display = ('id', 'owner', 'customer', 'payment_method', 'total', 'created_at')
    list_filter = ('owner', 'created_at', 'payment_method')
    inlines = [SaleItemInline]
    readonly_fields = ('subtotal', 'tax_total', 'discount_total', 'total', 'created_at', 'updated_at')
