/* ==== ULTRA RESPONSIVE POS SYSTEM ==== */
/* ULTRA-MOBILE FIRST DESIGN - COMPLETELY OPTIMIZED FOR TOUCH */
/* Designed for mobile devices with perfect responsiveness */

/* Core Layout */
.pos-shell {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    gap: 0;
    position: relative;
}

.pos-shell.sale-screen {
    display: grid;
    grid-template-areas:
        "header"
        "catalog"
        "basket";
    grid-template-rows: auto 1fr auto;
    gap: 0;
    height: 100vh;
    overflow: hidden;
}

/* Header Section */
.pos-header {
    grid-area: header;
    background: linear-gradient(135deg, var(--primary) 0%, #006b2d 100%);
    color: white;
    padding: clamp(16px, 4vw, 24px);
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: 0 4px 20px rgba(0, 75, 35, 0.3);
}

.pos-title h1 {
    margin: 0;
    font-size: clamp(1.5rem, 4vw, 2rem);
    font-weight: 700;
    letter-spacing: -0.02em;
}

.pos-title p {
    margin: 0;
    opacity: 0.9;
    font-size: clamp(0.85rem, 2.5vw, 1rem);
}

.pos-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

/* Sale Layout */
.sale-layout {
    display: grid;
    grid-template-areas:
        "catalog"
        "basket";
    grid-template-rows: 1fr auto;
    height: calc(100vh - 80px);
    overflow: hidden;
}

/* Catalog Panel */
.catalog-panel {
    grid-area: catalog;
    display: flex;
    flex-direction: column;
    background: #f8fffe;
    overflow: hidden;
}

.catalog-header {
    background: white;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 75, 35, 0.1);
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: sticky;
    top: 0;
    z-index: 10;
}

.catalog-tabs {
    display: flex;
    gap: 8px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    padding-bottom: 4px;
}

.catalog-tabs::-webkit-scrollbar {
    display: none;
}

.catalog-tab {
    flex: 0 0 auto;
    padding: 10px 16px;
    border: none;
    background: transparent;
    color: var(--muted);
    font-weight: 600;
    font-size: 0.9rem;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.catalog-tab.is-active {
    background: var(--primary);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 75, 35, 0.3);
}

.catalog-tools {
    display: flex;
    gap: 8px;
    align-items: center;
}

.search-field {
    flex: 1;
    position: relative;
}

.search-field input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(0, 75, 35, 0.15);
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    transition: all 0.2s ease;
}

.search-field input:focus {
    outline: none;
    border-color: var(--primary);
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 75, 35, 0.1);
}

.catalog-buttons {
    display: flex;
    gap: 8px;
}

.icon-button {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    border: 1px solid rgba(0, 75, 35, 0.15);
    background: white;
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
}

.icon-button:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: scale(1.05);
}

/* Product Grid */
.sale-product-grid {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 12px;
    align-content: start;
}

.sale-product-card {
    background: white;
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 75, 35, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.sale-product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary), #00d4aa);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sale-product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 75, 35, 0.15);
}

.sale-product-card:hover::before {
    opacity: 1;
}

.sale-product-card.is-hidden {
    display: none;
}

.sale-product-card.is-max {
    opacity: 0.6;
}

.sale-product-card header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 8px;
}

.product-name {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--text);
    line-height: 1.3;
    flex: 1;
}

.product-price {
    font-weight: 700;
    font-size: 1rem;
    color: var(--primary);
    white-space: nowrap;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    font-size: 0.8rem;
    color: var(--muted);
}

.badge {
    background: rgba(0, 75, 35, 0.1);
    color: var(--primary);
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 600;
}

.product-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    background: rgba(0, 75, 35, 0.05);
    border-radius: 12px;
    padding: 12px;
}

.product-actions-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 4px;
}

.input-mode-selector {
    display: flex;
    gap: 4px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    padding: 2px;
}

.mode-btn {
    padding: 6px 8px;
    border: none;
    background: transparent;
    color: var(--muted);
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.mode-btn.active {
    background: var(--primary);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 75, 35, 0.3);
}

.quantity-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.quantity-controls.unit-mode {
    justify-content: space-between;
}

.quantity-controls.decimal-mode,
.quantity-controls.price-mode {
    justify-content: flex-end;
}

.qty-input-group {
    display: flex;
    align-items: center;
    gap: 6px;
    flex: 1;
    max-width: 120px;
}

.qty-input {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid rgba(0, 75, 35, 0.2);
    border-radius: 6px;
    font-size: 0.9rem;
    font-weight: 600;
    text-align: center;
    background: white;
    color: var(--primary);
}

.qty-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 75, 35, 0.1);
}

.qty-input-label {
    font-size: 0.75rem;
    color: var(--muted);
    font-weight: 600;
    white-space: nowrap;
}

.calculated-result {
    font-size: 0.75rem;
    color: var(--primary);
    font-weight: 600;
    text-align: center;
    margin-top: 4px;
    padding: 4px;
    background: rgba(0, 75, 35, 0.1);
    border-radius: 4px;
}

.quick-add-btn {
    padding: 6px 12px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.quick-add-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.quick-add-btn:disabled {
    background: #ddd;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

.qty-btn {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: none;
    background: var(--primary);
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover {
    background: var(--primary-dark);
    transform: scale(1.1);
}

.qty-btn:disabled {
    background: #ddd;
    color: #999;
    cursor: not-allowed;
    transform: none;
}

.qty-display {
    font-weight: 600;
    font-size: 1rem;
    min-width: 20px;
    text-align: center;
    color: var(--primary);
}

/* Basket Panel */
.basket-panel {
    grid-area: basket;
    background: white;
    border-top: 1px solid rgba(0, 75, 35, 0.1);
    display: flex;
    flex-direction: column;
    max-height: 50vh;
    overflow: hidden;
}

.basket-card {
    padding: 16px;
    border-bottom: 1px solid rgba(0, 75, 35, 0.08);
}

.basket-card:last-child {
    border-bottom: none;
}

.basket-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.basket-header h2 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--primary);
    font-weight: 700;
}

#basket-count {
    font-size: 0.9rem;
    color: var(--muted);
    font-weight: 600;
}

.basket-items {
    max-height: 200px;
    overflow-y: auto;
    margin: 0 -16px;
    padding: 0 16px;
}

.basket-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 75, 35, 0.06);
    gap: 12px;
}

.basket-row:last-child {
    border-bottom: none;
}

.basket-row-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.basket-row-main strong {
    font-size: 0.95rem;
    color: var(--text);
}

.basket-row-main .muted {
    font-size: 0.8rem;
    color: var(--muted);
}

.basket-row-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.basket-row-actions .qty-btn {
    width: 28px;
    height: 28px;
    font-size: 1rem;
}

.basket-row-actions .qty-display {
    font-size: 0.9rem;
    min-width: 16px;
}

/* Summary */
.summary-grid {
    display: grid;
    gap: 8px;
}

.summary-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.95rem;
}

.summary-line.total {
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary);
    padding-top: 8px;
    border-top: 2px solid rgba(0, 75, 35, 0.1);
}

/* Payment Methods */
.payment-methods {
    display: grid;
    gap: 8px;
}

.payment-card {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border: 2px solid rgba(0, 75, 35, 0.1);
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
}

.payment-card.is-active {
    border-color: var(--primary);
    background: rgba(0, 75, 35, 0.05);
}

.payment-radio {
    width: 18px;
    height: 18px;
    accent-color: var(--primary);
}

.payment-label {
    font-weight: 600;
    color: var(--text);
}

/* Customer Search */
.customer-results {
    max-height: 150px;
    overflow-y: auto;
    margin-top: 8px;
    border: 1px solid rgba(0, 75, 35, 0.1);
    border-radius: 8px;
    background: white;
}

.customer-row {
    width: 100%;
    padding: 12px;
    border: none;
    background: white;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border-bottom: 1px solid rgba(0, 75, 35, 0.06);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customer-row:last-child {
    border-bottom: none;
}

.customer-row:hover {
    background: rgba(0, 75, 35, 0.05);
}

.customer-row.is-active {
    background: rgba(0, 75, 35, 0.1);
    color: var(--primary);
}

.radio-indicator {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(0, 75, 35, 0.3);
    border-radius: 50%;
    position: relative;
}

.customer-row.is-active .radio-indicator {
    border-color: var(--primary);
}

.customer-row.is-active .radio-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 8px;
    height: 8px;
    background: var(--primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
}

/* Form Styles */
.form-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: 1fr 1fr;
}

.form-group {
    display: grid;
    gap: 6px;
}

.form-group.span-2 {
    grid-column: span 2;
}

.form-group label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--muted);
}

.form-group input,
.form-group textarea,
.form-group select {
    padding: 10px 12px;
    border: 1px solid rgba(0, 75, 35, 0.15);
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(0, 75, 35, 0.1);
}

/* Buttons */
.sticky-action {
    position: sticky;
    bottom: 0;
    background: white;
    padding: 16px;
    border-top: 1px solid rgba(0, 75, 35, 0.1);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
}

.primary-button {
    width: 100%;
    padding: 16px;
    background: linear-gradient(135deg, var(--primary) 0%, #006b2d 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 75, 35, 0.3);
}

.primary-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 6px 20px rgba(0, 75, 35, 0.4);
}

.secondary-button {
    padding: 8px 16px;
    background: white;
    color: var(--primary);
    border: 1px solid var(--primary);
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.secondary-button:hover {
    background: var(--primary);
    color: white;
}

.secondary-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
}

.secondary-link:hover {
    text-decoration: underline;
}

/* Product Grid for Listing */
.pos-card {
    background: white;
    border-radius: 16px;
    padding: clamp(20px, 4vw, 32px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 75, 35, 0.08);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.product-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(0, 75, 35, 0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 75, 35, 0.15);
}

.product-card header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    gap: 12px;
}

.product-card h2 {
    margin: 0;
    font-size: 1.1rem;
    color: var(--primary);
    font-weight: 700;
}

.customer-meta {
    display: grid;
    gap: 6px;
    font-size: 0.9rem;
    color: var(--muted);
    margin-bottom: 16px;
}

.card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: auto;
}

.card-actions a {
    color: var(--primary);
    font-weight: 600;
    text-decoration: none;
    font-size: 0.9rem;
}

.card-actions a:hover {
    text-decoration: underline;
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: clamp(40px, 8vw, 80px);
    background: rgba(255, 255, 255, 0.8);
    border-radius: 16px;
    border: 1px solid rgba(0, 75, 35, 0.1);
}

.empty-state h2 {
    margin: 0 0 12px 0;
    color: var(--primary);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0 0 24px 0;
    color: var(--muted);
    font-size: 1rem;
}

/* Responsive Design */
@media (min-width: 768px) {
    .pos-shell.sale-screen {
        grid-template-areas:
            "header header"
            "catalog basket";
        grid-template-columns: 1fr 400px;
        grid-template-rows: auto 1fr;
    }

    .sale-layout {
        display: contents;
    }

    .catalog-panel {
        border-right: 1px solid rgba(0, 75, 35, 0.1);
    }

    .basket-panel {
        max-height: none;
        border-top: none;
        overflow-y: auto;
    }

    .sale-product-grid {
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        gap: 16px;
    }

    .catalog-header {
        padding: 20px;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .form-group.span-2 {
        grid-column: span 1;
    }
}

@media (min-width: 1024px) {
    .sale-product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 20px;
    }

    .basket-panel {
        min-width: 450px;
    }

    .pos-shell.sale-screen {
        grid-template-columns: 1fr 450px;
    }
}

/* Mobile viewport height variable */
:root {
    --vh: 1vh;
}

/* ULTRA-MOBILE OPTIMIZATION - PRIMARY FOCUS */
@media (max-width: 767px) {
    /* Ultra-mobile layout optimization */
    .pos-shell.sale-screen {
        height: 100vh;
        height: calc(var(--vh, 1vh) * 100); /* Dynamic viewport height for better mobile support */
        overflow: hidden;
        grid-template-rows: auto 1fr 0;
        grid-template-areas:
            "header"
            "catalog"
            "basket";
    }

    /* Mobile-first header with larger touch targets */
    .pos-header {
        padding: 16px;
        min-height: 80px;
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .pos-title h1 {
        font-size: 1.5rem;
        text-align: center;
    }

    .pos-title p {
        font-size: 0.9rem;
        text-align: center;
        opacity: 0.9;
    }

    .pos-actions {
        width: 100%;
        flex-direction: row;
        gap: 8px;
        justify-content: space-between;
    }

    .pos-actions > * {
        flex: 1;
        min-height: 48px; /* Minimum touch target */
        font-size: 0.9rem;
        padding: 12px 8px;
    }

    /* Ultra-mobile catalog optimization */
    .catalog-header {
        padding: 12px;
        position: sticky;
        top: 0;
        z-index: 20;
        background: white;
        border-bottom: 2px solid rgba(0, 75, 35, 0.1);
    }

    .catalog-tabs {
        gap: 6px;
        margin-bottom: 12px;
        padding: 0;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .catalog-tab {
        min-width: 80px;
        height: 48px; /* Large touch target */
        padding: 12px 16px;
        font-size: 0.85rem;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .catalog-tools {
        flex-direction: column;
        gap: 10px;
    }

    .search-field {
        order: 1;
    }

    .search-field input {
        height: 48px; /* Large touch target */
        font-size: 16px; /* Prevent zoom on iOS */
        padding: 12px 16px;
        border-radius: 12px;
    }

    .catalog-buttons {
        order: 2;
        justify-content: space-evenly;
        gap: 12px;
    }

    .icon-button {
        width: 56px; /* Extra large for mobile */
        height: 56px;
        font-size: 1.4rem;
    }

    /* Ultra-mobile product grid */
    .sale-product-grid {
        grid-template-columns: repeat(2, 1fr); /* Always 2 columns on mobile */
        gap: 8px;
        padding: 12px;
        padding-bottom: 100px; /* Space for floating cart */
    }

    .sale-product-card {
        padding: 12px;
        min-height: 160px;
        border-radius: 12px;
    }

    .product-name {
        font-size: 0.85rem;
        line-height: 1.2;
        height: 2.4em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .product-price {
        font-size: 0.9rem;
        font-weight: 800;
    }

    .product-meta {
        font-size: 0.75rem;
        margin-bottom: 10px;
    }

    .product-actions {
        padding: 8px;
        gap: 6px;
    }

    .input-mode-selector {
        padding: 1px;
        gap: 2px;
    }

    .mode-btn {
        padding: 4px 6px;
        font-size: 0.7rem;
        min-width: 28px;
        height: 28px;
    }

    .qty-btn {
        width: 40px; /* Large touch targets */
        height: 40px;
        font-size: 1.2rem;
        border-radius: 10px;
    }

    .qty-display {
        font-size: 0.9rem;
        min-width: 24px;
    }

    .qty-input {
        height: 36px;
        font-size: 0.85rem;
        padding: 6px 8px;
    }

    .quick-add-btn {
        padding: 8px 12px;
        font-size: 0.75rem;
        min-height: 36px;
    }

    /* Mobile cart - floating and collapsible */
    .basket-panel {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background: white;
        border-top: 2px solid rgba(0, 75, 35, 0.1);
        border-radius: 20px 20px 0 0;
        max-height: 60vh;
        overflow: hidden;
        z-index: 30;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
        transform: translateY(calc(100% - 80px)); /* Show only header initially */
        transition: transform 0.3s ease;
    }

    .basket-panel.expanded {
        transform: translateY(0);
    }

    .basket-panel::before {
        content: '';
        position: absolute;
        top: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: rgba(0, 75, 35, 0.3);
        border-radius: 2px;
    }

    .basket-card:first-child {
        padding-top: 20px; /* Space for drag handle */
        cursor: pointer;
    }

    .basket-header {
        padding: 16px;
        background: white;
        border-bottom: 1px solid rgba(0, 75, 35, 0.1);
    }

    .basket-header h2 {
        font-size: 1.1rem;
    }

    #basket-count {
        font-size: 0.9rem;
    }

    .basket-items {
        max-height: 200px;
        overflow-y: auto;
        -webkit-overflow-scrolling: touch;
    }

    .basket-row {
        padding: 12px 16px;
        border-bottom: 1px solid rgba(0, 75, 35, 0.05);
    }

    .basket-row-actions .qty-btn {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }

    /* Mobile payment and forms */
    .form-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .form-group.span-2 {
        grid-column: span 1;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        height: 48px; /* Large touch targets */
        font-size: 16px; /* Prevent zoom */
        border-radius: 8px;
    }

    .payment-card {
        padding: 16px;
        min-height: 56px;
        border-radius: 12px;
    }

    .payment-radio {
        width: 20px;
        height: 20px;
    }

    .customer-results {
        max-height: 180px;
    }

    .customer-row {
        padding: 16px;
        min-height: 56px;
    }

    /* Mobile sticky checkout */
    .sticky-action {
        position: sticky;
        bottom: 0;
        background: white;
        padding: 16px;
        border-top: 2px solid rgba(0, 75, 35, 0.1);
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    }

    .primary-button {
        height: 56px; /* Extra large for mobile */
        font-size: 1.1rem;
        font-weight: 700;
        border-radius: 12px;
    }

    .secondary-button {
        height: 48px;
        font-size: 0.9rem;
        border-radius: 8px;
    }

    /* Product listing mobile optimization */
    .product-grid {
        grid-template-columns: 1fr; /* Single column on mobile */
        gap: 12px;
    }

    .product-card {
        padding: 16px;
        border-radius: 12px;
    }

    .customer-meta {
        gap: 8px;
        font-size: 0.85rem;
    }

    .card-actions {
        gap: 16px;
        margin-top: 12px;
    }

    .card-actions a {
        padding: 8px 12px;
        border-radius: 6px;
        background: rgba(0, 75, 35, 0.1);
        font-size: 0.85rem;
    }

    /* Ultra-mobile specific improvements */
    .sale-product-card:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
    }

    .qty-btn:active {
        transform: scale(0.95);
    }

    .mode-btn:active {
        transform: scale(0.95);
    }

    /* Improved mobile scrolling */
    .catalog-panel {
        -webkit-overflow-scrolling: touch;
    }

    .sale-product-grid {
        -webkit-overflow-scrolling: touch;
    }

    /* Mobile-specific animations */
    .basket-panel {
        will-change: transform;
    }

    .sale-product-card {
        will-change: transform;
    }

    /* Performance optimizations for mobile */
    .sale-product-grid {
        contain: layout style paint;
        transform: translateZ(0); /* Force GPU acceleration */
    }

    .basket-panel {
        contain: layout style paint;
        transform: translateZ(0);
    }

    /* Better mobile interaction states */
    .qty-btn:active,
    .mode-btn:active,
    .quick-add-btn:active {
        transition: none; /* Immediate feedback */
    }

    /* Prevent text selection on mobile interactions */
    .qty-btn,
    .mode-btn,
    .quick-add-btn,
    .catalog-tab,
    .payment-card {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-touch-callout: none;
    }

    /* Improve scrolling on older mobile browsers */
    .catalog-panel,
    .basket-items,
    .catalog-tabs {
        -webkit-overflow-scrolling: touch;
        scroll-behavior: smooth;
    }

    /* Mobile safe area handling */
    .pos-header {
        padding-top: max(16px, env(safe-area-inset-top));
        padding-left: max(16px, env(safe-area-inset-left));
        padding-right: max(16px, env(safe-area-inset-right));
    }

    .basket-panel {
        padding-bottom: max(16px, env(safe-area-inset-bottom));
    }
}

/* Extra small mobile devices (iPhone SE, etc.) */
@media (max-width: 375px) {
    .pos-header {
        padding: 12px;
        min-height: 70px;
    }

    .pos-title h1 {
        font-size: 1.3rem;
    }

    .pos-title p {
        font-size: 0.8rem;
    }

    .pos-actions > * {
        font-size: 0.8rem;
        padding: 10px 6px;
    }

    .catalog-header {
        padding: 10px;
    }

    .catalog-tab {
        min-width: 70px;
        padding: 10px 12px;
        font-size: 0.8rem;
        height: 44px;
    }

    .search-field input {
        height: 44px;
        font-size: 16px;
        padding: 10px 14px;
    }

    .icon-button {
        width: 48px;
        height: 48px;
        font-size: 1.2rem;
    }

    .sale-product-grid {
        grid-template-columns: 1fr; /* Single column for very small screens */
        gap: 6px;
        padding: 10px;
    }

    .sale-product-card {
        padding: 10px;
        min-height: 140px;
    }

    .product-name {
        font-size: 0.8rem;
    }

    .product-price {
        font-size: 0.85rem;
    }

    .product-meta {
        font-size: 0.7rem;
    }

    .mode-btn {
        min-width: 24px;
        height: 24px;
        font-size: 0.65rem;
        padding: 2px 4px;
    }

    .qty-btn {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }

    .basket-panel {
        transform: translateY(calc(100% - 70px));
    }

    .basket-header h2 {
        font-size: 1rem;
    }

    #basket-count {
        font-size: 0.85rem;
    }
}

/* Landscape mode optimizations */
@media (max-height: 500px) and (orientation: landscape) {
    .pos-header {
        min-height: 60px;
        padding: 8px 16px;
    }

    .pos-title h1 {
        font-size: 1.2rem;
    }

    .pos-title p {
        display: none; /* Hide subtitle in landscape */
    }

    .catalog-header {
        padding: 8px 12px;
    }

    .catalog-tab {
        height: 40px;
        padding: 8px 12px;
    }

    .search-field input {
        height: 40px;
    }

    .icon-button {
        width: 48px;
        height: 48px;
    }

    .basket-panel {
        transform: translateY(calc(100% - 60px));
        max-height: 50vh;
    }

    .sale-product-card {
        min-height: 120px;
    }
}

/* Loading and States */
.is-loading {
    pointer-events: none;
    opacity: 0.6;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Touch enhancements */
@media (hover: none) and (pointer: coarse) {
    .qty-btn {
        width: 40px;
        height: 40px;
    }

    .icon-button {
        width: 48px;
        height: 48px;
    }

    .sale-product-card {
        padding: 20px;
    }

    .basket-row-actions .qty-btn {
        width: 36px;
        height: 36px;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .sale-product-card {
        border: 2px solid var(--primary);
    }

    .qty-btn {
        border: 2px solid var(--primary);
    }
}

/* Animations */
@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.calculated-result {
    animation: fadeIn 0.3s ease;
}

/* Input mode transitions */
.quantity-controls {
    transition: all 0.3s ease;
}

.mode-btn {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mode-btn:hover {
    transform: scale(1.05);
}

.mode-btn.active {
    transform: scale(1.1);
}

/* Real-time calculation preview */
.price-preview {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--primary);
    color: white;
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.2s ease;
    pointer-events: none;
}

.price-preview.show {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced input styling */
.qty-input:invalid {
    border-color: #d64545;
    box-shadow: 0 0 0 2px rgba(214, 69, 69, 0.1);
}

.qty-input:valid {
    border-color: var(--primary);
}

/* Loading state for quick add buttons */
.quick-add-btn.loading {
    position: relative;
    color: transparent;
}

.quick-add-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 12px;
    height: 12px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .mode-btn:hover,
    .mode-btn.active {
        transform: none;
    }
}
