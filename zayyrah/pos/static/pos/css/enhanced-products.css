/* Enhanced Product Management CSS - Zayyrah POS System */
/* Modern CSS with DaisyUI integration and custom brand colors */

:root {
    --primary-brand: #0da487;
    --primary-dark: #0a8470;
    --primary-light: #10b496;
    --primary-lighter: #b3f4ec;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --border-radius: 0.5rem;
    --border-radius-lg: 0.75rem;
    --transition: all 0.2s ease;
    --transition-slow: all 0.3s ease;
}

/* Custom DaisyUI theme overrides */
[data-theme] {
    --rounded-box: var(--border-radius-lg);
    --rounded-btn: var(--border-radius);
    --rounded-badge: 9999px;
    --animation-btn: 0.25s;
    --animation-input: 0.2s;
    --btn-text-case: inherit;
    --btn-focus-scale: 0.95;
    --border-btn: 1px;
    --tab-border: 1px;
    --tab-radius: var(--border-radius);
}

/* Override DaisyUI primary colors */
.btn-primary {
    background-color: var(--primary-brand);
    border-color: var(--primary-brand);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline:hover {
    background-color: var(--primary-brand);
    border-color: var(--primary-brand);
    color: white;
}

.badge-primary {
    background-color: var(--primary-brand);
    color: white;
}

.input-bordered:focus {
    border-color: var(--primary-brand);
    box-shadow: 0 0 0 2px rgba(13, 164, 135, 0.1);
}

.select-bordered:focus {
    border-color: var(--primary-brand);
    box-shadow: 0 0 0 2px rgba(13, 164, 135, 0.1);
}

/* Product Management Container */
.product-management-container {
    min-height: calc(100vh - 200px);
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Product Cards */
.product-card {
    position: relative;
    transition: var(--transition);
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-light);
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-brand) 0%, var(--primary-light) 100%);
    opacity: 0;
    transition: var(--transition);
}

.product-card:hover::before {
    opacity: 1;
}

/* Product Image Styling */
.product-image-container {
    position: relative;
    overflow: hidden;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.product-image-container img {
    transition: var(--transition-slow);
}

.product-card:hover .product-image-container img {
    transform: scale(1.05);
}

.product-image-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--gray-100) 0%, var(--gray-200) 100%);
    color: var(--gray-400);
}

/* Stock Status Indicators */
.stock-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.stock-indicator.in-stock {
    color: var(--success);
}

.stock-indicator.low-stock {
    color: var(--warning);
}

.stock-indicator.out-of-stock {
    color: var(--error);
}

.stock-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Animated Progress Bars */
.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--gray-200);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-brand) 0%, var(--primary-light) 100%);
    border-radius: 3px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Enhanced Statistics Cards */
.stats {
    background: white;
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.stat {
    padding: 1.5rem;
    border-right: 1px solid var(--gray-200);
}

.stat:last-child {
    border-right: none;
}

.stat-title {
    color: var(--gray-500);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1;
}

.stat-value.text-warning {
    color: var(--warning);
}

.stat-value.text-success {
    color: var(--success);
}

.stat-desc {
    color: var(--gray-500);
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Filter Panel Animations */
#advancedFilters {
    transition: var(--transition-slow);
    overflow: hidden;
}

#advancedFilters.show {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 500px;
        transform: translateY(0);
    }
}

/* Custom Form Controls */
.form-control label {
    color: var(--gray-700);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.input, .select, .textarea {
    transition: var(--transition);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
}

.input:focus, .select:focus, .textarea:focus {
    border-color: var(--primary-brand);
    box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
    outline: none;
}

/* Enhanced Search Input */
.search-input-container {
    position: relative;
}

.search-input-container .input {
    padding-left: 2.5rem;
    background: white;
    border: 2px solid var(--gray-200);
}

.search-input-container .search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    pointer-events: none;
}

.search-input-container .input:focus + .search-icon {
    color: var(--primary-brand);
}

/* View Toggle Buttons */
.view-toggle {
    background: white;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
    display: inline-flex;
}

.view-toggle button {
    padding: 0.5rem 1rem;
    border: none;
    background: transparent;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.view-toggle button:hover {
    background: var(--gray-50);
    color: var(--gray-800);
}

.view-toggle button.active {
    background: var(--primary-brand);
    color: white;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Grid Layout Enhancements */
#gridView .product-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

#gridView .product-card .p-4 {
    flex: 1;
    display: flex;
    flex-direction: column;
}

#gridView .product-card .flex.gap-2 {
    margin-top: auto;
}

/* List View Enhancements */
#listView .product-card {
    transition: var(--transition);
}

#listView .product-card:hover {
    border-color: var(--primary-light);
    box-shadow: var(--shadow-md);
}

/* Badge Enhancements */
.badge {
    font-weight: 500;
    border-radius: var(--border-radius);
    border: 1px solid transparent;
}

.badge-ghost {
    background: var(--gray-100);
    color: var(--gray-700);
    border-color: var(--gray-200);
}

.badge-primary {
    background: var(--primary-brand);
    color: white;
}

.badge-secondary {
    background: var(--gray-600);
    color: white;
}

.badge-success {
    background: var(--success);
    color: white;
}

.badge-warning {
    background: var(--warning);
    color: white;
}

.badge-error {
    background: var(--error);
    color: white;
}

/* Modal Enhancements */
.modal {
    backdrop-filter: blur(4px);
}

.modal-box {
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

.modal-action .btn {
    border-radius: var(--border-radius);
}

/* Loading States */
.loading-overlay {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(2px);
}

.skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .product-management-container {
        padding: 1rem;
    }

    .stats {
        overflow-x: auto;
    }

    .stat {
        min-width: 120px;
    }

    #listView .product-card {
        padding: 1rem;
    }

    #listView .flex.flex-col.lg\\:flex-row {
        flex-direction: column;
        gap: 1rem;
    }

    #listView .grid.grid-cols-2.lg\\:grid-cols-4 {
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .product-management-container {
        padding: 0.75rem;
    }

    .stats {
        flex-direction: column;
    }

    .stat {
        border-right: none;
        border-bottom: 1px solid var(--gray-200);
        padding: 1rem;
    }

    .stat:last-child {
        border-bottom: none;
    }

    #listView .grid.grid-cols-2 {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: var(--border-radius) !important;
        margin-bottom: 0.25rem;
    }
}

/* Accessibility Enhancements */
.btn:focus {
    outline: 2px solid var(--primary-brand);
    outline-offset: 2px;
}

.input:focus, .select:focus {
    outline: 2px solid var(--primary-brand);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .product-management-container {
        padding: 0;
    }

    .btn, .modal, .dropdown {
        display: none !important;
    }

    .product-card {
        break-inside: avoid;
        margin-bottom: 1rem;
        box-shadow: none;
        border: 1px solid var(--gray-300);
    }

    #gridView {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem;
    }

    .stats {
        display: none;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --gray-50: #1f2937;
        --gray-100: #374151;
        --gray-200: #4b5563;
        --gray-300: #6b7280;
        --gray-400: #9ca3af;
        --gray-500: #d1d5db;
        --gray-600: #e5e7eb;
        --gray-700: #f3f4f6;
        --gray-800: #f9fafb;
        --gray-900: #ffffff;
    }

    body {
        background: var(--gray-50);
        color: var(--gray-900);
    }

    .product-card, .stats, .modal-box {
        background: var(--gray-100);
        border-color: var(--gray-200);
    }
}

/* Utility Classes */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.transition-all {
    transition: var(--transition);
}

.transition-slow {
    transition: var(--transition-slow);
}

/* Animation Classes */
.animate-fadeIn {
    animation: fadeIn 0.3s ease-out;
}

.animate-slideIn {
    animation: slideInRight 0.3s ease-out;
}

.animate-bounceIn {
    animation: bounceIn 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}