/**
 * Product Form Management JavaScript
 * Enhanced product form with image upload, unit conversions, and advanced features
 * Zayyrah POS System
 */

class ProductFormManager {
    constructor() {
        this.imageFiles = [];
        this.removedImages = [];
        this.unitConversions = {};
        this.maxImageSize = 5 * 1024 * 1024; // 5MB
        this.maxImages = 10;

        // Initialize all form features
        this.initializeImageUpload();
        this.initializePricingCalculators();
        this.initializeUnitSystem();
        this.initializeFormValidation();
        this.initializeFormData();
        this.setupEventListeners();
    }

    /**
     * Initialize image upload functionality
     */
    initializeImageUpload() {
        const imageInput = document.getElementById('imageInput');
        const uploadArea = document.getElementById('imageUploadArea');
        const previewArea = document.getElementById('imagePreview');

        // File input change handler
        if (imageInput) {
            imageInput.addEventListener('change', (e) => {
                this.handleImageSelection(e.target.files);
            });
        }

        // Drag and drop functionality
        if (uploadArea) {
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-primary', 'bg-primary-light', 'bg-opacity-10');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-primary', 'bg-primary-light', 'bg-opacity-10');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-primary', 'bg-primary-light', 'bg-opacity-10');
                this.handleImageSelection(e.dataTransfer.files);
            });
        }

        // Setup global remove image function
        window.removeExistingImage = (imageId) => this.removeExistingImage(imageId);
    }

    /**
     * Handle image file selection
     */
    handleImageSelection(files) {
        const validFiles = [];

        Array.from(files).forEach(file => {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                this.showToast(`${file.name} is not a valid image file`, 'error');
                return;
            }

            // Validate file size
            if (file.size > this.maxImageSize) {
                this.showToast(`${file.name} is too large. Maximum size is 5MB`, 'error');
                return;
            }

            // Check total images limit
            if (this.imageFiles.length + validFiles.length >= this.maxImages) {
                this.showToast(`Maximum ${this.maxImages} images allowed`, 'warning');
                return;
            }

            validFiles.push(file);
        });

        // Add valid files
        validFiles.forEach(file => {
            this.imageFiles.push(file);
            this.createImagePreview(file);
        });

        this.updateImageInput();
    }

    /**
     * Create image preview
     */
    createImagePreview(file) {
        const previewArea = document.getElementById('imagePreview');
        const reader = new FileReader();

        reader.onload = (e) => {
            const previewItem = document.createElement('div');
            previewItem.className = 'relative group';
            previewItem.innerHTML = `
                <img src="${e.target.result}" alt="Preview"
                     class="w-full aspect-square object-cover rounded-lg border border-gray-200">
                <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg flex items-center justify-center">
                    <button type="button" class="btn btn-error btn-sm" onclick="window.productForm.removeImagePreview(this)">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <div class="absolute bottom-2 left-2">
                    <span class="badge badge-sm bg-black bg-opacity-50 text-white">${file.name}</span>
                </div>
            `;

            previewArea.appendChild(previewItem);
            previewArea.classList.remove('hidden');
        };

        reader.readAsDataURL(file);
    }

    /**
     * Remove image preview
     */
    removeImagePreview(button) {
        const previewItem = button.closest('.relative');
        const index = Array.from(previewItem.parentNode.children).indexOf(previewItem);

        // Remove from files array
        this.imageFiles.splice(index, 1);

        // Remove preview element
        previewItem.remove();

        // Hide preview area if empty
        if (this.imageFiles.length === 0) {
            document.getElementById('imagePreview').classList.add('hidden');
        }

        this.updateImageInput();
    }

    /**
     * Remove existing image
     */
    removeExistingImage(imageId) {
        if (confirm('Are you sure you want to remove this image?')) {
            // Add to removed images list
            this.removedImages.push(imageId);

            // Remove from display
            const imageItem = document.querySelector(`[data-image-id="${imageId}"]`);
            if (imageItem) {
                imageItem.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => imageItem.remove(), 300);
            }

            // Update hidden input
            document.getElementById('removedImages').value = this.removedImages.join(',');

            this.showToast('Image marked for removal', 'success');
        }
    }

    /**
     * Update image input with new files
     */
    updateImageInput() {
        const imageInput = document.getElementById('imageInput');
        if (imageInput && this.imageFiles.length > 0) {
            const dataTransfer = new DataTransfer();
            this.imageFiles.forEach(file => dataTransfer.items.add(file));
            imageInput.files = dataTransfer.files;
        }
    }

    /**
     * Initialize pricing calculators
     */
    initializePricingCalculators() {
        const purchasePrice = document.querySelector('input[name="purchase_price"]');
        const sellingPrice = document.querySelector('input[name="selling_price"]');
        const profitMargin = document.getElementById('profitMargin');

        const calculateMargin = () => {
            const purchase = parseFloat(purchasePrice?.value) || 0;
            const selling = parseFloat(sellingPrice?.value) || 0;

            if (purchase > 0 && selling > 0) {
                const margin = ((selling - purchase) / selling * 100);
                if (profitMargin) {
                    profitMargin.textContent = `${margin.toFixed(1)}%`;
                    profitMargin.className = `font-medium ${margin >= 20 ? 'text-success' : margin >= 10 ? 'text-warning' : 'text-error'}`;
                }
            } else {
                if (profitMargin) {
                    profitMargin.textContent = '0%';
                    profitMargin.className = 'font-medium text-gray-500';
                }
            }
        };

        if (purchasePrice) {
            purchasePrice.addEventListener('input', calculateMargin);
        }
        if (sellingPrice) {
            sellingPrice.addEventListener('input', calculateMargin);
        }

        // Initial calculation
        calculateMargin();
    }

    /**
     * Initialize unit system and conversions
     */
    initializeUnitSystem() {
        const unitSelect = document.getElementById('unitSelect');
        const customUnitGroup = document.getElementById('customUnitGroup');
        const conversionPreview = document.getElementById('unitConversionPreview');

        // Unit conversions data
        this.unitConversions = {
            'kg': [
                { unit: 'g', factor: 1000, label: 'Grams' },
                { unit: 'lb', factor: 2.20462, label: 'Pounds' },
                { unit: 'oz', factor: 35.274, label: 'Ounces' }
            ],
            'g': [
                { unit: 'kg', factor: 0.001, label: 'Kilograms' },
                { unit: 'mg', factor: 1000, label: 'Milligrams' }
            ],
            'l': [
                { unit: 'ml', factor: 1000, label: 'Milliliters' },
                { unit: 'gal', factor: 0.264172, label: 'Gallons' },
                { unit: 'qt', factor: 1.05669, label: 'Quarts' }
            ],
            'ml': [
                { unit: 'l', factor: 0.001, label: 'Liters' },
                { unit: 'fl_oz', factor: 0.033814, label: 'Fluid Ounces' }
            ],
            'm': [
                { unit: 'cm', factor: 100, label: 'Centimeters' },
                { unit: 'mm', factor: 1000, label: 'Millimeters' },
                { unit: 'ft', factor: 3.28084, label: 'Feet' },
                { unit: 'in', factor: 39.3701, label: 'Inches' }
            ],
            'cm': [
                { unit: 'm', factor: 0.01, label: 'Meters' },
                { unit: 'mm', factor: 10, label: 'Millimeters' },
                { unit: 'in', factor: 0.393701, label: 'Inches' }
            ]
        };

        if (unitSelect) {
            unitSelect.addEventListener('change', (e) => {
                const selectedUnit = e.target.value;

                // Show/hide custom unit field
                if (customUnitGroup) {
                    customUnitGroup.style.display = selectedUnit === 'custom' ? 'block' : 'none';
                }

                // Update conversion preview
                this.updateUnitConversionPreview(selectedUnit);
            });

            // Initial state
            this.updateUnitConversionPreview(unitSelect.value);
        }
    }

    /**
     * Update unit conversion preview
     */
    updateUnitConversionPreview(unit) {
        const conversionPreview = document.getElementById('unitConversionPreview');
        const conversionList = document.getElementById('conversionList');

        if (!conversionPreview || !conversionList) return;

        const conversions = this.unitConversions[unit];

        if (conversions && conversions.length > 0) {
            conversionList.innerHTML = conversions.map(conv => `
                <div class="flex justify-between items-center text-sm">
                    <span>1 ${unit} = ${conv.factor} ${conv.unit}</span>
                    <span class="text-gray-500">${conv.label}</span>
                </div>
            `).join('');
            conversionPreview.style.display = 'block';
        } else {
            conversionPreview.style.display = 'none';
        }
    }

    /**
     * Initialize form validation
     */
    initializeFormValidation() {
        const form = document.getElementById('productForm');

        if (form) {
            form.addEventListener('submit', (e) => {
                if (!this.validateForm()) {
                    e.preventDefault();
                    return false;
                }

                this.showLoading();
                return true;
            });
        }

        // Real-time validation
        this.setupRealTimeValidation();
    }

    /**
     * Setup real-time validation
     */
    setupRealTimeValidation() {
        // Required fields validation
        const requiredFields = document.querySelectorAll('input[required], select[required]');
        requiredFields.forEach(field => {
            field.addEventListener('blur', () => this.validateField(field));
            field.addEventListener('input', () => this.clearFieldError(field));
        });

        // Price validation
        const priceFields = document.querySelectorAll('input[name*="price"]');
        priceFields.forEach(field => {
            field.addEventListener('input', () => this.validatePrice(field));
        });

        // SKU validation
        const skuField = document.querySelector('input[name="sku"]');
        if (skuField) {
            skuField.addEventListener('blur', () => this.validateSKU(skuField));
        }

        // Barcode validation
        const barcodeField = document.querySelector('input[name="barcode"]');
        if (barcodeField) {
            barcodeField.addEventListener('blur', () => this.validateBarcode(barcodeField));
        }
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let errorMessage = '';

        // Required field validation
        if (field.hasAttribute('required') && !value) {
            isValid = false;
            errorMessage = 'This field is required';
        }

        // Type-specific validation
        if (isValid && value) {
            switch (field.type) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(value)) {
                        isValid = false;
                        errorMessage = 'Please enter a valid email address';
                    }
                    break;
                case 'number':
                    const numValue = parseFloat(value);
                    if (isNaN(numValue) || numValue < 0) {
                        isValid = false;
                        errorMessage = 'Please enter a valid positive number';
                    }
                    break;
            }
        }

        this.displayFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    /**
     * Validate price fields
     */
    validatePrice(field) {
        const value = parseFloat(field.value);
        let isValid = true;
        let errorMessage = '';

        if (field.value && (isNaN(value) || value < 0)) {
            isValid = false;
            errorMessage = 'Price must be a positive number';
        }

        // Additional price logic validation
        if (isValid && field.name === 'selling_price') {
            const purchasePrice = parseFloat(document.querySelector('input[name="purchase_price"]')?.value);
            const minimumPrice = parseFloat(document.querySelector('input[name="minimum_selling_price"]')?.value);

            if (purchasePrice && value < purchasePrice) {
                isValid = false;
                errorMessage = 'Selling price should not be less than purchase price';
            }

            if (minimumPrice && value < minimumPrice) {
                isValid = false;
                errorMessage = 'Price cannot be below minimum selling price';
            }
        }

        this.displayFieldValidation(field, isValid, errorMessage);
        return isValid;
    }

    /**
     * Validate SKU uniqueness
     */
    async validateSKU(field) {
        const sku = field.value.trim();
        if (!sku) return true;

        try {
            const response = await fetch(`/api/products/validate-sku/?sku=${encodeURIComponent(sku)}`);
            const data = await response.json();

            if (!data.is_valid) {
                this.displayFieldValidation(field, false, 'SKU already exists');
                return false;
            }

            this.displayFieldValidation(field, true);
            return true;
        } catch (error) {
            console.error('SKU validation error:', error);
            return true; // Allow form submission on validation error
        }
    }

    /**
     * Validate barcode
     */
    validateBarcode(field) {
        const barcode = field.value.trim();
        if (!barcode) return true;

        // Basic barcode format validation
        const barcodeRegex = /^[0-9]{8,14}$/;
        const isValid = barcodeRegex.test(barcode);

        this.displayFieldValidation(field, isValid, isValid ? '' : 'Invalid barcode format');
        return isValid;
    }

    /**
     * Display field validation result
     */
    displayFieldValidation(field, isValid, errorMessage = '') {
        // Remove existing validation classes
        field.classList.remove('input-error', 'input-success');

        // Find or create error element
        let errorElement = field.parentNode.querySelector('.validation-error');

        if (!isValid && errorMessage) {
            field.classList.add('input-error');

            if (!errorElement) {
                errorElement = document.createElement('label');
                errorElement.className = 'label validation-error';
                errorElement.innerHTML = `<span class="label-text-alt text-error">${errorMessage}</span>`;
                field.parentNode.appendChild(errorElement);
            } else {
                errorElement.querySelector('.label-text-alt').textContent = errorMessage;
            }
        } else {
            if (field.value.trim()) {
                field.classList.add('input-success');
            }

            if (errorElement) {
                errorElement.remove();
            }
        }
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('input-error');
        const errorElement = field.parentNode.querySelector('.validation-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * Validate entire form
     */
    validateForm() {
        let isValid = true;
        const errors = [];

        // Validate required fields
        const requiredFields = document.querySelectorAll('input[required], select[required]');
        requiredFields.forEach(field => {
            if (!this.validateField(field)) {
                isValid = false;
                errors.push(`${field.name}: Required field`);
            }
        });

        // Validate prices
        const priceFields = document.querySelectorAll('input[name*="price"]');
        priceFields.forEach(field => {
            if (!this.validatePrice(field)) {
                isValid = false;
                errors.push(`${field.name}: Invalid price`);
            }
        });

        // Custom validation rules
        const sellingPrice = parseFloat(document.querySelector('input[name="selling_price"]')?.value);
        const purchasePrice = parseFloat(document.querySelector('input[name="purchase_price"]')?.value);

        if (sellingPrice && purchasePrice && sellingPrice < purchasePrice) {
            isValid = false;
            this.showToast('Selling price cannot be less than purchase price', 'error');
        }

        if (!isValid) {
            this.showToast('Please fix the form errors before submitting', 'error');
            // Scroll to first error
            const firstError = document.querySelector('.input-error');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }
        }

        return isValid;
    }

    /**
     * Initialize form data loading
     */
    initializeFormData() {
        this.loadCategories();
        this.loadBrands();
        this.initializeExistingData();
    }

    /**
     * Load categories
     */
    async loadCategories() {
        try {
            const response = await fetch('/api/categories/');
            if (!response.ok) throw new Error('Failed to load categories');

            const categories = await response.json();
            const select = document.getElementById('categorySelect');

            if (select) {
                select.innerHTML = '<option value="">Select Category</option>';
                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });

                // Set current value if editing
                const currentValue = select.dataset.currentValue;
                if (currentValue) {
                    select.value = currentValue;
                }
            }
        } catch (error) {
            console.error('Error loading categories:', error);
        }
    }

    /**
     * Load brands
     */
    async loadBrands() {
        try {
            const response = await fetch('/api/brands/');
            if (!response.ok) throw new Error('Failed to load brands');

            const brands = await response.json();
            const select = document.getElementById('brandSelect');

            if (select) {
                select.innerHTML = '<option value="">Select Brand</option>';
                brands.forEach(brand => {
                    const option = document.createElement('option');
                    option.value = brand.id;
                    option.textContent = brand.name;
                    select.appendChild(option);
                });

                // Set current value if editing
                const currentValue = select.dataset.currentValue;
                if (currentValue) {
                    select.value = currentValue;
                }
            }
        } catch (error) {
            console.error('Error loading brands:', error);
        }
    }

    /**
     * Initialize existing data for edit mode
     */
    initializeExistingData() {
        // Set initial unit display
        const unitSelect = document.getElementById('unitSelect');
        if (unitSelect && unitSelect.value) {
            const customUnitGroup = document.getElementById('customUnitGroup');
            if (customUnitGroup) {
                customUnitGroup.style.display = unitSelect.value === 'custom' ? 'block' : 'none';
            }
            this.updateUnitConversionPreview(unitSelect.value);
        }

        // Calculate initial profit margin
        this.initializePricingCalculators();
    }

    /**
     * Setup additional event listeners
     */
    setupEventListeners() {
        // Form auto-save (draft functionality)
        this.setupAutoSave();

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            // Ctrl+S to save
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                const submitBtn = document.querySelector('button[type="submit"]');
                if (submitBtn) submitBtn.click();
            }
        });

        // Prevent accidental form exit
        let formChanged = false;
        const formInputs = document.querySelectorAll('#productForm input, #productForm select, #productForm textarea');

        formInputs.forEach(input => {
            input.addEventListener('change', () => formChanged = true);
        });

        window.addEventListener('beforeunload', (e) => {
            if (formChanged) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });

        // Clear form changed flag on successful submit
        document.getElementById('productForm')?.addEventListener('submit', () => {
            formChanged = false;
        });
    }

    /**
     * Setup auto-save functionality
     */
    setupAutoSave() {
        let autoSaveTimeout;
        const formInputs = document.querySelectorAll('#productForm input, #productForm select, #productForm textarea');

        const autoSave = () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                this.saveDraft();
            }, 30000); // Auto-save every 30 seconds
        };

        formInputs.forEach(input => {
            input.addEventListener('input', autoSave);
        });
    }

    /**
     * Save form as draft
     */
    saveDraft() {
        const formData = new FormData(document.getElementById('productForm'));
        const draftData = {};

        for (const [key, value] of formData.entries()) {
            draftData[key] = value;
        }

        localStorage.setItem('product-form-draft', JSON.stringify(draftData));
        this.showToast('Draft saved automatically', 'info');
    }

    /**
     * Load form draft
     */
    loadDraft() {
        const draft = localStorage.getItem('product-form-draft');
        if (draft) {
            try {
                const draftData = JSON.parse(draft);
                Object.keys(draftData).forEach(key => {
                    const field = document.querySelector(`[name="${key}"]`);
                    if (field && field.type !== 'file') {
                        field.value = draftData[key];
                    }
                });
                this.showToast('Draft loaded', 'success');
            } catch (error) {
                console.error('Error loading draft:', error);
            }
        }
    }

    /**
     * Clear form draft
     */
    clearDraft() {
        localStorage.removeItem('product-form-draft');
    }

    /**
     * Utility methods
     */
    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} fixed top-4 right-4 w-auto max-w-sm z-50 animate-fadeIn`;
        toast.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>${message}</span>
            <button class="btn btn-sm btn-ghost ml-auto" onclick="this.parentElement.remove()">✕</button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Get CSRF token
     */
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
    }
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductFormManager;
}