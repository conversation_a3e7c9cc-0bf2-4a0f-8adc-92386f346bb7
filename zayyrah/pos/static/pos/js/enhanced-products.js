/**
 * Enhanced Product Management JavaScript
 * Modern ES6+ JavaScript for Zayyrah POS System
 * Features: AJAX operations, real-time updates, advanced filtering, and responsive UI
 */

class ProductManager {
    constructor() {
        this.currentView = 'list';
        this.currentFilters = {};
        this.currentSort = 'name_asc';
        this.searchTimeout = null;
        this.deleteProductId = null;
        this.isLoading = false;

        // API endpoints (to be configured based on Django URLs)
        this.endpoints = {
            search: '/api/products/search/',
            delete: '/api/products/delete/',
            export: '/api/products/export/',
            import: '/api/products/import/',
            stats: '/api/products/stats/',
            filters: '/api/products/filters/'
        };

        // Initialize event listeners
        this.initializeEventListeners();
        this.loadInitialData();
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
            searchInput.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    e.target.value = '';
                    this.handleSearch('');
                }
            });
        }

        // Filter functionality
        const filterInputs = document.querySelectorAll('#categoryFilter, #stockFilter, #brandFilter, #minPrice, #maxPrice');
        filterInputs.forEach(input => {
            input.addEventListener('change', () => this.updateFilters());
        });

        // View toggle functionality
        this.initializeViewToggle();

        // Modal functionality
        this.initializeModals();

        // Keyboard shortcuts
        this.initializeKeyboardShortcuts();

        // Responsive handlers
        this.initializeResponsiveHandlers();
    }

    /**
     * Initialize view toggle functionality
     */
    initializeViewToggle() {
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');

        if (gridViewBtn) {
            gridViewBtn.addEventListener('click', () => this.setView('grid'));
        }
        if (listViewBtn) {
            listViewBtn.addEventListener('click', () => this.setView('list'));
        }

        // Load saved view preference
        const savedView = localStorage.getItem('productView');
        if (savedView) {
            this.setView(savedView);
        }
    }

    /**
     * Initialize modal functionality
     */
    initializeModals() {
        // Click outside to close modals
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // ESC key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * Initialize keyboard shortcuts
     */
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for search focus
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.getElementById('searchInput');
                if (searchInput) searchInput.focus();
            }

            // Ctrl/Cmd + F for filter toggle
            if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                e.preventDefault();
                this.toggleFilters();
            }

            // Ctrl/Cmd + G for grid view toggle
            if ((e.ctrlKey || e.metaKey) && e.key === 'g') {
                e.preventDefault();
                this.setView(this.currentView === 'grid' ? 'list' : 'grid');
            }
        });
    }

    /**
     * Initialize responsive handlers
     */
    initializeResponsiveHandlers() {
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResponsiveChanges();
        }, 250));

        // Handle orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => this.handleResponsiveChanges(), 100);
        });
    }

    /**
     * Load initial data and populate filters
     */
    async loadInitialData() {
        try {
            await this.loadFilterOptions();
            await this.updateStats();
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showToast('Error loading initial data', 'error');
        }
    }

    /**
     * Load filter options from API
     */
    async loadFilterOptions() {
        try {
            const response = await fetch(this.endpoints.filters);
            if (!response.ok) throw new Error('Failed to load filter options');

            const data = await response.json();
            this.populateFilterOptions(data);
        } catch (error) {
            console.error('Error loading filter options:', error);
            // Fallback to static options if API fails
            this.loadStaticFilterOptions();
        }
    }

    /**
     * Populate filter dropdowns with options
     */
    populateFilterOptions(data) {
        // Populate categories
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter && data.categories) {
            categoryFilter.innerHTML = '<option value="">All Categories</option>';
            data.categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categoryFilter.appendChild(option);
            });
        }

        // Populate brands
        const brandFilter = document.getElementById('brandFilter');
        if (brandFilter && data.brands) {
            brandFilter.innerHTML = '<option value="">All Brands</option>';
            data.brands.forEach(brand => {
                const option = document.createElement('option');
                option.value = brand.id;
                option.textContent = brand.name;
                brandFilter.appendChild(option);
            });
        }
    }

    /**
     * Load static filter options as fallback
     */
    loadStaticFilterOptions() {
        // This would be populated with basic options when API is not available
        console.log('Loading static filter options as fallback');
    }

    /**
     * Handle search input with debouncing
     */
    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    /**
     * Perform the actual search
     */
    async performSearch(query) {
        if (this.isLoading) return;

        this.showLoading();
        try {
            const searchParams = new URLSearchParams({
                q: query,
                ...this.currentFilters,
                sort: this.currentSort
            });

            const response = await fetch(`${this.endpoints.search}?${searchParams}`);
            if (!response.ok) throw new Error('Search failed');

            const data = await response.json();
            this.updateProductDisplay(data.products);
            this.updatePagination(data.pagination);
            this.updateStats(data.stats);

        } catch (error) {
            console.error('Search error:', error);
            this.showToast('Search failed. Please try again.', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Update filters and refresh results
     */
    updateFilters() {
        const categoryFilter = document.getElementById('categoryFilter');
        const stockFilter = document.getElementById('stockFilter');
        const brandFilter = document.getElementById('brandFilter');
        const minPrice = document.getElementById('minPrice');
        const maxPrice = document.getElementById('maxPrice');

        this.currentFilters = {
            category: categoryFilter?.value || '',
            stock_status: stockFilter?.value || '',
            brand: brandFilter?.value || '',
            min_price: minPrice?.value || '',
            max_price: maxPrice?.value || ''
        };

        // Remove empty filters
        Object.keys(this.currentFilters).forEach(key => {
            if (!this.currentFilters[key]) {
                delete this.currentFilters[key];
            }
        });

        this.performSearch(document.getElementById('searchInput')?.value || '');
    }

    /**
     * Clear all filters
     */
    clearFilters() {
        document.getElementById('categoryFilter').value = '';
        document.getElementById('stockFilter').value = '';
        document.getElementById('brandFilter').value = '';
        document.getElementById('minPrice').value = '';
        document.getElementById('maxPrice').value = '';

        this.currentFilters = {};
        this.performSearch(document.getElementById('searchInput')?.value || '');
    }

    /**
     * Apply filters
     */
    applyFilters() {
        this.updateFilters();
        this.toggleFilters(); // Hide filter panel after applying
    }

    /**
     * Toggle advanced filters visibility
     */
    toggleFilters() {
        const advancedFilters = document.getElementById('advancedFilters');
        if (advancedFilters) {
            advancedFilters.classList.toggle('hidden');
            advancedFilters.classList.toggle('show');
        }
    }

    /**
     * Set view mode (grid or list)
     */
    setView(view) {
        this.currentView = view;

        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');

        if (view === 'grid') {
            gridView?.classList.remove('hidden');
            listView?.classList.add('hidden');
            gridViewBtn?.classList.add('btn-active');
            gridViewBtn?.classList.remove('btn-outline');
            listViewBtn?.classList.remove('btn-active');
            listViewBtn?.classList.add('btn-outline');
        } else {
            listView?.classList.remove('hidden');
            gridView?.classList.add('hidden');
            listViewBtn?.classList.add('btn-active');
            listViewBtn?.classList.remove('btn-outline');
            gridViewBtn?.classList.remove('btn-active');
            gridViewBtn?.classList.add('btn-outline');
        }

        // Save preference
        localStorage.setItem('productView', view);

        // Trigger responsive adjustments
        this.handleResponsiveChanges();
    }

    /**
     * Sort products
     */
    sortProducts(sortBy) {
        this.currentSort = sortBy;
        this.performSearch(document.getElementById('searchInput')?.value || '');
    }

    /**
     * Update product display
     */
    updateProductDisplay(products) {
        const listContainer = document.querySelector('#listView');
        const gridContainer = document.querySelector('#gridView');

        if (products && products.length > 0) {
            // Update both views with new product data
            this.renderProducts(products, listContainer, 'list');
            this.renderProducts(products, gridContainer, 'grid');
        } else {
            this.showEmptyState();
        }
    }

    /**
     * Render products in specified container
     */
    renderProducts(products, container, viewType) {
        if (!container) return;

        const productHTML = products.map(product => {
            if (viewType === 'grid') {
                return this.renderGridProduct(product);
            } else {
                return this.renderListProduct(product);
            }
        }).join('');

        container.innerHTML = productHTML;

        // Add event listeners to new elements
        this.attachProductEventListeners();
    }

    /**
     * Render product in grid view
     */
    renderGridProduct(product) {
        const stockStatus = this.getStockStatus(product);
        const imageUrl = product.images && product.images.length > 0
            ? product.images[0].image_url
            : null;

        return `
            <div class="product-card bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200" data-product-id="${product.id}">
                <div class="aspect-square bg-gray-100 flex items-center justify-center overflow-hidden">
                    ${imageUrl
                        ? `<img src="${imageUrl}" alt="${product.name}" class="w-full h-full object-cover">`
                        : `<svg class="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                             <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                           </svg>`
                    }
                </div>
                <div class="p-4">
                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2">${product.name}</h3>
                    <div class="flex flex-wrap gap-1 mb-3">
                        ${product.category ? `<span class="badge badge-primary badge-xs">${product.category.name}</span>` : ''}
                        ${stockStatus.badge}
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Price:</span>
                            <span class="font-semibold">$${product.selling_price}</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-500">Stock:</span>
                            <span class="${stockStatus.class}">
                                ${product.current_stock} ${product.unit_label}
                            </span>
                        </div>
                    </div>
                    <div class="flex gap-2">
                        <a href="/products/${product.id}/" class="btn btn-primary btn-sm flex-1">View</a>
                        <a href="/products/${product.id}/edit/" class="btn btn-outline btn-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Render product in list view
     */
    renderListProduct(product) {
        const stockStatus = this.getStockStatus(product);
        const imageUrl = product.images && product.images.length > 0
            ? product.images[0].thumbnail_url
            : null;

        return `
            <div class="product-card bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200" data-product-id="${product.id}">
                <div class="flex flex-col lg:flex-row lg:items-center gap-6">
                    <div class="w-20 h-20 lg:w-24 lg:h-24 rounded-lg bg-gray-100 flex items-center justify-center flex-shrink-0 overflow-hidden">
                        ${imageUrl
                            ? `<img src="${imageUrl}" alt="${product.name}" class="w-full h-full object-cover">`
                            : `<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                               </svg>`
                        }
                    </div>
                    <div class="flex-1 min-w-0">
                        <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
                            <div class="flex-1">
                                <h3 class="text-lg font-semibold text-gray-900 mb-1">${product.name}</h3>
                                <div class="flex flex-wrap gap-2 mb-3">
                                    ${product.sku ? `<span class="badge badge-ghost badge-sm">SKU: ${product.sku}</span>` : ''}
                                    ${product.category ? `<span class="badge badge-primary badge-sm">${product.category.name}</span>` : ''}
                                    ${product.brand ? `<span class="badge badge-secondary badge-sm">${product.brand.name}</span>` : ''}
                                </div>
                                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-500 block">Selling Price</span>
                                        <span class="font-semibold text-lg">$${product.selling_price}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500 block">Current Stock</span>
                                        <span class="font-semibold ${stockStatus.class}">
                                            ${product.current_stock} ${product.unit_label}
                                        </span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500 block">Stock Value</span>
                                        <span class="font-semibold">$${(product.total_stock_value || 0).toFixed(2)}</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-500 block">Status</span>
                                        ${stockStatus.badge}
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-row lg:flex-col gap-2">
                                <a href="/products/${product.id}/" class="btn btn-primary btn-sm">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    View
                                </a>
                                <a href="/products/${product.id}/edit/" class="btn btn-outline btn-sm">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                    Edit
                                </a>
                                <button class="btn btn-ghost btn-sm text-error" onclick="ProductManager.instance.deleteProduct(${product.id}, '${product.name.replace(/'/g, "\\'")}')">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Get stock status information
     */
    getStockStatus(product) {
        if (product.is_out_of_stock) {
            return {
                class: 'text-error',
                badge: '<span class="badge badge-error badge-sm">Out of Stock</span>'
            };
        } else if (product.is_low_stock) {
            return {
                class: 'text-warning',
                badge: '<span class="badge badge-warning badge-sm">Low Stock</span>'
            };
        } else {
            return {
                class: 'text-success',
                badge: '<span class="badge badge-success badge-sm">In Stock</span>'
            };
        }
    }

    /**
     * Attach event listeners to product elements
     */
    attachProductEventListeners() {
        // Add click handlers for product cards (for quick view)
        document.querySelectorAll('.product-card').forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking on buttons or links
                if (!e.target.closest('button, a')) {
                    const productId = card.dataset.productId;
                    if (productId) {
                        window.location.href = `/products/${productId}/`;
                    }
                }
            });
        });
    }

    /**
     * Show empty state
     */
    showEmptyState() {
        const emptyHTML = `
            <div class="text-center py-12">
                <div class="w-24 h-24 mx-auto mb-4 text-gray-300">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
                <p class="text-gray-600 mb-6">Try adjusting your search terms or filters.</p>
                <button class="btn btn-ghost" onclick="ProductManager.instance.clearFilters()">Clear Filters</button>
            </div>
        `;

        document.getElementById('listView').innerHTML = emptyHTML;
        document.getElementById('gridView').innerHTML = emptyHTML;
    }

    /**
     * Update pagination
     */
    updatePagination(paginationData) {
        // Implementation would depend on Django pagination structure
        console.log('Updating pagination:', paginationData);
    }

    /**
     * Update statistics
     */
    async updateStats(stats = null) {
        try {
            if (!stats) {
                const response = await fetch(this.endpoints.stats);
                if (response.ok) {
                    stats = await response.json();
                }
            }

            if (stats) {
                const totalProducts = document.getElementById('totalProducts');
                const lowStockCount = document.getElementById('lowStockCount');
                const totalValue = document.getElementById('totalValue');

                if (totalProducts) totalProducts.textContent = stats.total_products || 0;
                if (lowStockCount) lowStockCount.textContent = stats.low_stock_count || 0;
                if (totalValue) totalValue.textContent = `$${(stats.total_value || 0).toLocaleString()}`;
            }
        } catch (error) {
            console.error('Error updating stats:', error);
        }
    }

    /**
     * Delete product
     */
    deleteProduct(productId, productName) {
        this.deleteProductId = productId;
        document.getElementById('deleteProductName').textContent = productName;
        this.showModal('deleteModal');
    }

    /**
     * Confirm deletion
     */
    async confirmDelete() {
        if (!this.deleteProductId) return;

        this.showLoading();
        try {
            const response = await fetch(`${this.endpoints.delete}${this.deleteProductId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) throw new Error('Delete failed');

            this.showToast('Product deleted successfully', 'success');
            this.closeModal('deleteModal');

            // Remove product from display
            const productCard = document.querySelector(`[data-product-id="${this.deleteProductId}"]`);
            if (productCard) {
                productCard.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => productCard.remove(), 300);
            }

            // Refresh stats
            this.updateStats();

        } catch (error) {
            console.error('Delete error:', error);
            this.showToast('Failed to delete product', 'error');
        } finally {
            this.hideLoading();
            this.deleteProductId = null;
        }
    }

    /**
     * Export products
     */
    async exportProducts() {
        try {
            this.showLoading();
            const response = await fetch(this.endpoints.export);

            if (!response.ok) throw new Error('Export failed');

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `products-export-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.showToast('Products exported successfully', 'success');
        } catch (error) {
            console.error('Export error:', error);
            this.showToast('Export failed', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Import products
     */
    importProducts() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.csv,.xlsx,.xls';
        input.onchange = (e) => this.handleFileImport(e.target.files[0]);
        input.click();
    }

    /**
     * Handle file import
     */
    async handleFileImport(file) {
        if (!file) return;

        const formData = new FormData();
        formData.append('file', file);

        try {
            this.showLoading();
            const response = await fetch(this.endpoints.import, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                },
                body: formData
            });

            if (!response.ok) throw new Error('Import failed');

            const result = await response.json();
            this.showToast(`Import completed: ${result.imported} products imported`, 'success');

            // Refresh the product list
            this.performSearch(document.getElementById('searchInput')?.value || '');

        } catch (error) {
            console.error('Import error:', error);
            this.showToast('Import failed', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Handle responsive changes
     */
    handleResponsiveChanges() {
        const isMobile = window.innerWidth <= 768;

        // Adjust view for mobile
        if (isMobile && this.currentView === 'grid') {
            // Adjust grid columns for mobile
            const gridView = document.getElementById('gridView');
            if (gridView) {
                gridView.className = gridView.className.replace(/grid-cols-\d+/g, 'grid-cols-1');
            }
        }

        // Update stats layout for mobile
        const stats = document.querySelector('.stats');
        if (stats) {
            if (isMobile) {
                stats.classList.add('stats-vertical');
            } else {
                stats.classList.remove('stats-vertical');
            }
        }
    }

    /**
     * Show modal
     */
    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Close modal
     */
    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('modal-open');
            document.body.style.overflow = '';
        }
    }

    /**
     * Close all modals
     */
    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('modal-open');
        });
        document.body.style.overflow = '';
    }

    /**
     * Show loading overlay
     */
    showLoading() {
        this.isLoading = true;
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        this.isLoading = false;
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} fixed top-4 right-4 w-auto max-w-sm z-50 animate-fadeIn`;
        toast.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>${message}</span>
            <button class="btn btn-sm btn-ghost ml-auto" onclick="this.parentElement.remove()">✕</button>
        `;

        document.body.appendChild(toast);

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    /**
     * Get CSRF token for Django
     */
    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
    }

    /**
     * Debounce function for performance
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Initialize the ProductManager
     */
    static init() {
        if (!ProductManager.instance) {
            ProductManager.instance = new ProductManager();
        }
        return ProductManager.instance;
    }
}

// Global functions for HTML onclick handlers
window.toggleFilters = () => ProductManager.instance?.toggleFilters();
window.clearFilters = () => ProductManager.instance?.clearFilters();
window.applyFilters = () => ProductManager.instance?.applyFilters();
window.setView = (view) => ProductManager.instance?.setView(view);
window.sortProducts = (sort) => ProductManager.instance?.sortProducts(sort);
window.deleteProduct = (id, name) => ProductManager.instance?.deleteProduct(id, name);
window.confirmDelete = () => ProductManager.instance?.confirmDelete();
window.closeDeleteModal = () => ProductManager.instance?.closeModal('deleteModal');
window.exportProducts = () => ProductManager.instance?.exportProducts();
window.importProducts = () => ProductManager.instance?.importProducts();

// CSS animation keyframes for JavaScript animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeOut {
        from { opacity: 1; transform: translateY(0); }
        to { opacity: 0; transform: translateY(-10px); }
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
`;
document.head.appendChild(style);

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ProductManager;
}