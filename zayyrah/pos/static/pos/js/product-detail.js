/**
 * Product Detail Management JavaScript
 * Enhanced product detail page with batch tracking, analytics, and real-time updates
 * Zayyrah POS System
 */

class ProductDetailManager {
    constructor(productId) {
        this.productId = productId;
        this.currentTab = 'overview';
        this.charts = {};
        this.refreshInterval = null;

        // API endpoints
        this.endpoints = {
            product: `/api/products/${productId}/`,
            batches: `/api/products/${productId}/batches/`,
            movements: `/api/products/${productId}/movements/`,
            analytics: `/api/products/${productId}/analytics/`,
            sales: `/api/products/${productId}/sales/`,
            alerts: `/api/products/${productId}/alerts/`,
            addStock: `/api/products/${productId}/add-stock/`,
            suppliers: `/api/suppliers/`,
        };

        this.initializeEventListeners();
        this.loadInitialData();
        this.startRealTimeUpdates();
    }

    /**
     * Initialize all event listeners
     */
    initializeEventListeners() {
        // Tab navigation
        this.initializeTabSystem();

        // Form submissions
        this.initializeFormHandlers();

        // Filter handlers
        this.initializeFilterHandlers();

        // Modal handlers
        this.initializeModalHandlers();

        // Real-time refresh buttons
        this.initializeRefreshButtons();
    }

    /**
     * Initialize tab system
     */
    initializeTabSystem() {
        const tabs = document.querySelectorAll('.tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                if (tabName) {
                    this.switchTab(tabName);
                }
            });
        });
    }

    /**
     * Switch between tabs
     */
    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab').forEach(tab => {
            tab.classList.remove('tab-active');
            if (tab.dataset.tab === tabName) {
                tab.classList.add('tab-active');
            }
        });

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
            content.classList.remove('active');
        });

        const activeContent = document.getElementById(tabName);
        if (activeContent) {
            activeContent.classList.remove('hidden');
            activeContent.classList.add('active');
        }

        this.currentTab = tabName;

        // Load tab-specific data
        this.loadTabData(tabName);
    }

    /**
     * Load data for specific tab
     */
    async loadTabData(tabName) {
        switch (tabName) {
            case 'overview':
                await this.loadSuppliers();
                break;
            case 'batches':
                await this.loadBatches();
                break;
            case 'movements':
                await this.loadMovements();
                break;
            case 'analytics':
                await this.loadAnalytics();
                break;
            case 'sales':
                await this.loadSales();
                break;
            case 'alerts':
                await this.loadAlerts();
                break;
        }
    }

    /**
     * Initialize form handlers
     */
    initializeFormHandlers() {
        const addStockForm = document.getElementById('addStockForm');
        if (addStockForm) {
            addStockForm.addEventListener('submit', (e) => this.handleAddStock(e));
        }
    }

    /**
     * Initialize filter handlers
     */
    initializeFilterHandlers() {
        // Batch filters
        const batchStatusFilter = document.getElementById('batchStatusFilter');
        if (batchStatusFilter) {
            batchStatusFilter.addEventListener('change', () => this.loadBatches());
        }

        // Movement filters
        const movementTypeFilter = document.getElementById('movementTypeFilter');
        const movementDateFilter = document.getElementById('movementDateFilter');

        if (movementTypeFilter) {
            movementTypeFilter.addEventListener('change', () => this.loadMovements());
        }
        if (movementDateFilter) {
            movementDateFilter.addEventListener('change', () => this.loadMovements());
        }

        // Sales filters
        const salesDateFilter = document.getElementById('salesDateFilter');
        const salesPeriodFilter = document.getElementById('salesPeriodFilter');

        if (salesDateFilter) {
            salesDateFilter.addEventListener('change', () => this.loadSales());
        }
        if (salesPeriodFilter) {
            salesPeriodFilter.addEventListener('change', () => this.loadSales());
        }
    }

    /**
     * Initialize modal handlers
     */
    initializeModalHandlers() {
        // Close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal')) {
                this.closeModal(e.target.id);
            }
        });

        // ESC key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeAllModals();
            }
        });
    }

    /**
     * Initialize refresh buttons
     */
    initializeRefreshButtons() {
        // Add refresh functionality to buttons
        window.refreshBatches = () => this.loadBatches();
        window.refreshMovements = () => this.loadMovements();
        window.refreshAnalytics = () => this.loadAnalytics();
    }

    /**
     * Load initial data
     */
    async loadInitialData() {
        try {
            await Promise.all([
                this.loadSuppliers(),
                this.refreshProductData()
            ]);
        } catch (error) {
            console.error('Error loading initial data:', error);
            this.showToast('Error loading product data', 'error');
        }
    }

    /**
     * Start real-time updates
     */
    startRealTimeUpdates() {
        // Refresh data every 30 seconds
        this.refreshInterval = setInterval(() => {
            this.refreshCurrentTabData();
        }, 30000);
    }

    /**
     * Refresh current tab data
     */
    async refreshCurrentTabData() {
        if (document.hidden) return; // Don't refresh if tab is not visible

        try {
            await this.loadTabData(this.currentTab);
        } catch (error) {
            console.error('Error refreshing data:', error);
        }
    }

    /**
     * Refresh product data
     */
    async refreshProductData() {
        try {
            const response = await fetch(this.endpoints.product);
            if (!response.ok) throw new Error('Failed to fetch product data');

            const productData = await response.json();
            this.updateProductDisplay(productData);
        } catch (error) {
            console.error('Error refreshing product data:', error);
        }
    }

    /**
     * Update product display with fresh data
     */
    updateProductDisplay(productData) {
        // Update stock levels
        const stockElements = document.querySelectorAll('[data-stock-value]');
        stockElements.forEach(element => {
            element.textContent = productData.current_stock;
        });

        // Update stock value
        const valueElements = document.querySelectorAll('[data-stock-value-amount]');
        valueElements.forEach(element => {
            element.textContent = `$${productData.total_stock_value.toFixed(2)}`;
        });

        // Update alerts
        this.updateStockAlerts(productData);
    }

    /**
     * Update stock alerts
     */
    updateStockAlerts(productData) {
        const alertsContainer = document.querySelector('.space-y-2');
        if (!alertsContainer) return;

        // Clear existing alerts
        alertsContainer.innerHTML = '';

        if (productData.is_out_of_stock) {
            alertsContainer.innerHTML = `
                <div class="alert alert-error">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>This product is out of stock!</span>
                </div>
            `;
        } else if (productData.is_low_stock) {
            alertsContainer.innerHTML = `
                <div class="alert alert-warning">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span>Low stock alert: Only ${productData.current_stock} ${productData.unit_label.toLowerCase()} remaining!</span>
                </div>
            `;
        }
    }

    /**
     * Load suppliers for dropdown
     */
    async loadSuppliers() {
        try {
            const response = await fetch(this.endpoints.suppliers);
            if (!response.ok) throw new Error('Failed to load suppliers');

            const suppliers = await response.json();
            this.populateSupplierDropdown(suppliers);
        } catch (error) {
            console.error('Error loading suppliers:', error);
        }
    }

    /**
     * Populate supplier dropdown
     */
    populateSupplierDropdown(suppliers) {
        const supplierSelect = document.querySelector('select[name="supplier"]');
        if (!supplierSelect) return;

        // Clear existing options except the first one
        supplierSelect.innerHTML = '<option value="">Select Supplier</option>';

        suppliers.forEach(supplier => {
            const option = document.createElement('option');
            option.value = supplier.id;
            option.textContent = supplier.name;
            supplierSelect.appendChild(option);
        });
    }

    /**
     * Handle add stock form submission
     */
    async handleAddStock(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const stockData = Object.fromEntries(formData.entries());

        try {
            this.showLoading();

            const response = await fetch(this.endpoints.addStock, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(stockData)
            });

            if (!response.ok) throw new Error('Failed to add stock');

            const result = await response.json();

            this.showToast('Stock added successfully', 'success');
            e.target.reset();

            // Refresh data
            await this.refreshProductData();
            await this.loadBatches();

        } catch (error) {
            console.error('Error adding stock:', error);
            this.showToast('Failed to add stock', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Load stock batches
     */
    async loadBatches() {
        try {
            const statusFilter = document.getElementById('batchStatusFilter')?.value || '';
            const params = new URLSearchParams();
            if (statusFilter) params.append('status', statusFilter);

            const response = await fetch(`${this.endpoints.batches}?${params}`);
            if (!response.ok) throw new Error('Failed to load batches');

            const batches = await response.json();
            this.renderBatches(batches);
        } catch (error) {
            console.error('Error loading batches:', error);
            this.showToast('Error loading batches', 'error');
        }
    }

    /**
     * Render batches in table
     */
    renderBatches(batches) {
        const tbody = document.getElementById('batchesTableBody');
        if (!tbody) return;

        if (batches.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="text-center py-8 text-gray-500">No batches found</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = batches.map(batch => {
            const statusClass = this.getBatchStatusClass(batch.status);
            const expiryWarning = this.isExpiringSoon(batch.expiry_date) ? 'text-warning' : '';

            return `
                <tr>
                    <td>
                        <button class="btn btn-link btn-sm p-0" onclick="window.productDetail.showBatchDetails(${batch.id})">
                            ${batch.batch_number}
                        </button>
                    </td>
                    <td>${this.formatDate(batch.received_date)}</td>
                    <td class="${expiryWarning}">
                        ${batch.expiry_date ? this.formatDate(batch.expiry_date) : 'N/A'}
                    </td>
                    <td>${batch.quantity_received}</td>
                    <td class="font-semibold">${batch.quantity_available}</td>
                    <td>$${batch.purchase_price}</td>
                    <td>
                        <span class="badge ${statusClass}">${batch.status}</span>
                    </td>
                    <td>
                        <div class="flex gap-1">
                            <button class="btn btn-ghost btn-xs" onclick="window.productDetail.showBatchDetails(${batch.id})">
                                View
                            </button>
                            ${batch.status === 'active' ? `
                                <button class="btn btn-ghost btn-xs text-warning" onclick="window.productDetail.adjustBatch(${batch.id})">
                                    Adjust
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Get batch status CSS class
     */
    getBatchStatusClass(status) {
        const classes = {
            'active': 'badge-success',
            'expired': 'badge-error',
            'sold_out': 'badge-ghost',
            'damaged': 'badge-warning'
        };
        return classes[status] || 'badge-ghost';
    }

    /**
     * Check if batch is expiring soon
     */
    isExpiringSoon(expiryDate, days = 30) {
        if (!expiryDate) return false;

        const expiry = new Date(expiryDate);
        const today = new Date();
        const diffTime = expiry - today;
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        return diffDays <= days && diffDays > 0;
    }

    /**
     * Show batch details in modal
     */
    async showBatchDetails(batchId) {
        try {
            this.showLoading();

            const response = await fetch(`/api/batches/${batchId}/`);
            if (!response.ok) throw new Error('Failed to load batch details');

            const batch = await response.json();
            this.renderBatchDetails(batch);
            this.showModal('batchModal');

        } catch (error) {
            console.error('Error loading batch details:', error);
            this.showToast('Error loading batch details', 'error');
        } finally {
            this.hideLoading();
        }
    }

    /**
     * Render batch details in modal
     */
    renderBatchDetails(batch) {
        const content = document.getElementById('batchModalContent');
        if (!content) return;

        content.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="space-y-2">
                    <div><span class="font-medium">Batch Number:</span> ${batch.batch_number}</div>
                    <div><span class="font-medium">Received Date:</span> ${this.formatDate(batch.received_date)}</div>
                    <div><span class="font-medium">Expiry Date:</span> ${batch.expiry_date ? this.formatDate(batch.expiry_date) : 'N/A'}</div>
                    <div><span class="font-medium">Supplier:</span> ${batch.supplier ? batch.supplier.name : 'N/A'}</div>
                </div>
                <div class="space-y-2">
                    <div><span class="font-medium">Quantity Received:</span> ${batch.quantity_received}</div>
                    <div><span class="font-medium">Quantity Available:</span> ${batch.quantity_available}</div>
                    <div><span class="font-medium">Purchase Price:</span> $${batch.purchase_price}</div>
                    <div><span class="font-medium">Status:</span> <span class="badge ${this.getBatchStatusClass(batch.status)}">${batch.status}</span></div>
                </div>
            </div>
            ${batch.notes ? `
                <div class="mt-4">
                    <span class="font-medium">Notes:</span>
                    <p class="mt-1 text-gray-600">${batch.notes}</p>
                </div>
            ` : ''}
        `;
    }

    /**
     * Load stock movements
     */
    async loadMovements() {
        try {
            const typeFilter = document.getElementById('movementTypeFilter')?.value || '';
            const dateFilter = document.getElementById('movementDateFilter')?.value || '';

            const params = new URLSearchParams();
            if (typeFilter) params.append('type', typeFilter);
            if (dateFilter) params.append('date', dateFilter);

            const response = await fetch(`${this.endpoints.movements}?${params}`);
            if (!response.ok) throw new Error('Failed to load movements');

            const movements = await response.json();
            this.renderMovements(movements);
        } catch (error) {
            console.error('Error loading movements:', error);
            this.showToast('Error loading movements', 'error');
        }
    }

    /**
     * Render movements in table
     */
    renderMovements(movements) {
        const tbody = document.getElementById('movementsTableBody');
        if (!tbody) return;

        if (movements.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="6" class="text-center py-8 text-gray-500">No movements found</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = movements.map(movement => {
            const quantityClass = movement.quantity > 0 ? 'text-success' : 'text-error';
            const quantityPrefix = movement.quantity > 0 ? '+' : '';

            return `
                <tr>
                    <td>${this.formatDateTime(movement.created_at)}</td>
                    <td>
                        <span class="badge ${this.getMovementTypeClass(movement.movement_type)}">
                            ${movement.movement_type}
                        </span>
                    </td>
                    <td class="${quantityClass} font-semibold">
                        ${quantityPrefix}${movement.quantity}
                    </td>
                    <td>${movement.reference_number || 'N/A'}</td>
                    <td>${movement.notes || 'N/A'}</td>
                    <td>${movement.created_by ? movement.created_by.username : 'System'}</td>
                </tr>
            `;
        }).join('');
    }

    /**
     * Get movement type CSS class
     */
    getMovementTypeClass(type) {
        const classes = {
            'purchase': 'badge-success',
            'sale': 'badge-primary',
            'adjustment': 'badge-warning',
            'transfer': 'badge-info',
            'damage': 'badge-error'
        };
        return classes[type] || 'badge-ghost';
    }

    /**
     * Load analytics data
     */
    async loadAnalytics() {
        try {
            const response = await fetch(this.endpoints.analytics);
            if (!response.ok) throw new Error('Failed to load analytics');

            const analytics = await response.json();
            this.renderAnalytics(analytics);
        } catch (error) {
            console.error('Error loading analytics:', error);
            this.showToast('Error loading analytics', 'error');
        }
    }

    /**
     * Render analytics charts and data
     */
    renderAnalytics(analytics) {
        // Update analytics summary
        document.getElementById('totalSales30d').textContent = analytics.total_sales_30d || 0;
        document.getElementById('turnoverRate').textContent = `${analytics.turnover_rate || 0}%`;
        document.getElementById('profitMargin').textContent = `${analytics.profit_margin || 0}%`;

        // Render charts
        this.renderStockTrendChart(analytics.stock_trend);
        this.renderSalesChart(analytics.sales_data);
    }

    /**
     * Render stock trend chart
     */
    renderStockTrendChart(stockTrend) {
        const ctx = document.getElementById('stockTrendChart');
        if (!ctx) return;

        if (this.charts.stockTrend) {
            this.charts.stockTrend.destroy();
        }

        this.charts.stockTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: stockTrend.map(item => this.formatDate(item.date)),
                datasets: [{
                    label: 'Stock Level',
                    data: stockTrend.map(item => item.stock_level),
                    borderColor: '#0da487',
                    backgroundColor: 'rgba(13, 164, 135, 0.1)',
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    /**
     * Render sales chart
     */
    renderSalesChart(salesData) {
        const ctx = document.getElementById('salesChart');
        if (!ctx) return;

        if (this.charts.sales) {
            this.charts.sales.destroy();
        }

        this.charts.sales = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: salesData.map(item => this.formatDate(item.date)),
                datasets: [{
                    label: 'Sales Quantity',
                    data: salesData.map(item => item.quantity_sold),
                    backgroundColor: '#0da487',
                    borderColor: '#0a8470',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }

    /**
     * Load sales history
     */
    async loadSales() {
        try {
            const dateFilter = document.getElementById('salesDateFilter')?.value || '';
            const periodFilter = document.getElementById('salesPeriodFilter')?.value || '30';

            const params = new URLSearchParams();
            if (dateFilter) params.append('date', dateFilter);
            params.append('period', periodFilter);

            const response = await fetch(`${this.endpoints.sales}?${params}`);
            if (!response.ok) throw new Error('Failed to load sales');

            const sales = await response.json();
            this.renderSales(sales);
        } catch (error) {
            console.error('Error loading sales:', error);
            this.showToast('Error loading sales', 'error');
        }
    }

    /**
     * Render sales history
     */
    renderSales(sales) {
        const container = document.getElementById('salesHistoryContainer');
        if (!container) return;

        if (sales.length === 0) {
            container.innerHTML = `
                <div class="text-center py-8 text-gray-500">
                    <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"></path>
                    </svg>
                    <p>No sales recorded for this period</p>
                </div>
            `;
            return;
        }

        container.innerHTML = sales.map(sale => `
            <div class="card bg-base-100 shadow-sm">
                <div class="card-body">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-semibold">Sale #${sale.id}</h4>
                            <p class="text-sm text-gray-600">${this.formatDateTime(sale.created_at)}</p>
                            <div class="mt-2 space-y-1">
                                <div class="text-sm">Quantity: <span class="font-medium">${sale.quantity}</span></div>
                                <div class="text-sm">Price: <span class="font-medium">$${sale.price}</span></div>
                                <div class="text-sm">Total: <span class="font-medium">$${(sale.quantity * sale.price).toFixed(2)}</span></div>
                            </div>
                        </div>
                        <div class="text-right">
                            <div class="text-sm text-gray-600">Customer</div>
                            <div class="font-medium">${sale.customer ? sale.customer.name : 'Walk-in'}</div>
                            <a href="/sales/${sale.id}/" class="btn btn-outline btn-xs mt-2">View Sale</a>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Load alerts
     */
    async loadAlerts() {
        try {
            const response = await fetch(this.endpoints.alerts);
            if (!response.ok) throw new Error('Failed to load alerts');

            const alerts = await response.json();
            this.renderAlerts(alerts);
        } catch (error) {
            console.error('Error loading alerts:', error);
            this.showToast('Error loading alerts', 'error');
        }
    }

    /**
     * Render alerts
     */
    renderAlerts(alerts) {
        const container = document.getElementById('alertsContainer');
        if (!container) return;

        if (alerts.length === 0) {
            container.innerHTML = `
                <div class="alert alert-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>No active alerts for this product</span>
                </div>
            `;
            return;
        }

        container.innerHTML = alerts.map(alert => {
            const alertClass = this.getAlertClass(alert.severity);

            return `
                <div class="alert ${alertClass}">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div>
                        <div class="font-semibold">${alert.title}</div>
                        <div class="text-sm">${alert.message}</div>
                        <div class="text-xs opacity-70">${this.formatDateTime(alert.created_at)}</div>
                    </div>
                    ${alert.status === 'active' ? `
                        <button class="btn btn-sm btn-ghost" onclick="window.productDetail.acknowledgeAlert(${alert.id})">
                            Acknowledge
                        </button>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    /**
     * Get alert CSS class based on severity
     */
    getAlertClass(severity) {
        const classes = {
            'low': 'alert-info',
            'medium': 'alert-warning',
            'high': 'alert-error',
            'critical': 'alert-error'
        };
        return classes[severity] || 'alert-info';
    }

    /**
     * Acknowledge alert
     */
    async acknowledgeAlert(alertId) {
        try {
            const response = await fetch(`/api/alerts/${alertId}/acknowledge/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCSRFToken(),
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) throw new Error('Failed to acknowledge alert');

            this.showToast('Alert acknowledged', 'success');
            await this.loadAlerts();

        } catch (error) {
            console.error('Error acknowledging alert:', error);
            this.showToast('Failed to acknowledge alert', 'error');
        }
    }

    /**
     * Utility methods
     */
    formatDate(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleDateString();
    }

    formatDateTime(dateString) {
        if (!dateString) return 'N/A';
        return new Date(dateString).toLocaleString();
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('modal-open');
            document.body.style.overflow = 'hidden';
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('modal-open');
            document.body.style.overflow = '';
        }
    }

    closeAllModals() {
        document.querySelectorAll('.modal').forEach(modal => {
            modal.classList.remove('modal-open');
        });
        document.body.style.overflow = '';
    }

    showLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.remove('hidden');
        }
    }

    hideLoading() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.classList.add('hidden');
        }
    }

    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} fixed top-4 right-4 w-auto max-w-sm z-50 animate-fadeIn`;
        toast.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <span>${message}</span>
            <button class="btn btn-sm btn-ghost ml-auto" onclick="this.parentElement.remove()">✕</button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentElement) {
                toast.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => toast.remove(), 300);
            }
        }, 5000);
    }

    getCSRFToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value ||
               document.querySelector('meta[name=csrf-token]')?.getAttribute('content') || '';
    }

    /**
     * Cleanup when leaving page
     */
    destroy() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // Destroy charts
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
    }
}

// Global functions for HTML onclick handlers
window.deleteProduct = (id, name) => {
    document.getElementById('deleteProductName').textContent = name;
    const modal = document.getElementById('deleteModal');
    if (modal) modal.classList.add('modal-open');
};

window.closeDeleteModal = () => {
    const modal = document.getElementById('deleteModal');
    if (modal) modal.classList.remove('modal-open');
};

window.confirmDelete = async () => {
    try {
        const response = await fetch(`/api/products/${window.productDetail.productId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': window.productDetail.getCSRFToken(),
            }
        });

        if (!response.ok) throw new Error('Delete failed');

        window.productDetail.showToast('Product deleted successfully', 'success');
        setTimeout(() => {
            window.location.href = '/products/';
        }, 1500);

    } catch (error) {
        console.error('Delete error:', error);
        window.productDetail.showToast('Failed to delete product', 'error');
    }

    window.closeDeleteModal();
};

window.closeBatchModal = () => {
    const modal = document.getElementById('batchModal');
    if (modal) modal.classList.remove('modal-open');
};

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.productDetail) {
        window.productDetail.destroy();
    }
});