from django.urls import path
from .api_views import (
    ProductCategoryListCreateView,
    # ProductListCreateView, ProductDetailView, ProductStockView,  # DEPRECATED
    SaleListCreateView, SaleDetailView,
    ManualSaleListCreateView, ManualSaleDetailView, CustomerManualSalesView,
    dashboard_view, manual_sales_analytics_view
)

app_name = 'pos_api'

urlpatterns = [
    # Category endpoints
    path('categories/', ProductCategoryListCreateView.as_view(), name='category_list_create'),

    # Product endpoints DEPRECATED - Use /inventory/pos/api/products/ instead

    # Sale endpoints
    path('sales/', SaleListCreateView.as_view(), name='sale_list_create'),
    path('sales/<int:pk>/', SaleDetailView.as_view(), name='sale_detail'),

    # Manual Sales endpoints
    path('manual-sales/', ManualSaleListCreateView.as_view(), name='manual_sale_list_create'),
    path('manual-sales/<int:pk>/', ManualSaleDetailView.as_view(), name='manual_sale_detail'),
    path('manual-sales/analytics/', manual_sales_analytics_view, name='manual_sales_analytics'),
    path('customers/<int:customer_id>/manual-sales/', CustomerManualSalesView.as_view(), name='customer_manual_sales'),

    # Dashboard endpoint
    path('dashboard/', dashboard_view, name='dashboard'),
]