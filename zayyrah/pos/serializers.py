from rest_framework import serializers
from django.db import transaction
from django.db.models import Sum, Q
from decimal import Decimal
from .models import (
    ProductStockEntry, Sale, SaleItem
)
from inventory.models import EnhancedProduct
from customers.models import Customer




class ProductStockEntrySerializer(serializers.ModelSerializer):
    class Meta:
        model = ProductStockEntry
        fields = (
            'id', 'quantity_added', 'quantity_remaining', 'purchase_price',
            'added_on', 'expiry_date', 'notes', 'created_at', 'updated_at'
        )
        read_only_fields = ('id', 'quantity_remaining', 'created_at', 'updated_at')

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        validated_data['product'] = self.context['product']
        return super().create(validated_data)


class ProductListSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    unit_label = serializers.ReadOnlyField()
    stock_quantity = serializers.SerializerMethodField()

    class Meta:
        model = EnhancedProduct
        fields = (
            'id', 'name', 'sku', 'category', 'category_name', 'purchase_price', 'selling_price',
            'stock_quantity', 'unit', 'unit_label', 'tax_rate', 'created_at', 'updated_at'
        )

    def get_stock_quantity(self, obj):
        """Get current stock quantity from inventory system"""
        return int(obj.current_stock) if hasattr(obj, 'current_stock') else 0


class ProductDetailSerializer(serializers.ModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    unit_label = serializers.ReadOnlyField()
    stock_quantity = serializers.SerializerMethodField()

    class Meta:
        model = EnhancedProduct
        fields = (
            'id', 'name', 'sku', 'category', 'category_name', 'purchase_price', 'selling_price',
            'stock_quantity', 'unit', 'unit_label', 'unit_custom_label', 'tax_rate',
            'created_at', 'updated_at'
        )

    def get_stock_quantity(self, obj):
        """Get current stock quantity from inventory system"""
        return int(obj.current_stock) if hasattr(obj, 'current_stock') else 0


class ProductCreateSerializer(serializers.ModelSerializer):
    category_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = EnhancedProduct
        fields = (
            'name', 'sku', 'category_id', 'purchase_price', 'selling_price',
            'stock_quantity', 'unit', 'unit_custom_label', 'tax_rate'
        )

    def validate_category_id(self, value):
        if value is not None:
            try:
                category = ProductCategory.objects.get(id=value, owner=self.context['request'].user)
                return category
            except ProductCategory.DoesNotExist:
                raise serializers.ValidationError("Category not found.")
        return None

    def create(self, validated_data):
        category = validated_data.pop('category_id', None)
        validated_data['owner'] = self.context['request'].user
        if category:
            validated_data['category'] = category
        return super().create(validated_data)


class ProductUpdateSerializer(serializers.ModelSerializer):
    category_id = serializers.IntegerField(required=False, allow_null=True)

    class Meta:
        model = EnhancedProduct
        fields = (
            'name', 'sku', 'category_id', 'purchase_price', 'selling_price',
            'unit', 'unit_custom_label', 'tax_rate'
        )

    def validate_category_id(self, value):
        if value is not None:
            try:
                category = ProductCategory.objects.get(id=value, owner=self.context['request'].user)
                return category
            except ProductCategory.DoesNotExist:
                raise serializers.ValidationError("Category not found.")
        return None

    def update(self, instance, validated_data):
        category = validated_data.pop('category_id', None)
        if category is not None:
            validated_data['category'] = category
        return super().update(instance, validated_data)


class SaleItemSerializer(serializers.ModelSerializer):
    product = ProductListSerializer(read_only=True)
    line_total = serializers.SerializerMethodField()

    class Meta:
        model = SaleItem
        fields = ('id', 'product', 'quantity', 'price', 'tax_rate', 'line_total')

    def get_line_total(self, obj):
        return str(obj.price * obj.quantity)


class SaleListSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField()
    items_count = serializers.SerializerMethodField()

    class Meta:
        model = Sale
        fields = (
            'id', 'customer', 'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'items_count', 'notes', 'created_at'
        )

    def get_customer(self, obj):
        if obj.customer:
            return {
                'id': obj.customer.id,
                'name': obj.customer.name or 'Customer',
                'mobile_number': obj.customer.mobile_number or ''
            }
        return None

    def get_items_count(self, obj):
        return obj.items.count()


class SaleDetailSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField()
    items = SaleItemSerializer(many=True, read_only=True)

    class Meta:
        model = Sale
        fields = (
            'id', 'customer', 'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'notes', 'items', 'created_at', 'updated_at'
        )

    def get_customer(self, obj):
        if obj.customer:
            return {
                'id': obj.customer.id,
                'name': obj.customer.name or 'Customer',
                'mobile_number': obj.customer.mobile_number or '',
                'email': obj.customer.email or ''
            }
        return None


class SaleItemCreateSerializer(serializers.Serializer):
    product_id = serializers.IntegerField()
    quantity = serializers.IntegerField(min_value=1)
    price = serializers.DecimalField(max_digits=10, decimal_places=2, min_value=0)


class SaleCreateSerializer(serializers.Serializer):
    customer_id = serializers.IntegerField(required=False, allow_null=True)
    payment_method = serializers.ChoiceField(choices=Sale.PaymentMethod.choices, default=Sale.PaymentMethod.CASH)
    discount_total = serializers.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'), min_value=0)
    notes = serializers.CharField(required=False, allow_blank=True, default='')
    items = SaleItemCreateSerializer(many=True)

    def validate_customer_id(self, value):
        if value is not None:
            try:
                customer = Customer.objects.get(id=value, owner=self.context['request'].user)
                return customer
            except Customer.DoesNotExist:
                raise serializers.ValidationError("Customer not found.")
        return None

    def validate_items(self, value):
        if not value:
            raise serializers.ValidationError("At least one item is required.")

        # Aggregate quantities for the same product
        aggregated = {}
        for item in value:
            product_id = item['product_id']
            quantity = item['quantity']
            if product_id in aggregated:
                aggregated[product_id]['quantity'] += quantity
            else:
                aggregated[product_id] = item.copy()

        # Validate products exist and have sufficient stock
        user = self.context['request'].user
        validated_items = []
        for product_id, item_data in aggregated.items():
            try:
                product = EnhancedProduct.objects.get(id=product_id, owner=user, pos_enabled=True)
            except EnhancedProduct.DoesNotExist:
                raise serializers.ValidationError(f"Product with ID {product_id} not found.")

            quantity = item_data['quantity']
            current_stock = product.current_stock if hasattr(product, 'current_stock') else 0
            if current_stock < quantity:
                raise serializers.ValidationError(f"Insufficient stock for {product.name}. Available: {current_stock}")

            validated_items.append({
                'product': product,
                'quantity': quantity,
                'price': item_data['price']
            })

        return validated_items

    def create(self, validated_data):
        customer = validated_data.get('customer_id')
        payment_method = validated_data['payment_method']
        discount_total = validated_data['discount_total']
        notes = validated_data['notes']
        items_data = validated_data['items']
        user = self.context['request'].user

        # Calculate totals
        subtotal = Decimal('0.00')
        tax_total = Decimal('0.00')
        sale_items = []

        for item_data in items_data:
            product = item_data['product']
            quantity = item_data['quantity']
            price = item_data['price']

            line_subtotal = price * quantity
            line_tax = (product.tax_rate / Decimal('100')) * line_subtotal
            subtotal += line_subtotal
            tax_total += line_tax

            sale_items.append({
                'product': product,
                'quantity': quantity,
                'price': price,
                'tax_rate': product.tax_rate,
                'line_subtotal': line_subtotal
            })

        total = subtotal + tax_total - discount_total
        if total < 0:
            total = Decimal('0.00')

        # Create sale and items in transaction
        with transaction.atomic():
            sale = Sale.objects.create(
                owner=user,
                customer=customer if customer and customer.owner == user else None,
                subtotal=subtotal,
                tax_total=tax_total,
                discount_total=discount_total,
                total=total,
                payment_method=payment_method,
                notes=notes,
            )

            for item_data in sale_items:
                product = item_data['product']
                quantity = item_data['quantity']

                # Allocate stock
                product.allocate_stock(quantity)

                # Create sale item
                SaleItem.objects.create(
                    sale=sale,
                    product=product,
                    quantity=quantity,
                    price=item_data['price'],
                    tax_rate=item_data['tax_rate'],
                )

        return sale


class DashboardSerializer(serializers.Serializer):
    period = serializers.CharField()
    sales = serializers.DictField()
    products = serializers.DictField()
    customers = serializers.DictField()
    revenue = serializers.DictField()
    top_products = serializers.ListField()


# Manual Sales Serializers
class ManualSaleListSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField()
    payment_method_display = serializers.SerializerMethodField()
    payment_status_display = serializers.SerializerMethodField()

    class Meta:
        model = Sale
        fields = (
            'id', 'customer', 'manual_description', 'manual_category',
            'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'payment_method_display', 'payment_status', 'payment_status_display',
            'amount_paid', 'credit_amount', 'cash_amount',
            'notes', 'created_at', 'updated_at'
        )

    def get_customer(self, obj):
        if obj.customer:
            return {
                'id': obj.customer.id,
                'name': obj.customer.name or 'Customer',
                'mobile_number': obj.customer.mobile_number or ''
            }
        return None

    def get_payment_method_display(self, obj):
        return obj.get_payment_method_display()

    def get_payment_status_display(self, obj):
        return obj.get_payment_status_display()


class ManualSaleDetailSerializer(serializers.ModelSerializer):
    customer = serializers.SerializerMethodField()
    payment_method_display = serializers.SerializerMethodField()
    payment_status_display = serializers.SerializerMethodField()
    remaining_balance = serializers.ReadOnlyField()
    is_fully_paid = serializers.ReadOnlyField()

    class Meta:
        model = Sale
        fields = (
            'id', 'customer', 'manual_description', 'manual_category',
            'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'payment_method_display', 'payment_status', 'payment_status_display',
            'amount_paid', 'credit_amount', 'cash_amount',
            'remaining_balance', 'is_fully_paid',
            'notes', 'created_at', 'updated_at'
        )

    def get_customer(self, obj):
        if obj.customer:
            return {
                'id': obj.customer.id,
                'name': obj.customer.name or 'Customer',
                'mobile_number': obj.customer.mobile_number or '',
                'email': obj.customer.email or ''
            }
        return None

    def get_payment_method_display(self, obj):
        return obj.get_payment_method_display()

    def get_payment_status_display(self, obj):
        return obj.get_payment_status_display()


class ManualSaleCreateSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(required=False, allow_null=True)
    manual_category = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    notes = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = Sale
        fields = (
            'customer_id', 'manual_description', 'manual_category',
            'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'amount_paid', 'credit_amount', 'cash_amount',
            'notes'
        )

    def validate_customer_id(self, value):
        if value is not None:
            try:
                customer = Customer.objects.get(id=value, owner=self.context['request'].user)
                return customer
            except Customer.DoesNotExist:
                raise serializers.ValidationError("Customer not found.")
        return None

    def validate(self, attrs):
        print(f"DEBUG SERIALIZER VALIDATE:")
        print(f"  Input attrs: {attrs}")

        # Handle null values for optional fields
        if attrs.get('manual_category') is None:
            attrs['manual_category'] = ''
        if attrs.get('notes') is None:
            attrs['notes'] = ''

        manual_description = attrs.get('manual_description', '').strip()
        print(f"  Manual description: '{manual_description}'")

        if not manual_description:
            print(f"  ERROR: Manual description is empty")
            raise serializers.ValidationError({
                'manual_description': 'Manual description is required for manual sales.'
            })

        # Validate payment amounts
        total = attrs.get('total', Decimal('0.00'))
        amount_paid = attrs.get('amount_paid', Decimal('0.00'))
        payment_method = attrs.get('payment_method')

        print(f"  Financial validation:")
        print(f"    total: {total} (type: {type(total)})")
        print(f"    amount_paid: {amount_paid} (type: {type(amount_paid)})")
        print(f"    payment_method: {payment_method}")

        if total < 0:
            print(f"  ERROR: Total is negative")
            raise serializers.ValidationError({
                'total': 'Total cannot be negative.'
            })

        if amount_paid < 0:
            print(f"  ERROR: Amount paid is negative")
            raise serializers.ValidationError({
                'amount_paid': 'Amount paid cannot be negative.'
            })

        if amount_paid > total:
            print(f"  ERROR: Amount paid exceeds total")
            raise serializers.ValidationError({
                'amount_paid': 'Amount paid cannot exceed total.'
            })

        # Validate mixed payment breakdown
        if payment_method == Sale.PaymentMethod.MIXED:
            cash_amount = attrs.get('cash_amount', Decimal('0.00'))
            credit_amount = attrs.get('credit_amount', Decimal('0.00'))
            print(f"  Mixed payment validation:")
            print(f"    cash_amount: {cash_amount}")
            print(f"    credit_amount: {credit_amount}")
            print(f"    sum: {cash_amount + credit_amount}")

            if cash_amount + credit_amount != amount_paid:
                print(f"  ERROR: Mixed payment amounts don't add up")
                raise serializers.ValidationError({
                    'cash_amount': 'Cash amount + credit amount must equal amount paid for mixed payments.'
                })

        print(f"  Validation passed, returning: {attrs}")
        return attrs

    def create(self, validated_data):
        customer = validated_data.pop('customer_id', None)
        validated_data['owner'] = self.context['request'].user
        validated_data['customer'] = customer
        validated_data['sale_type'] = Sale.SaleType.MANUAL

        return super().create(validated_data)


class ManualSaleUpdateSerializer(serializers.ModelSerializer):
    customer_id = serializers.IntegerField(required=False, allow_null=True)
    manual_category = serializers.CharField(required=False, allow_blank=True, allow_null=True)
    notes = serializers.CharField(required=False, allow_blank=True, allow_null=True)

    class Meta:
        model = Sale
        fields = (
            'customer_id', 'manual_description', 'manual_category',
            'subtotal', 'tax_total', 'discount_total', 'total',
            'payment_method', 'amount_paid', 'credit_amount', 'cash_amount',
            'notes'
        )

    def validate_customer_id(self, value):
        if value is not None:
            try:
                customer = Customer.objects.get(id=value, owner=self.context['request'].user)
                return customer
            except Customer.DoesNotExist:
                raise serializers.ValidationError("Customer not found.")
        return None

    def validate(self, attrs):
        # Handle null values for optional fields
        if attrs.get('manual_category') is None:
            attrs['manual_category'] = ''
        if attrs.get('notes') is None:
            attrs['notes'] = ''

        # Get instance for existing values
        instance = self.instance
        manual_description = attrs.get('manual_description', instance.manual_description if instance else '').strip()

        if not manual_description:
            raise serializers.ValidationError({
                'manual_description': 'Manual description is required for manual sales.'
            })

        # Validate payment amounts
        total = attrs.get('total', instance.total if instance else Decimal('0.00'))
        amount_paid = attrs.get('amount_paid', instance.amount_paid if instance else Decimal('0.00'))
        payment_method = attrs.get('payment_method', instance.payment_method if instance else Sale.PaymentMethod.CASH)

        if total < 0:
            raise serializers.ValidationError({
                'total': 'Total cannot be negative.'
            })

        if amount_paid < 0:
            raise serializers.ValidationError({
                'amount_paid': 'Amount paid cannot be negative.'
            })

        if amount_paid > total:
            raise serializers.ValidationError({
                'amount_paid': 'Amount paid cannot exceed total.'
            })

        # Validate mixed payment breakdown
        if payment_method == Sale.PaymentMethod.MIXED:
            cash_amount = attrs.get('cash_amount', instance.cash_amount if instance else Decimal('0.00'))
            credit_amount = attrs.get('credit_amount', instance.credit_amount if instance else Decimal('0.00'))

            if cash_amount + credit_amount != amount_paid:
                raise serializers.ValidationError({
                    'cash_amount': 'Cash amount + credit amount must equal amount paid for mixed payments.'
                })

        return attrs

    def update(self, instance, validated_data):
        customer = validated_data.pop('customer_id', None)
        if customer is not None:
            validated_data['customer'] = customer

        return super().update(instance, validated_data)