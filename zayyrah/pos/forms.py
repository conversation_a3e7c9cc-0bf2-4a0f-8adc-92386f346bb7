from django import forms
from django.utils import timezone

from .models import ProductStockEntry, Sale
from inventory.models import EnhancedProduct
from inventory.models import Category


class ProductForm(forms.ModelForm):
    new_category_name = forms.CharField(
        required=False,
        label='New category',
        widget=forms.TextInput(attrs={'placeholder': 'Create a category on save'}),
    )

    def __init__(self, *args, owner=None, **kwargs):
        print(f"[DEBUG] ProductForm.__init__: owner={owner}, instance={kwargs.get('instance')}")
        self.owner = owner
        super().__init__(*args, **kwargs)

        if self.instance.pk and not self.owner:
            self.owner = self.instance.owner
            print(f"[DEBUG] Set owner from instance: {self.owner}")

        if self.owner:
            categories = Category.objects.filter(owner=self.owner, is_active=True).order_by('name')
            self.fields['category'].queryset = categories
            print(f"[DEBUG] Set category queryset: {categories.count()} categories")
        else:
            print(f"[DEBUG] No owner, category queryset not filtered")

        self.fields['category'].empty_label = 'Select category'
        self.fields['category'].required = False
        self.fields['purchase_price'].widget = forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'class': 'form-control'})
        self.fields['selling_price'].widget = forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'class': 'form-control'})
        self.fields['tax_rate'].widget = forms.NumberInput(attrs={'step': '0.01', 'min': '0', 'class': 'form-control'})
        self.fields['unit'].widget = forms.Select(attrs={'class': 'form-control'})
        self.fields['unit_custom_label'].widget = forms.TextInput(attrs={'placeholder': 'e.g. Bundle', 'class': 'form-control'})

    class Meta:
        model = EnhancedProduct
        fields = [
            'name',
            'category',
            'sku',
            'purchase_price',
            'selling_price',
            'unit',
            'unit_custom_label',
            'tax_rate',
        ]
        widgets = {
            'name': forms.TextInput(attrs={'placeholder': 'Product name', 'class': 'form-control'}),
            'sku': forms.TextInput(attrs={'placeholder': 'SKU (optional)', 'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-control'}),
        }

    def clean_unit_custom_label(self):
        unit = self.cleaned_data.get('unit')
        label = self.cleaned_data.get('unit_custom_label')
        if unit == 'custom':
            if not label:
                raise forms.ValidationError('Provide a label for the custom unit.')
            return label
        return ''

    def clean_new_category_name(self):
        value = self.cleaned_data.get('new_category_name')
        return value.strip() if value else ''

    def save(self, commit=True):
        print(f"[DEBUG] ProductForm.save: Starting save process, commit={commit}")
        print(f"[DEBUG] Form owner: {self.owner}")
        print(f"[DEBUG] Form cleaned_data: {self.cleaned_data}")

        instance = super().save(commit=False)
        print(f"[DEBUG] Instance after super().save(commit=False): {instance}")

        owner = self.owner or getattr(instance, 'owner', None)
        print(f"[DEBUG] Determined owner: {owner}")

        if owner and not getattr(instance, 'owner_id', None):
            print(f"[DEBUG] Setting owner on instance")
            instance.owner = owner

        new_category_name = self.cleaned_data.get('new_category_name')
        print(f"[DEBUG] New category name: {new_category_name}")

        if new_category_name and owner:
            print(f"[DEBUG] Creating new category: {new_category_name}")
            category, created = Category.objects.get_or_create(owner=owner, name=new_category_name)
            print(f"[DEBUG] Category {'created' if created else 'found'}: {category}")
            instance.category = category

        if commit:
            print(f"[DEBUG] Committing instance save")
            try:
                instance.save()
                print(f"[DEBUG] Instance saved successfully: {instance}")
                self.save_m2m()
                print(f"[DEBUG] M2M relationships saved")
            except Exception as e:
                print(f"[DEBUG] Error during save: {e}")
                raise
        else:
            print(f"[DEBUG] Not committing save (commit=False)")

        return instance


class ProductStockEntryForm(forms.ModelForm):
    def __init__(self, *args, product=None, **kwargs):
        super().__init__(*args, **kwargs)
        if product:
            self.fields['purchase_price'].initial = product.purchase_price
        if not self.initial.get('added_on'):
            self.fields['added_on'].initial = timezone.localdate()

    class Meta:
        model = ProductStockEntry
        fields = ['quantity_added', 'purchase_price', 'added_on', 'expiry_date', 'notes']
        widgets = {
            'quantity_added': forms.NumberInput(attrs={'min': '1'}),
            'purchase_price': forms.NumberInput(attrs={'step': '0.01', 'min': '0'}),
            'added_on': forms.DateInput(attrs={'type': 'date'}),
            'expiry_date': forms.DateInput(attrs={'type': 'date'}),
            'notes': forms.TextInput(attrs={'placeholder': 'Optional note'}),
        }

    def clean(self):
        cleaned = super().clean()
        expiry = cleaned.get('expiry_date')
        added_on = cleaned.get('added_on')
        if expiry and added_on and expiry < added_on:
            self.add_error('expiry_date', 'Expiry date must be on or after the added date.')
        return cleaned


class SaleCheckoutForm(forms.ModelForm):
    discount_total = forms.DecimalField(
        required=False,
        min_value=0,
        decimal_places=2,
        max_digits=10,
        initial=0,
    )
    payment_method = forms.ChoiceField(
        choices=Sale.PaymentMethod.choices,
        initial=Sale.PaymentMethod.CASH,
        widget=forms.RadioSelect(attrs={'class': 'payment-method-choice'}),
    )

    def __init__(self, *args, owner=None, **kwargs):
        super().__init__(*args, **kwargs)
        if owner is not None and 'customer' in self.fields:
            self.fields['customer'].queryset = owner.customers.order_by('-updated_at')

    class Meta:
        model = Sale
        fields = ['customer', 'payment_method', 'discount_total', 'notes']
        widgets = {
            'notes': forms.Textarea(attrs={'rows': 3, 'placeholder': 'Add a note to this sale'}),
        }
