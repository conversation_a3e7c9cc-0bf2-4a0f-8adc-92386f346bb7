import json
from decimal import Decimal

from django.contrib.auth import get_user_model
from django.test import TestCase
from django.urls import reverse

from customers.models import Customer
from .models import Product, ProductStockEntry, Sale


class ProductTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.owner = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.BUSINESS,
            shop_name='Test Shop',
        )
        self.client.login(mobile_number=self.owner.mobile_number, password='safepass123')

    def test_product_create_assigns_owner(self):
        response = self.client.post(
            reverse('pos:product-create'),
            data={
                'name': 'Green Tea',
                'sku': 'TEA-001',
                'category': '',
                'new_category_name': '',
                'purchase_price': '8.50',
                'selling_price': '12.50',
                'unit': Product.Unit.EACH,
                'unit_custom_label': '',
                'tax_rate': '5',
            },
            follow=True,
        )
        self.assertEqual(response.status_code, 200)
        product = Product.objects.get(name='Green Tea')
        self.assertEqual(product.owner, self.owner)


class SaleFlowTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.owner = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.BUSINESS,
            shop_name='POS Shop',
        )
        self.client.login(mobile_number=self.owner.mobile_number, password='safepass123')
        self.product = Product.objects.create(
            owner=self.owner,
            name='Notebook',
            sku='NOTE-001',
            purchase_price=Decimal('2.50'),
            selling_price=Decimal('4.00'),
            stock_quantity=10,
            tax_rate=Decimal('5.00'),
        )
        ProductStockEntry.objects.create(
            owner=self.owner,
            product=self.product,
            quantity_added=10,
            quantity_remaining=10,
            purchase_price=Decimal('2.50'),
        )
        self.customer = Customer.objects.create(owner=self.owner, name='Quick Buyer')

    def test_sale_creation_reduces_stock(self):
        payload = {
            'cart_payload': json.dumps([
                {'product_id': self.product.pk, 'quantity': 2},
            ]),
            'customer': str(self.customer.pk),
            'payment_method': Sale.PaymentMethod.CASH,
            'discount_total': '0',
            'notes': '',
        }
        response = self.client.post(reverse('pos:sale-create'), data=payload, follow=True)
        self.assertEqual(response.status_code, 200)
        sale = Sale.objects.get(owner=self.owner)
        self.product.refresh_from_db()
        self.assertEqual(self.product.stock_quantity, 8)
        self.assertEqual(sale.total, Decimal('8.40'))
        self.assertEqual(sale.items.count(), 1)

    def test_sale_does_not_use_other_user_product(self):
        other_user = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.BUSINESS,
            shop_name='Other Shop',
        )
        foreign_product = Product.objects.create(
            owner=other_user,
            name='Foreign',
            sku='FOR-001',
            purchase_price=Decimal('2.00'),
            selling_price=Decimal('3.00'),
            stock_quantity=5,
        )
        ProductStockEntry.objects.create(
            owner=other_user,
            product=foreign_product,
            quantity_added=5,
            quantity_remaining=5,
            purchase_price=Decimal('2.00'),
        )
        payload = {
            'cart_payload': json.dumps([
                {'product_id': foreign_product.pk, 'quantity': 1},
            ]),
            'customer': '',
            'payment_method': Sale.PaymentMethod.CASH,
            'discount_total': '0',
            'notes': '',
        }
        response = self.client.post(reverse('pos:sale-create'), data=payload, follow=True)
        self.assertEqual(response.status_code, 200)
        self.assertFalse(Sale.objects.filter(owner=self.owner).exists())
        self.assertContains(response, 'One or more products could not be found.', status_code=200)
