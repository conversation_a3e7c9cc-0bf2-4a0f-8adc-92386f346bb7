from decimal import Decimal

from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import MinValueValidator
from django.db import models
from django.db.models import F, OrderBy, Sum
from django.utils import timezone

from customers.models import Customer



# Product model removed - using inventory.EnhancedProduct instead


class ProductStockEntry(models.Model):
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='product_stock_entries',
    )
    product = models.ForeignKey(
        'inventory.EnhancedProduct',
        on_delete=models.CASCADE,
        related_name='stock_entries',
    )
    quantity_added = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    quantity_remaining = models.PositiveIntegerField(default=0)
    purchase_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
    )
    added_on = models.DateField(default=timezone.localdate)
    expiry_date = models.DateField(blank=True, null=True)
    notes = models.CharField(max_length=255, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-added_on', '-created_at']

    def __str__(self):
        return f"{self.product.name} stock on {self.added_on}"

    def clean(self):
        super().clean()
        if self.quantity_remaining > self.quantity_added:
            raise ValidationError('Remaining quantity cannot exceed added quantity.')

    def save(self, *args, **kwargs):
        if not self.pk and not self.quantity_remaining:
            self.quantity_remaining = self.quantity_added
        super().save(*args, **kwargs)
        self.product.refresh_stock()


class Sale(models.Model):
    class SaleType(models.TextChoices):
        MANUAL = ('manual', 'Manual Entry')
        POS = ('pos', 'POS Transaction')

    class PaymentMethod(models.TextChoices):
        CASH = ('cash', 'Cash')
        CREDIT = ('credit', 'Credit')
        MIXED = ('mixed', 'Cash + Credit')

    class PaymentStatus(models.TextChoices):
        FULL = ('full', 'Fully Paid')
        PARTIAL = ('partial', 'Partially Paid')
        PENDING = ('pending', 'Payment Pending')
        CREDIT = ('credit', 'On Credit')

    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='sales',
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='sales',
    )

    # Sale Type and Processing
    sale_type = models.CharField(
        max_length=10,
        choices=SaleType.choices,
        default=SaleType.POS,
        help_text="How this sale was recorded"
    )

    # Financial Fields
    subtotal = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    tax_total = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    discount_total = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))

    # Payment Details
    payment_method = models.CharField(
        max_length=16,
        choices=PaymentMethod.choices,
        default=PaymentMethod.CASH,
    )
    payment_status = models.CharField(
        max_length=10,
        choices=PaymentStatus.choices,
        default=PaymentStatus.FULL,
        help_text="Payment completion status"
    )
    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount actually paid by customer"
    )
    credit_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Amount added to customer credit"
    )
    cash_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00'),
        help_text="Cash portion of payment"
    )

    # Manual Sale Specific Fields
    manual_description = models.TextField(
        blank=True,
        help_text="Description for manual sales (what was sold)"
    )
    manual_category = models.CharField(
        max_length=100,
        blank=True,
        help_text="Category for manual sales"
    )

    # General Fields
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        sale_type = "Manual" if self.sale_type == self.SaleType.MANUAL else "POS"
        return f"{sale_type} Sale #{self.pk}"

    @property
    def is_manual_sale(self):
        return self.sale_type == self.SaleType.MANUAL

    @property
    def is_pos_sale(self):
        return self.sale_type == self.SaleType.POS

    @property
    def remaining_balance(self):
        """Calculate remaining balance if payment is partial"""
        return max(Decimal('0.00'), self.total - self.amount_paid)

    @property
    def is_fully_paid(self):
        return self.amount_paid >= self.total

    def calculate_payment_breakdown(self):
        """Calculate cash vs credit breakdown for mixed payments"""
        if self.payment_method == self.PaymentMethod.CASH:
            return {'cash': self.amount_paid, 'credit': Decimal('0.00')}
        elif self.payment_method == self.PaymentMethod.CREDIT:
            return {'cash': Decimal('0.00'), 'credit': self.amount_paid}
        elif self.payment_method == self.PaymentMethod.MIXED:
            return {'cash': self.cash_amount, 'credit': self.credit_amount}
        return {'cash': Decimal('0.00'), 'credit': Decimal('0.00')}

    def update_customer_balance(self):
        """Update customer's credit balance based on this sale"""
        if self.customer and self.credit_amount > 0:
            # This would be implemented when we add balance tracking to Customer model
            pass

    def save(self, *args, **kwargs):
        # Auto-calculate payment status
        if self.amount_paid <= 0:
            self.payment_status = self.PaymentStatus.PENDING
        elif self.amount_paid >= self.total:
            self.payment_status = self.PaymentStatus.FULL
        else:
            self.payment_status = self.PaymentStatus.PARTIAL

        # For credit sales, set credit amount if not specified
        if self.payment_method == self.PaymentMethod.CREDIT and self.credit_amount <= 0:
            self.credit_amount = self.total - self.amount_paid

        super().save(*args, **kwargs)


class SaleItem(models.Model):
    sale = models.ForeignKey(Sale, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey('inventory.EnhancedProduct', on_delete=models.PROTECT, related_name='sale_items')
    quantity = models.PositiveIntegerField(validators=[MinValueValidator(1)])
    price = models.DecimalField(max_digits=10, decimal_places=2)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))

    class Meta:
        ordering = ['id']

    def __str__(self):
        return f"{self.product} x {self.quantity}"

# Create your models here.
