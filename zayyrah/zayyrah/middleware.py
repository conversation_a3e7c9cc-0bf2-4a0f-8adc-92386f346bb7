import logging
import time
import json
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger('django.request')

class APIRequestLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all API requests and responses
    """

    def process_request(self, request):
        """Log incoming requests"""
        request._start_time = time.time()

        # Log all requests (remove API-only filter)
        if True:  # Changed to log all requests
            # Get request body for POST/PUT requests
            body = None
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    if hasattr(request, 'body') and request.body:
                        body = request.body.decode('utf-8')
                        # Try to parse as JSON for better formatting
                        try:
                            body_json = json.loads(body)
                            # Mask sensitive fields
                            if isinstance(body_json, dict):
                                masked_body = self._mask_sensitive_data(body_json.copy())
                                body = json.dumps(masked_body)
                        except json.JSONDecodeError:
                            pass
                except Exception:
                    body = "[Unable to decode request body]"

            # Get Cloudflare information
            cf_info = self._get_cloudflare_info(request)
            is_cloudflare = self._is_cloudflare_request(request)

            # Build log message with Cloudflare info
            log_message = (
                f"{request.method} {request.path} | "
                f"IP: {self._get_client_ip(request)} | "
                f"User: {getattr(request.user, 'mobile_number', 'Anonymous')} | "
                f"User-Agent: {request.META.get('HTTP_USER_AGENT', 'Unknown')} | "
                f"Body: {body if body else 'None'}"
            )

            if is_cloudflare:
                cf_details = " | ".join([f"{k}={v}" for k, v in cf_info.items()])
                log_message += f" | CLOUDFLARE: {cf_details}"

            # Log the request
            logger.info(log_message)

    def process_response(self, request, response):
        """Log response details"""
        # Log all responses (remove API-only filter)
        if True:  # Changed to log all responses
            # Calculate request duration
            duration = None
            if hasattr(request, '_start_time'):
                duration = round((time.time() - request._start_time) * 1000, 2)  # milliseconds

            # Get response content for errors or successful API calls
            response_content = None
            if response.status_code >= 400 or (response.status_code < 300 and len(response.content) < 1000):
                try:
                    if response.content:
                        content = response.content.decode('utf-8')
                        # Try to parse as JSON
                        try:
                            content_json = json.loads(content)
                            response_content = json.dumps(content_json)
                        except json.JSONDecodeError:
                            response_content = content[:500]  # Limit length
                except Exception:
                    response_content = "[Unable to decode response]"

            # Determine log level based on status code
            log_level = logging.INFO
            if response.status_code >= 500:
                log_level = logging.ERROR
            elif response.status_code >= 400:
                log_level = logging.WARNING

            # Get Cloudflare information for response logging
            cf_info = self._get_cloudflare_info(request)
            is_cloudflare = self._is_cloudflare_request(request)

            # Build response log message
            response_log = (
                f"RESPONSE {request.method} {request.path} | "
                f"Status: {response.status_code} | "
                f"Duration: {duration}ms | "
                f"IP: {self._get_client_ip(request)} | "
                f"User: {getattr(request.user, 'mobile_number', 'Anonymous')} | "
                f"Response: {response_content if response_content else f'[{len(response.content)} bytes]'}"
            )

            if is_cloudflare:
                cf_details = " | ".join([f"{k}={v}" for k, v in cf_info.items()])
                response_log += f" | CLOUDFLARE: {cf_details}"

            # Log the response
            logger.log(log_level, response_log)

        return response

    def process_exception(self, request, exception):
        """Log exceptions"""
        if True:  # Changed to log all exceptions
            logger.error(
                f"EXCEPTION {request.method} {request.path} | "
                f"IP: {self._get_client_ip(request)} | "
                f"User: {getattr(request.user, 'mobile_number', 'Anonymous')} | "
                f"Exception: {type(exception).__name__}: {str(exception)}"
            )
        return None

    def _get_client_ip(self, request):
        """Get the client IP address with Cloudflare detection"""
        # Check for Cloudflare connecting IP first
        cf_connecting_ip = request.META.get('HTTP_CF_CONNECTING_IP')
        if cf_connecting_ip:
            return cf_connecting_ip

        # Then check X-Forwarded-For
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
            return ip

        # Finally fall back to remote addr
        return request.META.get('REMOTE_ADDR', 'Unknown')

    def _get_cloudflare_info(self, request):
        """Extract Cloudflare-specific information"""
        cf_info = {}

        # Cloudflare Ray ID
        cf_ray = request.META.get('HTTP_CF_RAY')
        if cf_ray:
            cf_info['cf_ray'] = cf_ray

        # Cloudflare IP Country
        cf_country = request.META.get('HTTP_CF_IPCOUNTRY')
        if cf_country:
            cf_info['cf_country'] = cf_country

        # Cloudflare Visitor (HTTPS/HTTP)
        cf_visitor = request.META.get('HTTP_CF_VISITOR')
        if cf_visitor:
            cf_info['cf_visitor'] = cf_visitor

        # Cloudflare Connecting IP (original client IP)
        cf_connecting_ip = request.META.get('HTTP_CF_CONNECTING_IP')
        if cf_connecting_ip:
            cf_info['cf_connecting_ip'] = cf_connecting_ip

        return cf_info

    def _is_cloudflare_request(self, request):
        """Check if request is coming through Cloudflare"""
        return bool(request.META.get('HTTP_CF_RAY') or
                   request.META.get('HTTP_CF_CONNECTING_IP'))

    def _mask_sensitive_data(self, data):
        """Mask sensitive data in request/response"""
        sensitive_fields = [
            'password', 'password_confirm', 'token', 'secret',
            'key', 'auth', 'authorization', 'otp_code'
        ]

        if isinstance(data, dict):
            for key, value in data.items():
                if any(field in key.lower() for field in sensitive_fields):
                    data[key] = "***MASKED***"
                elif isinstance(value, dict):
                    data[key] = self._mask_sensitive_data(value)
                elif isinstance(value, list):
                    data[key] = [
                        self._mask_sensitive_data(item) if isinstance(item, dict)
                        else item for item in value
                    ]

        return data