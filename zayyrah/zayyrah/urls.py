"""zayyrah URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import include, path

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('accounts.urls')),
    path('customers/', include('customers.urls')),
    path('pos/', include('pos.urls')),
    path('employees/', include('employees.urls')),
    path('inventory/', include('inventory.urls')),
    # API URLs
    path('api/', include('api_urls')),
]

# Override admin site properties for better branding
admin.site.site_header = 'Zayyrah POS - Site Administration'
admin.site.site_title = 'Zayyrah POS Admin'
admin.site.index_title = 'Business Management Center'
