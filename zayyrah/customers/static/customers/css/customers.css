/* ==== CHANNAB-INSPIRED CUSTOMER MANAGEMENT SYSTEM ==== */
/* Ultra-responsive design matching the inspiration screenshots */

.customers-shell {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
    padding: 0;
    background: #f8fafb;
    min-height: 100vh;
}

/* Header Section */
.collection-header {
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.collection-title {
    margin-bottom: 24px;
}

.collection-title h1 {
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 700;
    color: #4F46E5;
    display: flex;
    align-items: center;
    gap: 12px;
}

.collection-title h1::before {
    content: '👥';
    font-size: 1.5rem;
}

.collection-title p {
    margin: 0;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.5;
}

/* Stats Cards Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin: 24px 0;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    padding: 24px;
    color: white;
    position: relative;
    overflow: hidden;
}

.stat-card.customers {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
}

.stat-card.sales {
    background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
}

.stat-card.balance {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.stat-title {
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.9;
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 800;
    margin-bottom: 8px;
    line-height: 1;
}

.stat-details {
    opacity: 0.9;
    font-size: 0.85rem;
    line-height: 1.4;
}

/* Search and Actions */
.collection-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
}

.search-section {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    max-width: 500px;
}

.action-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.search-input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid #E5E7EB;
    border-radius: 12px;
    background: white;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-input:focus {
    outline: none;
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.search-input::placeholder {
    color: #9CA3AF;
}

.secondary-button {
    padding: 12px 20px;
    border: 2px solid #E5E7EB;
    border-radius: 12px;
    background: white;
    color: #374151;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.secondary-button:hover {
    border-color: #4F46E5;
    color: #4F46E5;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.15);
}

.primary-button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #4F46E5 0%, #7C3AED 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-weight: 600;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
}

.primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
}

.primary-button::before {
    content: '+';
    font-size: 1.2rem;
    font-weight: 700;
}

/* Customer List Layout */
.customers-container {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
}

.customers-header {
    background: #F9FAFB;
    padding: 16px 24px;
    border-bottom: 1px solid #E5E7EB;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.customers-header h3 {
    margin: 0;
    color: #4F46E5;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.customers-header h3::before {
    content: '📋';
    font-size: 1rem;
}

.export-button {
    padding: 8px 16px;
    background: white;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    color: #6B7280;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.export-button:hover {
    border-color: #4F46E5;
    color: #4F46E5;
}

.export-button::before {
    content: '📄';
    font-size: 0.9rem;
}

/* Desktop Table View */
.customer-table {
    width: 100%;
    border-collapse: collapse;
}

.customer-table th {
    background: #F9FAFB;
    padding: 16px 24px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #E5E7EB;
}

.customer-table td {
    padding: 16px 24px;
    border-bottom: 1px solid #F3F4F6;
    vertical-align: middle;
}

.customer-table tbody tr:hover {
    background: #F9FAFB;
}

/* Mobile Card View */
.customer-grid {
    display: none;
    padding: 16px;
    gap: 16px;
}

.customer-card {
    background: white;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #E5E7EB;
    transition: all 0.3s ease;
    position: relative;
}

.customer-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Customer Avatar */
.customer-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: 1.2rem;
    color: white;
    margin-bottom: 16px;
}

.customer-avatar.avatar-1 { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
.customer-avatar.avatar-2 { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); }
.customer-avatar.avatar-3 { background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); }
.customer-avatar.avatar-4 { background: linear-gradient(135deg, #9C27B0 0%, #7B1FA2 100%); }
.customer-avatar.avatar-5 { background: linear-gradient(135deg, #F44336 0%, #D32F2F 100%); }
.customer-avatar.avatar-6 { background: linear-gradient(135deg, #00BCD4 0%, #0097A7 100%); }

.customer-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 12px;
}

.customer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
    margin-bottom: 16px;
}

.stat-item {
    text-align: center;
    padding: 8px;
    background: #F9FAFB;
    border-radius: 8px;
}

.stat-label {
    font-size: 0.75rem;
    color: #6B7280;
    font-weight: 500;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    font-size: 0.9rem;
    font-weight: 600;
}

.stat-value.milk { color: #2563EB; }
.stat-value.amount { color: #D97706; }
.stat-value.status { color: #059669; }

.customer-contact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #F9FAFB;
    border-radius: 8px;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.contact-label {
    font-size: 0.75rem;
    color: #6B7280;
    font-weight: 500;
}

.contact-value {
    font-size: 0.9rem;
    color: #111827;
    font-weight: 500;
}

.balance-info {
    text-align: right;
}

.balance-label {
    font-size: 0.75rem;
    color: #6B7280;
    margin-bottom: 4px;
}

.balance-value {
    font-size: 1rem;
    font-weight: 700;
    color: #059669;
}

/* Action Buttons */
.customer-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.action-button {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.action-button.view {
    color: #2563EB;
}

.action-button.view:hover {
    background: #EFF6FF;
    border-color: #2563EB;
}

.action-button.edit {
    color: #D97706;
}

.action-button.edit:hover {
    background: #FFFBEB;
    border-color: #D97706;
}

.action-button.delete {
    color: #DC2626;
}

.action-button.delete:hover {
    background: #FEF2F2;
    border-color: #DC2626;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.active {
    background: #D1FAE5;
    color: #065F46;
}

.status-badge.inactive {
    background: #FEE2E2;
    color: #991B1B;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
}

.empty-state::before {
    content: '👥';
    font-size: 4rem;
    display: block;
    margin-bottom: 16px;
}

.empty-state h2 {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0 0 24px 0;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.5;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 32px;
    padding: 24px;
}

.pagination .secondary-button {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.muted {
    color: #6B7280;
    font-size: 0.9rem;
}

/* Filter Section */
.filter-section {
    background: white;
    padding: 20px 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
    border: 1px solid #E5E7EB;
}

.current-filter {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
}

.filter-label {
    font-weight: 600;
    color: #374151;
}

.filter-tag {
    padding: 6px 12px;
    background: #4F46E5;
    color: white;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.filter-dates {
    color: #6B7280;
    font-size: 0.9rem;
}

.clear-filters {
    margin-left: auto;
    padding: 6px 16px;
    background: white;
    border: 1px solid #D1D5DB;
    border-radius: 8px;
    color: #6B7280;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.clear-filters:hover {
    border-color: #4F46E5;
    color: #4F46E5;
}

.stats-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    border-top: 1px solid #E5E7EB;
    font-size: 0.9rem;
    color: #6B7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .customers-shell {
        padding: 16px;
        gap: 16px;
    }

    .collection-header {
        padding: 20px;
        margin-bottom: 16px;
    }

    .collection-title h1 {
        font-size: 1.5rem;
    }

    .stats-section {
        grid-template-columns: 1fr;
        gap: 16px;
        margin: 16px 0;
    }

    .stat-card {
        padding: 20px;
    }

    .stat-value {
        font-size: 2rem;
    }

    .collection-actions {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .search-section {
        max-width: none;
    }

    .action-buttons {
        justify-content: stretch;
    }

    .primary-button,
    .secondary-button {
        flex: 1;
        justify-content: center;
    }

    /* Hide table, show cards on mobile */
    .customer-table {
        display: none;
    }

    .customer-grid {
        display: flex;
        flex-direction: column;
    }

    .customers-container {
        border-radius: 12px;
    }

    .customers-header {
        padding: 16px 20px;
    }

    .customer-card {
        padding: 16px;
    }

    .customer-stats {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .customer-contact {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .balance-info {
        text-align: center;
    }
}

@media (min-width: 769px) {
    /* Show table, hide cards on desktop */
    .customer-table {
        display: table;
    }

    .customer-grid {
        display: none;
    }

    .customers-shell {
        padding: 32px;
    }

    .stats-section {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Utility Classes */
.text-green { color: #059669; }
.text-blue { color: #2563EB; }
.text-orange { color: #D97706; }
.text-purple { color: #7C3AED; }
.text-red { color: #DC2626; }
.text-gray { color: #6B7280; }

.bg-green { background-color: #D1FAE5; }
.bg-blue { background-color: #DBEAFE; }
.bg-orange { background-color: #FED7AA; }
.bg-purple { background-color: #E9D5FF; }
.bg-red { background-color: #FECACA; }
.bg-gray { background-color: #F3F4F6; }

/* Animation for smooth transitions */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Advanced JavaScript Features */
/* Search Empty State */
.search-empty-state {
    text-align: center;
    padding: 60px 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    border: 1px solid #E5E7EB;
    animation: fadeIn 0.3s ease-in-out;
}

.search-empty-state .empty-icon {
    font-size: 4rem;
    margin-bottom: 16px;
    opacity: 0.5;
}

.search-empty-state h3 {
    margin: 0 0 12px 0;
    color: #374151;
    font-size: 1.5rem;
}

.search-empty-state p {
    margin: 0 0 24px 0;
    color: #6B7280;
    font-size: 1rem;
    line-height: 1.5;
}

.clear-search-btn {
    padding: 12px 24px;
    background: #4F46E5;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
}

.clear-search-btn:hover {
    background: #4338CA;
    transform: translateY(-1px);
}

/* Pull to Refresh Indicator */
.pull-indicator {
    position: fixed;
    top: -60px;
    left: 50%;
    transform: translateX(-50%);
    background: #4F46E5;
    color: white;
    padding: 12px 24px;
    border-radius: 0 0 12px 12px;
    font-size: 0.9rem;
    font-weight: 500;
    z-index: 1000;
    transition: top 0.3s ease;
}

.pull-indicator.visible {
    top: 0;
}

/* Advanced Options Modal */
.advanced-options-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    animation: fadeIn 0.2s ease-out;
}

.modal-content {
    background: white;
    border-radius: 16px;
    padding: 24px;
    max-width: 320px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideUp 0.3s ease-out;
}

.modal-content h3 {
    margin: 0 0 20px 0;
    font-size: 1.2rem;
    color: #374151;
    text-align: center;
}

.option-btn {
    width: 100%;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: #F9FAFB;
    border: 1px solid #E5E7EB;
    border-radius: 8px;
    color: #374151;
    font-size: 0.95rem;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    gap: 12px;
}

.option-btn:hover {
    background: #F3F4F6;
    border-color: #D1D5DB;
}

.option-btn:last-of-type {
    color: #DC2626;
}

.close-modal {
    width: 100%;
    padding: 12px 16px;
    margin-top: 16px;
    background: #6B7280;
    border: none;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
}

.close-modal:hover {
    background: #4B5563;
}

@keyframes modalSlideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Scrolled Header State */
.channab-header.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #E5E7EB;
}

/* Dark Theme Support */
.dark-theme .channab-container {
    background: #111827;
}

.dark-theme .channab-header {
    background: #1F2937;
    border-bottom-color: #374151;
}

.dark-theme .stat-card {
    background: #1F2937;
    border-color: #374151;
}

.dark-theme .customers-table {
    background: #1F2937;
}

.dark-theme .customer-row {
    border-color: #374151;
}

.dark-theme .customer-row:hover {
    background: #374151;
}

.dark-theme .customer-card-mobile {
    background: #1F2937;
    border-color: #374151;
}

.dark-theme .search-input {
    background: #374151;
    border-color: #4B5563;
    color: white;
}

.dark-theme .search-input::placeholder {
    color: #9CA3AF;
}

/* Enhanced Touch Feedback */
.action-btn, .mobile-action-btn, .stat-card {
    transition: transform 0.1s ease, opacity 0.1s ease;
}

/* Performance Optimizations */
.customer-avatar, .customer-avatar-mobile {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.customer-avatar.loaded, .customer-avatar-mobile.loaded {
    opacity: 1;
}

/* Ultra-responsive improvements */
@media (max-width: 320px) {
    .channab-header h1 {
        font-size: 1.2rem;
    }

    .stat-card {
        padding: 16px;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .customer-card-mobile {
        padding: 12px;
    }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .customer-avatar, .customer-avatar-mobile {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}