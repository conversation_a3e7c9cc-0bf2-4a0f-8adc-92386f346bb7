from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.test import TestCase
from django.urls import reverse

from .models import Customer


class CustomerModelTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.owner = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.CUSTOMER,
        )

    def test_customer_requires_name_or_mobile(self):
        customer = Customer(owner=self.owner)
        with self.assertRaisesMessage(ValidationError, 'Provide at least a mobile number or name.'):
            customer.full_clean()

    def test_customer_creates_with_name_only(self):
        customer = Customer.objects.create(owner=self.owner, name='Alice Kingston')
        self.assertEqual(customer.name, 'Alice Kingston')

    def test_customer_creates_with_mobile_only(self):
        customer = Customer.objects.create(owner=self.owner, mobile_number='***********')
        self.assertEqual(customer.mobile_number, '***********')


class CustomerViewTests(TestCase):
    def setUp(self):
        self.User = get_user_model()
        self.owner = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.CUSTOMER,
        )
        self.client.login(mobile_number=self.owner.mobile_number, password='safepass123')

    def test_create_customer_flow(self):
        response = self.client.post(
            reverse('customers:create'),
            data={'name': 'Beta Tester', 'mobile_number': ''},
            follow=True,
        )
        self.assertEqual(response.status_code, 200)
        self.assertTrue(Customer.objects.filter(owner=self.owner, name='Beta Tester').exists())

    def test_list_shows_only_owner_customers(self):
        other_user = self.User.objects.create_user(
            mobile_number='**********',
            password='safepass123',
            account_type=self.User.AccountType.CUSTOMER,
        )
        Customer.objects.create(owner=self.owner, name='Visible')
        Customer.objects.create(owner=other_user, name='Hidden')
        response = self.client.get(reverse('customers:list'))
        self.assertContains(response, 'Visible')
        self.assertNotContains(response, 'Hidden')
