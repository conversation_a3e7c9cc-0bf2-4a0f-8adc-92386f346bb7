from django.contrib import admin

from .models import Customer


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ('display_name', 'mobile_number', 'email', 'owner', 'updated_at')
    list_filter = ('owner',)
    search_fields = ('name', 'mobile_number', 'email')
    readonly_fields = ('created_at', 'updated_at')

    def display_name(self, obj):
        return obj.name or '—'

    display_name.short_description = 'Name'
