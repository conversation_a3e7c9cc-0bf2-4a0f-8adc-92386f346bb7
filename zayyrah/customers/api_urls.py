from django.urls import path
from .api_views import (
    CustomerListCreateView, CustomerDetailView, CustomerSalesView,
    CustomerPaymentListCreateView, CustomerPaymentDetailView
)
from pos.api_views import CustomerManualSalesView

app_name = 'customers_api'

urlpatterns = [
    # Customer endpoints
    path('', CustomerListCreateView.as_view(), name='customer_list_create'),
    path('<int:pk>/', CustomerDetailView.as_view(), name='customer_detail'),

    # Customer Sales History endpoints
    path('<int:pk>/sales/', CustomerSalesView.as_view(), name='customer_sales'),

    # Customer Manual Sales endpoints
    path('<int:customer_id>/manual-sales/', CustomerManualSalesView.as_view(), name='customer_manual_sales'),

    # Customer Payment endpoints
    path('<int:customer_id>/payments/', CustomerPaymentListCreateView.as_view(), name='customer_payments'),
    path('<int:customer_id>/payments/<int:pk>/', CustomerPaymentDetailView.as_view(), name='customer_payment_detail'),
]