# Generated by Django 5.2.6 on 2025-09-23 13:12

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0002_customer_clearance_custom_date_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, help_text='Payment amount in PKR', max_digits=10)),
                ('payment_type', models.CharField(choices=[('cash', 'Cash'), ('card', 'Card'), ('bank_transfer', 'Bank Transfer'), ('mobile_money', 'Mobile Money'), ('cheque', 'Cheque'), ('other', 'Other')], default='cash', max_length=20)),
                ('payment_date', models.DateTimeField(default=django.utils.timezone.now, help_text='Date and time when payment was received')),
                ('note', models.TextField(blank=True, help_text='Optional note about the payment')),
                ('reference_number', models.CharField(blank=True, help_text='Reference number for bank transfers, cheques, etc.', max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer_payments_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='customers.customer')),
            ],
            options={
                'ordering': ['-payment_date'],
            },
        ),
    ]
