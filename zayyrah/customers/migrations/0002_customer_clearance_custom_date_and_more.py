# Generated by Django 5.2.6 on 2025-09-18 13:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='clearance_custom_date',
            field=models.DateField(blank=True, help_text='Custom clearance date', null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='clearance_day_of_month',
            field=models.IntegerField(blank=True, help_text='Day of month (1-31) for fixed date clearance', null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='clearance_day_of_week',
            field=models.IntegerField(blank=True, choices=[(1, 'Monday'), (2, 'Tuesday'), (3, 'Wednesday'), (4, 'Thursday'), (5, 'Friday'), (6, 'Saturday'), (7, 'Sunday')], help_text='Day of week for weekly clearance', null=True),
        ),
        migrations.AddField(
            model_name='customer',
            name='clearance_type',
            field=models.CharField(choices=[('fixed_date', 'Fixed Date of Month'), ('weekly', 'Day of Week'), ('custom', 'Custom Date')], default='fixed_date', help_text='Type of clearance schedule', max_length=20),
        ),
        migrations.AddField(
            model_name='customer',
            name='credit_limit',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Maximum credit allowed for this customer', max_digits=10),
        ),
        migrations.AddField(
            model_name='customer',
            name='opening_balance',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Initial balance for this customer', max_digits=10),
        ),
    ]
