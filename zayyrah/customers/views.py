from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.models import Q
from django.urls import reverse, reverse_lazy
from django.views.generic import DetailView, ListView
from django.views.generic.edit import CreateView, UpdateView

from .forms import CustomerForm
from .models import Customer


class OwnerQuerysetMixin(LoginRequiredMixin):
    def get_queryset(self):
        base_qs = Customer.objects.filter(owner=self.request.user)
        return base_qs


class CustomerListView(OwnerQuerysetMixin, ListView):
    template_name = 'customers/customer_list.html'
    context_object_name = 'customers'
    paginate_by = 12

    def get_queryset(self):
        qs = super().get_queryset()
        search_term = self.request.GET.get('q', '').strip()
        if search_term:
            qs = qs.filter(Q(name__icontains=search_term) | Q(mobile_number__icontains=search_term))
        return qs

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_term'] = self.request.GET.get('q', '').strip()

        # Get the customers that are being displayed (after pagination/filtering)
        customers = context['customers']

        # Add sales statistics to each customer in the current page
        for customer in customers:
            sales = customer.sales.all()
            customer.total_sales_count = sales.count()
            customer.total_sales_amount = sum(sale.total for sale in sales)
            customer.pending_amount = sum(sale.remaining_balance for sale in sales)

        # Calculate overall statistics for all customers (not just current page)
        all_customers = Customer.objects.filter(owner=self.request.user)
        total_customers = all_customers.count()

        # Get all sales for all customers for overall stats
        all_sales = []
        customers_with_sales = []

        for customer in all_customers:
            sales = customer.sales.all()
            all_sales.extend(sales)
            if sales.exists():
                customers_with_sales.append(customer)

        # Overall statistics
        total_sales_amount = sum(sale.total for sale in all_sales)
        total_pending_amount = sum(sale.remaining_balance for sale in all_sales)
        total_sales_count = len(all_sales)

        context.update({
            'total_customers': total_customers,
            'total_sales_amount': total_sales_amount,
            'total_pending_amount': total_pending_amount,
            'total_sales_count': total_sales_count,
            'customers_with_sales_count': len(customers_with_sales),
        })

        return context


class CustomerDetailView(OwnerQuerysetMixin, DetailView):
    template_name = 'customers/customer_detail.html'
    context_object_name = 'customer'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        customer = self.object

        # Get all old sales for this customer (both POS and Manual)
        old_sales = customer.sales.all().order_by('-created_at')

        # Get new POS transactions for this customer
        new_pos_transactions = customer.pos_transactions.all().order_by('-created_at')

        # Separate manual and old POS sales
        manual_sales = old_sales.filter(sale_type='manual')
        old_pos_sales = old_sales.filter(sale_type='pos')

        # Calculate statistics for old sales
        old_sales_count = old_sales.count()
        old_sales_amount = sum(sale.total for sale in old_sales)
        old_pending_amount = sum(sale.remaining_balance for sale in old_sales)

        # Calculate statistics for new POS transactions
        new_pos_count = new_pos_transactions.count()
        new_pos_amount = sum(transaction.total_amount for transaction in new_pos_transactions)

        # Combined statistics
        total_sales_count = old_sales_count + new_pos_count
        total_sales_amount = old_sales_amount + new_pos_amount
        pending_amount = old_pending_amount  # New POS transactions handle payment differently

        context.update({
            # Old sales (legacy)
            'sales': old_sales[:10],  # Latest 10 old sales
            'manual_sales': manual_sales[:5],  # Latest 5 manual sales
            'old_pos_sales': old_pos_sales[:5],  # Latest 5 old POS sales

            # New POS transactions
            'pos_transactions': new_pos_transactions[:10],  # Latest 10 new POS transactions
            'new_pos_count': new_pos_count,
            'new_pos_amount': new_pos_amount,

            # Combined statistics
            'total_sales_count': total_sales_count,
            'total_sales_amount': total_sales_amount,
            'pending_amount': pending_amount,
            'has_sales': total_sales_count > 0,
            'has_new_pos': new_pos_count > 0,
        })

        return context


class CustomerCreateView(OwnerQuerysetMixin, CreateView):
    template_name = 'customers/customer_form.html'
    form_class = CustomerForm
    success_url = reverse_lazy('customers:list')

    def form_valid(self, form):
        form.instance.owner = self.request.user
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Add customer'
        context['submit_label'] = 'Create customer'
        return context


class CustomerUpdateView(OwnerQuerysetMixin, UpdateView):
    template_name = 'customers/customer_form.html'
    form_class = CustomerForm
    context_object_name = 'customer'

    def get_success_url(self):
        return reverse('customers:detail', args=[self.object.pk])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'Edit customer'
        context['submit_label'] = 'Save changes'
        return context
