from django import forms

from .models import Customer


class CustomerForm(forms.ModelForm):
    class Meta:
        model = Customer
        fields = [
            'name',
            'mobile_number',
            'email',
            'opening_balance',
            'credit_limit',
            'clearance_type',
            'clearance_day_of_month',
            'clearance_day_of_week',
            'clearance_custom_date',
            'notes'
        ]
        widgets = {
            'name': forms.TextInput(
                attrs={
                    'placeholder': 'Customer name',
                    'autocomplete': 'name',
                }
            ),
            'mobile_number': forms.TextInput(
                attrs={
                    'placeholder': '03XXXXXXXXX',
                    'inputmode': 'numeric',
                    'autocomplete': 'tel',
                }
            ),
            'email': forms.EmailInput(
                attrs={'placeholder': '<EMAIL>', 'autocomplete': 'email'}
            ),
            'opening_balance': forms.NumberInput(
                attrs={
                    'placeholder': '0.00',
                    'step': '0.01',
                    'min': '0',
                    'class': 'currency-input'
                }
            ),
            'credit_limit': forms.NumberInput(
                attrs={
                    'placeholder': '0.00',
                    'step': '0.01',
                    'min': '0',
                    'class': 'currency-input'
                }
            ),
            'clearance_type': forms.Select(
                attrs={'class': 'clearance-type-select'}
            ),
            'clearance_day_of_month': forms.NumberInput(
                attrs={
                    'placeholder': 'Day (1-31)',
                    'min': '1',
                    'max': '31',
                    'class': 'clearance-day-input'
                }
            ),
            'clearance_day_of_week': forms.Select(
                attrs={'class': 'clearance-day-select'}
            ),
            'clearance_custom_date': forms.DateInput(
                attrs={
                    'type': 'date',
                    'class': 'clearance-date-input'
                }
            ),
            'notes': forms.Textarea(
                attrs={'rows': 4, 'placeholder': 'Additional context about this customer'}
            ),
        }

    def clean(self):
        cleaned_data = super().clean()
        name = cleaned_data.get('name')
        mobile = cleaned_data.get('mobile_number')
        if not name and not mobile:
            raise forms.ValidationError('Add at least a name or mobile number to create a customer.')
        if mobile == '':
            cleaned_data['mobile_number'] = None
        return cleaned_data
