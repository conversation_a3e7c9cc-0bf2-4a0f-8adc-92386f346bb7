from rest_framework import serializers
from .models import Customer
from django.db.models import Sum


class CustomerSerializer(serializers.ModelSerializer):
    total_sales = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    clearance_day_of_week_display = serializers.SerializerMethodField()
    clearance_type_display = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = (
            'id', 'name', 'mobile_number', 'email', 'notes',
            'opening_balance', 'credit_limit',
            'clearance_type', 'clearance_type_display',
            'clearance_day_of_month', 'clearance_day_of_week', 'clearance_day_of_week_display',
            'clearance_custom_date',
            'created_at', 'updated_at', 'total_sales', 'total_amount'
        )
        read_only_fields = ('id', 'created_at', 'updated_at', 'total_sales', 'total_amount',
                           'clearance_type_display', 'clearance_day_of_week_display')

    def get_total_sales(self, obj):
        return obj.sales.count()

    def get_total_amount(self, obj):
        total = obj.sales.aggregate(total=Sum('total'))['total']
        return str(total) if total else "0.00"

    def get_clearance_type_display(self, obj):
        return obj.get_clearance_type_display()

    def get_clearance_day_of_week_display(self, obj):
        return obj.get_clearance_day_of_week_display() if obj.clearance_day_of_week else None

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class CustomerListSerializer(serializers.ModelSerializer):
    """Lightweight serializer for customer lists"""
    total_sales_count = serializers.SerializerMethodField()
    total_sales_amount = serializers.SerializerMethodField()
    pending_amount = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = ('id', 'name', 'mobile_number', 'email', 'created_at', 'updated_at',
                 'total_sales_count', 'total_sales_amount', 'pending_amount')

    def get_total_sales_count(self, obj):
        """Total number of sales for this customer"""
        return obj.sales.count()

    def get_total_sales_amount(self, obj):
        """Total amount of all sales for this customer"""
        total = obj.sales.aggregate(total=Sum('total'))['total']
        return str(total) if total else "0.00"

    def get_pending_amount(self, obj):
        """Total pending/unpaid amount across all sales"""
        # Calculate sum of remaining balances for all sales
        sales = obj.sales.all()
        pending = sum(sale.remaining_balance for sale in sales)
        return str(pending)


class CustomerDetailSerializer(serializers.ModelSerializer):
    total_sales = serializers.SerializerMethodField()
    total_amount = serializers.SerializerMethodField()
    recent_sales = serializers.SerializerMethodField()
    clearance_day_of_week_display = serializers.SerializerMethodField()
    clearance_type_display = serializers.SerializerMethodField()

    class Meta:
        model = Customer
        fields = (
            'id', 'name', 'mobile_number', 'email', 'notes',
            'opening_balance', 'credit_limit',
            'clearance_type', 'clearance_type_display',
            'clearance_day_of_month', 'clearance_day_of_week', 'clearance_day_of_week_display',
            'clearance_custom_date',
            'created_at', 'updated_at', 'total_sales', 'total_amount', 'recent_sales'
        )
        read_only_fields = ('id', 'created_at', 'updated_at', 'total_sales', 'total_amount',
                           'clearance_type_display', 'clearance_day_of_week_display')

    def get_total_sales(self, obj):
        return obj.sales.count()

    def get_total_amount(self, obj):
        total = obj.sales.aggregate(total=Sum('total'))['total']
        return str(total) if total else "0.00"

    def get_clearance_type_display(self, obj):
        return obj.get_clearance_type_display()

    def get_clearance_day_of_week_display(self, obj):
        return obj.get_clearance_day_of_week_display() if obj.clearance_day_of_week else None

    def get_recent_sales(self, obj):
        from pos.serializers import SaleListSerializer
        recent_sales = obj.sales.all()[:5]
        return SaleListSerializer(recent_sales, many=True).data


class CustomerCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = (
            'name', 'mobile_number', 'email', 'notes',
            'opening_balance', 'credit_limit',
            'clearance_type', 'clearance_day_of_month', 'clearance_day_of_week', 'clearance_custom_date'
        )

    def validate(self, attrs):
        if not attrs.get('name') and not attrs.get('mobile_number'):
            raise serializers.ValidationError('Provide at least a mobile number or name.')
        return attrs

    def create(self, validated_data):
        validated_data['owner'] = self.context['request'].user
        return super().create(validated_data)


class CustomerUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Customer
        fields = (
            'name', 'mobile_number', 'email', 'notes',
            'opening_balance', 'credit_limit',
            'clearance_type', 'clearance_day_of_month', 'clearance_day_of_week', 'clearance_custom_date'
        )

    def validate(self, attrs):
        if not attrs.get('name') and not attrs.get('mobile_number'):
            raise serializers.ValidationError('Provide at least a mobile number or name.')
        return attrs