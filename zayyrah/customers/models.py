from decimal import Decimal
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import RegexValidator
from django.db import models
from django.utils import timezone


class Customer(models.Model):
    owner = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='customers',
    )
    name = models.CharField(max_length=255, blank=True)
    mobile_number = models.CharField(
        max_length=15,
        blank=True,
        null=True,
        validators=[
            RegexValidator(r'^$|^\d{6,15}$', 'Enter 6 to 15 digits.'),
        ],
    )
    email = models.EmailField(blank=True)
    notes = models.TextField(blank=True)

    # Financial fields
    opening_balance = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Initial balance for this customer"
    )
    credit_limit = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0.00,
        help_text="Maximum credit allowed for this customer"
    )

    # Clearance date options
    CLEARANCE_TYPES = [
        ('fixed_date', 'Fixed Date of Month'),
        ('weekly', 'Day of Week'),
        ('custom', 'Custom Date'),
    ]

    DAYS_OF_WEEK = [
        (1, 'Monday'),
        (2, 'Tuesday'),
        (3, 'Wednesday'),
        (4, 'Thursday'),
        (5, 'Friday'),
        (6, 'Saturday'),
        (7, 'Sunday'),
    ]

    clearance_type = models.CharField(
        max_length=20,
        choices=CLEARANCE_TYPES,
        default='fixed_date',
        help_text="Type of clearance schedule"
    )
    clearance_day_of_month = models.IntegerField(
        blank=True,
        null=True,
        help_text="Day of month (1-31) for fixed date clearance"
    )
    clearance_day_of_week = models.IntegerField(
        choices=DAYS_OF_WEEK,
        blank=True,
        null=True,
        help_text="Day of week for weekly clearance"
    )
    clearance_custom_date = models.DateField(
        blank=True,
        null=True,
        help_text="Custom clearance date"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
        unique_together = ('owner', 'mobile_number')

    def clean(self):
        super().clean()
        if self.mobile_number == '':
            self.mobile_number = None
        if not self.name and not self.mobile_number:
            raise ValidationError('Provide at least a mobile number or name.')

    def __str__(self):
        if self.name:
            return self.name
        if self.mobile_number:
            return self.mobile_number
        return f'Customer #{self.pk}'

    @property
    def current_balance(self):
        """Calculate current credit balance from POS transactions"""
        from django.db.models import Sum
        # This will be calculated from POSTransaction credit_amount
        # For now, return opening_balance (will be enhanced when transactions are implemented)
        return self.opening_balance

    @property
    def available_credit(self):
        """Calculate available credit limit"""
        return max(Decimal('0.00'), self.credit_limit - self.current_balance)

    def can_purchase(self, amount):
        """Check if customer can make a purchase of given amount"""
        if self.credit_limit <= 0:
            return True  # No credit limit set
        return self.current_balance + amount <= self.credit_limit

    @property
    def display_name(self):
        """Get best display name for POS"""
        if self.name:
            return self.name
        if self.mobile_number:
            return f"Customer ({self.mobile_number})"
        return f"Customer #{self.pk}"


class CustomerPayment(models.Model):
    """Model to track customer payments"""
    PAYMENT_TYPES = [
        ('cash', 'Cash'),
        ('card', 'Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('mobile_money', 'Mobile Money'),
        ('cheque', 'Cheque'),
        ('other', 'Other'),
    ]

    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='payments'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Payment amount in PKR"
    )
    payment_type = models.CharField(
        max_length=20,
        choices=PAYMENT_TYPES,
        default='cash'
    )
    payment_date = models.DateTimeField(
        default=timezone.now,
        help_text="Date and time when payment was received"
    )
    note = models.TextField(
        blank=True,
        help_text="Optional note about the payment"
    )
    reference_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Reference number for bank transfers, cheques, etc."
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='customer_payments_created'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-payment_date']

    def __str__(self):
        return f"Payment of Rs. {self.amount} by {self.customer.display_name} on {self.payment_date.strftime('%Y-%m-%d')}"

    def clean(self):
        super().clean()
        if self.amount <= 0:
            raise ValidationError('Payment amount must be greater than zero.')

    @property
    def formatted_amount(self):
        """Return formatted amount for display"""
        return f"Rs. {self.amount:,.2f}"
