from rest_framework import status, generics, filters
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from .models import Customer, CustomerPayment
from .serializers import (
    CustomerListSerializer, CustomerDetailSerializer,
    CustomerCreateSerializer, CustomerUpdateSerializer
)


class IsOwnerPermission:
    """Custom permission to only allow owners of an object to access it."""
    def has_permission(self, request, view):
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return obj.owner == request.user


class CustomerListCreateView(generics.ListCreateAPIView):
    """List customers with search and create new customer"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'mobile_number']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['-updated_at']

    def get_queryset(self):
        return Customer.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.request.method == 'POST':
            return CustomerCreateSerializer
        return CustomerListSerializer

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            customer = serializer.save()
            return Response({
                'success': True,
                'message': 'Customer created successfully',
                'data': CustomerDetailSerializer(customer).data
            }, status=status.HTTP_201_CREATED)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def list(self, request, *args, **kwargs):
        """Enhanced customer list with time-filtered sales analytics"""
        try:
            from datetime import datetime, timedelta
            from django.utils import timezone
            from django.db.models import Q, Count, Sum
            from inventory.models import POSTransaction

            print(f"\n=== ENHANCED CUSTOMER LIST API ===")
            print(f"Method: GET")
            print(f"User: {request.user}")
            print(f"Query Params: {dict(request.GET)}")

            # === TIME FILTERING (Default: Today) ===
            time_filter = request.query_params.get('time_filter', 'today')
            today = timezone.now().date()

            # Calculate date range based on filter
            start_date = None
            end_date = None

            if time_filter == 'today':
                start_date = today
                end_date = today
            elif time_filter == 'yesterday':
                yesterday = today - timedelta(days=1)
                start_date = yesterday
                end_date = yesterday
            elif time_filter == 'last_7_days':
                start_date = today - timedelta(days=7)
                end_date = today
            elif time_filter == 'last_30_days':
                start_date = today - timedelta(days=30)
                end_date = today
            elif time_filter == 'this_week':
                # Monday of current week
                start_date = today - timedelta(days=today.weekday())
                end_date = today
            elif time_filter == 'this_month':
                start_date = today.replace(day=1)
                end_date = today
            elif time_filter == 'last_month':
                last_month_end = today.replace(day=1) - timedelta(days=1)
                start_date = last_month_end.replace(day=1)
                end_date = last_month_end
            elif time_filter == 'this_year':
                start_date = today.replace(month=1, day=1)
                end_date = today
            elif time_filter == 'custom':
                # Custom date range
                start_date_param = request.query_params.get('start_date')
                end_date_param = request.query_params.get('end_date')
                if start_date_param:
                    start_date = datetime.strptime(start_date_param, '%Y-%m-%d').date()
                if end_date_param:
                    end_date = datetime.strptime(end_date_param, '%Y-%m-%d').date()
                if not start_date:
                    start_date = today
                if not end_date:
                    end_date = today

            print(f"Time Filter Applied: {time_filter}")
            print(f"Date Range: {start_date} to {end_date}")

            # === GET BASE CUSTOMER QUERYSET ===
            queryset = self.filter_queryset(self.get_queryset())

            # === PREPARE ENHANCED CUSTOMER LIST WITH SALES ANALYTICS ===
            customers_list = []

            for customer in queryset:
                # === COLLECT ALL SALES DATA FOR TIME PERIOD ===

                # POS Transactions (from inventory app)
                pos_transactions = POSTransaction.objects.filter(
                    owner=request.user,
                    customer=customer,
                    status='completed',
                    created_at__date__gte=start_date,
                    created_at__date__lte=end_date
                )

                # Manual Sales (from pos app - if exists)
                manual_sales = None
                try:
                    from pos.models import Sale
                    manual_sales = Sale.objects.filter(
                        owner=request.user,
                        customer=customer,
                        created_at__date__gte=start_date,
                        created_at__date__lte=end_date
                    )
                except ImportError:
                    # pos.models.Sale doesn't exist, only use POS transactions
                    manual_sales = None

                # === CALCULATE ANALYTICS ===

                # POS Transactions Analytics
                pos_analytics = pos_transactions.aggregate(
                    total_amount=Sum('total_amount'),
                    transaction_count=Count('id')
                )

                pos_total_amount = float(pos_analytics['total_amount'] or 0)
                pos_transaction_count = pos_analytics['transaction_count'] or 0

                # Manual Sales Analytics
                manual_total_amount = 0
                manual_transaction_count = 0

                if manual_sales is not None:
                    manual_analytics = manual_sales.aggregate(
                        total_amount=Sum('total'),
                        transaction_count=Count('id')
                    )
                    manual_total_amount = float(manual_analytics['total_amount'] or 0)
                    manual_transaction_count = manual_analytics['transaction_count'] or 0

                # === COMBINED ANALYTICS ===
                combined_total_amount = pos_total_amount + manual_total_amount
                combined_transaction_count = pos_transaction_count + manual_transaction_count

                # === BUILD CUSTOMER DATA ===
                customer_data = {
                    'id': customer.id,
                    'name': customer.display_name,  # Use display_name which handles fallbacks
                    'mobile_number': customer.mobile_number,
                    'email': customer.email,
                    'created_at': customer.created_at.isoformat(),
                    'updated_at': customer.updated_at.isoformat(),

                    # Time-filtered sales analytics
                    'period_sales': {
                        'total_amount': combined_total_amount,
                        'total_transactions': combined_transaction_count,
                        'pos_transactions': {
                            'count': pos_transaction_count,
                            'amount': pos_total_amount
                        },
                        'manual_sales': {
                            'count': manual_transaction_count,
                            'amount': manual_total_amount
                        }
                    },

                    # Lifetime analytics (for comparison)
                    'lifetime_sales': {
                        'total_amount': float(customer.current_balance),  # Current running balance
                        'total_transactions': customer.sales.count() if hasattr(customer, 'sales') else 0
                    },

                    # Customer financial info
                    'credit_info': {
                        'credit_limit': float(customer.credit_limit),
                        'current_balance': float(customer.current_balance),
                        'available_credit': float(customer.available_credit)
                    }
                }

                customers_list.append(customer_data)

            # === SORTING OPTIONS ===
            sort_by = request.query_params.get('sort_by', 'period_sales_desc')

            if sort_by == 'period_sales_desc':
                customers_list.sort(key=lambda x: x['period_sales']['total_amount'], reverse=True)
            elif sort_by == 'period_sales_asc':
                customers_list.sort(key=lambda x: x['period_sales']['total_amount'])
            elif sort_by == 'transactions_desc':
                customers_list.sort(key=lambda x: x['period_sales']['total_transactions'], reverse=True)
            elif sort_by == 'name_asc':
                customers_list.sort(key=lambda x: x['name'].lower())
            elif sort_by == 'recent':
                customers_list.sort(key=lambda x: x['updated_at'], reverse=True)

            # === PAGINATION ===
            page_size = int(request.query_params.get('page_size', 20))
            page_number = int(request.query_params.get('page', 1))

            from django.core.paginator import Paginator
            paginator = Paginator(customers_list, page_size)
            page_obj = paginator.get_page(page_number)

            # === SUMMARY ANALYTICS ===
            total_customers = len(customers_list)
            customers_with_sales = len([c for c in customers_list if c['period_sales']['total_amount'] > 0])
            total_period_sales = sum(c['period_sales']['total_amount'] for c in customers_list)
            total_period_transactions = sum(c['period_sales']['total_transactions'] for c in customers_list)

            avg_sale_per_customer = total_period_sales / total_customers if total_customers > 0 else 0
            avg_transactions_per_customer = total_period_transactions / total_customers if total_customers > 0 else 0

            response_data = {
                'success': True,
                'data': {
                    'customers': list(page_obj),
                    'pagination': {
                        'current_page': page_obj.number,
                        'total_pages': paginator.num_pages,
                        'total_customers': paginator.count,
                        'page_size': page_size,
                        'has_next': page_obj.has_next(),
                        'has_previous': page_obj.has_previous()
                    },
                    'period_summary': {
                        'time_filter': time_filter,
                        'date_range': {
                            'start_date': start_date.isoformat(),
                            'end_date': end_date.isoformat()
                        },
                        'total_customers': total_customers,
                        'customers_with_sales': customers_with_sales,
                        'customers_without_sales': total_customers - customers_with_sales,
                        'total_sales_amount': total_period_sales,
                        'total_transactions': total_period_transactions,
                        'avg_sale_per_customer': round(avg_sale_per_customer, 2),
                        'avg_transactions_per_customer': round(avg_transactions_per_customer, 2)
                    },
                    'filters_applied': {
                        'time_filter': time_filter,
                        'sort_by': sort_by,
                        'start_date': start_date.isoformat() if start_date else None,
                        'end_date': end_date.isoformat() if end_date else None
                    }
                }
            }

            print(f"Returning {len(list(page_obj))} customers")
            print(f"Period Total Sales: ${total_period_sales}")
            print(f"Period Total Transactions: {total_period_transactions}")

            return Response(response_data)

        except Exception as e:
            print(f"Error in enhanced customer list: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete a specific customer"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]

    def get_queryset(self):
        return Customer.objects.filter(owner=self.request.user)

    def get_serializer_class(self):
        if self.request.method in ['PUT', 'PATCH']:
            return CustomerUpdateSerializer
        return CustomerDetailSerializer

    def retrieve(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance)
        return Response({
            'success': True,
            'data': serializer.data
        })

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Customer updated successfully',
                'data': CustomerDetailSerializer(instance).data
            })

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response({
            'success': True,
            'message': 'Customer deleted successfully'
        }, status=status.HTTP_200_OK)


class CustomerSalesView(generics.RetrieveAPIView):
    """
    Customer Sales History API with Advanced Time Filtering

    Ultra-thinking filtering capabilities:
    - Time period filtering (default: last 30 days)
    - Transaction status filtering
    - Payment method filtering
    - Amount range filtering
    - Search functionality
    - Sales analytics and summaries
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Customer.objects.filter(owner=self.request.user)

    def retrieve(self, request, *args, **kwargs):
        """Get customer sales history with advanced filtering"""
        print(f"\n=== CUSTOMER SALES API ===")
        print(f"Method: GET")
        print(f"User: {request.user}")
        print(f"Customer ID: {kwargs.get('pk')}")
        print(f"Query Params: {dict(request.GET)}")

        try:
            from datetime import datetime, timedelta
            from django.utils import timezone
            from django.db.models import Q, Count, Sum, Avg
            from django.core.paginator import Paginator
            from inventory.models import POSTransaction, POSTransactionItem

            # Get customer
            customer = self.get_object()

            # Base queryset - customer's transactions
            transactions = POSTransaction.objects.filter(
                owner=request.user,
                customer=customer
            ).select_related('customer').prefetch_related('items__product')

            # === TIME FILTERING (Default: Last 30 days) ===
            time_filter = request.GET.get('time_filter', 'last_30_days')
            today = timezone.now().date()

            if time_filter == 'today':
                transactions = transactions.filter(created_at__date=today)
            elif time_filter == 'yesterday':
                yesterday = today - timedelta(days=1)
                transactions = transactions.filter(created_at__date=yesterday)
            elif time_filter == 'last_7_days':
                week_ago = today - timedelta(days=7)
                transactions = transactions.filter(created_at__date__gte=week_ago)
            elif time_filter == 'last_30_days':  # Default
                month_ago = today - timedelta(days=30)
                transactions = transactions.filter(created_at__date__gte=month_ago)
            elif time_filter == 'last_90_days':
                quarter_ago = today - timedelta(days=90)
                transactions = transactions.filter(created_at__date__gte=quarter_ago)
            elif time_filter == 'this_month':
                month_start = today.replace(day=1)
                transactions = transactions.filter(created_at__date__gte=month_start)
            elif time_filter == 'last_month':
                last_month_end = today.replace(day=1) - timedelta(days=1)
                last_month_start = last_month_end.replace(day=1)
                transactions = transactions.filter(
                    created_at__date__gte=last_month_start,
                    created_at__date__lte=last_month_end
                )
            elif time_filter == 'this_year':
                year_start = today.replace(month=1, day=1)
                transactions = transactions.filter(created_at__date__gte=year_start)
            elif time_filter == 'custom':
                # Custom date range
                start_date = request.GET.get('start_date')
                end_date = request.GET.get('end_date')
                if start_date:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                    transactions = transactions.filter(created_at__date__gte=start_date)
                if end_date:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                    transactions = transactions.filter(created_at__date__lte=end_date)

            print(f"Time Filter Applied: {time_filter}")

            # === STATUS FILTERING ===
            status_filter = request.GET.get('status')
            if status_filter:
                transactions = transactions.filter(status=status_filter)
                print(f"Status Filter: {status_filter}")

            # === PAYMENT METHOD FILTERING ===
            payment_method = request.GET.get('payment_method')
            if payment_method:
                transactions = transactions.filter(payment_method=payment_method)
                print(f"Payment Method Filter: {payment_method}")

            # === AMOUNT RANGE FILTERING ===
            min_amount = request.GET.get('min_amount')
            max_amount = request.GET.get('max_amount')
            if min_amount:
                transactions = transactions.filter(total_amount__gte=float(min_amount))
            if max_amount:
                transactions = transactions.filter(total_amount__lte=float(max_amount))
            if min_amount or max_amount:
                print(f"Amount Range: {min_amount} - {max_amount}")

            # === SEARCH FUNCTIONALITY ===
            search = request.GET.get('search', '').strip()
            if search:
                transactions = transactions.filter(
                    Q(transaction_number__icontains=search) |
                    Q(notes__icontains=search) |
                    Q(items__product__name__icontains=search)
                ).distinct()
                print(f"Search Query: {search}")

            # === SORTING ===
            sort_by = request.GET.get('sort_by', '-created_at')
            valid_sort_fields = [
                'created_at', '-created_at',
                'total_amount', '-total_amount',
                'transaction_number', '-transaction_number'
            ]
            if sort_by in valid_sort_fields:
                transactions = transactions.order_by(sort_by)
            else:
                transactions = transactions.order_by('-created_at')

            # === PAGINATION ===
            page_size = int(request.GET.get('page_size', 20))
            page_number = int(request.GET.get('page', 1))

            paginator = Paginator(transactions, page_size)
            page_obj = paginator.get_page(page_number)

            # === PREPARE SALES DATA ===
            sales_list = []
            for transaction in page_obj:
                # Calculate transaction details
                items_data = []
                for item in transaction.items.all():
                    items_data.append({
                        'id': item.id,
                        'product_name': item.product.name,
                        'quantity': float(item.quantity),
                        'unit_price': float(item.unit_price),
                        'line_total': float(item.line_total),
                        'discount_amount': float(item.discount_amount)
                    })

                sales_data = {
                    'id': transaction.id,
                    'transaction_number': transaction.transaction_number,
                    'status': transaction.status,
                    'date': transaction.created_at.date().isoformat(),
                    'time': transaction.created_at.time().strftime('%H:%M:%S'),
                    'created_at': transaction.created_at.isoformat(),
                    'total_amount': float(transaction.total_amount),
                    'subtotal': float(transaction.subtotal),
                    'tax_total': float(transaction.tax_total),
                    'discount_total': float(transaction.discount_total),
                    'payment_method': transaction.payment_method,
                    'cash_amount': float(transaction.cash_amount),
                    'loan_amount': float(transaction.loan_amount),
                    'card_amount': float(transaction.card_amount),
                    'change_amount': float(transaction.change_amount),
                    'items_count': transaction.total_item_count,
                    'total_items': float(transaction.total_items),
                    'notes': transaction.notes or '',
                    'items': items_data
                }
                sales_list.append(sales_data)

            # === CUSTOMER SALES ANALYTICS ===
            all_customer_transactions = POSTransaction.objects.filter(
                owner=request.user,
                customer=customer
            )

            # Filtered period analytics
            period_analytics = transactions.aggregate(
                total_sales=Sum('total_amount'),
                total_transactions=Count('id'),
                avg_transaction_amount=Avg('total_amount')
            )

            # Calculate total items sold manually since it's not a database field
            total_items_sold = 0
            for transaction in transactions:
                total_items_sold += float(transaction.total_items)

            period_analytics['total_items_sold'] = total_items_sold

            # All-time analytics
            lifetime_analytics = all_customer_transactions.aggregate(
                lifetime_sales=Sum('total_amount'),
                lifetime_transactions=Count('id'),
                lifetime_avg_amount=Avg('total_amount')
            )

            # Calculate lifetime items sold manually
            lifetime_items_sold = 0
            for transaction in all_customer_transactions:
                lifetime_items_sold += float(transaction.total_items)

            lifetime_analytics['lifetime_items_sold'] = lifetime_items_sold

            # Payment method breakdown for the period
            payment_breakdown = transactions.values('payment_method').annotate(
                count=Count('id'),
                total=Sum('total_amount')
            )

            # Monthly sales trend (last 12 months)
            from django.db.models.functions import TruncMonth
            monthly_sales = all_customer_transactions.annotate(
                month=TruncMonth('created_at')
            ).values('month').annotate(
                sales=Sum('total_amount'),
                transactions=Count('id')
            ).order_by('-month')[:12]

            # Customer info
            customer_data = {
                'id': customer.id,
                'name': customer.display_name,
                'mobile_number': customer.mobile_number,
                'email': customer.email,
                'credit_limit': float(customer.credit_limit),
                'current_balance': float(customer.current_balance),
                'available_credit': float(customer.available_credit),
                'created_at': customer.created_at.isoformat()
            }

            response_data = {
                'success': True,
                'customer': customer_data,
                'sales': sales_list,
                'pagination': {
                    'current_page': page_obj.number,
                    'total_pages': paginator.num_pages,
                    'total_sales': paginator.count,
                    'page_size': page_size,
                    'has_next': page_obj.has_next(),
                    'has_previous': page_obj.has_previous()
                },
                'analytics': {
                    'period': {
                        'total_sales': float(period_analytics['total_sales'] or 0),
                        'total_transactions': period_analytics['total_transactions'],
                        'avg_transaction_amount': float(period_analytics['avg_transaction_amount'] or 0),
                        'total_items_sold': float(period_analytics['total_items_sold'] or 0)
                    },
                    'lifetime': {
                        'total_sales': float(lifetime_analytics['lifetime_sales'] or 0),
                        'total_transactions': lifetime_analytics['lifetime_transactions'],
                        'avg_transaction_amount': float(lifetime_analytics['lifetime_avg_amount'] or 0),
                        'total_items_sold': float(lifetime_analytics['lifetime_items_sold'] or 0)
                    },
                    'payment_method_breakdown': list(payment_breakdown),
                    'monthly_trend': [
                        {
                            'month': item['month'].strftime('%Y-%m'),
                            'month_name': item['month'].strftime('%B %Y'),
                            'sales': float(item['sales']),
                            'transactions': item['transactions']
                        } for item in monthly_sales
                    ]
                },
                'filters_applied': {
                    'time_filter': time_filter,
                    'status_filter': status_filter,
                    'payment_method': payment_method,
                    'min_amount': min_amount,
                    'max_amount': max_amount,
                    'search': search,
                    'sort_by': sort_by
                }
            }

            print(f"Returning {len(sales_list)} sales records")
            print(f"Period Total Sales: {period_analytics['total_sales']}")
            print(f"Lifetime Total Sales: {lifetime_analytics['lifetime_sales']}")

            print(f"\n=== CUSTOMER SALES API RESPONSE ===")
            import json
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END RESPONSE ===\n")

            return Response(response_data)

        except Exception as e:
            print(f"Error in CustomerSalesView: {str(e)}")
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerPaymentListCreateView(generics.ListCreateAPIView):
    """List customer payments and create new payments"""
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['payment_type', 'payment_date']
    ordering_fields = ['payment_date', 'amount', 'created_at']
    ordering = ['-payment_date']

    def get_queryset(self):
        customer_id = self.kwargs.get('customer_id')
        return CustomerPayment.objects.filter(
            customer_id=customer_id,
            customer__owner=self.request.user
        )

    def create(self, request, *args, **kwargs):
        """Create a new customer payment"""
        try:
            from django.utils import timezone
            customer_id = kwargs.get('customer_id')

            # Validate customer exists and belongs to user
            try:
                customer = Customer.objects.get(id=customer_id, owner=request.user)
            except Customer.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Customer not found'
                }, status=status.HTTP_404_NOT_FOUND)

            # Validate payment data
            required_fields = ['amount']
            for field in required_fields:
                if field not in request.data:
                    return Response({
                        'success': False,
                        'message': f'{field} is required'
                    }, status=status.HTTP_400_BAD_REQUEST)

            amount = request.data.get('amount')
            try:
                amount = float(amount)
                if amount <= 0:
                    raise ValueError("Amount must be greater than zero")
            except (ValueError, TypeError):
                return Response({
                    'success': False,
                    'message': 'Invalid amount value'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Handle payment date
            payment_date = request.data.get('payment_date')
            if payment_date:
                try:
                    from django.utils.dateparse import parse_datetime
                    parsed_date = parse_datetime(payment_date)
                    if parsed_date:
                        # Ensure timezone awareness
                        if parsed_date.tzinfo is None:
                            parsed_date = timezone.make_aware(parsed_date)
                        payment_date = parsed_date
                    else:
                        # Try parsing as ISO format
                        from datetime import datetime
                        parsed_date = datetime.fromisoformat(payment_date.replace('Z', '+00:00'))
                        if parsed_date.tzinfo is None:
                            parsed_date = timezone.make_aware(parsed_date)
                        payment_date = parsed_date
                except (ValueError, AttributeError):
                    payment_date = timezone.now()
            else:
                payment_date = timezone.now()

            # Create payment
            payment = CustomerPayment.objects.create(
                customer=customer,
                amount=amount,
                payment_type=request.data.get('payment_type', 'cash'),
                payment_date=payment_date,
                note=request.data.get('note') or '',  # Ensure empty string instead of None
                reference_number=request.data.get('reference_number') or '',  # Ensure empty string instead of None
                created_by=request.user
            )

            # Return payment data
            payment_data = {
                'id': payment.id,
                'customer_id': payment.customer.id,
                'customer_name': payment.customer.display_name,
                'amount': float(payment.amount),
                'formatted_amount': payment.formatted_amount,
                'payment_type': payment.payment_type,
                'payment_type_display': payment.get_payment_type_display(),
                'payment_date': payment.payment_date.isoformat(),
                'note': payment.note,
                'reference_number': payment.reference_number,
                'created_by': getattr(payment.created_by, 'username', payment.created_by.mobile_number),
                'created_at': payment.created_at.isoformat()
            }

            response_data = {
                'success': True,
                'message': 'Payment recorded successfully',
                'data': payment_data
            }

            # Print complete JSON response to terminal
            import json
            print(f"\n=== PAYMENT CREATED JSON RESPONSE ===")
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END JSON RESPONSE ===\n")

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error creating payment: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def list(self, request, *args, **kwargs):
        """List customer payments with pagination"""
        try:
            customer_id = kwargs.get('customer_id')

            # Validate customer exists and belongs to user
            try:
                customer = Customer.objects.get(id=customer_id, owner=request.user)
            except Customer.DoesNotExist:
                return Response({
                    'success': False,
                    'message': 'Customer not found'
                }, status=status.HTTP_404_NOT_FOUND)

            queryset = self.filter_queryset(self.get_queryset())

            # Pagination
            page_size = int(request.GET.get('page_size', 20))
            page_number = int(request.GET.get('page', 1))

            from django.core.paginator import Paginator
            paginator = Paginator(queryset, page_size)
            page_obj = paginator.get_page(page_number)

            # Prepare payments data
            payments_list = []
            for payment in page_obj:
                payment_data = {
                    'id': payment.id,
                    'amount': float(payment.amount),
                    'formatted_amount': payment.formatted_amount,
                    'payment_type': payment.payment_type,
                    'payment_type_display': payment.get_payment_type_display(),
                    'payment_date': payment.payment_date.isoformat(),
                    'note': payment.note,
                    'reference_number': payment.reference_number,
                    'created_by': getattr(payment.created_by, 'username', payment.created_by.mobile_number),
                    'created_at': payment.created_at.isoformat()
                }
                payments_list.append(payment_data)

            # Calculate totals
            from django.db.models import Sum
            total_payments = queryset.aggregate(total=Sum('amount'))['total'] or 0

            response_data = {
                'success': True,
                'data': {
                    'customer': {
                        'id': customer.id,
                        'name': customer.display_name,
                        'mobile_number': customer.mobile_number
                    },
                    'payments': payments_list,
                    'pagination': {
                        'current_page': page_obj.number,
                        'total_pages': paginator.num_pages,
                        'total_payments': paginator.count,
                        'page_size': page_size,
                        'has_next': page_obj.has_next(),
                        'has_previous': page_obj.has_previous()
                    },
                    'summary': {
                        'total_payments_amount': float(total_payments),
                        'total_payments_count': queryset.count()
                    }
                }
            }

            # Print complete JSON response to terminal
            import json
            print(f"\n=== PAYMENTS LIST JSON RESPONSE ===")
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END JSON RESPONSE ===\n")

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error listing payments: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class CustomerPaymentDetailView(generics.RetrieveUpdateDestroyAPIView):
    """Get, update, or delete a specific payment"""
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        customer_id = self.kwargs.get('customer_id')
        return CustomerPayment.objects.filter(
            customer_id=customer_id,
            customer__owner=self.request.user
        )

    def retrieve(self, request, *args, **kwargs):
        """Get payment details"""
        try:
            payment = self.get_object()

            payment_data = {
                'id': payment.id,
                'customer_id': payment.customer.id,
                'customer_name': payment.customer.display_name,
                'amount': float(payment.amount),
                'formatted_amount': payment.formatted_amount,
                'payment_type': payment.payment_type,
                'payment_type_display': payment.get_payment_type_display(),
                'payment_date': payment.payment_date.isoformat(),
                'note': payment.note,
                'reference_number': payment.reference_number,
                'created_by': getattr(payment.created_by, 'username', payment.created_by.mobile_number),
                'created_at': payment.created_at.isoformat(),
                'updated_at': payment.updated_at.isoformat()
            }

            response_data = {
                'success': True,
                'data': payment_data
            }

            # Print complete JSON response to terminal
            import json
            print(f"\n=== PAYMENT DETAILS JSON RESPONSE ===")
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END JSON RESPONSE ===\n")

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error retrieving payment: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update(self, request, *args, **kwargs):
        """Update payment details"""
        try:
            payment = self.get_object()

            # Update allowed fields
            if 'amount' in request.data:
                try:
                    amount = float(request.data['amount'])
                    if amount <= 0:
                        raise ValueError("Amount must be greater than zero")
                    payment.amount = amount
                except (ValueError, TypeError):
                    return Response({
                        'success': False,
                        'message': 'Invalid amount value'
                    }, status=status.HTTP_400_BAD_REQUEST)

            if 'payment_type' in request.data:
                payment.payment_type = request.data['payment_type']

            if 'payment_date' in request.data:
                payment_date = request.data['payment_date']
                try:
                    from django.utils.dateparse import parse_datetime
                    parsed_date = parse_datetime(payment_date)
                    if parsed_date:
                        # Ensure timezone awareness
                        if parsed_date.tzinfo is None:
                            parsed_date = timezone.make_aware(parsed_date)
                        payment.payment_date = parsed_date
                    else:
                        # Try parsing as ISO format
                        from datetime import datetime
                        parsed_date = datetime.fromisoformat(payment_date.replace('Z', '+00:00'))
                        if parsed_date.tzinfo is None:
                            parsed_date = timezone.make_aware(parsed_date)
                        payment.payment_date = parsed_date
                except (ValueError, AttributeError):
                    # Keep current date if parsing fails
                    pass

            if 'note' in request.data:
                payment.note = request.data['note'] or ''  # Ensure empty string instead of None

            if 'reference_number' in request.data:
                payment.reference_number = request.data['reference_number'] or ''  # Ensure empty string instead of None

            payment.save()

            payment_data = {
                'id': payment.id,
                'customer_id': payment.customer.id,
                'customer_name': payment.customer.display_name,
                'amount': float(payment.amount),
                'formatted_amount': payment.formatted_amount,
                'payment_type': payment.payment_type,
                'payment_type_display': payment.get_payment_type_display(),
                'payment_date': payment.payment_date.isoformat(),
                'note': payment.note,
                'reference_number': payment.reference_number,
                'created_by': getattr(payment.created_by, 'username', payment.created_by.mobile_number),
                'created_at': payment.created_at.isoformat(),
                'updated_at': payment.updated_at.isoformat()
            }

            response_data = {
                'success': True,
                'message': 'Payment updated successfully',
                'data': payment_data
            }

            # Print complete JSON response to terminal
            import json
            print(f"\n=== PAYMENT UPDATE JSON RESPONSE ===")
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END JSON RESPONSE ===\n")

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error updating payment: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def destroy(self, request, *args, **kwargs):
        """Delete payment"""
        try:
            payment = self.get_object()
            payment_info = f"Payment of {payment.formatted_amount} on {payment.payment_date.date()}"
            payment.delete()

            response_data = {
                'success': True,
                'message': f'{payment_info} deleted successfully'
            }

            # Print complete JSON response to terminal
            import json
            print(f"\n=== PAYMENT DELETE JSON RESPONSE ===")
            print(json.dumps(response_data, indent=2, default=str))
            print(f"=== END JSON RESPONSE ===\n")

            return Response(response_data)

        except Exception as e:
            return Response({
                'success': False,
                'message': f'Error deleting payment: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)