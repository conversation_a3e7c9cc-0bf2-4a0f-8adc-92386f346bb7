<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHANNAB - Customer Management</title>
    <style>
        /* ==== CHANNAB STANDALONE - ULTRA-RESPONSIVE CUSTOMER MANAGEMENT ==== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-green: #0da487;
            --success-green: #10B981;
            --info-blue: #3B82F6;
            --warning-orange: #F59E0B;
            --danger-red: #EF4444;
            --purple-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --green-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --orange-gradient: linear-gradient(135deg, #fc7303 0%, #ffa726 100%);
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --vh: 1vh;
        }

        html {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: var(--gray-700);
        }

        body {
            background: var(--gray-50);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* ==== HEADER SECTION ==== */
        .channab-header {
            background: var(--white);
            border-bottom: 1px solid var(--gray-200);
            padding: 16px 24px;
            position: sticky;
            top: 0;
            z-index: 100;
            transition: all 0.3s ease;
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--green-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: white;
        }

        .logo-text {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-green);
            letter-spacing: -0.025em;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .search-container {
            position: relative;
        }

        .search-input {
            width: 300px;
            padding: 12px 16px 12px 40px;
            border: 2px solid var(--gray-200);
            border-radius: 12px;
            background: var(--white);
            font-size: 0.95rem;
            transition: all 0.2s ease;
            outline: none;
        }

        .search-input:focus {
            border-color: var(--primary-green);
            box-shadow: 0 0 0 3px rgba(13, 164, 135, 0.1);
        }

        .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--gray-400);
            font-size: 1.1rem;
        }

        .theme-toggle {
            width: 44px;
            height: 44px;
            border: 2px solid var(--gray-200);
            border-radius: 50%;
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            border-color: var(--primary-green);
            background: var(--gray-50);
        }

        /* ==== MAIN CONTAINER ==== */
        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }

        /* ==== FILTER SECTION ==== */
        .filter-section {
            margin-bottom: 24px;
        }

        .clear-filters-btn {
            padding: 8px 16px;
            background: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            color: var(--gray-600);
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .clear-filters-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }

        /* ==== STATS SECTION ==== */
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }

        .stat-card {
            background: var(--white);
            border-radius: 16px;
            padding: 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .stat-card.customers-stat {
            background: var(--purple-gradient);
            color: white;
            border: none;
        }

        .stat-card.sales-stat {
            background: var(--green-gradient);
            color: white;
            border: none;
        }

        .stat-card.balance-stat {
            background: var(--orange-gradient);
            color: white;
            border: none;
        }

        .stat-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 16px;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }

        .stat-content {
            flex: 1;
        }

        .stat-title {
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            opacity: 0.9;
            margin-bottom: 4px;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            line-height: 1;
            margin-bottom: 8px;
        }

        .stat-details {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .stat-details span {
            display: block;
            margin-bottom: 2px;
        }

        /* ==== FILTER PILLS ==== */
        .filter-pills {
            background: var(--white);
            border-radius: 12px;
            padding: 20px 24px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            margin-bottom: 32px;
        }

        .current-filter {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 12px;
        }

        .filter-tag {
            background: var(--primary-green);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .filter-date {
            color: var(--gray-500);
            font-size: 0.9rem;
        }

        .filter-summary {
            color: var(--gray-600);
            font-size: 0.9rem;
            margin-bottom: 16px;
        }

        .action-buttons {
            display: flex;
            gap: 12px;
        }

        .clear-filter-btn {
            padding: 8px 16px;
            background: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            color: var(--gray-600);
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .clear-filter-btn:hover {
            background: var(--gray-50);
        }

        .add-customer-btn {
            padding: 10px 20px;
            background: var(--primary-green);
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .add-customer-btn:hover {
            background: #0b9478;
            transform: translateY(-1px);
        }

        /* ==== CUSTOMERS TABLE (DESKTOP) ==== */
        .desktop-view {
            display: block;
        }

        .table-container {
            background: var(--white);
            border-radius: 12px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            overflow: hidden;
        }

        .customers-table {
            width: 100%;
            border-collapse: collapse;
        }

        .customers-table thead {
            background: var(--gray-50);
        }

        .customers-table th {
            padding: 16px 20px;
            text-align: left;
            font-weight: 600;
            color: var(--gray-700);
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            border-bottom: 1px solid var(--gray-200);
        }

        .customers-table td {
            padding: 16px 20px;
            border-bottom: 1px solid var(--gray-100);
            vertical-align: middle;
        }

        .customer-row {
            transition: background-color 0.2s ease;
        }

        .customer-row:hover {
            background: var(--gray-50);
        }

        .customer-cell {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--green-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1rem;
        }

        .customer-name {
            font-weight: 600;
            color: var(--gray-800);
        }

        .contact-info .phone {
            font-weight: 500;
            color: var(--gray-700);
            margin-bottom: 4px;
        }

        .contact-info .balance {
            font-size: 0.9rem;
            color: var(--gray-500);
        }

        .milk-info .amount {
            font-weight: 600;
            color: var(--info-blue);
        }

        .balance-info .amount {
            font-weight: 600;
            color: var(--success-green);
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.active {
            background: #D1FAE5;
            color: #065F46;
        }

        .action-buttons-table {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: 1px solid var(--gray-300);
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            transition: all 0.2s ease;
            text-decoration: none;
            color: var(--gray-600);
        }

        .action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
        }

        .action-btn.view-btn:hover {
            background: #DBEAFE;
            border-color: var(--info-blue);
            color: var(--info-blue);
        }

        .action-btn.edit-btn:hover {
            background: #FEF3C7;
            border-color: var(--warning-orange);
            color: var(--warning-orange);
        }

        .action-btn.delete-btn:hover {
            background: #FEE2E2;
            border-color: var(--danger-red);
            color: var(--danger-red);
        }

        /* ==== MOBILE CARD VIEW ==== */
        .mobile-view {
            display: none;
        }

        .customer-cards {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .customer-card-mobile {
            background: var(--white);
            border-radius: 16px;
            padding: 20px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
            transition: all 0.3s ease;
        }

        .customer-card-mobile:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .card-header {
            display: flex;
            align-items: flex-start;
            gap: 16px;
            margin-bottom: 16px;
        }

        .customer-avatar-mobile {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: var(--green-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .customer-details {
            flex: 1;
        }

        .customer-name-mobile {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 12px;
        }

        .customer-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-label {
            display: block;
            font-size: 0.75rem;
            color: var(--gray-500);
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .stat-value {
            display: block;
            font-weight: 600;
            color: var(--gray-800);
        }

        .stat-value.status-active {
            color: var(--success-green);
        }

        .card-contact {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 16px;
            padding: 12px;
            background: var(--gray-50);
            border-radius: 8px;
        }

        .contact-item {
            text-align: center;
        }

        .contact-label {
            display: block;
            font-size: 0.75rem;
            color: var(--gray-500);
            text-transform: uppercase;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .contact-value {
            display: block;
            font-weight: 600;
            color: var(--gray-800);
        }

        .balance-amount {
            color: var(--success-green);
        }

        .card-actions-mobile {
            display: flex;
            justify-content: center;
            gap: 12px;
        }

        .mobile-action-btn {
            width: 44px;
            height: 44px;
            border-radius: 12px;
            border: 1px solid var(--gray-300);
            background: var(--white);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.2s ease;
            text-decoration: none;
            color: var(--gray-600);
        }

        .mobile-action-btn:hover {
            background: var(--gray-50);
            border-color: var(--gray-400);
            transform: scale(1.05);
        }

        /* ==== EMPTY STATE ==== */
        .empty-state-channab {
            text-align: center;
            padding: 80px 24px;
            background: var(--white);
            border-radius: 16px;
            box-shadow: var(--shadow);
            border: 1px solid var(--gray-200);
        }

        .empty-icon {
            font-size: 4rem;
            margin-bottom: 24px;
            opacity: 0.5;
        }

        .empty-state-channab h2 {
            font-size: 1.5rem;
            color: var(--gray-800);
            margin-bottom: 12px;
        }

        .empty-state-channab p {
            color: var(--gray-600);
            margin-bottom: 32px;
            font-size: 1rem;
        }

        .add-first-customer-btn {
            padding: 14px 28px;
            background: var(--primary-green);
            color: white;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .add-first-customer-btn:hover {
            background: #0b9478;
            transform: translateY(-2px);
        }

        /* ==== PAGINATION ==== */
        .pagination-nav {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 16px;
            margin-top: 32px;
            padding: 24px;
        }

        .pagination-btn {
            padding: 10px 20px;
            background: var(--white);
            border: 1px solid var(--gray-300);
            border-radius: 8px;
            color: var(--gray-700);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .pagination-btn:hover {
            background: var(--gray-50);
            border-color: var(--primary-green);
        }

        .pagination-info {
            color: var(--gray-500);
            font-size: 0.9rem;
        }

        /* ==== RESPONSIVE DESIGN ==== */
        @media (max-width: 768px) {
            .channab-header {
                padding: 12px 16px;
            }

            .header-content {
                flex-direction: column;
                gap: 16px;
                align-items: stretch;
            }

            .logo-section {
                justify-content: center;
            }

            .header-actions {
                justify-content: space-between;
            }

            .search-input {
                width: 100%;
                max-width: 250px;
            }

            .main-container {
                padding: 16px;
            }

            .stats-section {
                grid-template-columns: 1fr;
                gap: 16px;
                margin-bottom: 24px;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 1.75rem;
            }

            .filter-pills {
                padding: 16px;
            }

            .current-filter {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
            }

            .action-buttons {
                flex-direction: column;
            }

            .desktop-view {
                display: none;
            }

            .mobile-view {
                display: block;
            }

            .customer-stats {
                grid-template-columns: 1fr;
                gap: 8px;
            }

            .card-contact {
                grid-template-columns: 1fr;
                gap: 12px;
            }
        }

        @media (max-width: 480px) {
            .logo-text {
                font-size: 1.25rem;
            }

            .main-container {
                padding: 12px;
            }

            .stat-card {
                padding: 16px;
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .customer-card-mobile {
                padding: 16px;
            }

            .card-header {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }

            .customer-avatar-mobile {
                align-self: center;
            }
        }

        @media (max-width: 320px) {
            .stats-section {
                gap: 12px;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .stat-number {
                font-size: 1.25rem;
            }
        }

        /* ==== ANIMATIONS ==== */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }

        /* ==== DARK THEME ==== */
        .dark-theme {
            background: var(--gray-900);
            color: var(--gray-100);
        }

        .dark-theme .channab-header {
            background: var(--gray-800);
            border-bottom-color: var(--gray-700);
        }

        .dark-theme .search-input {
            background: var(--gray-700);
            border-color: var(--gray-600);
            color: var(--gray-100);
        }

        .dark-theme .search-input::placeholder {
            color: var(--gray-400);
        }

        .dark-theme .theme-toggle {
            background: var(--gray-700);
            border-color: var(--gray-600);
            color: var(--gray-100);
        }

        .dark-theme .filter-pills,
        .dark-theme .table-container,
        .dark-theme .customer-card-mobile,
        .dark-theme .empty-state-channab {
            background: var(--gray-800);
            border-color: var(--gray-700);
        }

        .dark-theme .customers-table thead {
            background: var(--gray-700);
        }

        .dark-theme .customers-table th,
        .dark-theme .customers-table td {
            border-color: var(--gray-700);
            color: var(--gray-100);
        }

        .dark-theme .customer-row:hover {
            background: var(--gray-700);
        }

        .dark-theme .card-contact {
            background: var(--gray-700);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="channab-header">
        <div class="header-content">
            <div class="logo-section">
                <div class="logo-icon">🌿</div>
                <h1 class="logo-text">CHANNAB</h1>
            </div>
            <div class="header-actions">
                <div class="search-container">
                    <span class="search-icon">🔍</span>
                    <input type="search" class="search-input" placeholder="Search..." id="searchInput">
                </div>
                <button class="theme-toggle" id="themeToggle">🌙</button>
            </div>
        </div>
    </header>

    <!-- Main Container -->
    <main class="main-container">
        <!-- Filter Section -->
        <div class="filter-section">
            <button class="clear-filters-btn" id="clearFilters">Clear Filters</button>
        </div>

        <!-- Stats Section -->
        <div class="stats-section">
            <div class="stat-card customers-stat fade-in">
                <div class="stat-header">
                    <div class="stat-icon">👥</div>
                    <div class="stat-content">
                        <div class="stat-title">TOTAL CUSTOMERS</div>
                        <div class="stat-number" id="totalCustomers">11</div>
                        <div class="stat-details">
                            <span>Registered: 11</span>
                            <span>Status: Active</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card sales-stat fade-in">
                <div class="stat-header">
                    <div class="stat-icon">💰</div>
                    <div class="stat-content">
                        <div class="stat-title">TOTAL SALES</div>
                        <div class="stat-number">Rs. 26875</div>
                        <div class="stat-details">
                            <span>Total Amount: Rs. 26875</span>
                            <span>Pending: Rs. 0</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="stat-card balance-stat fade-in">
                <div class="stat-header">
                    <div class="stat-icon">⚠️</div>
                    <div class="stat-content">
                        <div class="stat-title">OUTSTANDING BALANCE</div>
                        <div class="stat-number">Rs. -12845</div>
                        <div class="stat-details">
                            <span>Pending Payment</span>
                            <span>Service in Demand</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filter Pills -->
        <div class="filter-pills fade-in">
            <div class="current-filter">
                <span class="filter-tag">This Month</span>
                <span class="filter-date">Sep 01, 2025 - Sep 30, 2025</span>
            </div>
            <p class="filter-summary">Stats: 11 customers • 27676 milk • Rs. 26875</p>
            <div class="action-buttons">
                <button class="clear-filter-btn">Clear Filter</button>
                <button class="add-customer-btn">➕ Add New Customer</button>
            </div>
        </div>

        <!-- Customers Section -->
        <div id="customersSection" class="fade-in">
            <!-- Desktop Table View -->
            <div class="desktop-view">
                <div class="table-container">
                    <table class="customers-table">
                        <thead>
                            <tr>
                                <th>Customer</th>
                                <th>Contact Info</th>
                                <th>Total Milk</th>
                                <th>Outstanding Balance</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- Customers will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Mobile Card View -->
            <div class="mobile-view">
                <div class="customer-cards" id="customerCardsMobile">
                    <!-- Mobile cards will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <nav class="pagination-nav">
            <a href="#" class="pagination-btn">Previous</a>
            <span class="pagination-info">Page 1 of 1</span>
            <a href="#" class="pagination-btn">Next</a>
        </nav>
    </main>

    <script>
        // Mock customer data - exactly matching the inspiration
        const customers = [
            {
                id: 1,
                name: 'Billal',
                phone: 'N/A',
                milk: '0L',
                amount: 'Rs.0',
                balance: 'Rs. 0',
                status: 'Active',
                avatar: 'B'
            },
            {
                id: 2,
                name: 'Qamar',
                phone: 'N/A',
                milk: '4.50L',
                amount: 'Rs.0',
                balance: 'Rs. 0',
                status: 'Active',
                avatar: 'Q'
            },
            {
                id: 3,
                name: 'Nestle',
                phone: 'N/A',
                milk: '0L',
                amount: 'Rs.0',
                balance: 'Rs. 0',
                status: 'Active',
                avatar: 'N'
            },
            {
                id: 4,
                name: 'Ali Hassan',
                phone: '03001234567',
                milk: '2.5L',
                amount: 'Rs.500',
                balance: 'Rs. 250',
                status: 'Active',
                avatar: 'A'
            },
            {
                id: 5,
                name: 'Sara Ahmed',
                phone: '03459876543',
                milk: '3.0L',
                amount: 'Rs.750',
                balance: 'Rs. 0',
                status: 'Active',
                avatar: 'S'
            }
        ];

        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });

        function initializeApp() {
            renderCustomers();
            setupEventListeners();
            setupMobileOptimizations();
        }

        function renderCustomers() {
            renderDesktopTable();
            renderMobileCards();
        }

        function renderDesktopTable() {
            const tbody = document.getElementById('customersTableBody');
            tbody.innerHTML = '';

            customers.forEach(customer => {
                const row = document.createElement('tr');
                row.className = 'customer-row';
                row.innerHTML = `
                    <td class="customer-cell">
                        <div class="customer-avatar">${customer.avatar}</div>
                        <div class="customer-info">
                            <div class="customer-name">${customer.name}</div>
                        </div>
                    </td>
                    <td class="contact-cell">
                        <div class="contact-info">
                            <div class="phone">${customer.phone}</div>
                            <div class="balance">${customer.balance}</div>
                        </div>
                    </td>
                    <td class="milk-cell">
                        <div class="milk-info">
                            <div class="amount">${customer.milk}</div>
                        </div>
                    </td>
                    <td class="balance-cell">
                        <div class="balance-info">
                            <div class="amount">${customer.balance}</div>
                        </div>
                    </td>
                    <td class="status-cell">
                        <span class="status-badge active">${customer.status}</span>
                    </td>
                    <td class="actions-cell">
                        <div class="action-buttons-table">
                            <button class="action-btn view-btn" onclick="viewCustomer(${customer.id})">👁️</button>
                            <button class="action-btn edit-btn" onclick="editCustomer(${customer.id})">✏️</button>
                            <button class="action-btn delete-btn" onclick="deleteCustomer(${customer.id})">🗑️</button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function renderMobileCards() {
            const container = document.getElementById('customerCardsMobile');
            container.innerHTML = '';

            customers.forEach(customer => {
                const card = document.createElement('div');
                card.className = 'customer-card-mobile';
                card.innerHTML = `
                    <div class="card-header">
                        <div class="customer-avatar-mobile">${customer.avatar}</div>
                        <div class="customer-details">
                            <h3 class="customer-name-mobile">${customer.name}</h3>
                            <div class="customer-stats">
                                <div class="stat-item">
                                    <span class="stat-label">Total Milk</span>
                                    <span class="stat-value">${customer.milk}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Amount</span>
                                    <span class="stat-value">${customer.amount}</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">Status</span>
                                    <span class="stat-value status-active">${customer.status}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-contact">
                        <div class="contact-item">
                            <span class="contact-label">Phone</span>
                            <span class="contact-value">${customer.phone}</span>
                        </div>
                        <div class="contact-item">
                            <span class="contact-label">Balance</span>
                            <span class="contact-value balance-amount">${customer.balance}</span>
                        </div>
                    </div>

                    <div class="card-actions-mobile">
                        <button class="mobile-action-btn view-btn-mobile" onclick="viewCustomer(${customer.id})">👁️</button>
                        <button class="mobile-action-btn edit-btn-mobile" onclick="editCustomer(${customer.id})">✏️</button>
                        <button class="mobile-action-btn delete-btn-mobile" onclick="deleteCustomer(${customer.id})">🗑️</button>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        function setupEventListeners() {
            // Search functionality
            const searchInput = document.getElementById('searchInput');
            searchInput.addEventListener('input', handleSearch);

            // Theme toggle
            const themeToggle = document.getElementById('themeToggle');
            themeToggle.addEventListener('click', toggleTheme);

            // Clear filters
            const clearFilters = document.getElementById('clearFilters');
            clearFilters.addEventListener('click', clearAllFilters);

            // Add haptic feedback to interactive elements
            document.querySelectorAll('.stat-card, .action-btn, .mobile-action-btn, .add-customer-btn').forEach(element => {
                element.addEventListener('click', hapticFeedback);
            });
        }

        function setupMobileOptimizations() {
            // Dynamic viewport height
            function setVH() {
                const vh = window.innerHeight * 0.01;
                document.documentElement.style.setProperty('--vh', `${vh}px`);
            }
            setVH();
            window.addEventListener('resize', setVH);
            window.addEventListener('orientationchange', setVH);

            // Touch feedback
            document.querySelectorAll('.stat-card, .action-btn, .mobile-action-btn').forEach(element => {
                element.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                }, { passive: true });

                element.addEventListener('touchend', function() {
                    this.style.transform = 'scale(1)';
                }, { passive: true });
            });

            // Header scroll effect
            let lastScrollY = window.scrollY;
            window.addEventListener('scroll', () => {
                const header = document.querySelector('.channab-header');
                if (window.scrollY > 100) {
                    header.classList.add('scrolled');
                } else {
                    header.classList.remove('scrolled');
                }
            }, { passive: true });
        }

        function handleSearch(event) {
            const query = event.target.value.toLowerCase().trim();
            const rows = document.querySelectorAll('.customer-row');
            const cards = document.querySelectorAll('.customer-card-mobile');
            let visibleCount = 0;

            // Filter desktop table
            rows.forEach(row => {
                const name = row.querySelector('.customer-name').textContent.toLowerCase();
                const phone = row.querySelector('.phone').textContent.toLowerCase();
                const isVisible = name.includes(query) || phone.includes(query);
                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });

            // Filter mobile cards
            cards.forEach(card => {
                const name = card.querySelector('.customer-name-mobile').textContent.toLowerCase();
                const phone = card.querySelector('.contact-value').textContent.toLowerCase();
                const isVisible = name.includes(query) || phone.includes(query);
                card.style.display = isVisible ? '' : 'none';
            });

            // Update stats
            updateSearchStats(visibleCount, query);
        }

        function updateSearchStats(count, query) {
            const totalCustomersEl = document.getElementById('totalCustomers');
            if (query) {
                totalCustomersEl.textContent = count;
            } else {
                totalCustomersEl.textContent = customers.length;
            }
        }

        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.getElementById('themeToggle');

            body.classList.toggle('dark-theme');
            const isDark = body.classList.contains('dark-theme');

            themeToggle.textContent = isDark ? '☀️' : '🌙';
            localStorage.setItem('channab-theme', isDark ? 'dark' : 'light');

            hapticFeedback();
        }

        function clearAllFilters() {
            const searchInput = document.getElementById('searchInput');
            searchInput.value = '';
            handleSearch({ target: { value: '' } });
            hapticFeedback();
        }

        function hapticFeedback() {
            if ('vibrate' in navigator) {
                navigator.vibrate(10);
            }
        }

        // Customer action functions
        function viewCustomer(id) {
            const customer = customers.find(c => c.id === id);
            alert(`Viewing customer: ${customer.name}`);
            hapticFeedback();
        }

        function editCustomer(id) {
            const customer = customers.find(c => c.id === id);
            alert(`Editing customer: ${customer.name}`);
            hapticFeedback();
        }

        function deleteCustomer(id) {
            const customer = customers.find(c => c.id === id);
            if (confirm(`Delete customer: ${customer.name}?`)) {
                alert(`Deleted customer: ${customer.name}`);
                hapticFeedback();
            }
        }

        // Initialize theme from localStorage
        const savedTheme = localStorage.getItem('channab-theme');
        if (savedTheme === 'dark') {
            document.body.classList.add('dark-theme');
            document.getElementById('themeToggle').textContent = '☀️';
        }
    </script>
</body>
</html>