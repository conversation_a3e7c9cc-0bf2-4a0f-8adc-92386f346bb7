 we need to create the sales and then relate to the customer so why default should be the date , product, qty, payment (cash/credit) full/Half later on we can extend this based on our requrient as project grow
 prepare beautifull POS UI and link all this with that can also support Walk in Customer(unknownownn sales) 


 In the product we need to add Purchase price and selling price also I need to add the options like for example the product is what type my ear units like liter KG cent whatever other possible dimensions or custom
 I'll send the stock we need to add the the date and the stock addition how much stock is remaining should be automatic recirculated as stock availability and sales it can be possible that different stocks added with a different price on different dates and also add the expiry date for the product
 We need to walk at the category and the categories for the products