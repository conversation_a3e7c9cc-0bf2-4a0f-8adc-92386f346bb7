#!/usr/bin/env python3
"""
Check transactions for specific customer ID 24
"""

import os
import sys
import django

# Set up Django environment
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import POSTransaction
from customers.models import Customer

def check_customer_transactions():
    print("Checking Customer ID 24 Transactions")
    print("=" * 40)

    User = get_user_model()

    # Get the test user
    try:
        user = User.objects.get(mobile_number='03478181583')
        print(f"✓ Found user: {user.mobile_number}")
    except User.DoesNotExist:
        print("✗ User not found")
        return

    # Get customer ID 24
    try:
        customer = Customer.objects.get(id=24, owner=user)
        print(f"✓ Found customer: {customer.display_name}")
        print(f"  • ID: {customer.id}")
        print(f"  • Mobile: {customer.mobile_number or 'None'}")
        print(f"  • Notes: {customer.notes}")
    except Customer.DoesNotExist:
        print("✗ Customer ID 24 not found")
        return

    # Get POS transactions for this customer
    pos_transactions = POSTransaction.objects.filter(customer=customer).order_by('-created_at')
    print(f"\n📋 POS Transactions for {customer.display_name}:")
    print(f"Total: {pos_transactions.count()}")

    if pos_transactions.count() > 0:
        print("-" * 80)
        for transaction in pos_transactions:
            print(f"ID: {transaction.id:2d} | {transaction.transaction_number} | Status: {transaction.status:10s} | Amount: Rs. {transaction.total_amount:6.0f} | Items: {transaction.total_item_count}")
        print("-" * 80)
    else:
        print("No POS transactions found for this customer")

    # Check old POS system transactions (pos.models.Sale)
    try:
        from pos.models import Sale
        old_sales = Sale.objects.filter(customer=customer).order_by('-created_at')
        print(f"\n📋 Old POS Sales for {customer.display_name}:")
        print(f"Total: {old_sales.count()}")

        if old_sales.count() > 0:
            print("-" * 80)
            for sale in old_sales:
                print(f"ID: {sale.id:2d} | Created: {sale.created_at} | Total: Rs. {sale.total_amount:6.0f}")
            print("-" * 80)
        else:
            print("No old POS sales found for this customer")

    except Exception as e:
        print(f"Old POS system not available or error: {e}")

    # Check all transactions for this user to see what's available
    print(f"\n📊 All POS Transactions for user:")
    all_transactions = POSTransaction.objects.filter(owner=user).order_by('-created_at')
    print(f"Total: {all_transactions.count()}")

    for transaction in all_transactions:
        customer_name = transaction.customer.display_name if transaction.customer else "None"
        customer_id = str(transaction.customer.id) if transaction.customer else "None"
        print(f"ID: {transaction.id:2d} | Customer: {customer_name:20s} (ID: {customer_id:>4s}) | {transaction.transaction_number}")

if __name__ == '__main__':
    check_customer_transactions()