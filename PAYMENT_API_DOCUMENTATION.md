# 💳 Customer Payment API Documentation

## Base URL
```
http://zayyrah.com/api/v1/customers/{customer_id}/payments/
```

## Authentication
All endpoints require authentication. Include the authorization token in headers:
```
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
```

---

## 📋 API Endpoints

### 1. 📝 CREATE PAYMENT
**POST** `/api/v1/customers/{customer_id}/payments/`

#### Request Body
```json
{
  "amount": 250.75,
  "payment_type": "card",
  "payment_date": "2025-09-23T18:30:00",
  "note": "Payment for groceries",
  "reference_number": "REF123456"
}
```

#### Payment Types
- `cash` - Cash
- `card` - Card
- `bank_transfer` - Bank Transfer
- `mobile_money` - Mobile Money
- `cheque` - Cheque
- `other` - Other

#### Success Response (201)
```json
{
  "success": true,
  "message": "Payment recorded successfully",
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "<PERSON>",
    "amount": 250.75,
    "formatted_amount": "Rs. 250.75",
    "payment_type": "card",
    "payment_type_display": "Card",
    "payment_date": "2025-09-23T18:30:00+00:00",
    "note": "Payment for groceries",
    "reference_number": "REF123456",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00"
  }
}
```

#### Error Response (400)
```json
{
  "success": false,
  "message": "Invalid amount value"
}
```

---

### 2. 📋 GET PAYMENTS LIST
**GET** `/api/v1/customers/{customer_id}/payments/`

#### Query Parameters
- `page` (optional) - Page number (default: 1)
- `page_size` (optional) - Items per page (default: 20)
- `payment_type` (optional) - Filter by payment type
- `ordering` (optional) - Sort order (`-payment_date`, `amount`, etc.)

#### Example Request
```
GET /api/v1/customers/28/payments/?page=1&page_size=10&ordering=-payment_date
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "customer": {
      "id": 28,
      "name": "John Doe",
      "mobile_number": "03001234567"
    },
    "payments": [
      {
        "id": 12,
        "amount": 250.75,
        "formatted_amount": "Rs. 250.75",
        "payment_type": "card",
        "payment_type_display": "Card",
        "payment_date": "2025-09-23T18:30:00+00:00",
        "note": "Payment for groceries",
        "reference_number": "REF123456",
        "created_by": "***********",
        "created_at": "2025-09-23T13:21:39.577900+00:00"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 2,
      "total_payments": 25,
      "page_size": 20,
      "has_next": true,
      "has_previous": false
    },
    "summary": {
      "total_payments_amount": 2500.50,
      "total_payments_count": 25
    }
  }
}
```

---

### 3. 👁️ GET SINGLE PAYMENT
**GET** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "John Doe",
    "amount": 250.75,
    "formatted_amount": "Rs. 250.75",
    "payment_type": "card",
    "payment_type_display": "Card",
    "payment_date": "2025-09-23T18:30:00+00:00",
    "note": "Payment for groceries",
    "reference_number": "REF123456",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00",
    "updated_at": "2025-09-23T14:21:39.577900+00:00"
  }
}
```

---

### 4. ✏️ UPDATE PAYMENT
**PUT** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

#### Request Body (All fields optional)
```json
{
  "amount": 300.00,
  "payment_type": "bank_transfer",
  "payment_date": "2025-09-23T19:00:00",
  "note": "Updated payment note",
  "reference_number": "NEW_REF789"
}
```

#### Success Response (200)
```json
{
  "success": true,
  "message": "Payment updated successfully",
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "John Doe",
    "amount": 300.00,
    "formatted_amount": "Rs. 300.00",
    "payment_type": "bank_transfer",
    "payment_type_display": "Bank Transfer",
    "payment_date": "2025-09-23T19:00:00+00:00",
    "note": "Updated payment note",
    "reference_number": "NEW_REF789",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00",
    "updated_at": "2025-09-23T14:25:15.123456+00:00"
  }
}
```

---

### 5. 🗑️ DELETE PAYMENT
**DELETE** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

#### Success Response (200)
```json
{
  "success": true,
  "message": "Payment of Rs. 250.75 on 2025-09-23 deleted successfully"
}
```

#### Error Response (404)
```json
{
  "success": false,
  "message": "Payment not found"
}
```

---

## 🚨 Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created successfully |
| 400 | Bad request (validation error) |
| 401 | Unauthorized (missing/invalid token) |
| 404 | Resource not found |
| 500 | Internal server error |

---

## 📱 Flutter Integration

### Dependencies
Add to your `pubspec.yaml`:
```yaml
dependencies:
  http: ^1.1.0
```

### Basic Usage
```dart
import 'package:http/http.dart' as http;
import 'dart:convert';

class PaymentAPI {
  static const String baseUrl = 'http://zayyrah.com';
  final String authToken;

  PaymentAPI({required this.authToken});

  Future<Map<String, dynamic>> createPayment({
    required int customerId,
    required double amount,
    required String paymentType,
    DateTime? paymentDate,
    String? note,
    String? referenceNumber,
  }) async {
    final url = '$baseUrl/api/v1/customers/$customerId/payments/';

    final response = await http.post(
      Uri.parse(url),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $authToken',
      },
      body: jsonEncode({
        'amount': amount,
        'payment_type': paymentType,
        if (paymentDate != null) 'payment_date': paymentDate.toIso8601String(),
        if (note != null) 'note': note,
        if (referenceNumber != null) 'reference_number': referenceNumber,
      }),
    );

    return jsonDecode(response.body);
  }

  Future<Map<String, dynamic>> getPayments({
    required int customerId,
    int page = 1,
    int pageSize = 20,
  }) async {
    final url = '$baseUrl/api/v1/customers/$customerId/payments/?page=$page&page_size=$pageSize';

    final response = await http.get(
      Uri.parse(url),
      headers: {
        'Authorization': 'Bearer $authToken',
      },
    );

    return jsonDecode(response.body);
  }
}
```

---

## 🔧 Advanced Features

### Date Formatting
- **Input**: Accept both `datetime-local` format (`2025-09-23T18:30:00`) and ISO format
- **Output**: Always returns ISO 8601 format with timezone (`2025-09-23T18:30:00+00:00`)

### Validation Rules
- **Amount**: Must be positive decimal (> 0), up to 2 decimal places
- **Payment Type**: Must be one of the allowed types
- **Date**: Optional, defaults to current timestamp
- **Note**: Optional, can be empty
- **Reference Number**: Optional, can be empty

### Security
- All endpoints require authentication
- Users can only access payments for their own customers
- CSRF protection enabled
- Input sanitization applied

### Pagination
- Default page size: 20 items
- Maximum page size: 100 items
- Returns pagination metadata with `has_next` and `has_previous` flags

---

## 🎯 Quick Examples

### Create Cash Payment
```bash
curl -X POST "http://zayyrah.com/api/v1/customers/28/payments/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 100.50,
    "payment_type": "cash",
    "note": "Cash payment"
  }'
```

### Get All Payments
```bash
curl -X GET "http://zayyrah.com/api/v1/customers/28/payments/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update Payment
```bash
curl -X PUT "http://zayyrah.com/api/v1/customers/28/payments/12/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 150.00,
    "note": "Updated amount"
  }'
```

### Delete Payment
```bash
curl -X DELETE "http://zayyrah.com/api/v1/customers/28/payments/12/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 💡 Best Practices

1. **Error Handling**: Always check the `success` field in responses
2. **Pagination**: Use pagination for large datasets
3. **Date Handling**: Use ISO 8601 format for dates
4. **Validation**: Validate amounts on client-side before sending
5. **Security**: Store auth tokens securely
6. **Offline**: Consider implementing offline support with local storage
7. **Retry Logic**: Implement retry logic for network failures

---

**🎉 Your payment API is ready for Flutter integration!**