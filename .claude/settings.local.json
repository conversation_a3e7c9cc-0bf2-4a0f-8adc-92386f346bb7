{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python manage.py:*)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python manage.py check)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py check)", "Bash(export:*)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/test_api_setup.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python manage.py check --deploy)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/zayyrah/manage.py check)", "mcp__ide__getDiagnostics", "WebFetch(domain:*************)", "Bash(python zayyrah/manage.py check)", "Bash(python zayyrah/manage.py makemigrations customers)", "<PERSON><PERSON>(python:*)", "Bash(sudo systemctl status:*)", "Bash(sudo systemctl start:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "Bash(lsof:*)", "Bash(/bashes)", "<PERSON><PERSON>(playwright install:*)", "Bash(kill:*)", "Bash(git init:*)", "Bash(git remote add:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(git push:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py makemigrations inventory)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py migrate)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py test inventory)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py test inventory.test_phase2_features.Phase2APIIntegrationTestCase.test_bulk_stock_update_endpoint)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py test inventory.test_phase2_features.Phase2APIIntegrationTestCase.test_category_tree_endpoint_with_caching)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python zayyrah/manage.py test inventory.test_phase2_features.StockMovementAuditTestCase.test_anomaly_detection)", "<PERSON><PERSON>(sudo cat:*)", "Read(//etc/systemd/system/**)", "<PERSON>sh(sudo cp:*)", "Bash(sudo chown:*)", "Bash(sudo chmod:*)", "Bash(sudo systemctl:*)", "<PERSON><PERSON>(chmod:*)", "Bash(sudo find /etc/nginx -name \"*.conf\" -type f)", "Bash(sudo find /etc/nginx/sites-available -name \"*\" -type f)", "Bash(sudo find /etc/nginx/sites-enabled -name \"*\" -type f)", "Read(//etc/nginx/sites-enabled/**)", "Read(//etc/nginx/sites-available/**)", "<PERSON><PERSON>(sudo nginx:*)", "Bash(echo $VIRTUAL_ENV)", "Bash(sudo lsof:*)", "Bash(sudo ls:*)", "Bash(sudo touch:*)", "<PERSON><PERSON>(sudo apt:*)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python test_password_reset_simple.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python test_employee_system.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python manage.py runserver 0.0.0.0:8000)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python manage.py shell:*)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/zayyrah/manage.py makemigrations inventory)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/zayyrah/manage.py runserver 0.0.0.0:8000)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/test_category_system.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/populate_pakistani_categories.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python /home/<USER>/ChannabPOS/verify_category_structure.py)", "Bash(/home/<USER>/ChannabPOS/.venv/bin/python:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(echo:*)", "Bash(sudo tail:*)"], "deny": [], "ask": []}}