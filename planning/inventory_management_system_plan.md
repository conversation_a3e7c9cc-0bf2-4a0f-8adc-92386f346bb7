# Comprehensive Inventory Management System Planning Document

## 1. Overview

**Objective**: Design and implement a comprehensive multi-level inventory management system for the Django POS application that supports hierarchical categorization, detailed product management, batch-level stock tracking, and seamless integration with existing sales functionality.

**Scope**:
- Multi-level category system (Category → Sub-category → Sub-sub-category)
- Enhanced product management with detailed attributes
- Advanced stock management with batch tracking, expiry dates, and FIFO/LIFO allocation
- Shop/location-based inventory tracking
- Integration with existing sales and customer management systems
- Comprehensive reporting and analytics

**Dependencies**:
- Django 3.2.16 with PostgreSQL database
- Django REST Framework with JWT authentication
- Existing accounts, customers, and pos apps
- Current User model with shop_name field for business accounts

## 2. Existing Code Review

**Current Implementation Analysis**:

**Strengths of Existing System**:
- Basic product categorization with ProductCategory model
- FIFO stock allocation system with ProductStockEntry
- Integration with sales through SaleItem model
- Multi-tenant architecture (owner-based segregation)
- JWT authentication and proper permissions
- REST API structure with comprehensive serializers

**Reusable Components**:
- User model (accounts.User) - already supports business accounts with shop_name
- Customer model - well-designed with financial tracking
- Sale and SaleItem models - comprehensive sales tracking
- API infrastructure - JWT auth, pagination, filtering
- Permission system - IsOwnerPermission class

**Identified Limitations**:
- Single-level category system
- Limited product attributes (no variants, images, descriptions)
- No supplier management
- No reorder point/minimum stock level alerts
- No location/warehouse management
- Basic stock entry system without advanced batch management
- No barcode/QR code support
- Limited reporting capabilities

**Optimization Opportunities**:
- Database indexing for frequently queried fields
- Query optimization with select_related/prefetch_related
- Caching for category trees and product listings
- Bulk operations for stock adjustments

## 3. Proposed Changes

### 3.1 Database Layer

**New Models to Create**:

```python
# inventory/models.py

class Category(models.Model):
    """Multi-level category system with tree structure"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=120)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    level = models.PositiveIntegerField(default=0)  # 0=root, 1=sub, 2=sub-sub
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)
    image = models.ImageField(upload_to='categories/', blank=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['level', 'sort_order', 'name']
        unique_together = ('owner', 'parent', 'name')
        indexes = [
            models.Index(fields=['owner', 'parent', 'is_active']),
            models.Index(fields=['level', 'sort_order']),
        ]

class Supplier(models.Model):
    """Supplier/Vendor management"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='suppliers')
    name = models.CharField(max_length=255)
    contact_person = models.CharField(max_length=100, blank=True)
    phone = models.CharField(max_length=15, blank=True)
    email = models.EmailField(blank=True)
    address = models.TextField(blank=True)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    rating = models.PositiveIntegerField(default=5, validators=[MinValueValidator(1), MaxValueValidator(5)])
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class Location(models.Model):
    """Storage locations/warehouses"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='locations')
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=20)
    location_type = models.CharField(max_length=20, choices=[
        ('warehouse', 'Warehouse'),
        ('store', 'Store'),
        ('section', 'Section')
    ], default='store')
    address = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('owner', 'code')

class ProductBrand(models.Model):
    """Product brands"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='brands')
    name = models.CharField(max_length=100)
    logo = models.ImageField(upload_to='brands/', blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('owner', 'name')

class EnhancedProduct(models.Model):
    """Enhanced product model replacing existing Product"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='enhanced_products')
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    brand = models.ForeignKey(ProductBrand, on_delete=models.SET_NULL, null=True, blank=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)

    # Basic Information
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    sku = models.CharField(max_length=64, blank=True)
    barcode = models.CharField(max_length=50, blank=True)
    qr_code = models.CharField(max_length=100, blank=True)

    # Pricing
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, default=0.00)
    selling_price = models.DecimalField(max_digits=12, decimal_places=2)
    wholesale_price = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)
    minimum_selling_price = models.DecimalField(max_digits=12, decimal_places=2, blank=True, null=True)

    # Stock Management
    track_stock = models.BooleanField(default=True)
    allow_negative_stock = models.BooleanField(default=False)
    reorder_level = models.PositiveIntegerField(default=5)
    maximum_stock_level = models.PositiveIntegerField(blank=True, null=True)

    # Physical Attributes
    unit = models.CharField(max_length=24, choices=Product.Unit.choices, default=Product.Unit.EACH)
    unit_custom_label = models.CharField(max_length=32, blank=True)
    weight = models.DecimalField(max_digits=8, decimal_places=3, blank=True, null=True)
    dimensions = models.CharField(max_length=50, blank=True, help_text="L×W×H")

    # Tax and Accounting
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_exempt = models.BooleanField(default=False)

    # Status and Metadata
    is_active = models.BooleanField(default=True)
    is_featured = models.BooleanField(default=False)
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        unique_together = [('owner', 'sku'), ('owner', 'barcode')]
        indexes = [
            models.Index(fields=['owner', 'is_active']),
            models.Index(fields=['category', 'is_active']),
            models.Index(fields=['sku']),
            models.Index(fields=['barcode']),
            models.Index(fields=['reorder_level']),
        ]

class ProductImage(models.Model):
    """Product images"""
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='images')
    image = models.ImageField(upload_to='products/')
    is_primary = models.BooleanField(default=False)
    alt_text = models.CharField(max_length=200, blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['sort_order', 'id']

class StockBatch(models.Model):
    """Enhanced stock batch tracking"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='stock_batches')
    location = models.ForeignKey(Location, on_delete=models.CASCADE, related_name='stock_batches')
    supplier = models.ForeignKey(Supplier, on_delete=models.SET_NULL, null=True, blank=True)

    # Batch Information
    batch_number = models.CharField(max_length=50)
    purchase_order_number = models.CharField(max_length=50, blank=True)

    # Quantities
    quantity_received = models.PositiveIntegerField()
    quantity_available = models.PositiveIntegerField()
    quantity_reserved = models.PositiveIntegerField(default=0)
    quantity_damaged = models.PositiveIntegerField(default=0)

    # Pricing and Dates
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2)
    received_date = models.DateField(default=timezone.now)
    manufacture_date = models.DateField(blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True)

    # Status
    status = models.CharField(max_length=20, choices=[
        ('active', 'Active'),
        ('reserved', 'Reserved'),
        ('expired', 'Expired'),
        ('damaged', 'Damaged'),
        ('sold_out', 'Sold Out')
    ], default='active')

    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('owner', 'batch_number', 'product')
        indexes = [
            models.Index(fields=['owner', 'product', 'status']),
            models.Index(fields=['expiry_date']),
            models.Index(fields=['received_date']),
        ]

class StockMovement(models.Model):
    """Track all stock movements"""
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    product = models.ForeignKey(EnhancedProduct, on_delete=models.CASCADE, related_name='stock_movements')
    batch = models.ForeignKey(StockBatch, on_delete=models.CASCADE, related_name='movements')
    location = models.ForeignKey(Location, on_delete=models.CASCADE)

    movement_type = models.CharField(max_length=20, choices=[
        ('purchase', 'Purchase'),
        ('sale', 'Sale'),
        ('return', 'Return'),
        ('adjustment', 'Adjustment'),
        ('transfer', 'Transfer'),
        ('damage', 'Damage'),
        ('expired', 'Expired')
    ])

    quantity = models.IntegerField()  # Can be negative for outgoing
    reference_number = models.CharField(max_length=50, blank=True)
    reference_type = models.CharField(max_length=20, blank=True)  # 'sale', 'purchase_order', etc.
    reference_id = models.PositiveIntegerField(blank=True, null=True)

    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='stock_movements_created')

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner', 'product', 'created_at']),
            models.Index(fields=['movement_type', 'created_at']),
        ]
```

**Migration Strategy**:
1. Create new inventory app with enhanced models
2. Data migration script to transfer existing ProductCategory → Category
3. Data migration script to transfer existing Product → EnhancedProduct
4. Data migration script to convert ProductStockEntry → StockBatch
5. Update foreign key relationships in Sale and SaleItem models
6. Gradual deprecation of old models with fallback support

**Index Optimization**:
- Multi-column indexes for owner-based queries
- Indexes on frequently filtered fields (category, is_active, stock levels)
- Full-text search indexes for product names and descriptions
- Composite indexes for complex filtering scenarios

### 3.2 Business Logic Layer

**ViewSet Specifications**:

```python
# inventory/api_views.py

class CategoryViewSet(viewsets.ModelViewSet):
    """Complete category management with tree operations"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['parent', 'level', 'is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'level', 'sort_order', 'created_at']

    def get_queryset(self):
        return Category.objects.filter(owner=self.request.user).select_related('parent')

class EnhancedProductViewSet(viewsets.ModelViewSet):
    """Advanced product management with stock integration"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'brand', 'supplier', 'is_active', 'track_stock']
    search_fields = ['name', 'description', 'sku', 'barcode', 'tags']
    ordering_fields = ['name', 'selling_price', 'created_at', 'reorder_level']

    def get_queryset(self):
        return EnhancedProduct.objects.filter(owner=self.request.user).select_related(
            'category', 'brand', 'supplier'
        ).prefetch_related('images', 'stock_batches')

    @action(detail=True, methods=['get'])
    def stock_summary(self, request, pk=None):
        """Get comprehensive stock summary for product"""
        # Implementation for stock levels across all locations and batches

    @action(detail=True, methods=['post'])
    def adjust_stock(self, request, pk=None):
        """Manual stock adjustment with reason tracking"""
        # Implementation for stock adjustments with movement tracking

class StockBatchViewSet(viewsets.ModelViewSet):
    """Batch-level stock management"""
    permission_classes = [IsAuthenticated, IsOwnerPermission]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product', 'location', 'supplier', 'status']
    search_fields = ['batch_number', 'purchase_order_number']
    ordering_fields = ['received_date', 'expiry_date', 'quantity_available']

    @action(detail=False, methods=['get'])
    def expiring_soon(self, request):
        """Get batches expiring within specified days"""
        # Implementation for expiry tracking

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get batches below reorder level"""
        # Implementation for reorder alerts
```

**Signal Handlers**:
```python
# inventory/signals.py

@receiver(post_save, sender=StockMovement)
def update_stock_levels(sender, instance, created, **kwargs):
    """Automatically update stock levels when movements are recorded"""
    # Update batch quantities and product stock levels

@receiver(pre_save, sender=EnhancedProduct)
def generate_sku(sender, instance, **kwargs):
    """Auto-generate SKU if not provided"""
    # SKU generation logic based on category and sequence
```

### 3.3 API Layer

**Endpoint Specifications**:

```
# Categories
GET    /api/v1/inventory/categories/              # List categories (tree view support)
POST   /api/v1/inventory/categories/              # Create category
GET    /api/v1/inventory/categories/{id}/         # Category details
PUT    /api/v1/inventory/categories/{id}/         # Update category
DELETE /api/v1/inventory/categories/{id}/         # Delete category (if no products)
GET    /api/v1/inventory/categories/tree/         # Full category tree
POST   /api/v1/inventory/categories/{id}/move/    # Move category to different parent

# Products
GET    /api/v1/inventory/products/                # List products with advanced filtering
POST   /api/v1/inventory/products/                # Create product
GET    /api/v1/inventory/products/{id}/           # Product details with stock info
PUT    /api/v1/inventory/products/{id}/           # Update product
DELETE /api/v1/inventory/products/{id}/           # Delete product (if no stock)
GET    /api/v1/inventory/products/{id}/stock/     # Stock summary across all locations
POST   /api/v1/inventory/products/{id}/adjust/    # Manual stock adjustment
GET    /api/v1/inventory/products/low-stock/      # Products below reorder level
GET    /api/v1/inventory/products/search/         # Advanced product search
POST   /api/v1/inventory/products/bulk-update/    # Bulk product updates

# Stock Batches
GET    /api/v1/inventory/batches/                 # List stock batches
POST   /api/v1/inventory/batches/                 # Create stock batch
GET    /api/v1/inventory/batches/{id}/            # Batch details
PUT    /api/v1/inventory/batches/{id}/            # Update batch
GET    /api/v1/inventory/batches/expiring/        # Expiring batches
GET    /api/v1/inventory/batches/by-product/{id}/ # Batches for specific product

# Stock Movements
GET    /api/v1/inventory/movements/               # Stock movement history
POST   /api/v1/inventory/movements/               # Record movement
GET    /api/v1/inventory/movements/by-product/{id}/ # Movement history for product
GET    /api/v1/inventory/movements/summary/       # Movement summary/analytics

# Suppliers
GET    /api/v1/inventory/suppliers/               # List suppliers
POST   /api/v1/inventory/suppliers/               # Create supplier
GET    /api/v1/inventory/suppliers/{id}/          # Supplier details
PUT    /api/v1/inventory/suppliers/{id}/          # Update supplier
GET    /api/v1/inventory/suppliers/{id}/products/ # Products from supplier

# Locations
GET    /api/v1/inventory/locations/               # List locations
POST   /api/v1/inventory/locations/               # Create location
GET    /api/v1/inventory/locations/{id}/          # Location details
GET    /api/v1/inventory/locations/{id}/stock/    # Stock summary for location

# Reports
GET    /api/v1/inventory/reports/stock-valuation/ # Stock valuation report
GET    /api/v1/inventory/reports/movement-summary/ # Stock movement analytics
GET    /api/v1/inventory/reports/abc-analysis/     # ABC analysis of products
GET    /api/v1/inventory/reports/dead-stock/       # Dead stock identification
```

**Authentication & Permissions**:
- JWT token authentication required for all endpoints
- Owner-based permission system (users can only access their inventory)
- Role-based permissions for different inventory operations
- Rate limiting for bulk operations

**Request/Response Examples**:

```json
// Create Product Request
POST /api/v1/inventory/products/
{
    "name": "Premium Coffee Beans",
    "description": "High-quality arabica coffee beans",
    "category": 12,
    "brand": 5,
    "supplier": 3,
    "sku": "COF-001",
    "barcode": "1234567890123",
    "purchase_price": "15.50",
    "selling_price": "25.00",
    "reorder_level": 20,
    "unit": "kg",
    "weight": "1.000",
    "tax_rate": "5.00",
    "tags": "coffee, premium, organic"
}

// Stock Adjustment Request
POST /api/v1/inventory/products/15/adjust/
{
    "location": 1,
    "adjustment_type": "increase",
    "quantity": 50,
    "reason": "New stock arrival",
    "reference_number": "PO-2024-001",
    "notes": "Fresh stock from supplier XYZ"
}

// Low Stock Response
GET /api/v1/inventory/products/low-stock/
{
    "success": true,
    "data": {
        "count": 3,
        "results": [
            {
                "id": 15,
                "name": "Premium Coffee Beans",
                "sku": "COF-001",
                "current_stock": 8,
                "reorder_level": 20,
                "status": "critical",
                "last_movement_date": "2024-01-15T10:30:00Z"
            }
        ]
    }
}
```

### 3.4 Performance Considerations

**Query Optimization**:
- Use select_related() for foreign key lookups (category, supplier, brand)
- Use prefetch_related() for reverse foreign keys (images, batches, movements)
- Implement database-level aggregations for stock calculations
- Use raw queries for complex reporting scenarios

**Caching Strategy**:
- Redis caching for category trees (TTL: 1 hour)
- Product listing cache with cache invalidation on updates (TTL: 30 minutes)
- Stock level caching with automatic invalidation on movements (TTL: 5 minutes)
- Report result caching for expensive analytics queries (TTL: 6 hours)

**Pagination Approach**:
- Cursor-based pagination for large product listings
- Standard page number pagination for filtered results
- Custom pagination for tree structures (category hierarchy)

**Async Operations**:
- Celery tasks for bulk operations (bulk product creation, stock adjustments)
- Background tasks for stock level recalculations
- Scheduled tasks for expiry date checks and reorder point alerts
- Async image processing and optimization

## 4. Risk Assessment & Mitigation

**Security Risks**:
- **Risk**: Unauthorized access to inventory data
  **Mitigation**: Strict owner-based permission system, JWT token validation, input sanitization
- **Risk**: Data integrity issues with concurrent stock movements
  **Mitigation**: Database-level constraints, atomic transactions, optimistic locking
- **Risk**: File upload vulnerabilities (product images)
  **Mitigation**: File type validation, size limits, secure storage, antivirus scanning

**Performance Risks**:
- **Risk**: Slow queries on large product datasets
  **Mitigation**: Proper indexing strategy, query optimization, pagination
- **Risk**: Stock calculation bottlenecks
  **Mitigation**: Denormalized stock fields, background recalculation, caching
- **Risk**: Report generation timeouts
  **Mitigation**: Async report generation, result caching, query optimization

**Data Integrity Risks**:
- **Risk**: Stock level inconsistencies
  **Mitigation**: Atomic stock transactions, periodic reconciliation, audit logs
- **Risk**: Category tree corruption
  **Mitigation**: Tree integrity checks, validation constraints, backup/restore procedures

**Backward Compatibility**:
- **Risk**: Breaking changes to existing API
  **Mitigation**: API versioning, gradual migration, fallback support for old endpoints

## 5. Frontend Integration Guide

**API Usage Examples**:

```javascript
// Fetch category tree
const categoryTree = await fetch('/api/v1/inventory/categories/tree/', {
    headers: { 'Authorization': `Bearer ${token}` }
}).then(r => r.json());

// Create product with image
const formData = new FormData();
formData.append('name', 'New Product');
formData.append('category', '12');
formData.append('selling_price', '29.99');
formData.append('image', fileInput.files[0]);

const product = await fetch('/api/v1/inventory/products/', {
    method: 'POST',
    headers: { 'Authorization': `Bearer ${token}` },
    body: formData
}).then(r => r.json());

// Real-time stock updates using WebSocket
const stockSocket = new WebSocket(`wss://api.example.com/ws/stock/`);
stockSocket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    updateStockDisplay(data.product_id, data.new_quantity);
};
```

**Error Handling Guide**:
```javascript
// Standard error response format
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "selling_price": ["This field is required."],
        "sku": ["Product with this SKU already exists."]
    },
    "error_code": "VALIDATION_ERROR"
}

// Handle different error types
const handleApiResponse = async (response) => {
    const data = await response.json();

    if (!data.success) {
        switch (response.status) {
            case 400:
                showValidationErrors(data.errors);
                break;
            case 401:
                redirectToLogin();
                break;
            case 403:
                showAccessDeniedMessage();
                break;
            case 404:
                showNotFoundMessage();
                break;
            case 429:
                showRateLimitMessage();
                break;
            default:
                showGenericError(data.message);
        }
    }
    return data;
};
```

## 6. Implementation Checklist

**Phase 1: Core Infrastructure (Week 1-2)**
- [ ] Create new inventory Django app with enhanced models
- [ ] Implement database migrations with data transfer scripts
- [ ] Set up API viewsets with basic CRUD operations
- [ ] Configure proper database indexes and constraints
- [ ] Implement JWT authentication and owner-based permissions
- [ ] Create basic serializers with validation rules

**Phase 2: Advanced Features (Week 3-4)**
- [ ] Implement multi-level category tree operations
- [ ] Build batch-level stock tracking system
- [ ] Create stock movement logging and audit trail
- [ ] Develop supplier and location management
- [ ] Implement product image handling with optimization
- [ ] Build advanced search and filtering capabilities

**Phase 3: Integration & Analytics (Week 5)**
- [ ] Integrate with existing sales system for automatic stock updates
- [ ] Implement real-time stock level calculations
- [ ] Build comprehensive reporting and analytics endpoints
- [ ] Create low stock and expiry date alert systems
- [ ] Implement bulk operations for mass updates
- [ ] Add barcode/QR code scanning support

**Phase 4: Performance & Polish (Week 6)**
- [ ] Implement Redis caching layer for improved performance
- [ ] Set up Celery for background task processing
- [ ] Optimize database queries and add monitoring
- [ ] Create comprehensive API documentation with examples
- [ ] Implement rate limiting and security hardening
- [ ] Add comprehensive unit and integration tests

**Phase 5: Migration & Deployment (Week 7)**
- [ ] Create migration scripts for existing data
- [ ] Set up staging environment for testing
- [ ] Perform load testing with realistic data volumes
- [ ] Create deployment scripts and CI/CD pipeline
- [ ] Train end users and create user documentation
- [ ] Deploy to production with rollback capability

**Quality Gates**:
- Unit test coverage > 90% for all new models and views
- API response times < 200ms for standard operations
- Database query optimization (no N+1 queries)
- Security audit passed (SQL injection, XSS, CSRF protection)
- Load testing passed (1000+ concurrent users)
- Data integrity verified (stock calculations accurate)

**Key Files and Locations**:
- Models: `/home/<USER>/ChannabPOS/zayyrah/inventory/models.py`
- API Views: `/home/<USER>/ChannabPOS/zayyrah/inventory/api_views.py`
- Serializers: `/home/<USER>/ChannabPOS/zayyrah/inventory/serializers.py`
- URLs: `/home/<USER>/ChannabPOS/zayyrah/inventory/api_urls.py`
- Migrations: `/home/<USER>/ChannabPOS/zayyrah/inventory/migrations/`
- Tests: `/home/<USER>/ChannabPOS/zayyrah/inventory/tests/`

This comprehensive plan provides a robust foundation for implementing a world-class inventory management system that seamlessly integrates with the existing POS application while providing advanced features for modern business needs. The system is designed to be scalable, secure, and performant while maintaining backward compatibility with existing functionality.