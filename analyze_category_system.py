#!/usr/bin/env python3
"""
Analyze Category System: Ownership, Sharing, and Product Relationships
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import Category
from pos.models import Product
from django.db import transaction

User = get_user_model()

def analyze_category_system():
    """Analyze how categories work with ownership and product relationships"""
    print("🔍 Analyzing Category System: Ownership & Product Relationships")
    print("="*70)

    # Get existing users
    users = User.objects.all()
    print(f"\n👥 Users in System: {users.count()}")
    for user in users:
        categories_count = Category.objects.filter(owner=user).count()
        products_count = Product.objects.filter(owner=user).count()
        print(f"   📱 {user.mobile_number}: {categories_count} categories, {products_count} products")

    # Check current category ownership model
    print(f"\n📂 Category Ownership Analysis:")
    print("-" * 40)

    # Get category model field information
    category_owner_field = Category._meta.get_field('owner')
    print(f"   🔗 Owner Field: {category_owner_field}")
    print(f"   🔗 Owner Relationship: {category_owner_field.related_model}")
    print(f"   🔗 On Delete Behavior: {category_owner_field.remote_field.on_delete}")

    # Check if categories are shared or per-user
    all_categories = Category.objects.all()
    unique_owners = all_categories.values('owner').distinct().count()
    print(f"   📊 Total Categories: {all_categories.count()}")
    print(f"   👥 Unique Owners: {unique_owners}")

    if unique_owners > 1:
        print("   ✅ Categories are PER-USER (each business has their own)")
    else:
        print("   ⚠️  Categories are currently owned by single user")

    # Analyze product-category relationship
    print(f"\n📦 Product-Category Relationship Analysis:")
    print("-" * 45)

    # Check if Product model has category field
    try:
        product_category_field = Product._meta.get_field('category')
        print(f"   🔗 Product.category Field: EXISTS")
        print(f"   🔗 Field Type: {type(product_category_field)}")
        print(f"   🔗 Related Model: {product_category_field.related_model}")
        print(f"   🔗 On Delete Behavior: {product_category_field.remote_field.on_delete}")
        print(f"   🔗 Null Allowed: {product_category_field.null}")
        print(f"   🔗 Blank Allowed: {product_category_field.blank}")
    except:
        print("   ❌ Product.category Field: NOT FOUND")
        print("   💡 Products are not directly linked to categories")

    # Check inventory.models.EnhancedProduct
    try:
        from inventory.models import EnhancedProduct
        enhanced_category_field = EnhancedProduct._meta.get_field('category')
        print(f"\n   🔗 EnhancedProduct.category Field: EXISTS")
        print(f"   🔗 Field Type: {type(enhanced_category_field)}")
        print(f"   🔗 On Delete Behavior: {enhanced_category_field.remote_field.on_delete}")
        print(f"   🔗 Null Allowed: {enhanced_category_field.null}")
        print(f"   🔗 Blank Allowed: {enhanced_category_field.blank}")
    except Exception as e:
        print(f"   ❌ EnhancedProduct.category Field: {e}")

    # Test what happens when category is deleted
    print(f"\n🗑️  Category Deletion Impact Test:")
    print("-" * 35)

    try:
        with transaction.atomic():
            # Create test user
            test_user, created = User.objects.get_or_create(
                mobile_number='TEST123',
                defaults={'first_name': 'Test', 'last_name': 'User'}
            )

            # Create test category
            test_category = Category.objects.create(
                owner=test_user,
                name="Test Category",
                description="For deletion testing"
            )
            print(f"   ✅ Created test category: {test_category.name}")

            # Try to create products linked to this category
            try:
                # Test with regular Product model
                test_product = Product.objects.create(
                    owner=test_user,
                    name="Test Product",
                    selling_price=100.00,
                    category=test_category  # This might fail if no category field
                )
                print(f"   ✅ Created test product linked to category")

                # Now test deletion
                print(f"   🧪 Testing category deletion with linked products...")
                test_category.delete()

                # Check what happened to product
                test_product.refresh_from_db()
                print(f"   📦 Product after category deletion:")
                print(f"      - Product exists: {test_product.pk is not None}")
                print(f"      - Product category: {test_product.category}")

            except Exception as e:
                print(f"   ❌ Product-category linking failed: {e}")
                print(f"   💡 Regular Product model may not have category field")

            # Test with EnhancedProduct if available
            try:
                from inventory.models import EnhancedProduct

                enhanced_product = EnhancedProduct.objects.create(
                    owner=test_user,
                    name="Enhanced Test Product",
                    selling_price=150.00,
                    category=test_category
                )
                print(f"   ✅ Created enhanced product linked to category")

                # Test deletion behavior
                print(f"   🧪 Testing category deletion with enhanced products...")
                category_id = test_category.id
                test_category.delete()

                # Check what happened to enhanced product
                enhanced_product.refresh_from_db()
                print(f"   📦 Enhanced Product after category deletion:")
                print(f"      - Product exists: {enhanced_product.pk is not None}")
                print(f"      - Product category: {enhanced_product.category}")

            except Exception as e:
                print(f"   ❌ Enhanced product test failed: {e}")

            # Force rollback
            raise Exception("Intentional rollback to cleanup test data")

    except Exception as e:
        if "Intentional rollback" in str(e):
            print(f"   🧹 Test data cleaned up")
        else:
            print(f"   ❌ Test failed: {e}")

    # Recommendations
    print(f"\n💡 System Analysis & Recommendations:")
    print("=" * 45)

    print(f"\n1️⃣  CATEGORY OWNERSHIP:")
    print(f"   📋 Current Status: Each business/user has their own categories")
    print(f"   ✅ This is CORRECT for multi-tenant system")
    print(f"   💼 Each business can customize their categories")
    print(f"   🔒 Categories are NOT shared between businesses")

    print(f"\n2️⃣  CATEGORY SHARING OPTIONS:")
    print(f"   Option A: Keep current model (Recommended)")
    print(f"      ✅ Each business has complete control")
    print(f"      ✅ Can customize for their specific needs")
    print(f"      ✅ No conflicts between businesses")
    print(f"   ")
    print(f"   Option B: Shared categories with customization")
    print(f"      ⚠️  Would require system-wide categories")
    print(f"      ⚠️  Plus user-specific categories")
    print(f"      ⚠️  More complex to implement")

    print(f"\n3️⃣  PRODUCT-CATEGORY RELATIONSHIP:")
    print(f"   🔗 Products are linked to categories via Foreign Key")
    print(f"   ⚠️  Deletion behavior depends on on_delete setting:")
    print(f"      - SET_NULL: Products remain, category becomes null")
    print(f"      - CASCADE: Products are deleted with category")
    print(f"      - PROTECT: Prevents category deletion if products exist")

    print(f"\n4️⃣  RECOMMENDED SETUP:")
    print(f"   ✅ Keep per-user categories (current system)")
    print(f"   ✅ Use SET_NULL for product-category relationship")
    print(f"   ✅ Provide category migration tools")
    print(f"   ✅ Allow category templates for new businesses")

def check_current_deletion_behavior():
    """Check what actually happens when categories are deleted"""
    print(f"\n🔍 Checking Current Deletion Behavior:")
    print("-" * 40)

    # Check the actual foreign key settings
    try:
        from inventory.models import EnhancedProduct
        category_field = EnhancedProduct._meta.get_field('category')
        on_delete = category_field.remote_field.on_delete
        print(f"   EnhancedProduct.category on_delete: {on_delete}")

        if hasattr(on_delete, '__name__'):
            behavior = on_delete.__name__
        else:
            behavior = str(on_delete)

        print(f"   Deletion Behavior: {behavior}")

        if 'SET_NULL' in behavior:
            print(f"   ✅ Products will remain, category will be set to NULL")
        elif 'CASCADE' in behavior:
            print(f"   ⚠️  Products will be DELETED with category")
        elif 'PROTECT' in behavior:
            print(f"   🛡️  Category deletion will be PREVENTED if products exist")
        else:
            print(f"   ❓ Unknown behavior: {behavior}")

    except Exception as e:
        print(f"   ❌ Could not determine deletion behavior: {e}")

    # Check regular Product model too
    try:
        from pos.models import Product
        if hasattr(Product, 'category'):
            category_field = Product._meta.get_field('category')
            on_delete = category_field.remote_field.on_delete
            print(f"   Product.category on_delete: {on_delete}")
        else:
            print(f"   Regular Product model has no category field")
    except Exception as e:
        print(f"   Regular Product category check failed: {e}")

if __name__ == "__main__":
    analyze_category_system()
    check_current_deletion_behavior()