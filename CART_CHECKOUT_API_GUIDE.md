# Cart & Checkout API Guide for Flutter App

## Overview
This guide provides two approaches for cart management:
1. **Simple Cart APIs** - Recommended for mobile apps (new)
2. **Transaction-based APIs** - Existing POS system (advanced)

## 🛒 Simple Cart APIs (Recommended for Flutter)

### Base URL: `http://54.166.200.11:8002/inventory/cart/api/`

### Authentication
All endpoints require JWT token in Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

---

## 📱 Cart Management

### 1. Get Current Cart
**GET** `/`

**Response:**
```json
{
  "success": true,
  "cart": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "customer_id": 45,
    "customer_name": "<PERSON>",
    "notes": ""
  },
  "items": [
    {
      "id": 1,
      "product_id": 67,
      "product_name": "Coca Cola 500ml",
      "sku": "CC500",
      "barcode": "1234567890",
      "quantity": 2.0,
      "unit_price": 25.00,
      "original_price": 25.00,
      "discount_percent": 0.00,
      "discount_amount": 0.00,
      "line_total": 50.00,
      "unit_label": "bottle",
      "image_url": "http://example.com/image.jpg"
    }
  ],
  "totals": {
    "items_count": 2,
    "subtotal": 50.00,
    "tax_total": 0.00,
    "discount_total": 0.00,
    "total_amount": 50.00
  }
}
```

### 2. Create Cart / Set Customer
**POST** `/`

**Body:**
```json
{
  "customer_id": 45,  // optional
  "notes": "Special order"  // optional
}
```

### 3. Clear Cart
**DELETE** `/`

---

## 🛒 Cart Items

### 1. Add Item to Cart
**POST** `/items/`

**Body:**
```json
{
  "product_id": 67,
  "quantity": 2.0,
  "custom_price": 24.00,  // optional
  "discount_percent": 5.0,  // optional
  "discount_amount": 2.50   // optional
}
```

### 2. Update Item Quantity
**PUT** `/items/`

**Body:**
```json
{
  "item_id": 1,
  "quantity": 3.0
}
```

### 3. Remove Item
**DELETE** `/items/`

**Body:**
```json
{
  "item_id": 1
}
```

---

## ⚡ Quick Operations

### 1. Quick Add by Barcode/ID
**POST** `/quick-add/`

**Body Option 1 (Barcode):**
```json
{
  "barcode": "1234567890",
  "quantity": 1.0  // optional, defaults to 1
}
```

**Body Option 2 (Product ID):**
```json
{
  "product_id": 67,
  "quantity": 2.0
}
```

### 2. Quick Checkout
**POST** `/checkout/`

**Body:**
```json
{
  "cash_amount": 100.00,     // optional
  "credit_amount": 0.00,     // optional
  "card_amount": 0.00        // optional
}
```

**Response:**
```json
{
  "success": true,
  "message": "Checkout completed successfully",
  "transaction": {
    "id": 123,
    "transaction_number": "TXN-2025-001",
    "total_amount": 50.00,
    "payment_method": "cash",
    "change_amount": 50.00,
    "items_count": 2,
    "completed_at": "2025-09-21T16:30:00Z",
    "customer_name": "John Doe"
  }
}
```

---

## 🏪 Advanced Transaction APIs (Existing)

### Base URL: `http://54.166.200.11:8002/inventory/pos/api/`

### Products
- **GET** `/products/` - Get all POS products
- **GET** `/products/?category=123&search=cola` - Filter products

### Customers
- **GET** `/customers/` - Get customers for POS
- **GET** `/customers/?search=john` - Search customers

### Transaction Management
- **POST** `/transactions/` - Create new transaction
- **GET** `/transactions/{id}/` - Get transaction details

### Transaction Items
- **POST** `/transactions/{id}/items/` - Add item
- **PUT** `/transactions/{id}/items/{item_id}/` - Update item
- **DELETE** `/transactions/{id}/items/{item_id}/` - Remove item

### Payment & Completion
- **POST** `/transactions/{id}/payment/` - Process payment
- **POST** `/transactions/{id}/complete/` - Complete transaction

---

## 📊 Error Handling

All APIs return consistent error format:
```json
{
  "success": false,
  "error": "Error description"
}
```

Common HTTP status codes:
- `200` - Success
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid/missing token)
- `404` - Not Found
- `500` - Internal Server Error

---

## 🎯 Recommended Flutter Implementation Flow

### 1. App Startup
```dart
// Get current cart state
GET /inventory/cart/api/
```

### 2. Browse Products
```dart
// Get products
GET /inventory/pos/api/products/

// Search products
GET /inventory/pos/api/products/?search=cola
```

### 3. Add to Cart (Option A - Simple)
```dart
// Quick add by barcode (for barcode scanner)
POST /inventory/cart/api/quick-add/
{
  "barcode": "1234567890",
  "quantity": 1
}
```

### 4. Add to Cart (Option B - Advanced)
```dart
// Add with custom price/discount
POST /inventory/cart/api/items/
{
  "product_id": 67,
  "quantity": 2.0,
  "discount_percent": 5.0
}
```

### 5. Update Cart
```dart
// Update quantity
PUT /inventory/cart/api/items/
{
  "item_id": 1,
  "quantity": 3.0
}

// Remove item
DELETE /inventory/cart/api/items/
{
  "item_id": 1
}
```

### 6. Checkout
```dart
// One-step checkout
POST /inventory/cart/api/checkout/
{
  "cash_amount": 100.00
}
```

### 7. After Checkout
```dart
// Cart is automatically cleared after successful checkout
// Get fresh cart state for next transaction
GET /inventory/cart/api/
```

---

## 🔧 Authentication Setup

### Get JWT Token
**POST** `http://54.166.200.11:8002/api/v1/accounts/login/`

**Body:**
```json
{
  "mobile_number": "**********",
  "password": "testpass123"
}
```

**Response:**
```json
{
  "success": true,
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

Use the `access` token in Authorization header for all API calls.

---

## 💡 Key Benefits of New Cart APIs

1. **Simplified**: Single-step operations vs multi-step transaction flow
2. **Mobile-Optimized**: Designed for Flutter app patterns
3. **Barcode Support**: Quick add by barcode scanning
4. **Real-time Cart**: Always get current cart state
5. **One-step Checkout**: Complete payment + transaction in single call
6. **Backward Compatible**: Original transaction APIs still available

Choose the **Simple Cart APIs** for Flutter development unless you need the advanced transaction features!