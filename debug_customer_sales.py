#!/usr/bin/env python3
"""
Debug the Customer Sales API step by step
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from customers.models import Customer
from inventory.models import POSTransaction
from accounts.models import User

def debug_customer_sales():
    print("=== DEBUG CUSTOMER SALES ===\n")

    # Get the user and customer
    user = User.objects.filter(mobile_number='***********').first()
    customer = Customer.objects.filter(id=26, owner=user).first()

    print(f"User: {user.mobile_number}")
    print(f"Customer: {customer.display_name if customer else 'Not found'}")

    if customer:
        # Check transactions for this customer
        transactions = POSTransaction.objects.filter(
            owner=user,
            customer=customer,
            status='completed'
        )

        print(f"Transactions found: {transactions.count()}")

        for i, transaction in enumerate(transactions[:3]):  # Test first 3
            print(f"\nTransaction {i+1}:")
            print(f"  - ID: {transaction.id}")
            print(f"  - Number: {transaction.transaction_number}")
            print(f"  - Status: {transaction.status}")
            print(f"  - Items count: {transaction.items.count()}")

            try:
                # Test the property that's causing issues
                total_items = transaction.total_items
                print(f"  - total_items: {total_items} (type: {type(total_items)})")

                # Test conversion to float
                total_items_float = float(total_items)
                print(f"  - float(total_items): {total_items_float}")

            except Exception as e:
                print(f"  - ERROR in total_items(): {e}")

            # Check individual items
            print(f"  - Items:")
            for item in transaction.items.all():
                print(f"    * {item.product.name}: {item.quantity} (type: {type(item.quantity)})")

    print(f"\n=== DEBUG COMPLETE ===")

if __name__ == '__main__':
    debug_customer_sales()