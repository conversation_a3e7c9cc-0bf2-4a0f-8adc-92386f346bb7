#!/usr/bin/env python3
"""
Check existing transactions to see customer status
"""

import os
import sys
import django

# Set up Django environment
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import POSTransaction
from customers.models import Customer

def check_existing_transactions():
    print("Checking Existing POS Transactions")
    print("=" * 50)

    User = get_user_model()

    # Get the test user
    try:
        user = User.objects.get(mobile_number='03478181583')
        print(f"✓ Found user: {user.mobile_number}")
    except User.DoesNotExist:
        print("✗ User not found")
        return

    # Check all transactions
    transactions = POSTransaction.objects.filter(owner=user).order_by('-created_at')

    print(f"\nTotal transactions: {transactions.count()}")
    print("\nTransaction Details:")
    print("-" * 80)

    walk_in_count = 0
    with_customer_count = 0

    for transaction in transactions:
        customer_info = "None (Walk-in)" if transaction.customer is None else transaction.customer.display_name
        if transaction.customer is None:
            walk_in_count += 1
        else:
            with_customer_count += 1

        print(f"ID: {transaction.id:2d} | {transaction.transaction_number} | Customer: {customer_info:20s} | Status: {transaction.status:10s} | Amount: Rs. {transaction.total_amount:6.0f}")

    print("-" * 80)
    print(f"Summary:")
    print(f"  • Transactions with customers: {with_customer_count}")
    print(f"  • Walk-in transactions (no customer): {walk_in_count}")

    # Check existing customers
    customers = Customer.objects.filter(owner=user)
    print(f"\nExisting Customers: {customers.count()}")

    for customer in customers:
        print(f"  • {customer.display_name} ({customer.mobile_number or 'No mobile'})")

if __name__ == '__main__':
    check_existing_transactions()