#!/usr/bin/env python3
"""
Enable fractional quantities for Fresh Milk and other liquid products
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from inventory.models import EnhancedProduct
from accounts.models import User

# Find the user
user = User.objects.filter(mobile_number='***********').first()
if not user:
    print('User not found')
    exit(1)

print(f'User: {user.mobile_number}')

# Find Fresh Milk product
fresh_milk = EnhancedProduct.objects.filter(name='Fresh Milk', owner=user).first()
if fresh_milk:
    print(f'\nBefore fix:')
    print(f'  - Fresh Milk ID: {fresh_milk.id}')
    print(f'  - Allow fractional: {fresh_milk.allow_fractional_quantities}')

    # Enable fractional quantities
    fresh_milk.allow_fractional_quantities = True
    fresh_milk.save()

    print(f'\nAfter fix:')
    print(f'  - Fresh Milk ID: {fresh_milk.id}')
    print(f'  - Allow fractional: {fresh_milk.allow_fractional_quantities}')
    print('✅ Fresh Milk now allows fractional quantities!')
else:
    print('Fresh Milk product not found')

# Also fix other liquid products that should allow fractional quantities
print(f'\nChecking other liquid products...')
liquid_units = ['Liter', 'Gallon', 'ML', 'Milliliter', 'L', 'ml']

for unit in liquid_units:
    products = EnhancedProduct.objects.filter(
        owner=user,
        unit__icontains=unit,
        allow_fractional_quantities=False
    )

    if products.exists():
        print(f'\nFound {products.count()} products with unit "{unit}" that don\'t allow fractional:')
        for p in products:
            print(f'  - {p.name} (${p.selling_price})')
            p.allow_fractional_quantities = True
            p.save()
            print(f'    ✅ Fixed: Now allows fractional quantities')

print(f'\n🎉 All liquid products now allow fractional quantities!')