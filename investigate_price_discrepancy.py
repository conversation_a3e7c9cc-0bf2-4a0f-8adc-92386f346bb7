#!/usr/bin/env python3
"""
Investigate price discrepancy between POS and customer detail views
"""

import os
import sys
import django

# Set up Django environment
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import POSTransaction
from customers.models import Customer

def investigate_price_discrepancy():
    print("🔍 Investigating Price Discrepancy")
    print("=" * 40)

    User = get_user_model()

    try:
        user = User.objects.get(mobile_number='03478181583')
        print(f"✓ Found user: {user.mobile_number}")
    except User.DoesNotExist:
        print("✗ User not found")
        return

    # Get walk-in customer
    try:
        customer = Customer.objects.get(id=24, owner=user)
        print(f"✓ Customer: {customer.display_name}")
    except Customer.DoesNotExist:
        print("✗ Customer not found")
        return

    # Get all transactions for this customer
    transactions = POSTransaction.objects.filter(customer=customer).order_by('-created_at')
    print(f"\n📊 All Transactions for {customer.display_name}:")
    print("-" * 80)

    for transaction in transactions:
        print(f"ID: {transaction.id:2d} | {transaction.transaction_number} | Status: {transaction.status:10s} | Total: Rs. {transaction.total_amount:7.2f} | Items: {transaction.total_item_count}")

        # Get detailed breakdown for each transaction
        items = transaction.items.all()
        if items:
            print(f"     Items breakdown:")
            subtotal = 0
            for item in items:
                item_total = item.line_total
                subtotal += item_total
                print(f"       - {item.product.pos_name}: {item.quantity} x Rs. {item.unit_price} = Rs. {item_total} (discount: {item.discount_percent}%)")

            print(f"     Calculated subtotal: Rs. {subtotal:.2f}")
            print(f"     Transaction subtotal: Rs. {transaction.subtotal:.2f}")
            print(f"     Tax total: Rs. {transaction.tax_total:.2f}")
            print(f"     Discount total: Rs. {transaction.discount_total:.2f}")
            print(f"     Final total: Rs. {transaction.total_amount:.2f}")

            # Check if there's a discrepancy
            calculated_total = transaction.subtotal + transaction.tax_total - transaction.discount_total
            if abs(calculated_total - transaction.total_amount) > 0.01:
                print(f"     ⚠️  DISCREPANCY: Calculated total (Rs. {calculated_total:.2f}) vs Stored total (Rs. {transaction.total_amount:.2f})")
            else:
                print(f"     ✓ Totals match")
        else:
            print(f"     No items found for this transaction")

        print()

    # Look for the specific transactions mentioned (176 vs 165)
    print(f"\n🔍 Looking for transactions with amounts around 165-176:")
    suspect_transactions = transactions.filter(total_amount__gte=160, total_amount__lte=180)

    for transaction in suspect_transactions:
        print(f"\n📋 DETAILED ANALYSIS: Transaction {transaction.transaction_number}")
        print(f"   • ID: {transaction.id}")
        print(f"   • Status: {transaction.status}")
        print(f"   • Created: {transaction.created_at}")
        print(f"   • Updated: {transaction.updated_at}")
        print(f"   • Subtotal: Rs. {transaction.subtotal}")
        print(f"   • Tax: Rs. {transaction.tax_total}")
        print(f"   • Discount: Rs. {transaction.discount_total}")
        print(f"   • TOTAL: Rs. {transaction.total_amount}")

        # Check transaction items
        items = transaction.items.all()
        print(f"   • Items ({items.count()}):")
        for item in items:
            print(f"     - {item.product.pos_name}")
            print(f"       Qty: {item.quantity}, Price: Rs. {item.unit_price}")
            print(f"       Discount: {item.discount_percent}% (Rs. {item.discount_amount})")
            print(f"       Line Total: Rs. {item.line_total}")

if __name__ == '__main__':
    investigate_price_discrepancy()