#!/usr/bin/env python3
"""
Final test - exactly reproducing the original issue scenario
"""
import os
import sys
import django
import json

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.test import Client
from accounts.models import User
from inventory.models import EnhancedProduct, POSTransaction

def final_test():
    print("=== FINAL TEST - ORIGINAL ISSUE REPRODUCTION ===\n")

    # Get the exact user and product from the original log
    user = User.objects.filter(mobile_number='***********').first()
    fresh_milk = EnhancedProduct.objects.filter(id=5, owner=user).first()  # ID 5 from original log

    print(f"✓ User: {user.mobile_number}")
    print(f"✓ Product: {fresh_milk.name} (ID: {fresh_milk.id})")
    print(f"✓ Price: ${fresh_milk.selling_price}/{fresh_milk.unit}")
    print(f"✓ Allow fractional: {fresh_milk.allow_fractional_quantities}")

    # Clear any active cart
    POSTransaction.objects.filter(owner=user, status='active').update(status='cancelled')
    print("✓ Cleared any existing active cart")

    # Create test client and login
    client = Client()
    client.force_login(user)

    print(f"\n--- EXACT REPRODUCTION OF ORIGINAL ISSUE ---")
    print(f"Original request: product_id=5, price_amount=200.0, discount_percent=0.0")
    print(f"Original problem: Got 1 Liter instead of 1.33 Liters")
    print(f"Original problem: Charged $150 instead of $200")

    # Exact same request as in the original log
    response = client.post(
        '/inventory/cart/api/add-by-price/',
        data=json.dumps({
            "product_id": 5,
            "price_amount": 200.0,
            "discount_percent": 0.0
        }),
        content_type='application/json'
    )

    print(f"\nResponse Status: {response.status_code}")

    if response.status_code == 200:
        data = json.loads(response.content)
        if data.get('success'):
            print(f"✅ SUCCESS: {data['message']}")

            qty = data['item']['calculated_quantity']
            line_total = data['item']['line_total']

            print(f"\n📊 RESULTS COMPARISON:")
            print(f"  BEFORE FIX:")
            print(f"    - Calculated Quantity: 1 Liter (rounded down)")
            print(f"    - Line total: $150.00 (not the requested $200)")
            print(f"    - Allows Fractional: False")
            print(f"")
            print(f"  AFTER FIX:")
            print(f"    - Calculated Quantity: {qty} Liters")
            print(f"    - Line total: ${line_total}")
            print(f"    - Allows Fractional: {fresh_milk.allow_fractional_quantities}")

            # Validate the fix
            expected_qty = 200.0 / 150.0  # 1.3333
            if abs(float(qty) - expected_qty) < 0.001:
                print(f"\n✅ FRACTIONAL QUANTITY FIX: WORKING!")
                print(f"   Got {qty} instead of 1 (correct calculation)")
            else:
                print(f"\n❌ FRACTIONAL QUANTITY FIX: NOT WORKING!")

            if abs(float(line_total) - 200.0) < 0.01:
                print(f"✅ PRICE CALCULATION FIX: WORKING!")
                print(f"   Charging ${line_total} instead of $150 (correct amount)")
            else:
                print(f"❌ PRICE CALCULATION FIX: NOT WORKING!")

            print(f"\n🎉 ISSUE RESOLVED!")
            print(f"   - User gets exactly what they request: ₨200 worth")
            print(f"   - Fractional quantities work correctly: 1.3333 Liters")
            print(f"   - No more rounding down to whole numbers")
            print(f"   - Cart shows correct totals")

        else:
            print(f"❌ Error: {data.get('error')}")
    else:
        print(f"❌ HTTP Error: {response.status_code}")

    print(f"\n=== FINAL TEST COMPLETE ===")

if __name__ == '__main__':
    final_test()