# Customer Management APIs

This document provides comprehensive details about the Customer Management API endpoints available in the Zayyrah POS system.

## Base URL
```
http://your-domain.com/api/v1/customers/
```

## Authentication
All Customer API endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Customer Model Fields

### Basic Information
- `id` (integer, read-only): Unique customer identifier
- `name` (string, optional): Customer's full name
- `mobile_number` (string, optional): Customer's mobile number (6-15 digits)
- `email` (string, optional): Customer's email address
- `notes` (text, optional): Additional notes about the customer

### Financial Information
- `opening_balance` (decimal, default: 0.00): Initial balance for the customer
- `credit_limit` (decimal, default: 0.00): Maximum credit allowed for the customer

### Clearance Schedule Configuration
- `clearance_type` (string, choices): Type of clearance schedule
  - `"fixed_date"`: Fixed Date of Month (default)
  - `"weekly"`: Day of Week
  - `"custom"`: Custom Date
- `clearance_day_of_month` (integer, optional): Day of month (1-31) for fixed date clearance
- `clearance_day_of_week` (integer, optional): Day of week for weekly clearance (1-7, where 1=Monday)
- `clearance_custom_date` (date, optional): Custom clearance date (YYYY-MM-DD format)

### System Fields
- `created_at` (datetime, read-only): Customer creation timestamp
- `updated_at` (datetime, read-only): Last update timestamp
- `total_sales` (integer, read-only): Total number of sales transactions
- `total_amount` (string, read-only): Total sales amount

### Display Fields (Read-only)
- `clearance_type_display` (string): Human-readable clearance type
- `clearance_day_of_week_display` (string): Human-readable day of week

## API Endpoints

### 1. List Customers
**GET** `/api/v1/customers/`

Retrieve a paginated list of customers with search and filtering capabilities.

#### Query Parameters
- `search` (string, optional): Search by customer name or mobile number
- `ordering` (string, optional): Order results by field (e.g., `name`, `-created_at`)
- `page` (integer, optional): Page number for pagination

#### Request Example
```bash
curl -X GET "http://localhost:8002/api/v1/customers/?search=Ahmed&ordering=-created_at" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
      {
        "id": 6,
        "name": "Ahmed Ali",
        "mobile_number": "03001234567",
        "email": "<EMAIL>",
        "created_at": "2025-09-18T18:26:37.363777Z",
        "updated_at": "2025-09-18T18:26:37.363805Z"
      }
    ]
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required

---

### 2. Create Customer
**POST** `/api/v1/customers/`

Create a new customer with comprehensive financial and clearance settings.

#### Request Body
```json
{
  "name": "Ahmed Ali",
  "mobile_number": "03001234567",
  "email": "<EMAIL>",
  "notes": "VIP customer with credit facility",
  "opening_balance": "1500.00",
  "credit_limit": "5000.00",
  "clearance_type": "weekly",
  "clearance_day_of_week": 1
}
```

#### Validation Rules
- At least one of `name` or `mobile_number` must be provided
- `mobile_number` must be 6-15 digits if provided
- `email` must be valid email format if provided
- Financial fields accept decimal values with up to 10 digits and 2 decimal places
- `clearance_day_of_week` should be 1-7 (Monday-Sunday) when `clearance_type` is "weekly"
- `clearance_day_of_month` should be 1-31 when `clearance_type` is "fixed_date"

#### Request Example
```bash
curl -X POST "http://localhost:8002/api/v1/customers/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Ahmed Ali",
    "mobile_number": "03001234567",
    "email": "<EMAIL>",
    "notes": "VIP customer with credit facility",
    "opening_balance": "1500.00",
    "credit_limit": "5000.00",
    "clearance_type": "weekly",
    "clearance_day_of_week": 1
  }'
```

#### Success Response
```json
{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "id": 6,
    "name": "Ahmed Ali",
    "mobile_number": "03001234567",
    "email": "<EMAIL>",
    "notes": "VIP customer with credit facility",
    "opening_balance": "1500.00",
    "credit_limit": "5000.00",
    "clearance_type": "weekly",
    "clearance_type_display": "Day of Week",
    "clearance_day_of_month": null,
    "clearance_day_of_week": 1,
    "clearance_day_of_week_display": "Monday",
    "clearance_custom_date": null,
    "created_at": "2025-09-18T18:26:37.363777Z",
    "updated_at": "2025-09-18T18:26:37.363805Z",
    "total_sales": 0,
    "total_amount": "0.00",
    "recent_sales": []
  }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "mobile_number": ["Enter 6 to 15 digits."],
    "email": ["Enter a valid email address."]
  }
}
```

#### Response Codes
- `201 Created`: Customer created successfully
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required

---

### 3. Get Customer Details
**GET** `/api/v1/customers/{customer_id}/`

Retrieve detailed information about a specific customer, including sales history.

#### Path Parameters
- `customer_id` (integer): The unique identifier of the customer

#### Request Example
```bash
curl -X GET "http://localhost:8002/api/v1/customers/6/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "id": 6,
    "name": "Ahmed Ali",
    "mobile_number": "03001234567",
    "email": "<EMAIL>",
    "notes": "VIP customer with credit facility",
    "opening_balance": "1500.00",
    "credit_limit": "5000.00",
    "clearance_type": "weekly",
    "clearance_type_display": "Day of Week",
    "clearance_day_of_month": null,
    "clearance_day_of_week": 1,
    "clearance_day_of_week_display": "Monday",
    "clearance_custom_date": null,
    "created_at": "2025-09-18T18:26:37.363777Z",
    "updated_at": "2025-09-18T18:26:37.363805Z",
    "total_sales": 0,
    "total_amount": "0.00",
    "recent_sales": []
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Customer belongs to another user
- `404 Not Found`: Customer not found

---

### 4. Update Customer
**PATCH** `/api/v1/customers/{customer_id}/`

Update specific fields of an existing customer. Also supports **PUT** for full updates.

#### Path Parameters
- `customer_id` (integer): The unique identifier of the customer

#### Request Body (Partial Update Example)
```json
{
  "name": "Ahmed Ali Updated",
  "credit_limit": "10000.00",
  "clearance_type": "fixed_date",
  "clearance_day_of_month": 15
}
```

#### Request Example
```bash
curl -X PATCH "http://localhost:8002/api/v1/customers/6/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "name": "Ahmed Ali Updated",
    "credit_limit": "10000.00",
    "clearance_type": "fixed_date",
    "clearance_day_of_month": 15
  }'
```

#### Success Response
```json
{
  "success": true,
  "message": "Customer updated successfully",
  "data": {
    "id": 6,
    "name": "Ahmed Ali Updated",
    "mobile_number": "03001234567",
    "email": "<EMAIL>",
    "notes": "VIP customer with credit facility",
    "opening_balance": "1500.00",
    "credit_limit": "10000.00",
    "clearance_type": "fixed_date",
    "clearance_type_display": "Fixed Date of Month",
    "clearance_day_of_month": 15,
    "clearance_day_of_week": null,
    "clearance_day_of_week_display": null,
    "clearance_custom_date": null,
    "created_at": "2025-09-18T18:26:37.363777Z",
    "updated_at": "2025-09-18T18:28:15.123456Z",
    "total_sales": 0,
    "total_amount": "0.00",
    "recent_sales": []
  }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "clearance_day_of_month": ["Ensure this value is between 1 and 31."]
  }
}
```

#### Response Codes
- `200 OK`: Customer updated successfully
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Customer belongs to another user
- `404 Not Found`: Customer not found

---

### 5. Delete Customer
**DELETE** `/api/v1/customers/{customer_id}/`

Permanently delete a customer and all associated data.

#### Path Parameters
- `customer_id` (integer): The unique identifier of the customer

#### Request Example
```bash
curl -X DELETE "http://localhost:8002/api/v1/customers/6/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Success Response
```json
{
  "success": true,
  "message": "Customer deleted successfully"
}
```

#### Response Codes
- `200 OK`: Customer deleted successfully
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Customer belongs to another user
- `404 Not Found`: Customer not found

---

## Clearance Schedule Examples

### Weekly Clearance (Every Monday)
```json
{
  "clearance_type": "weekly",
  "clearance_day_of_week": 1,
  "clearance_day_of_month": null,
  "clearance_custom_date": null
}
```

### Fixed Date (15th of every month)
```json
{
  "clearance_type": "fixed_date",
  "clearance_day_of_week": null,
  "clearance_day_of_month": 15,
  "clearance_custom_date": null
}
```

### Custom Date (Specific date)
```json
{
  "clearance_type": "custom",
  "clearance_day_of_week": null,
  "clearance_day_of_month": null,
  "clearance_custom_date": "2025-12-31"
}
```

## Error Handling

All API endpoints follow a consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Specific error message"]
  }
}
```

### Common HTTP Status Codes
- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication credentials missing or invalid
- `403 Forbidden`: User doesn't have permission to access the resource
- `404 Not Found`: Requested resource not found
- `500 Internal Server Error`: Server error

## Data Validation Rules

### Mobile Number
- Must be 6-15 digits
- Only numeric characters allowed
- Can be empty but not invalid format

### Email
- Must be valid email format when provided
- Can be empty

### Financial Fields
- Maximum 10 digits total with 2 decimal places
- Must be positive numbers
- Default value is 0.00

### Customer Identification
- At least one of `name` or `mobile_number` must be provided
- Mobile numbers must be unique per user account

## Security Notes

1. **User Isolation**: Customers are isolated by user account - users can only access their own customers
2. **JWT Authentication**: All endpoints require valid JWT token
3. **Input Validation**: All input data is validated before processing
4. **SQL Injection Protection**: All database queries use parameterized statements
5. **Permission Checks**: Object-level permissions ensure data privacy

## Integration Examples

### Frontend Integration (JavaScript)
```javascript
// Create customer
const createCustomer = async (customerData) => {
  const response = await fetch('/api/v1/customers/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(customerData)
  });

  return await response.json();
};

// Search customers
const searchCustomers = async (searchTerm) => {
  const response = await fetch(`/api/v1/customers/?search=${encodeURIComponent(searchTerm)}`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  return await response.json();
};
```

### Python Integration Example
```python
import requests

class CustomerAPI:
    def __init__(self, base_url, access_token):
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }

    def create_customer(self, customer_data):
        response = requests.post(
            f'{self.base_url}/api/v1/customers/',
            json=customer_data,
            headers=self.headers
        )
        return response.json()

    def get_customer(self, customer_id):
        response = requests.get(
            f'{self.base_url}/api/v1/customers/{customer_id}/',
            headers=self.headers
        )
        return response.json()
```

---

*Last Updated: 2025-09-18*
*API Version: v1*