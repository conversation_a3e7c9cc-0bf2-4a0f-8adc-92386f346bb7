# Product Detail APIs Documentation

## Overview
This documentation covers all Product Detail APIs for the ChannabPOS system, including comprehensive product information, stock management, and sales analytics.

**Base URL**: `http://*************:8003/api/v1/inventory/products/`

**Authentication**: Required (<PERSON><PERSON>ken or Session Authentication)

> **🚀 NEW: Flutter Compatible APIs** - All endpoints now return **null-free responses** with appropriate default values for better Flutter integration. See [Flutter Compatibility Section](#flutter-compatibility--null-handling) for details.

---

## Table of Contents

1. [Basic Product Detail API](#1-basic-product-detail-api)
2. [Comprehensive Product Detail API](#2-comprehensive-product-detail-api-new)
3. [Stock Management APIs](#3-stock-management-apis)
   - [Stock Batch Management](#34-stock-batch-management-add--edit--delete)
4. [Sales Analytics APIs](#4-sales-analytics-apis)
5. [Stock Action APIs](#5-stock-action-apis)
6. [Error Handling](#6-error-handling)
7. [Rate Limiting](#7-rate-limiting)
8. [Flutter Compatibility & Null Handling](#flutter-compatibility--null-handling)

---

## 1. Basic Product Detail API

### GET `/api/v1/inventory/products/{product_id}/`

**Description**: Get basic product information with essential details.

#### Request Parameters

**Path Parameters:**
- `product_id` (integer, required): The ID of the product

**Query Parameters:**
- None

#### Request Example

```bash
GET /api/v1/inventory/products/16/
Authorization: Bearer your_token_here
Content-Type: application/json
```

#### Response Format

**Success Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "id": 16,
    "name": "Fresh Milk",
    "description": "",
    "sku": "",
    "barcode": "",
    "qr_code": "",
    "category": 13,
    "category_name": "🥛 Dairy & Eggs",
    "category_path": "🍽️ Food & Grocery > 🥛 Dairy & Eggs",
    "brand": 0,
    "brand_name": "No brand",
    "supplier": 0,
    "supplier_name": "No supplier",
    "purchase_price": "140.00",
    "selling_price": "150.00",
    "wholesale_price": "0.00",
    "minimum_selling_price": "0.00",
    "track_stock": true,
    "allow_negative_stock": false,
    "reorder_level": 5,
    "maximum_stock_level": 0,
    "unit": "l",
    "unit_label": "Liter",
    "unit_custom_label": "",
    "weight": "0.00",
    "dimensions": "",
    "tax_rate": "0.00",
    "tax_exempt": false,
    "is_active": true,
    "is_featured": false,
    "tags": "",
    "current_stock": 100.0,
    "total_stock_value": 14000.0,
    "is_low_stock": false,
    "is_out_of_stock": false,
    "images": [],
    "active_batches_count": 1,
    "created_at": "2025-09-25T03:55:17.863260Z",
    "updated_at": "2025-09-25T03:55:17.863282Z"
  }
}
```

#### Response Fields

> **Note**: All fields are **guaranteed non-null** for Flutter compatibility. Previously null values are replaced with appropriate defaults.

| Field | Type | Default for Null | Description |
|-------|------|------------------|-------------|
| `id` | integer | - | Product unique identifier |
| `name` | string | - | Product name (required) |
| `description` | string | `""` | Product description |
| `sku` | string | `""` | Stock Keeping Unit |
| `barcode` | string | `""` | Product barcode |
| `qr_code` | string | `""` | QR code for product |
| `category` | integer | - | Category ID (required) |
| `category_name` | string | `"No category"` | Category display name |
| `category_path` | string | - | Full category hierarchy |
| `brand` | integer | `0` | Brand ID |
| `brand_name` | string | `"No brand"` | Brand name |
| `supplier` | integer | `0` | Supplier ID |
| `supplier_name` | string | `"No supplier"` | Supplier name |
| `purchase_price` | string (decimal) | `"0.00"` | Purchase/cost price |
| `selling_price` | string (decimal) | - | Current selling price (required) |
| `wholesale_price` | string (decimal) | `"0.00"` | Wholesale price |
| `minimum_selling_price` | string (decimal) | `"0.00"` | Minimum allowed selling price |
| `track_stock` | boolean | - | Whether stock tracking is enabled |
| `allow_negative_stock` | boolean | - | Allow negative stock levels |
| `reorder_level` | integer | - | Reorder threshold |
| `maximum_stock_level` | integer | `0` | Maximum stock level |
| `unit` | string | - | Unit of measurement |
| `unit_label` | string | - | Human-readable unit label |
| `unit_custom_label` | string | `""` | Custom unit label |
| `weight` | string (decimal) | `"0.00"` | Product weight |
| `dimensions` | string | `""` | Product dimensions (L×W×H) |
| `tax_rate` | string (decimal) | - | Tax rate percentage |
| `tax_exempt` | boolean | - | Tax exemption status |
| `is_active` | boolean | - | Product active status |
| `is_featured` | boolean | - | Featured product flag |
| `tags` | string | `""` | Product tags |
| `current_stock` | double | - | Real-time stock quantity |
| `total_stock_value` | double | - | Total inventory value |
| `is_low_stock` | boolean | - | Low stock indicator |
| `is_out_of_stock` | boolean | - | Out of stock indicator |
| `images` | array | `[]` | Product images array |
| `active_batches_count` | integer | - | Count of active stock batches |
| `created_at` | string (ISO 8601) | - | Creation timestamp |
| `updated_at` | string (ISO 8601) | - | Last update timestamp |

### Flutter Data Types Reference

| API Type | Flutter/Dart Type | Notes |
|----------|-------------------|--------|
| `integer` | `int` | 32-bit signed integer |
| `double` | `double` | 64-bit floating point |
| `string` | `String` | UTF-8 string |
| `string (decimal)` | `String` | Decimal as string (use `double.parse()`) |
| `string (ISO 8601)` | `String` | DateTime as string (use `DateTime.parse()`) |
| `boolean` | `bool` | true/false |
| `array` | `List<dynamic>` | JSON array |
| `object` | `Map<String, dynamic>` | JSON object |

---

## 2. Comprehensive Product Detail API (NEW!)

### GET `/api/v1/inventory/products/{product_id}/comprehensive_detail/`

**Description**: Get complete product information including stock management, sales analytics, and related data. This endpoint provides everything needed for a full product detail page.

> **Flutter Compatible**: All responses are **guaranteed null-free** with appropriate default values for previously null fields.

#### Request Parameters

**Path Parameters:**
- `product_id` (integer, required): The ID of the product

**Query Parameters:**
- None

#### Request Example

```bash
GET /api/v1/inventory/products/16/comprehensive_detail/
Authorization: Bearer your_token_here
Content-Type: application/json
```

#### Response Format

**Success Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk",
      "description": "",
      "sku": "",
      "barcode": "",
      "qr_code": "",
      "category": 13,
      "category_name": "🥛 Dairy & Eggs",
      "category_path": "🍽️ Food & Grocery > 🥛 Dairy & Eggs",
      "brand": 0,
      "brand_name": "No brand",
      "supplier": 0,
      "supplier_name": "No supplier",
      "purchase_price": "140.00",
      "selling_price": "150.00",
      "wholesale_price": "0.00",
      "minimum_selling_price": "0.00",
      "unit": "l",
      "unit_label": "Liter",
      "unit_custom_label": "",
      "weight": "0.00",
      "dimensions": "",
      "current_stock": 100.0,
      "is_low_stock": false,
      "is_out_of_stock": false,
      "track_stock": true,
      "reorder_level": 5,
      "maximum_stock_level": 0,
      "tax_rate": "0.00",
      "tax_exempt": false,
      "is_active": true,
      "is_featured": false,
      "tags": "",
      "created_at": "2025-09-25T03:55:17.863260Z",
      "updated_at": "2025-09-25T03:55:17.863282Z"
    },
    "stock_management": {
      "summary": {
        "current_stock": 100.0,
        "total_stock_value": 14000.0,
        "is_low_stock": false,
        "is_out_of_stock": false,
        "reorder_level": 5,
        "maximum_stock_level": 0,
        "total_batches": 1,
        "oldest_batch_date": "2025-09-25",
        "newest_batch_date": "2025-09-25"
      },
      "stock_entries": [
        {
          "id": 13,
          "batch_number": "BATCH-20250925035517-16",
          "quantity_received": 100.0,
          "quantity_available": 100.0,
          "purchase_price": "140.00",
          "received_date": "2025-09-25",
          "expiry_date": "",
          "status": "active",
          "location": 3,
          "supplier": 0,
          "notes": ""
        }
      ],
      "stock_by_location": {
        "Main Store": {
          "location_id": 3,
          "quantity": 100.0,
          "value": 14000.0
        }
      },
      "stock_valuation": {
        "method": "FIFO",
        "total_quantity": 100.0,
        "total_value": 14000.0,
        "batches": [
          {
            "batch_id": 13,
            "quantity": 100.0,
            "price": 140.0,
            "value": 14000.0
          }
        ]
      }
    },
    "sales_analytics": {
      "lifetime": {
        "total_quantity_sold": 0.0,
        "total_revenue": 0.0,
        "total_transactions": 0,
        "avg_quantity_per_sale": 0.0,
        "avg_price_per_unit": 0.0
      },
      "period_analysis": {
        "today": {
          "quantity": 0,
          "revenue": 0,
          "transactions": 0
        },
        "this_week": {
          "quantity": 0,
          "revenue": 0,
          "transactions": 0
        },
        "this_month": {
          "quantity": 0,
          "revenue": 0,
          "transactions": 0
        }
      },
      "recent_transactions": []
    },
    "metadata": {
      "generated_at": "2025-09-25T04:11:14.124567",
      "api_version": "v1.0",
      "data_freshness": "real_time",
      "available_actions": [
        "adjust_stock",
        "transfer_stock",
        "reserve_stock",
        "add_stock_batch",
        "update_product"
      ]
    },
    "related_endpoints": {
      "stock_summary": "/api/v1/inventory/products/16/stock_summary/",
      "sales_history": "/api/v1/inventory/products/16/sales_history/",
      "stock_by_location": "/api/v1/inventory/products/16/stock_by_location/",
      "stock_valuation": "/api/v1/inventory/products/16/stock_valuation/",
      "adjust_stock": "/api/v1/inventory/products/16/adjust_stock/",
      "transfer_stock": "/api/v1/inventory/products/16/transfer_stock/",
      "reserve_stock": "/api/v1/inventory/products/16/reserve_stock/"
    }
  }
}
```

#### Response Sections

**1. Product Section**
- Complete product information
- Pricing and tax details
- Category hierarchy
- Stock status indicators

**2. Stock Management Section**
- **Summary**: Stock levels, values, batch counts
- **Stock Entries**: Detailed batch information
- **Stock by Location**: Stock distribution
- **Stock Valuation**: FIFO-based valuation

**3. Sales Analytics Section**
- **Lifetime**: Total sales statistics
- **Period Analysis**: Today/Week/Month breakdowns
- **Recent Transactions**: Latest sales data

**4. Metadata Section**
- API information and available actions
- Related endpoints for specific operations

---

## 3. Stock Management APIs

### 3.1 Stock Summary

#### GET `/api/v1/inventory/products/{product_id}/stock_summary/`

**Description**: Get detailed stock summary for a product.

#### Request Example

```bash
GET /api/v1/inventory/products/16/stock_summary/
Authorization: Bearer your_token_here
```

#### Response Example

```json
{
  "success": true,
  "data": {
    "current_stock": 100.0,
    "total_stock_value": 14000.0,
    "is_low_stock": false,
    "is_out_of_stock": false,
    "reorder_level": 5,
    "maximum_stock_level": null,
    "total_batches": 1,
    "recent_movements": [
      {
        "id": 1,
        "movement_type": "purchase",
        "quantity": 100.0,
        "created_at": "2025-09-25T03:55:17.863260Z"
      }
    ]
  }
}
```

### 3.2 Stock by Location

#### GET `/api/v1/inventory/products/{product_id}/stock_by_location/`

**Description**: Get stock levels across different locations.

#### Response Example

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk"
    },
    "stock_by_location": {
      "Main Store": {
        "location_id": 3,
        "quantity": 100.0,
        "value": 14000.0
      },
      "Warehouse A": {
        "location_id": 4,
        "quantity": 50.0,
        "value": 7000.0
      }
    }
  }
}
```

### 3.3 Stock Valuation

#### GET `/api/v1/inventory/products/{product_id}/stock_valuation/`

**Description**: Get stock valuation using different methods.

#### Query Parameters
- `method` (string, optional): Valuation method (`FIFO`, `LIFO`, `WEIGHTED_AVERAGE`). Default: `FIFO`

#### Request Example

```bash
GET /api/v1/inventory/products/16/stock_valuation/?method=FIFO
Authorization: Bearer your_token_here
```

#### Response Example

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk"
    },
    "valuation": {
      "method": "FIFO",
      "total_quantity": 100.0,
      "total_value": 14000.0,
      "batches": [
        {
          "batch_id": 13,
          "quantity": 100.0,
          "price": 140.0,
          "value": 14000.0
        }
      ]
    }
  }
}
```

### 3.4 Stock Batch Management (Add / Edit / Delete)

**Base Path**: `/api/v1/inventory/batches/`

**Description**: Manage physical stock batches directly. These endpoints power add/edit/delete stock flows in the POS. Responses use the Flutter-compatible batch serializers, ensuring that optional values are returned as empty strings, `0`, or `"0.00"` instead of `null`.

#### Supported Operations

- `GET /api/v1/inventory/batches/` — List or filter batches
- `POST /api/v1/inventory/batches/` — Create a new batch (add stock)
- `GET /api/v1/inventory/batches/{batch_id}/` — Retrieve batch detail
- `PATCH /api/v1/inventory/batches/{batch_id}/` — Update an existing batch
- `DELETE /api/v1/inventory/batches/{batch_id}/` — Delete a batch (only when quantity_available is `0`)

#### GET `/api/v1/inventory/batches/?product={product_id}`

**Description**: List batches for a specific product. You can additionally filter by `location`, `status`, or `supplier`.

```bash
GET /api/v1/inventory/batches/?product=16&status=active
Authorization: Bearer your_token_here
```

**Success Response (200 OK):**

```json
{
  "success": true,
  "data": {
    "count": 2,
    "results": [
      {
        "id": 42,
        "batch_number": "BATCH-20250110-001",
        "product": 16,
        "product_name": "Fresh Milk",
        "product_sku": "",
        "location": 3,
        "location_name": "Main Store",
        "supplier": 0,
        "supplier_name": "No supplier",
        "quantity_received": 120.0,
        "quantity_available": 80.0,
        "quantity_reserved": 0.0,
        "quantity_damaged": 0.0,
        "quantity_sold": 40.0,
        "purchase_price": "140.00",
        "batch_value": 11200.0,
        "received_date": "2025-01-10",
        "expiry_date": "",
        "status": "active",
        "is_expiring_soon": false,
        "created_at": "2025-01-10T09:00:00Z",
        "updated_at": "2025-01-15T12:30:00Z"
      }
    ]
  }
}
```

#### POST `/api/v1/inventory/batches/`

**Description**: Add a new stock batch for a product. If `quantity_available` is omitted, it defaults to `quantity_received`. All of the Stock Management controls you see in the web UI are surfaced through these fields so batch status and reserved/damaged quantities stay in sync across channels.

**Request Body:**

```json
{
  "batch_number": "BATCH-20250115-001",
  "purchase_order_number": "PO-2025-115-A",
  "product": 16,
  "location": 3,
  "supplier": 7,
  "quantity_received": 50.0,
  "quantity_available": 50.0,
  "quantity_reserved": 0.0,
  "quantity_damaged": 0.0,
  "purchase_price": "142.50",
  "received_date": "2025-01-15",
  "manufacture_date": "2024-12-15",
  "expiry_date": "2025-03-15",
  "status": "active",
  "notes": "Restock shipment from Supplier #7"
}
```

**UI Equivalent (Quick Add)**

The web “Add New Stock” card only collects *Quantity to Add*, *Purchase Price*, *Received Date*, *Expiry Date*, and *Notes*. To mirror that exact workflow over the API:

1. Resolve the product and default location once (the UI silently uses the primary location, e.g. `Main Store`).
   - `GET /api/v1/inventory/locations/?search=Main%20Store` → pick `data.results[0].id` and cache it (e.g. `location_id = 4`).
2. Generate a batch number on the client the same way the UI does: `BATCH-{YYYYMMDDHHMMSS}-{product_id}`.
3. Send the minimal payload below; the API will backfill the rest:

```json
{
  "batch_number": "BATCH-20250926093015-17",
  "product": 17,
  "location": 4,
  "quantity_received": 150,
  "purchase_price": "99.96",
  "received_date": "2025-09-26T09:30:15Z",
  "expiry_date": "2025-10-10T18:00:00Z",
  "notes": "Quick add from mobile app"
}
```

What the API does for you:
- `received_date` and `expiry_date` accept full ISO 8601 timestamps (for example, `2025-09-26T09:30:15Z`). The API stores the date portion while preserving the time context in logs.
- You can omit `quantity_available`; the API automatically sets it to the same value as `quantity_received`, matching the UI’s single "Quantity to Add" field.
- `status` defaults to `active` and both `quantity_reserved` / `quantity_damaged` default to `0.0`.
- `supplier` and `purchase_order_number` stay blank unless you set them.

This keeps the mobile/Flutter request surface identical to the on-page form while still storing a fully qualified batch behind the scenes.

**Stock Management Controls**

| Field | Purpose | Notes |
|-------|---------|-------|
| `status` | Batch lifecycle state | Accepts `active`, `reserved`, `expired`, `damaged`, or `sold_out`. Mirrors the Stock Management dropdown in the web page. |
| `quantity_reserved` | Hold stock without reducing availability | Optional; defaults to `0.0`. Ensure `quantity_reserved + quantity_damaged ≤ quantity_received`. |
| `quantity_damaged` | Track unusable stock | Optional; defaults to `0.0`. Cannot exceed `quantity_received`. |
| `purchase_order_number` | Link back to procurement paperwork | Optional free-form string for audit trails. |
| `manufacture_date` | Capture production/pack date | Optional ISO date. Required only for regulated items. |

**Response Fields (Flutter Types & Null Defaults):**

| Field | Type | Null Replacement | Description |
|-------|------|------------------|-------------|
| `id` | `int` | – | Batch identifier |
| `batch_number` | `String` | `""` | Unique batch code |
| `purchase_order_number` | `String` | `""` | External purchase reference |
| `product` | `int` | `0` | Product ID |
| `product_name` | `String` | `"No name"` | Product display name |
| `location` | `int` | `0` | Location ID |
| `location_name` | `String` | `"No location"` | Location name |
| `supplier` | `int` | `0` | Supplier ID |
| `supplier_name` | `String` | `"No supplier"` | Supplier name |
| `quantity_received` | `double` | `0.0` | Total quantity received |
| `quantity_available` | `double` | `0.0` | Quantity still available |
| `quantity_reserved` | `double` | `0.0` | Reserved quantity |
| `quantity_damaged` | `double` | `0.0` | Damaged/unusable quantity |
| `quantity_sold` | `double` | `0.0` | Quantity already sold |
| `purchase_price` | `String` | `"0.00"` | Purchase price per unit |
| `batch_value` | `double` | `0.0` | Current value of available stock |
| `received_date` | `String` | `""` | ISO date (YYYY-MM-DD) |
| `manufacture_date` | `String` | `""` | Optional manufacture date |
| `expiry_date` | `String` | `""` | Optional expiry date |
| `status` | `String` | `"active"` | Batch status |
| `is_expiring_soon` | `bool` | `false` | Expiry indicator |
| `movements_count` | `int` | `0` | Audit trail count |
| `notes` | `String` | `""` | Additional notes |
| `created_at` | `String` | `""` | Timestamp (ISO 8601) |
| `updated_at` | `String` | `""` | Timestamp (ISO 8601) |

**Success Response (201 Created):**

```json
{
  "success": true,
  "message": "Stock batch created successfully",
  "data": {
    "id": 47,
    "batch_number": "BATCH-20250115-001",
    "purchase_order_number": "",
    "product": 16,
    "product_name": "Fresh Milk",
    "location": 3,
    "location_name": "Main Store",
    "supplier": 7,
    "supplier_name": "Dairy Suppliers Inc.",
    "quantity_received": 50.0,
    "quantity_available": 50.0,
    "quantity_reserved": 0.0,
    "quantity_damaged": 0.0,
    "quantity_sold": 0.0,
    "purchase_price": "142.50",
    "batch_value": 7125.0,
    "received_date": "2025-01-15",
    "manufacture_date": "",
    "expiry_date": "2025-03-15",
    "status": "active",
    "is_expiring_soon": false,
    "movements_count": 1,
    "notes": "Restock shipment from Supplier #7",
    "created_at": "2025-01-15T09:45:12.123456Z",
    "updated_at": "2025-01-15T09:45:12.123456Z"
  }
}
```

#### PATCH `/api/v1/inventory/batches/{batch_id}/`

**Description**: Update an existing batch. Send only the fields you need to change.

```bash
PATCH /api/v1/inventory/batches/47/
Authorization: Bearer your_token_here
Content-Type: application/json

{
  "quantity_available": 45.5,
  "notes": "Adjusted after cycle count"
}
```

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Stock batch updated successfully",
  "data": {
    "id": 47,
    "batch_number": "BATCH-20250115-001",
    "purchase_order_number": "",
    "product": 16,
    "product_name": "Fresh Milk",
    "location": 3,
    "location_name": "Main Store",
    "supplier": 7,
    "supplier_name": "Dairy Suppliers Inc.",
    "quantity_received": 50.0,
    "quantity_available": 45.5,
    "quantity_reserved": 0.0,
    "quantity_damaged": 0.0,
    "quantity_sold": 4.5,
    "purchase_price": "142.50",
    "batch_value": 6487.5,
    "received_date": "2025-01-15",
    "manufacture_date": "",
    "expiry_date": "2025-03-15",
    "status": "active",
    "is_expiring_soon": false,
    "movements_count": 2,
    "notes": "Adjusted after cycle count",
    "created_at": "2025-01-15T09:45:12.123456Z",
    "updated_at": "2025-01-16T08:02:00.000000Z"
  }
}
```

#### DELETE `/api/v1/inventory/batches/{batch_id}/`

**Description**: Remove a batch once all stock is consumed and `quantity_available` is `0`.

```bash
DELETE /api/v1/inventory/batches/47/
Authorization: Bearer your_token_here
```

**Success Response (200 OK):**

```json
{
  "success": true,
  "message": "Stock batch deleted successfully"
}
```

**Error Example (400 Bad Request):**

```json
{
  "success": false,
  "message": "Cannot delete batch with available stock"
}
```

> ✅ **Flutter Tip**: Because every numeric field is returned as an `int`, `double`, or numeric `String`, you can safely wrap deserialization with `double.parse()` or `int.parse()` without additional null checks.

---

## 4. Sales Analytics APIs

### 4.1 Sales History

#### GET `/api/v1/inventory/products/{product_id}/sales_history/`

**Description**: Get comprehensive sales history and analytics.

#### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `time_filter` | string | `last_30_days` | Time period filter |
| `page` | integer | 1 | Page number |
| `page_size` | integer | 20 | Items per page |
| `search` | string | - | Search transactions |

**Time Filter Options:**
- `today`
- `last_7_days`
- `last_30_days`
- `this_month`
- `this_year`

#### Request Example

```bash
GET /api/v1/inventory/products/16/sales_history/?time_filter=last_30_days&page=1&page_size=10
Authorization: Bearer your_token_here
```

#### Response Example

```json
{
  "success": true,
  "product": {
    "id": 16,
    "name": "Fresh Milk",
    "sku": null,
    "selling_price": 150.0,
    "current_stock": 100.0
  },
  "sales": [
    {
      "transaction_number": "20250925140532-1-7834",
      "date": "2025-09-25",
      "time": "14:05",
      "quantity": 2.5,
      "unit_price": 150.0,
      "line_total": 375.0,
      "discount_amount": 0.0,
      "customer": {
        "name": "John Doe",
        "mobile_number": "03001234567"
      },
      "payment_method": "cash",
      "transaction_total": 375.0,
      "notes": ""
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 1,
    "total_sales": 1,
    "page_size": 10,
    "has_next": false,
    "has_previous": false
  },
  "analytics": {
    "period": {
      "total_quantity_sold": 2.5,
      "total_revenue": 375.0,
      "total_transactions": 1,
      "avg_quantity_per_sale": 2.5,
      "avg_price_per_unit": 150.0
    },
    "lifetime": {
      "total_quantity_sold": 2.5,
      "total_revenue": 375.0,
      "total_transactions": 1,
      "avg_quantity_per_sale": 2.5,
      "avg_price_per_unit": 150.0
    },
    "monthly_trend": [
      {
        "month": "2025-09",
        "quantity": 2.5,
        "revenue": 375.0,
        "transactions": 1
      }
    ],
    "top_customers": [
      {
        "customer_name": "John Doe",
        "total_quantity": 2.5,
        "total_revenue": 375.0,
        "transactions_count": 1
      }
    ]
  },
  "filters_applied": {
    "time_filter": "last_30_days",
    "search": ""
  }
}
```

---

## 5. Stock Action APIs

### 5.1 Adjust Stock

#### POST `/api/v1/inventory/products/{product_id}/adjust_stock/`

**Description**: Manual stock adjustment with reason tracking.

#### Request Body

```json
{
  "location": 3,
  "adjustment_type": "increase",
  "quantity": 25.0,
  "reason": "Stock correction after physical count",
  "notes": "Found extra inventory during audit"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `location` | integer | Yes | Location ID |
| `adjustment_type` | string | Yes | `increase` or `decrease` |
| `quantity` | float | Yes | Adjustment quantity |
| `reason` | string | Yes | Reason for adjustment |
| `notes` | string | No | Additional notes |

#### Response Example

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk",
      "previous_stock": 100.0,
      "new_stock": 125.0
    },
    "adjustment": {
      "id": 15,
      "adjustment_type": "add",
      "quantity": 25.0,
      "reason": "Stock correction after physical count",
      "created_at": "2025-09-25T10:30:00Z"
    },
    "stock_movement": {
      "id": 23,
      "movement_type": "adjustment",
      "quantity": 25.0,
      "reference_number": "ADJ-000015"
    }
  }
}
```

### 5.2 Transfer Stock

#### POST `/api/v1/inventory/products/{product_id}/transfer_stock/`

**Description**: Transfer stock between locations.

#### Request Body

```json
{
  "quantity": 30.0,
  "from_location_id": 3,
  "to_location_id": 4,
  "notes": "Transfer to warehouse for storage"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `quantity` | float | Yes | Quantity to transfer |
| `from_location_id` | integer | Yes | Source location ID |
| `to_location_id` | integer | Yes | Destination location ID |
| `notes` | string | No | Transfer notes |

#### Response Example

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk"
    },
    "transfer": {
      "quantity_transferred": 30.0,
      "from_location": "Main Store",
      "to_location": "Warehouse A",
      "transfer_id": "TRF-000123"
    },
    "stock_movements": [
      {
        "id": 24,
        "movement_type": "transfer",
        "quantity": -30.0,
        "location": "Main Store"
      },
      {
        "id": 25,
        "movement_type": "transfer",
        "quantity": 30.0,
        "location": "Warehouse A"
      }
    ]
  }
}
```

### 5.3 Reserve Stock

#### POST `/api/v1/inventory/products/{product_id}/reserve_stock/`

**Description**: Reserve stock for pending orders.

#### Request Body

```json
{
  "quantity": 10.0,
  "location_id": 3,
  "reservation_reason": "Pending customer order",
  "reference_number": "ORDER-001234",
  "notes": "Reserved for John Doe - Order #1234"
}
```

#### Request Fields

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `quantity` | float | Yes | Quantity to reserve |
| `location_id` | integer | Yes | Location ID |
| `reservation_reason` | string | Yes | Reason for reservation |
| `reference_number` | string | No | Reference/order number |
| `notes` | string | No | Reservation notes |

#### Response Example

```json
{
  "success": true,
  "data": {
    "product": {
      "id": 16,
      "name": "Fresh Milk",
      "available_stock": 90.0,
      "reserved_stock": 10.0
    },
    "reservation": {
      "id": 45,
      "quantity_reserved": 10.0,
      "location": "Main Store",
      "reference_number": "ORDER-001234",
      "created_at": "2025-09-25T10:45:00Z"
    },
    "allocations": [
      {
        "batch_id": 13,
        "quantity": 10.0,
        "batch_number": "BATCH-20250925035517-16"
      }
    ]
  }
}
```

---

## 6. Error Handling

### Common Error Responses

#### 400 Bad Request

```json
{
  "success": false,
  "message": "Invalid quantity. Must be greater than 0",
  "errors": {
    "quantity": ["This field must be greater than 0"]
  }
}
```

#### 401 Unauthorized

```json
{
  "success": false,
  "message": "Authentication credentials were not provided"
}
```

#### 403 Forbidden

```json
{
  "success": false,
  "message": "You do not have permission to perform this action"
}
```

#### 404 Not Found

```json
{
  "success": false,
  "message": "Product not found"
}
```

#### 500 Internal Server Error

```json
{
  "success": false,
  "error": "An internal server error occurred",
  "traceback": "Error details (only for superusers)"
}
```

### Validation Errors

#### Stock Insufficient Error

```json
{
  "success": false,
  "message": "Insufficient stock. Available: 10.0, Requested: 25.0",
  "error_code": "INSUFFICIENT_STOCK",
  "available_stock": 10.0,
  "requested_quantity": 25.0,
  "shortfall": 15.0
}
```

#### Location Not Found Error

```json
{
  "success": false,
  "message": "Location not found or not accessible",
  "error_code": "LOCATION_NOT_FOUND",
  "location_id": 999
}
```

---

## 7. Rate Limiting

**Current Limits:**
- **GET Requests**: 1000 requests per hour per user
- **POST Requests**: 500 requests per hour per user
- **Bulk Operations**: 100 requests per hour per user

**Rate Limit Headers:**

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1640995200
```

**Rate Limit Exceeded Response:**

```json
{
  "success": false,
  "message": "Rate limit exceeded. Try again in 3600 seconds",
  "error_code": "RATE_LIMIT_EXCEEDED",
  "retry_after": 3600
}
```

---

## Usage Examples

### Frontend Integration Example (JavaScript)

```javascript
// Get comprehensive product detail
async function getProductDetail(productId) {
  try {
    const response = await fetch(
      `http://*************:8003/api/v1/inventory/products/${productId}/comprehensive_detail/`,
      {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer ' + authToken,
          'Content-Type': 'application/json'
        }
      }
    );

    const data = await response.json();

    if (data.success) {
      // Handle successful response
      displayProductInfo(data.data.product);
      displayStockManagement(data.data.stock_management);
      displaySalesAnalytics(data.data.sales_analytics);
    } else {
      // Handle error
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}

// Adjust stock
async function adjustStock(productId, adjustment) {
  try {
    const response = await fetch(
      `http://*************:8003/api/v1/inventory/products/${productId}/adjust_stock/`,
      {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer ' + authToken,
          'Content-Type': 'application/json',
          'X-CSRFToken': csrfToken
        },
        body: JSON.stringify(adjustment)
      }
    );

    const data = await response.json();

    if (data.success) {
      // Refresh product data
      getProductDetail(productId);
      showSuccess('Stock adjusted successfully');
    } else {
      showError(data.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
}
```

### Python Integration Example

```python
import requests

class ProductAPI:
    def __init__(self, base_url, auth_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {auth_token}',
            'Content-Type': 'application/json'
        }

    def get_comprehensive_detail(self, product_id):
        """Get comprehensive product detail"""
        url = f"{self.base_url}/products/{product_id}/comprehensive_detail/"
        response = requests.get(url, headers=self.headers)
        return response.json()

    def adjust_stock(self, product_id, adjustment_data):
        """Adjust product stock"""
        url = f"{self.base_url}/products/{product_id}/adjust_stock/"
        response = requests.post(url, json=adjustment_data, headers=self.headers)
        return response.json()

    def get_sales_history(self, product_id, time_filter='last_30_days'):
        """Get sales history for product"""
        url = f"{self.base_url}/products/{product_id}/sales_history/"
        params = {'time_filter': time_filter}
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()

# Usage
api = ProductAPI('http://*************:8003/api/v1/inventory', 'your_token_here')

# Get comprehensive product detail
product_detail = api.get_comprehensive_detail(16)
print(f"Product: {product_detail['data']['product']['name']}")
print(f"Stock: {product_detail['data']['stock_management']['summary']['current_stock']}")
```

---

## Flutter Compatibility & Null Handling

### Overview

All Product Detail APIs have been enhanced with **Flutter compatibility** in mind. The most significant improvement is the **elimination of null values** in API responses.

### Null Value Handling

Previously, many fields could return `null` values, which caused issues in Flutter applications. Now:

✅ **All fields are guaranteed non-null**
✅ **Appropriate default values** are provided for empty fields
✅ **Consistent data types** across all responses
✅ **No null pointer exceptions** in Flutter apps

### Default Values by Field Type

| Original Value | Field Type | New Default | Example Fields |
|----------------|------------|-------------|----------------|
| `null` | String (Empty) | `""` | `sku`, `barcode`, `description`, `dimensions` |
| `null` | String (Descriptive) | Meaningful text | `brand_name` → `"No brand"`<br>`supplier_name` → `"No supplier"` |
| `null` | Decimal | `"0.00"` | `wholesale_price`, `weight`, `minimum_selling_price` |
| `null` | Integer | `0` | `maximum_stock_level`, `brand`, `supplier` |
| `null` | Date | `""` | `expiry_date`, `oldest_batch_date` |
| `null` | Array | `[]` | `images`, `recent_transactions` |

### Flutter Model Example

With guaranteed non-null values, you can create clean Flutter models:

```dart
class Product {
  final int id;
  final String name;
  final String sku;              // Never null, defaults to ""
  final String brandName;        // Never null, defaults to "No brand"
  final String wholesalePrice;   // Never null, defaults to "0.00"
  final int maximumStockLevel;   // Never null, defaults to 0
  final double currentStock;
  final bool isLowStock;

  Product({
    required this.id,
    required this.name,
    required this.sku,
    required this.brandName,
    required this.wholesalePrice,
    required this.maximumStockLevel,
    required this.currentStock,
    required this.isLowStock,
  });

  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id'],
      name: json['name'],
      sku: json['sku'],                    // Safe, never null
      brandName: json['brand_name'],       // Safe, never null
      wholesalePrice: json['wholesale_price'], // Safe, never null
      maximumStockLevel: json['maximum_stock_level'], // Safe, never null
      currentStock: json['current_stock'].toDouble(),
      isLowStock: json['is_low_stock'],
    );
  }
}
```

### Migration from Previous Version

If you were handling null values in your Flutter code, you can now simplify:

**Before (with null handling):**
```dart
final String sku = json['sku'] ?? '';
final String brandName = json['brand_name'] ?? 'No brand';
final double wholesalePrice = double.parse(json['wholesale_price'] ?? '0.00');
```

**After (null-safe):**
```dart
final String sku = json['sku'];                    // Always string
final String brandName = json['brand_name'];       // Always string
final double wholesalePrice = double.parse(json['wholesale_price']); // Always string
```

### Backwards Compatibility

✅ **Fully backwards compatible** - existing integrations continue to work
✅ **Same field names** - no breaking changes to field names
✅ **Same data structure** - JSON structure remains unchanged
✅ **Enhanced reliability** - fewer runtime errors in client applications

---

## Support and Contact

For API support, please contact:
- **Email**: <EMAIL>
- **Documentation**: http://*************:8003/admin/doc/
- **GitHub**: https://github.com/channab-pos/api-docs

**Last Updated**: September 25, 2025
**API Version**: v1.0
