# Inventory Management APIs

This document provides comprehensive details about the Inventory Management API endpoints available in the Zayyrah POS system.

## Base URL
```
http://your-domain.com/api/v1/inventory/
```

## Authentication
All Inventory API endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Standard Response Format

### Success Response
```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data object
    }
}
```

### Error Response
```json
{
    "success": false,
    "message": "Error description",
    "errors": {
        "field_name": ["Error message"]
    }
}
```

## API Endpoints

---

## 1. Categories API

### Category Model Fields

#### Basic Information
- `id` (integer, read-only): Unique category identifier
- `name` (string, required): Category name (max 120 characters)
- `parent` (integer, optional): Parent category ID for sub-categories
- `level` (integer, default: 0): Category level (0=root, 1=sub, 2=sub-sub)
- `is_active` (boolean, default: true): Whether category is active
- `sort_order` (integer, default: 0): Sort order for display
- `image` (file, optional): Category image
- `description` (text, optional): Category description

#### System Fields (Read-only)
- `created_at` (datetime): Category creation timestamp
- `updated_at` (datetime): Last update timestamp
- `full_path` (string): Full hierarchical path (e.g., "Electronics > Mobile Phones")
- `children` (array): List of child categories
- `products_count` (integer): Number of products in this category

### 1.1 List Categories
**GET** `/api/v1/inventory/categories/`

Retrieve a paginated list of categories with hierarchical structure.

**Query Parameters:**
- `parent` (integer): Filter by parent category ID
- `level` (integer): Filter by category level (0, 1, 2)
- `is_active` (boolean): Filter by active status
- `search` (string): Search in name and description
- `ordering` (string): Sort by fields (name, level, sort_order, created_at)

**Example Request:**
```bash
GET /api/v1/inventory/categories/?level=0&is_active=true
Authorization: Bearer YOUR_ACCESS_TOKEN
```

**Example Response:**
```json
{
    "success": true,
    "message": "Categories retrieved successfully",
    "data": {
        "count": 15,
        "next": "http://example.com/api/v1/inventory/categories/?page=2",
        "previous": null,
        "results": [
            {
                "id": 1,
                "name": "Electronics",
                "parent": null,
                "level": 0,
                "is_active": true,
                "sort_order": 1,
                "description": "Electronic products and accessories",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "full_path": "Electronics",
                "children": [
                    {
                        "id": 2,
                        "name": "Mobile Phones",
                        "level": 1,
                        "products_count": 25
                    }
                ],
                "products_count": 50
            }
        ]
    }
}
```

### 1.2 Create Category
**POST** `/api/v1/inventory/categories/`

Create a new category.

**Request Body:**
```json
{
    "name": "Electronics",
    "parent": null,
    "level": 0,
    "description": "Electronic products and accessories",
    "sort_order": 1,
    "is_active": true
}
```

**Response (201 Created):**
```json
{
    "success": true,
    "message": "Category created successfully",
    "data": {
        "id": 1,
        "name": "Electronics",
        "parent": null,
        "level": 0,
        "is_active": true,
        "sort_order": 1,
        "description": "Electronic products and accessories",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "full_path": "Electronics",
        "children": [],
        "products_count": 0
    }
}
```

### 1.3 Get Category Details
**GET** `/api/v1/inventory/categories/{id}/`

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Category retrieved successfully",
    "data": {
        "id": 1,
        "name": "Electronics",
        "parent": null,
        "level": 0,
        "is_active": true,
        "sort_order": 1,
        "description": "Electronic products and accessories",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "full_path": "Electronics",
        "children": [],
        "products_count": 0
    }
}
```

### 1.4 Update Category
**PUT** `/api/v1/inventory/categories/{id}/`

**Request Body:**
```json
{
    "name": "Consumer Electronics",
    "description": "Updated description for consumer electronics",
    "sort_order": 2
}
```

### 1.5 Delete Category
**DELETE** `/api/v1/inventory/categories/{id}/`

**Response (204 No Content)**

---

## 2. Suppliers API

### Supplier Model Fields

#### Basic Information
- `id` (integer, read-only): Unique supplier identifier
- `name` (string, required): Supplier name (max 255 characters)
- `contact_person` (string, optional): Contact person name
- `phone` (string, optional): Phone number
- `email` (email, optional): Email address
- `address` (text, optional): Supplier address
- `tax_number` (string, optional): Tax identification number
- `payment_terms` (string, optional): Payment terms description
- `is_active` (boolean, default: true): Whether supplier is active
- `rating` (integer, default: 5): Supplier rating (1-5)
- `notes` (text, optional): Additional notes

#### System Fields (Read-only)
- `created_at` (datetime): Supplier creation timestamp
- `updated_at` (datetime): Last update timestamp
- `products_count` (integer): Number of products from this supplier

### 2.1 List Suppliers
**GET** `/api/v1/inventory/suppliers/`

**Query Parameters:**
- `is_active` (boolean): Filter by active status
- `rating` (integer): Filter by rating
- `search` (string): Search in name, contact_person, email
- `ordering` (string): Sort by fields (name, rating, created_at)

**Example Response:**
```json
{
    "success": true,
    "message": "Suppliers retrieved successfully",
    "data": {
        "count": 5,
        "results": [
            {
                "id": 1,
                "name": "TechSupply Co.",
                "contact_person": "John Doe",
                "phone": "1234567890",
                "email": "<EMAIL>",
                "address": "123 Tech Street, Tech City",
                "tax_number": "TAX123456",
                "payment_terms": "Net 30",
                "is_active": true,
                "rating": 4,
                "notes": "Reliable supplier for electronics",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "products_count": 15
            }
        ]
    }
}
```

### 2.2 Create Supplier
**POST** `/api/v1/inventory/suppliers/`

**Request Body:**
```json
{
    "name": "TechSupply Co.",
    "contact_person": "John Doe",
    "phone": "1234567890",
    "email": "<EMAIL>",
    "address": "123 Tech Street, Tech City",
    "tax_number": "TAX123456",
    "payment_terms": "Net 30",
    "rating": 4,
    "notes": "Reliable supplier for electronics"
}
```

---

## 3. Locations API

### Location Model Fields

#### Basic Information
- `id` (integer, read-only): Unique location identifier
- `name` (string, required): Location name (max 100 characters)
- `code` (string, required): Unique location code (max 20 characters)
- `location_type` (string, default: "store"): Type of location
  - `"warehouse"`: Warehouse
  - `"store"`: Store
  - `"section"`: Section
- `address` (text, optional): Location address
- `is_active` (boolean, default: true): Whether location is active

#### System Fields (Read-only)
- `created_at` (datetime): Location creation timestamp
- `stock_batches_count` (integer): Number of stock batches at this location

### 3.1 List Locations
**GET** `/api/v1/inventory/locations/`

**Example Response:**
```json
{
    "success": true,
    "message": "Locations retrieved successfully",
    "data": {
        "count": 3,
        "results": [
            {
                "id": 1,
                "name": "Main Warehouse",
                "code": "WH001",
                "location_type": "warehouse",
                "address": "456 Storage Ave, Storage City",
                "is_active": true,
                "created_at": "2024-01-15T10:30:00Z",
                "stock_batches_count": 125
            }
        ]
    }
}
```

---

## 4. Product Brands API

### Brand Model Fields

#### Basic Information
- `id` (integer, read-only): Unique brand identifier
- `name` (string, required): Brand name (max 100 characters)
- `logo` (file, optional): Brand logo image
- `is_active` (boolean, default: true): Whether brand is active

#### System Fields (Read-only)
- `created_at` (datetime): Brand creation timestamp
- `products_count` (integer): Number of products with this brand

### 4.1 List Brands
**GET** `/api/v1/inventory/brands/`

**Example Response:**
```json
{
    "success": true,
    "message": "Brands retrieved successfully",
    "data": {
        "count": 10,
        "results": [
            {
                "id": 1,
                "name": "Samsung",
                "logo": "/media/brands/samsung_logo.png",
                "is_active": true,
                "created_at": "2024-01-15T10:30:00Z",
                "products_count": 25
            }
        ]
    }
}
```

---

## 5. Products API

### Product Model Fields

#### Basic Information
- `id` (integer, read-only): Unique product identifier
- `name` (string, required): Product name (max 255 characters)
- `description` (text, optional): Product description
- `category` (integer, optional): Category ID
- `brand` (integer, optional): Brand ID
- `supplier` (integer, optional): Supplier ID
- `sku` (string, optional): Stock Keeping Unit (max 64 characters)
- `barcode` (string, optional): Product barcode
- `qr_code` (string, optional): QR code

#### Pricing
- `purchase_price` (decimal, default: 0.00): Purchase price per unit
- `selling_price` (decimal, required): Selling price per unit
- `wholesale_price` (decimal, optional): Wholesale price per unit
- `minimum_selling_price` (decimal, optional): Minimum allowed selling price

#### Stock Management
- `track_stock` (boolean, default: true): Whether to track stock levels
- `allow_negative_stock` (boolean, default: false): Allow negative stock
- `reorder_level` (integer, default: 5): Minimum stock level for reorder alert
- `maximum_stock_level` (integer, optional): Maximum stock level

#### Physical Attributes
- `unit` (string, default: "each"): Unit of measurement
- `unit_custom_label` (string, optional): Custom unit label
- `weight` (decimal, optional): Product weight
- `dimensions` (string, optional): Product dimensions (L×W×H)

#### Tax and Accounting
- `tax_rate` (decimal, default: 0.00): Tax rate percentage
- `tax_exempt` (boolean, default: false): Whether product is tax exempt

#### Status and Metadata
- `is_active` (boolean, default: true): Whether product is active
- `is_featured` (boolean, default: false): Whether product is featured
- `tags` (string, optional): Comma-separated tags

#### System Fields (Read-only)
- `created_at` (datetime): Product creation timestamp
- `updated_at` (datetime): Last update timestamp
- `current_stock` (integer): Current total stock across all locations
- `stock_value` (decimal): Total stock value
- `images` (array): Product images
- `stock_batches` (array): Stock batches for this product

### 5.1 List Products
**GET** `/api/v1/inventory/products/`

**Query Parameters:**
- `category` (integer): Filter by category ID
- `brand` (integer): Filter by brand ID
- `supplier` (integer): Filter by supplier ID
- `is_active` (boolean): Filter by active status
- `track_stock` (boolean): Filter by stock tracking
- `search` (string): Search in name, description, sku, barcode, tags
- `ordering` (string): Sort by fields (name, selling_price, created_at, reorder_level)

**Example Response:**
```json
{
    "success": true,
    "message": "Products retrieved successfully",
    "data": {
        "count": 50,
        "results": [
            {
                "id": 1,
                "name": "Samsung Galaxy S24",
                "description": "Latest Samsung smartphone with advanced features",
                "category": {
                    "id": 2,
                    "name": "Mobile Phones",
                    "full_path": "Electronics > Mobile Phones"
                },
                "brand": {
                    "id": 1,
                    "name": "Samsung"
                },
                "supplier": {
                    "id": 1,
                    "name": "TechSupply Co."
                },
                "sku": "SAM-GS24-001",
                "barcode": "1234567890123",
                "purchase_price": "800.00",
                "selling_price": "1200.00",
                "wholesale_price": "1000.00",
                "reorder_level": 5,
                "unit": "each",
                "weight": "0.200",
                "tax_rate": "10.00",
                "is_active": true,
                "tags": "smartphone, electronics, samsung",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:30:00Z",
                "current_stock": 25,
                "stock_value": "20000.00",
                "images": [
                    {
                        "id": 1,
                        "image": "/media/products/galaxy_s24.jpg",
                        "is_primary": true
                    }
                ]
            }
        ]
    }
}
```

### 5.2 Create Product
**POST** `/api/v1/inventory/products/`

**Request Body:**
```json
{
    "name": "Samsung Galaxy S24",
    "description": "Latest Samsung smartphone with advanced features",
    "category": 2,
    "brand": 1,
    "supplier": 1,
    "sku": "SAM-GS24-001",
    "barcode": "1234567890123",
    "purchase_price": "800.00",
    "selling_price": "1200.00",
    "wholesale_price": "1000.00",
    "reorder_level": 5,
    "unit": "each",
    "weight": "0.200",
    "tax_rate": "10.00",
    "tags": "smartphone, electronics, samsung"
}
```

### 5.3 Get Product Stock Summary
**GET** `/api/v1/inventory/products/{id}/stock_summary/`

Get comprehensive stock information for a product across all locations and batches.

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Stock summary retrieved successfully",
    "data": {
        "product_id": 1,
        "product_name": "Samsung Galaxy S24",
        "total_stock": 25,
        "total_value": "20000.00",
        "reorder_level": 5,
        "stock_status": "adequate",
        "locations": [
            {
                "location_id": 1,
                "location_name": "Main Warehouse",
                "stock_quantity": 25,
                "stock_value": "20000.00",
                "batches": [
                    {
                        "batch_id": 1,
                        "batch_number": "BATCH001",
                        "quantity_available": 25,
                        "expiry_date": "2025-01-15",
                        "days_to_expiry": 365
                    }
                ]
            }
        ]
    }
}
```

### 5.4 Adjust Product Stock
**POST** `/api/v1/inventory/products/{id}/adjust_stock/`

Manually adjust product stock levels with audit trail.

**Request Body:**
```json
{
    "location": 1,
    "adjustment_type": "increase",
    "quantity": 50,
    "reason": "New stock arrival",
    "reference_number": "PO-2024-001",
    "notes": "Fresh stock from supplier XYZ"
}
```

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Stock adjusted successfully",
    "data": {
        "movement_id": 15,
        "product_id": 1,
        "old_quantity": 25,
        "new_quantity": 75,
        "adjustment_quantity": 50,
        "reference_number": "PO-2024-001"
    }
}
```

### 5.5 Get Low Stock Products
**GET** `/api/v1/inventory/products/low_stock/`

Get products that are below their reorder level.

**Query Parameters:**
- `threshold` (integer): Custom threshold override

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Low stock products retrieved successfully",
    "data": {
        "count": 3,
        "results": [
            {
                "id": 5,
                "name": "iPhone 15",
                "sku": "APL-IP15-001",
                "current_stock": 2,
                "reorder_level": 10,
                "stock_status": "critical",
                "last_movement_date": "2024-01-10T15:20:00Z",
                "supplier": {
                    "id": 2,
                    "name": "Apple Authorized"
                }
            }
        ]
    }
}
```

---

## 6. Stock Batches API

### Stock Batch Model Fields

#### Basic Information
- `id` (integer, read-only): Unique batch identifier
- `product` (integer, required): Product ID
- `location` (integer, required): Location ID
- `supplier` (integer, optional): Supplier ID
- `batch_number` (string, required): Unique batch number
- `purchase_order_number` (string, optional): Purchase order reference

#### Quantities
- `quantity_received` (integer, required): Quantity initially received
- `quantity_available` (integer, required): Current available quantity
- `quantity_reserved` (integer, default: 0): Reserved quantity
- `quantity_damaged` (integer, default: 0): Damaged quantity

#### Pricing and Dates
- `purchase_price` (decimal, required): Purchase price per unit
- `received_date` (date, default: today): Date batch was received
- `manufacture_date` (date, optional): Manufacturing date
- `expiry_date` (date, optional): Expiry date

#### Status
- `status` (string, default: "active"): Batch status
  - `"active"`: Active
  - `"reserved"`: Reserved
  - `"expired"`: Expired
  - `"damaged"`: Damaged
  - `"sold_out"`: Sold Out
- `notes` (text, optional): Additional notes

#### System Fields (Read-only)
- `created_at` (datetime): Batch creation timestamp
- `updated_at` (datetime): Last update timestamp
- `days_to_expiry` (integer): Days until expiry (if expiry_date set)

### 6.1 List Stock Batches
**GET** `/api/v1/inventory/batches/`

**Query Parameters:**
- `product` (integer): Filter by product ID
- `location` (integer): Filter by location ID
- `supplier` (integer): Filter by supplier ID
- `status` (string): Filter by batch status
- `search` (string): Search in batch_number, purchase_order_number
- `ordering` (string): Sort by fields (received_date, expiry_date, quantity_available)

**Example Response:**
```json
{
    "success": true,
    "message": "Stock batches retrieved successfully",
    "data": {
        "count": 25,
        "results": [
            {
                "id": 1,
                "product": {
                    "id": 1,
                    "name": "Samsung Galaxy S24",
                    "sku": "SAM-GS24-001"
                },
                "location": {
                    "id": 1,
                    "name": "Main Warehouse",
                    "code": "WH001"
                },
                "supplier": {
                    "id": 1,
                    "name": "TechSupply Co."
                },
                "batch_number": "BATCH001",
                "purchase_order_number": "PO2024001",
                "quantity_received": 100,
                "quantity_available": 75,
                "quantity_reserved": 0,
                "quantity_damaged": 0,
                "purchase_price": "800.00",
                "received_date": "2024-01-20",
                "manufacture_date": "2024-01-15",
                "expiry_date": "2025-01-15",
                "status": "active",
                "notes": "First batch of Galaxy S24",
                "created_at": "2024-01-20T09:00:00Z",
                "updated_at": "2024-01-20T09:00:00Z",
                "days_to_expiry": 365
            }
        ]
    }
}
```

### 6.2 Create Stock Batch
**POST** `/api/v1/inventory/batches/`

**Request Body:**
```json
{
    "product": 1,
    "location": 1,
    "supplier": 1,
    "batch_number": "BATCH001",
    "purchase_order_number": "PO2024001",
    "quantity_received": 100,
    "quantity_available": 100,
    "purchase_price": "800.00",
    "received_date": "2024-01-20",
    "manufacture_date": "2024-01-15",
    "expiry_date": "2025-01-15",
    "notes": "First batch of Galaxy S24"
}
```

### 6.3 Get Expiring Batches
**GET** `/api/v1/inventory/batches/expiring_soon/`

Get batches that are expiring within a specified number of days.

**Query Parameters:**
- `days` (integer, default: 30): Number of days to check for expiry

**Response (200 OK):**
```json
{
    "success": true,
    "message": "Expiring batches retrieved successfully",
    "data": {
        "count": 2,
        "results": [
            {
                "id": 5,
                "product": {
                    "id": 3,
                    "name": "Milk Powder",
                    "sku": "FOOD-MP-001"
                },
                "batch_number": "BATCH005",
                "quantity_available": 15,
                "expiry_date": "2024-02-15",
                "days_to_expiry": 25,
                "status": "active",
                "location": {
                    "id": 1,
                    "name": "Main Warehouse"
                }
            }
        ]
    }
}
```

---

## 7. Stock Movements API

### Stock Movement Model Fields (Read-only)

#### Basic Information
- `id` (integer): Unique movement identifier
- `product` (object): Product information
- `batch` (object): Batch information
- `location` (object): Location information
- `movement_type` (string): Type of movement
  - `"purchase"`: Purchase
  - `"sale"`: Sale
  - `"return"`: Return
  - `"adjustment"`: Adjustment
  - `"transfer"`: Transfer
  - `"damage"`: Damage
  - `"expired"`: Expired

#### Movement Details
- `quantity` (integer): Quantity moved (negative for outgoing)
- `reference_number` (string): Reference number
- `reference_type` (string): Type of reference
- `reference_id` (integer): Reference ID
- `notes` (text): Movement notes

#### System Fields
- `created_at` (datetime): Movement timestamp
- `created_by` (object): User who created the movement

### 7.1 List Stock Movements
**GET** `/api/v1/inventory/movements/`

**Query Parameters:**
- `product` (integer): Filter by product ID
- `batch` (integer): Filter by batch ID
- `location` (integer): Filter by location ID
- `movement_type` (string): Filter by movement type
- `date_from` (date): Filter movements from date
- `date_to` (date): Filter movements to date
- `ordering` (string): Sort by fields (created_at, quantity)

**Example Response:**
```json
{
    "success": true,
    "message": "Stock movements retrieved successfully",
    "data": {
        "count": 100,
        "results": [
            {
                "id": 1,
                "product": {
                    "id": 1,
                    "name": "Samsung Galaxy S24",
                    "sku": "SAM-GS24-001"
                },
                "batch": {
                    "id": 1,
                    "batch_number": "BATCH001"
                },
                "location": {
                    "id": 1,
                    "name": "Main Warehouse"
                },
                "movement_type": "purchase",
                "quantity": 100,
                "reference_number": "PO2024001",
                "reference_type": "purchase_order",
                "reference_id": 1,
                "notes": "Initial stock purchase",
                "created_at": "2024-01-20T09:00:00Z",
                "created_by": {
                    "id": 1,
                    "mobile_number": "1234567890"
                }
            }
        ]
    }
}
```

---

## Error Codes

### Common HTTP Status Codes

- **200 OK**: Request successful
- **201 Created**: Resource created successfully
- **204 No Content**: Resource deleted successfully
- **400 Bad Request**: Invalid request data
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Access denied
- **404 Not Found**: Resource not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Validation Error Examples

```json
{
    "success": false,
    "message": "Validation failed",
    "errors": {
        "selling_price": ["This field is required."],
        "sku": ["Product with this SKU already exists."],
        "reorder_level": ["Ensure this value is greater than or equal to 0."]
    }
}
```

---

## Rate Limiting

API requests are rate-limited to prevent abuse:
- **Standard endpoints**: 100 requests per minute
- **Bulk operations**: 10 requests per minute
- **Search endpoints**: 50 requests per minute

---

## Pagination

List endpoints support pagination with the following parameters:
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 20, max: 100)

Pagination response includes:
- `count`: Total number of items
- `next`: URL to next page (if exists)
- `previous`: URL to previous page (if exists)
- `results`: Array of items for current page

---

## Filtering and Search

### Filtering
Most list endpoints support filtering by relevant fields:
```
GET /api/v1/inventory/products/?category=1&is_active=true&brand=2
```

### Search
Search functionality is available on specified fields:
```
GET /api/v1/inventory/products/?search=samsung galaxy
```

### Ordering
Results can be ordered by specified fields:
```
GET /api/v1/inventory/products/?ordering=-created_at,name
```
Use `-` prefix for descending order.

---

## Integration Examples

### Creating a Complete Product with Stock

```javascript
// 1. Create Category
const category = await fetch('/api/v1/inventory/categories/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        name: 'Electronics',
        level: 0
    })
}).then(r => r.json());

// 2. Create Supplier
const supplier = await fetch('/api/v1/inventory/suppliers/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        name: 'TechSupply Co.',
        contact_person: 'John Doe',
        email: '<EMAIL>'
    })
}).then(r => r.json());

// 3. Create Product
const product = await fetch('/api/v1/inventory/products/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        name: 'Samsung Galaxy S24',
        category: category.data.id,
        supplier: supplier.data.id,
        sku: 'SAM-GS24-001',
        selling_price: '1200.00',
        purchase_price: '800.00',
        reorder_level: 5
    })
}).then(r => r.json());

// 4. Add Stock Batch
const batch = await fetch('/api/v1/inventory/batches/', {
    method: 'POST',
    headers: {
        'Authorization': 'Bearer ' + token,
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        product: product.data.id,
        location: 1, // Assuming location exists
        batch_number: 'BATCH001',
        quantity_received: 100,
        quantity_available: 100,
        purchase_price: '800.00'
    })
}).then(r => r.json());
```

This comprehensive API documentation covers all the inventory management endpoints with detailed examples, error handling, and integration patterns.