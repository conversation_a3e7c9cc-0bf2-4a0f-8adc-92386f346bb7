# Product APIs Quick Reference Guide

## Base URL
```
http://*************:8003/api/v1/inventory/products/
```

## Authentication
All endpoints require authentication:
```http
Authorization: Bearer your_token_here
```

---

## 🚀 Quick Start Endpoints

### 1. **Comprehensive Product Detail** (Recommended)
**GET** `/api/v1/inventory/products/{id}/comprehensive_detail/`

✅ **Use this for**: Complete product pages, dashboards, detailed views
📦 **Returns**: Product info + Stock management + Sales analytics + Related endpoints

```bash
curl -H "Authorization: Bearer TOKEN" \
     http://*************:8003/api/v1/inventory/products/16/comprehensive_detail/
```

### 2. **Basic Product Detail**
**GET** `/api/v1/inventory/products/{id}/`

✅ **Use this for**: Product lists, quick lookups, basic info
📦 **Returns**: Essential product information only

---

## 📊 Stock Management Endpoints

| Endpoint | Method | Purpose |
|----------|---------|----------|
| `/stock_summary/` | GET | Stock levels & batch info |
| `/stock_by_location/` | GET | Stock across locations |
| `/stock_valuation/` | GET | FIFO/LIFO/Avg valuation |
| `/adjust_stock/` | POST | Manual stock adjustments |
| `/transfer_stock/` | POST | Transfer between locations |
| `/reserve_stock/` | POST | Reserve for orders |

---

## 📈 Sales & Analytics Endpoints

| Endpoint | Method | Parameters |
|----------|---------|-------------|
| `/sales_history/` | GET | `time_filter`, `page`, `search` |

**Time Filters**: `today`, `last_7_days`, `last_30_days`, `this_month`, `this_year`

---

## 💻 Common Request Examples

### Get Product Detail
```javascript
fetch('http://*************:8003/api/v1/inventory/products/16/comprehensive_detail/', {
  headers: { 'Authorization': 'Bearer YOUR_TOKEN' }
})
```

### Adjust Stock
```javascript
fetch('http://*************:8003/api/v1/inventory/products/16/adjust_stock/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    location: 3,
    adjustment_type: 'add',
    quantity: 25.0,
    reason: 'Stock correction'
  })
})
```

### Transfer Stock
```javascript
fetch('http://*************:8003/api/v1/inventory/products/16/transfer_stock/', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    quantity: 30.0,
    from_location_id: 3,
    to_location_id: 4,
    notes: 'Transfer to warehouse'
  })
})
```

---

## 🎯 Response Format

All APIs return this structure:
```json
{
  "success": true|false,
  "data": { /* response data */ },
  "message": "Error message if success=false"
}
```

---

## ⚠️ Common Error Codes

| Status | Code | Description |
|---------|------|-------------|
| 400 | `INVALID_QUANTITY` | Quantity must be > 0 |
| 400 | `INSUFFICIENT_STOCK` | Not enough stock available |
| 404 | `PRODUCT_NOT_FOUND` | Product doesn't exist |
| 404 | `LOCATION_NOT_FOUND` | Invalid location ID |
| 429 | `RATE_LIMIT_EXCEEDED` | Too many requests |

---

## 📱 Mobile App Integration

### React Native Example
```javascript
const ProductAPI = {
  baseURL: 'http://*************:8003/api/v1/inventory/products',

  async getDetail(productId) {
    const response = await fetch(`${this.baseURL}/${productId}/comprehensive_detail/`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    return response.json();
  },

  async adjustStock(productId, data) {
    const response = await fetch(`${this.baseURL}/${productId}/adjust_stock/`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });
    return response.json();
  }
};
```

### Flutter/Dart Example
```dart
class ProductAPI {
  static const String baseURL = 'http://*************:8003/api/v1/inventory/products';

  static Future<Map<String, dynamic>> getDetail(int productId) async {
    final response = await http.get(
      Uri.parse('$baseURL/$productId/comprehensive_detail/'),
      headers: {'Authorization': 'Bearer $token'},
    );
    return json.decode(response.body);
  }

  static Future<Map<String, dynamic>> adjustStock(int productId, Map<String, dynamic> data) async {
    final response = await http.post(
      Uri.parse('$baseURL/$productId/adjust_stock/'),
      headers: {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      },
      body: json.encode(data),
    );
    return json.decode(response.body);
  }
}
```

---

## 🔄 Real-time Updates

For real-time stock updates, consider:

1. **Polling**: Call comprehensive_detail every 30-60 seconds
2. **WebSockets**: Connect to `/ws/inventory/{product_id}/` (if available)
3. **Server-Sent Events**: Subscribe to stock update events

---

## 🎯 Best Practices

✅ **DO:**
- Use `comprehensive_detail` for full product pages
- Cache responses for 30-60 seconds
- Handle rate limits gracefully
- Validate data before sending POST requests

❌ **DON'T:**
- Make multiple API calls when one comprehensive call will do
- Ignore error responses
- Send invalid location/product IDs
- Make too many rapid requests

---

## 📞 Support

- **Full Documentation**: `/Documentation/APIs/product_detail_apis.md`
- **API Testing**: Use tools like Postman or curl
- **Issues**: Check response error messages first

**Last Updated**: September 25, 2025