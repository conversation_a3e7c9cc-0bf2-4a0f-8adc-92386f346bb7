# Manual Sales Management APIs

This document provides comprehensive details about the Manual Sales Management API endpoints available in the Zayyrah POS system.

## Overview

The Manual Sales API allows you to record sales transactions that don't involve inventory products from your POS system. This is perfect for services, custom orders, or any transaction that doesn't require stock deduction.

## Base URL
```
http://your-domain.com/api/v1/pos/manual-sales/
```

## Authentication
All Manual Sales API endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer YOUR_ACCESS_TOKEN
```

## Manual Sale Model Fields

### Core Information
- `id` (integer, read-only): Unique manual sale identifier
- `customer` (object, optional): Associated customer information
- `manual_description` (string, required): Description of what was sold/service provided
- `manual_category` (string, optional): Category for organizing manual sales

### Financial Information
- `subtotal` (decimal): Subtotal amount before tax and discounts
- `tax_total` (decimal): Total tax amount
- `discount_total` (decimal): Total discount amount
- `total` (decimal): Final total amount

### Payment Information
- `payment_method` (string, choices): Payment method used
  - `"cash"`: Cash payment
  - `"credit"`: Credit payment (on account)
  - `"mixed"`: Cash + Credit combination
- `payment_status` (string, read-only): Calculated payment status
  - `"full"`: Fully Paid
  - `"partial"`: Partially Paid
  - `"pending"`: Payment Pending
  - `"credit"`: On Credit
- `amount_paid` (decimal): Amount actually paid by customer
- `cash_amount` (decimal): Cash portion of payment (for mixed payments)
- `credit_amount` (decimal): Credit portion of payment (for mixed payments)

### Calculated Fields (Read-only)
- `remaining_balance` (decimal): Outstanding amount (total - amount_paid)
- `is_fully_paid` (boolean): Whether payment is complete
- `payment_method_display` (string): Human-readable payment method
- `payment_status_display` (string): Human-readable payment status

### System Fields
- `notes` (text, optional): Additional notes about the sale
- `created_at` (datetime, read-only): Sale creation timestamp
- `updated_at` (datetime, read-only): Last update timestamp

## API Endpoints

### 1. List Manual Sales
**GET** `/api/v1/pos/manual-sales/`

Retrieve a paginated list of manual sales with comprehensive filtering and search capabilities.

#### Query Parameters
- `search` (string, optional): Search in manual_description, manual_category, and notes
- `customer` (integer, optional): Filter by customer ID
- `payment_method` (string, optional): Filter by payment method (cash, credit, mixed)
- `payment_status` (string, optional): Filter by payment status (full, partial, pending, credit)
- `manual_category` (string, optional): Filter by category
- `date_from` (date, optional): Filter sales from this date (YYYY-MM-DD)
- `date_to` (date, optional): Filter sales until this date (YYYY-MM-DD)
- `min_amount` (decimal, optional): Filter sales with minimum total amount
- `max_amount` (decimal, optional): Filter sales with maximum total amount
- `ordering` (string, optional): Order results by field (e.g., `total`, `-created_at`)
- `page` (integer, optional): Page number for pagination

#### Request Example
```bash
curl -X GET "http://localhost:8002/api/v1/pos/manual-sales/?search=laptop&manual_category=IT%20Services&payment_status=pending" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "count": 1,
    "next": null,
    "previous": null,
    "results": [
      {
        "id": 2,
        "customer": {
          "id": 6,
          "name": "Ahmed Ali",
          "mobile_number": "***********"
        },
        "manual_description": "Laptop repair service",
        "manual_category": "IT Services",
        "subtotal": "800.00",
        "tax_total": "120.00",
        "discount_total": "20.00",
        "total": "900.00",
        "payment_method": "credit",
        "payment_method_display": "Credit",
        "payment_status": "pending",
        "payment_status_display": "Payment Pending",
        "amount_paid": "0.00",
        "credit_amount": "900.00",
        "cash_amount": "0.00",
        "notes": "MacBook Pro screen replacement - Due for payment next week",
        "created_at": "2025-09-18T19:54:00.354140Z",
        "updated_at": "2025-09-18T19:54:00.354164Z"
      }
    ],
    "summary": {
      "total_sales": 1,
      "total_amount": "900.00",
      "total_paid": "0.00",
      "pending_amount": "900.00"
    }
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required

---

### 2. Create Manual Sale
**POST** `/api/v1/pos/manual-sales/`

Create a new manual sale record with comprehensive payment tracking.

#### Request Body
```json
{
  "customer_id": 6,
  "manual_description": "Tea and snacks service for office meeting",
  "manual_category": "Catering Services",
  "subtotal": "450.00",
  "tax_total": "67.50",
  "discount_total": "17.50",
  "total": "500.00",
  "payment_method": "mixed",
  "amount_paid": "300.00",
  "cash_amount": "200.00",
  "credit_amount": "100.00",
  "notes": "Delivered to Blue Office Complex - Meeting Room A"
}
```

#### Validation Rules
- `manual_description` is required and cannot be empty
- `total` cannot be negative
- `amount_paid` cannot be negative or exceed total
- For mixed payments: `cash_amount + credit_amount` must equal `amount_paid`
- `customer_id` must exist and belong to the authenticated user
- Financial amounts support up to 10 digits with 2 decimal places

#### Request Example
```bash
curl -X POST "http://localhost:8002/api/v1/pos/manual-sales/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "customer_id": 6,
    "manual_description": "Tea and snacks service for office meeting",
    "manual_category": "Catering Services",
    "subtotal": "450.00",
    "tax_total": "67.50",
    "discount_total": "17.50",
    "total": "500.00",
    "payment_method": "mixed",
    "amount_paid": "300.00",
    "cash_amount": "200.00",
    "credit_amount": "100.00",
    "notes": "Delivered to Blue Office Complex - Meeting Room A"
  }'
```

#### Success Response
```json
{
  "success": true,
  "message": "Manual sale created successfully",
  "data": {
    "id": 1,
    "customer": {
      "id": 6,
      "name": "Ahmed Ali",
      "mobile_number": "***********",
      "email": "<EMAIL>"
    },
    "manual_description": "Tea and snacks service for office meeting",
    "manual_category": "Catering Services",
    "subtotal": "450.00",
    "tax_total": "67.50",
    "discount_total": "17.50",
    "total": "500.00",
    "payment_method": "mixed",
    "payment_method_display": "Cash + Credit",
    "payment_status": "partial",
    "payment_status_display": "Partially Paid",
    "amount_paid": "300.00",
    "credit_amount": "100.00",
    "cash_amount": "200.00",
    "remaining_balance": 200.0,
    "is_fully_paid": false,
    "notes": "Delivered to Blue Office Complex - Meeting Room A",
    "created_at": "2025-09-18T19:51:32.115097Z",
    "updated_at": "2025-09-18T19:51:32.115124Z"
  }
}
```

#### Error Response
```json
{
  "success": false,
  "message": "Manual sale creation failed",
  "errors": {
    "manual_description": ["Manual description is required for manual sales."],
    "cash_amount": ["Cash amount + credit amount must equal amount paid for mixed payments."]
  }
}
```

#### Response Codes
- `201 Created`: Manual sale created successfully
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required

---

### 3. Get Manual Sale Details
**GET** `/api/v1/pos/manual-sales/{sale_id}/`

Retrieve detailed information about a specific manual sale.

#### Path Parameters
- `sale_id` (integer): The unique identifier of the manual sale

#### Request Example
```bash
curl -X GET "http://localhost:8002/api/v1/pos/manual-sales/1/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "id": 1,
    "customer": {
      "id": 6,
      "name": "Ahmed Ali",
      "mobile_number": "***********",
      "email": "<EMAIL>"
    },
    "manual_description": "Tea and snacks service for office meeting",
    "manual_category": "Catering Services",
    "subtotal": "450.00",
    "tax_total": "67.50",
    "discount_total": "17.50",
    "total": "500.00",
    "payment_method": "cash",
    "payment_method_display": "Cash",
    "payment_status": "full",
    "payment_status_display": "Fully Paid",
    "amount_paid": "500.00",
    "credit_amount": "0.00",
    "cash_amount": "500.00",
    "remaining_balance": 0.0,
    "is_fully_paid": true,
    "notes": "Delivered to Blue Office Complex - Meeting Room A. Payment completed in full.",
    "created_at": "2025-09-18T19:51:32.115097Z",
    "updated_at": "2025-09-18T19:53:25.040362Z"
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Manual sale belongs to another user
- `404 Not Found`: Manual sale not found

---

### 4. Update Manual Sale
**PATCH** `/api/v1/pos/manual-sales/{sale_id}/`

Update specific fields of an existing manual sale. Also supports **PUT** for full updates.

#### Path Parameters
- `sale_id` (integer): The unique identifier of the manual sale

#### Request Body (Partial Update Example)
```json
{
  "amount_paid": "500.00",
  "payment_method": "cash",
  "cash_amount": "500.00",
  "credit_amount": "0.00",
  "notes": "Delivered to Blue Office Complex - Meeting Room A. Payment completed in full."
}
```

#### Request Example
```bash
curl -X PATCH "http://localhost:8002/api/v1/pos/manual-sales/1/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -d '{
    "amount_paid": "500.00",
    "payment_method": "cash",
    "cash_amount": "500.00",
    "credit_amount": "0.00",
    "notes": "Payment completed in full."
  }'
```

#### Success Response
```json
{
  "success": true,
  "message": "Manual sale updated successfully",
  "data": {
    "id": 1,
    "customer": {
      "id": 6,
      "name": "Ahmed Ali",
      "mobile_number": "***********",
      "email": "<EMAIL>"
    },
    "manual_description": "Tea and snacks service for office meeting",
    "manual_category": "Catering Services",
    "subtotal": "450.00",
    "tax_total": "67.50",
    "discount_total": "17.50",
    "total": "500.00",
    "payment_method": "cash",
    "payment_method_display": "Cash",
    "payment_status": "full",
    "payment_status_display": "Fully Paid",
    "amount_paid": "500.00",
    "credit_amount": "0.00",
    "cash_amount": "500.00",
    "remaining_balance": 0.0,
    "is_fully_paid": true,
    "notes": "Payment completed in full.",
    "created_at": "2025-09-18T19:51:32.115097Z",
    "updated_at": "2025-09-18T19:55:45.123456Z"
  }
}
```

#### Response Codes
- `200 OK`: Manual sale updated successfully
- `400 Bad Request`: Validation errors
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Manual sale belongs to another user
- `404 Not Found`: Manual sale not found

---

### 5. Delete Manual Sale
**DELETE** `/api/v1/pos/manual-sales/{sale_id}/`

Permanently delete a manual sale record.

#### Path Parameters
- `sale_id` (integer): The unique identifier of the manual sale

#### Request Example
```bash
curl -X DELETE "http://localhost:8002/api/v1/pos/manual-sales/1/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Success Response
```json
{
  "success": true,
  "message": "Manual sale deleted successfully"
}
```

#### Response Codes
- `200 OK`: Manual sale deleted successfully
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Manual sale belongs to another user
- `404 Not Found`: Manual sale not found

---

### 6. Customer Manual Sales
**GET** `/api/v1/customers/{customer_id}/manual-sales/`
**GET** `/api/v1/pos/customers/{customer_id}/manual-sales/`

List all manual sales for a specific customer with summary statistics. Available through both customers and POS API endpoints for convenience.

#### Path Parameters
- `customer_id` (integer): The unique identifier of the customer

#### Query Parameters
- `payment_method` (string, optional): Filter by payment method
- `payment_status` (string, optional): Filter by payment status
- `manual_category` (string, optional): Filter by category
- `date_from` (date, optional): Filter sales from this date
- `date_to` (date, optional): Filter sales until this date
- `ordering` (string, optional): Order results by field

#### Request Example
```bash
# Via customers API
curl -X GET "http://localhost:8002/api/v1/customers/6/manual-sales/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"

# Via POS API (alternative)
curl -X GET "http://localhost:8002/api/v1/pos/customers/6/manual-sales/" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "count": 2,
    "results": [
      {
        "id": 2,
        "customer": {
          "id": 6,
          "name": "Ahmed Ali",
          "mobile_number": "***********"
        },
        "manual_description": "Laptop repair service",
        "manual_category": "IT Services",
        "subtotal": "800.00",
        "tax_total": "120.00",
        "discount_total": "20.00",
        "total": "900.00",
        "payment_method": "credit",
        "payment_method_display": "Credit",
        "payment_status": "pending",
        "payment_status_display": "Payment Pending",
        "amount_paid": "0.00",
        "credit_amount": "900.00",
        "cash_amount": "0.00",
        "notes": "MacBook Pro screen replacement - Due for payment next week",
        "created_at": "2025-09-18T19:54:00.354140Z",
        "updated_at": "2025-09-18T19:54:00.354164Z"
      },
      {
        "id": 1,
        "customer": {
          "id": 6,
          "name": "Ahmed Ali",
          "mobile_number": "***********"
        },
        "manual_description": "Tea and snacks service for office meeting",
        "manual_category": "Catering Services",
        "subtotal": "450.00",
        "tax_total": "67.50",
        "discount_total": "17.50",
        "total": "500.00",
        "payment_method": "cash",
        "payment_method_display": "Cash",
        "payment_status": "full",
        "payment_status_display": "Fully Paid",
        "amount_paid": "500.00",
        "credit_amount": "0.00",
        "cash_amount": "500.00",
        "notes": "Payment completed in full.",
        "created_at": "2025-09-18T19:51:32.115097Z",
        "updated_at": "2025-09-18T19:53:25.040362Z"
      }
    ],
    "customer": {
      "id": 6,
      "name": "Ahmed Ali",
      "mobile_number": "***********"
    },
    "summary": {
      "total_sales": 2,
      "total_amount": "1400.00",
      "total_paid": "500.00",
      "outstanding_balance": "900.00"
    }
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required
- `404 Not Found`: Customer not found

---

### 7. Manual Sales Analytics
**GET** `/api/v1/pos/manual-sales/analytics/`

Get comprehensive analytics and statistics for manual sales.

#### Query Parameters
- `period` (string, optional): Time period for analytics
  - `"today"`: Today only
  - `"week"`: Last 7 days
  - `"month"`: Last 30 days (default)
  - `"year"`: Last 365 days

#### Request Example
```bash
curl -X GET "http://localhost:8002/api/v1/pos/manual-sales/analytics/?period=month" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

#### Response Format
```json
{
  "success": true,
  "data": {
    "period": "month",
    "date_range": {
      "start_date": "2025-08-19",
      "end_date": "2025-09-18"
    },
    "summary": {
      "total_manual_sales": 2,
      "total_amount": "1400.00",
      "total_paid": "500.00",
      "pending_amount": "900.00",
      "average_sale": "700.00"
    },
    "payment_methods": {
      "cash": {
        "count": 1,
        "amount": "500.00"
      },
      "credit": {
        "count": 1,
        "amount": "900.00"
      },
      "mixed": {
        "count": 0,
        "amount": "0.00"
      }
    },
    "payment_status": {
      "fully_paid": 1,
      "partially_paid": 0,
      "pending": 1
    },
    "top_categories": [
      {
        "category": "IT Services",
        "total_sales": 1,
        "total_revenue": "900.00",
        "total_paid": "0.00"
      },
      {
        "category": "Catering Services",
        "total_sales": 1,
        "total_revenue": "500.00",
        "total_paid": "500.00"
      }
    ],
    "top_customers": [
      {
        "id": 6,
        "name": "Ahmed Ali",
        "mobile_number": "***********",
        "total_sales": 2,
        "total_amount": "1400.00",
        "total_paid": "500.00",
        "outstanding": "900.00"
      }
    ]
  }
}
```

#### Response Codes
- `200 OK`: Success
- `401 Unauthorized`: Authentication required

---

## Payment Method Examples

### Cash Payment
```json
{
  "payment_method": "cash",
  "amount_paid": "500.00",
  "cash_amount": "500.00",
  "credit_amount": "0.00"
}
```

### Credit Payment (On Account)
```json
{
  "payment_method": "credit",
  "amount_paid": "0.00",
  "cash_amount": "0.00",
  "credit_amount": "500.00"
}
```

### Mixed Payment (Cash + Credit)
```json
{
  "payment_method": "mixed",
  "amount_paid": "300.00",
  "cash_amount": "200.00",
  "credit_amount": "100.00"
}
```

### Partial Payment
```json
{
  "payment_method": "cash",
  "total": "500.00",
  "amount_paid": "300.00",
  "cash_amount": "300.00",
  "credit_amount": "0.00"
}
```

## Common Use Cases

### 1. Service-Based Business
- IT services and repairs
- Consulting and professional services
- Home services (cleaning, gardening)
- Beauty and wellness services

### 2. Custom Orders
- Made-to-order products
- Customization services
- Bulk orders with special pricing

### 3. Event Services
- Catering services
- Event planning
- Equipment rental

### 4. Subscription Services
- Monthly maintenance contracts
- Software licenses
- Membership fees

## Error Handling

All API endpoints follow a consistent error response format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Specific error message"]
  }
}
```

### Common HTTP Status Codes
- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication credentials missing or invalid
- `403 Forbidden`: User doesn't have permission to access the resource
- `404 Not Found`: Requested resource not found
- `500 Internal Server Error`: Server error

## Data Validation Rules

### Manual Description
- Required field, cannot be empty
- Used for searching and identifying the sale

### Financial Fields
- Maximum 10 digits total with 2 decimal places
- Total cannot be negative
- Amount paid cannot exceed total
- Must be positive numbers

### Payment Method Validation
- For mixed payments: cash_amount + credit_amount must equal amount_paid
- Credit amount automatically calculated for credit payments

### Customer Association
- Customer ID must exist and belong to the authenticated user
- Customer is optional but recommended for tracking

## Security Notes

1. **User Isolation**: Manual sales are isolated by user account
2. **JWT Authentication**: All endpoints require valid JWT token
3. **Input Validation**: All input data is validated before processing
4. **Financial Integrity**: Payment calculations are validated
5. **Permission Checks**: Object-level permissions ensure data privacy

## Integration Examples

### Frontend Integration (JavaScript)
```javascript
// Create manual sale
const createManualSale = async (saleData) => {
  const response = await fetch('/api/v1/pos/manual-sales/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(saleData)
  });

  return await response.json();
};

// Update payment status
const updatePayment = async (saleId, paymentData) => {
  const response = await fetch(`/api/v1/pos/manual-sales/${saleId}/`, {
    method: 'PATCH',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`
    },
    body: JSON.stringify(paymentData)
  });

  return await response.json();
};

// Get customer manual sales
const getCustomerSales = async (customerId) => {
  const response = await fetch(`/api/v1/customers/${customerId}/manual-sales/`, {
    headers: {
      'Authorization': `Bearer ${accessToken}`
    }
  });

  return await response.json();
};
```

### Python Integration Example
```python
import requests

class ManualSalesAPI:
    def __init__(self, base_url, access_token):
        self.base_url = base_url
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {access_token}'
        }

    def create_manual_sale(self, sale_data):
        response = requests.post(
            f'{self.base_url}/api/v1/pos/manual-sales/',
            json=sale_data,
            headers=self.headers
        )
        return response.json()

    def get_customer_sales(self, customer_id, filters=None):
        params = filters or {}
        response = requests.get(
            f'{self.base_url}/api/v1/customers/{customer_id}/manual-sales/',
            params=params,
            headers=self.headers
        )
        return response.json()

    def get_analytics(self, period='month'):
        response = requests.get(
            f'{self.base_url}/api/v1/pos/manual-sales/analytics/',
            params={'period': period},
            headers=self.headers
        )
        return response.json()

    def update_payment(self, sale_id, payment_data):
        response = requests.patch(
            f'{self.base_url}/api/v1/pos/manual-sales/{sale_id}/',
            json=payment_data,
            headers=self.headers
        )
        return response.json()
```

## Best Practices

### 1. Categorization
- Use consistent category names for better reporting
- Create a standard list of categories for your business
- Categories help with analytics and tax reporting

### 2. Payment Tracking
- Always record payments accurately
- Use mixed payment method for cash + credit combinations
- Update payment status as payments are received

### 3. Customer Association
- Link sales to customers whenever possible
- Helps with customer relationship management
- Enables customer-specific reporting

### 4. Documentation
- Use descriptive manual_description fields
- Add relevant notes for future reference
- Include delivery details or service specifics

### 5. Financial Accuracy
- Double-check calculations before creating sales
- Validate tax amounts according to local regulations
- Keep track of outstanding balances

---

*Last Updated: 2025-09-18*
*API Version: v1*
*Manual Sales API Documentation*