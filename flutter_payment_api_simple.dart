// =====================================================
// SIMPLIFIED FLUTTER PAYMENT API - READY TO USE
// =====================================================

import 'dart:convert';
import 'package:http/http.dart' as http;

// =====================================================
// PAYMENT MODEL (Essential)
// =====================================================

class Payment {
  final int id;
  final double amount;
  final String formattedAmount;
  final String paymentType;
  final String paymentTypeDisplay;
  final DateTime paymentDate;
  final String note;
  final String referenceNumber;

  Payment({
    required this.id,
    required this.amount,
    required this.formattedAmount,
    required this.paymentType,
    required this.paymentTypeDisplay,
    required this.paymentDate,
    required this.note,
    required this.referenceNumber,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      amount: (json['amount'] as num).toDouble(),
      formattedAmount: json['formatted_amount'] ?? 'Rs. ${json['amount']}',
      paymentType: json['payment_type'] ?? 'cash',
      paymentTypeDisplay: json['payment_type_display'] ?? 'Cash',
      paymentDate: DateTime.parse(json['payment_date']),
      note: json['note'] ?? '',
      referenceNumber: json['reference_number'] ?? '',
    );
  }
}

// =====================================================
// SIMPLE PAYMENT API SERVICE
// =====================================================

class PaymentAPI {
  static const String baseUrl = 'http://zayyrah.com';
  final String? authToken;

  PaymentAPI({this.authToken});

  Map<String, String> get _headers => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (authToken != null) 'Authorization': 'Bearer $authToken',
  };

  // =====================================================
  // 1. CREATE PAYMENT
  // =====================================================
  Future<Map<String, dynamic>> createPayment({
    required int customerId,
    required double amount,
    required String paymentType, // 'cash', 'card', 'bank_transfer', 'mobile_money', 'cheque', 'other'
    DateTime? paymentDate,
    String? note,
    String? referenceNumber,
  }) async {
    try {
      final url = '$baseUrl/api/v1/customers/$customerId/payments/';

      final body = {
        'amount': amount,
        'payment_type': paymentType,
        if (paymentDate != null) 'payment_date': paymentDate.toIso8601String(),
        if (note != null && note.isNotEmpty) 'note': note,
        if (referenceNumber != null && referenceNumber.isNotEmpty) 'reference_number': referenceNumber,
      };

      print('Creating payment: $body');

      final response = await http.post(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      final result = jsonDecode(response.body);

      if (response.statusCode == 201 && result['success'] == true) {
        return {
          'success': true,
          'payment': Payment.fromJson(result['data']),
          'message': result['message'],
        };
      } else {
        return {
          'success': false,
          'error': result['message'] ?? 'Failed to create payment',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }

  // =====================================================
  // 2. GET PAYMENTS LIST
  // =====================================================
  Future<Map<String, dynamic>> getPayments({
    required int customerId,
    int page = 1,
    int pageSize = 20,
  }) async {
    try {
      final url = '$baseUrl/api/v1/customers/$customerId/payments/?page=$page&page_size=$pageSize';

      print('Fetching payments from: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      final result = jsonDecode(response.body);

      if (response.statusCode == 200 && result['success'] == true) {
        final data = result['data'];
        final payments = (data['payments'] as List)
            .map((p) => Payment.fromJson(p))
            .toList();

        return {
          'success': true,
          'payments': payments,
          'total_amount': data['summary']['total_payments_amount'],
          'total_count': data['summary']['total_payments_count'],
          'customer_name': data['customer']['name'],
          'pagination': {
            'current_page': data['pagination']['current_page'],
            'total_pages': data['pagination']['total_pages'],
            'has_next': data['pagination']['has_next'],
            'has_previous': data['pagination']['has_previous'],
          },
        };
      } else {
        return {
          'success': false,
          'error': result['message'] ?? 'Failed to fetch payments',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }

  // =====================================================
  // 3. UPDATE PAYMENT
  // =====================================================
  Future<Map<String, dynamic>> updatePayment({
    required int customerId,
    required int paymentId,
    double? amount,
    String? paymentType,
    DateTime? paymentDate,
    String? note,
    String? referenceNumber,
  }) async {
    try {
      final url = '$baseUrl/api/v1/customers/$customerId/payments/$paymentId/';

      final body = <String, dynamic>{};
      if (amount != null) body['amount'] = amount;
      if (paymentType != null) body['payment_type'] = paymentType;
      if (paymentDate != null) body['payment_date'] = paymentDate.toIso8601String();
      if (note != null) body['note'] = note;
      if (referenceNumber != null) body['reference_number'] = referenceNumber;

      print('Updating payment $paymentId: $body');

      final response = await http.put(
        Uri.parse(url),
        headers: _headers,
        body: jsonEncode(body),
      );

      final result = jsonDecode(response.body);

      if (response.statusCode == 200 && result['success'] == true) {
        return {
          'success': true,
          'payment': Payment.fromJson(result['data']),
          'message': result['message'],
        };
      } else {
        return {
          'success': false,
          'error': result['message'] ?? 'Failed to update payment',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }

  // =====================================================
  // 4. DELETE PAYMENT
  // =====================================================
  Future<Map<String, dynamic>> deletePayment({
    required int customerId,
    required int paymentId,
  }) async {
    try {
      final url = '$baseUrl/api/v1/customers/$customerId/payments/$paymentId/';

      print('Deleting payment $paymentId');

      final response = await http.delete(
        Uri.parse(url),
        headers: _headers,
      );

      final result = jsonDecode(response.body);

      if (response.statusCode == 200 && result['success'] == true) {
        return {
          'success': true,
          'message': result['message'],
        };
      } else {
        return {
          'success': false,
          'error': result['message'] ?? 'Failed to delete payment',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }

  // =====================================================
  // 5. GET SINGLE PAYMENT DETAILS
  // =====================================================
  Future<Map<String, dynamic>> getPaymentDetails({
    required int customerId,
    required int paymentId,
  }) async {
    try {
      final url = '$baseUrl/api/v1/customers/$customerId/payments/$paymentId/';

      final response = await http.get(
        Uri.parse(url),
        headers: _headers,
      );

      final result = jsonDecode(response.body);

      if (response.statusCode == 200 && result['success'] == true) {
        return {
          'success': true,
          'payment': Payment.fromJson(result['data']),
        };
      } else {
        return {
          'success': false,
          'error': result['message'] ?? 'Failed to fetch payment details',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Network error: $e',
      };
    }
  }
}

// =====================================================
// USAGE EXAMPLES
// =====================================================

void main() async {
  // Initialize API service
  final paymentAPI = PaymentAPI(authToken: 'your_auth_token_here');

  // Example 1: Create a payment
  print('=== Creating Payment ===');
  final createResult = await paymentAPI.createPayment(
    customerId: 28,
    amount: 150.75,
    paymentType: 'card',
    paymentDate: DateTime.now(),
    note: 'Flutter API test payment',
    referenceNumber: 'FLUTTER-001',
  );

  if (createResult['success']) {
    final payment = createResult['payment'] as Payment;
    print('✅ Payment created: ${payment.formattedAmount}');
    print('   ID: ${payment.id}');
    print('   Type: ${payment.paymentTypeDisplay}');
    print('   Date: ${payment.paymentDate}');
  } else {
    print('❌ Failed to create payment: ${createResult['error']}');
  }

  // Example 2: Get payments list
  print('\n=== Getting Payments List ===');
  final listResult = await paymentAPI.getPayments(
    customerId: 28,
    page: 1,
    pageSize: 10,
  );

  if (listResult['success']) {
    final payments = listResult['payments'] as List<Payment>;
    final totalAmount = listResult['total_amount'];
    final totalCount = listResult['total_count'];
    final customerName = listResult['customer_name'];

    print('✅ Found $totalCount payments for $customerName');
    print('   Total amount: Rs. $totalAmount');
    print('   Showing ${payments.length} payments:');

    for (final payment in payments.take(3)) {
      print('   - ${payment.formattedAmount} via ${payment.paymentTypeDisplay}');
    }
  } else {
    print('❌ Failed to get payments: ${listResult['error']}');
  }

  // Example 3: Update a payment (if we have one)
  if (createResult['success']) {
    final payment = createResult['payment'] as Payment;

    print('\n=== Updating Payment ===');
    final updateResult = await paymentAPI.updatePayment(
      customerId: 28,
      paymentId: payment.id,
      amount: 200.00,
      note: 'Updated payment note',
    );

    if (updateResult['success']) {
      final updatedPayment = updateResult['payment'] as Payment;
      print('✅ Payment updated: ${updatedPayment.formattedAmount}');
    } else {
      print('❌ Failed to update payment: ${updateResult['error']}');
    }
  }

  // Example 4: Get payment details
  if (createResult['success']) {
    final payment = createResult['payment'] as Payment;

    print('\n=== Getting Payment Details ===');
    final detailsResult = await paymentAPI.getPaymentDetails(
      customerId: 28,
      paymentId: payment.id,
    );

    if (detailsResult['success']) {
      final paymentDetails = detailsResult['payment'] as Payment;
      print('✅ Payment details:');
      print('   Amount: ${paymentDetails.formattedAmount}');
      print('   Type: ${paymentDetails.paymentTypeDisplay}');
      print('   Note: ${paymentDetails.note}');
      print('   Reference: ${paymentDetails.referenceNumber}');
    } else {
      print('❌ Failed to get payment details: ${detailsResult['error']}');
    }
  }

  // Example 5: Delete payment (uncomment to test)
  /*
  if (createResult['success']) {
    final payment = createResult['payment'] as Payment;

    print('\n=== Deleting Payment ===');
    final deleteResult = await paymentAPI.deletePayment(
      customerId: 28,
      paymentId: payment.id,
    );

    if (deleteResult['success']) {
      print('✅ Payment deleted: ${deleteResult['message']}');
    } else {
      print('❌ Failed to delete payment: ${deleteResult['error']}');
    }
  }
  */
}