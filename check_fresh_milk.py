#!/usr/bin/env python3
"""
Check Fresh Milk product settings
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from inventory.models import EnhancedProduct
from accounts.models import User

# Find the user
user = User.objects.filter(mobile_number='***********').first()
if not user:
    print('User not found')
else:
    print(f'User: {user.mobile_number}')

    # Find Fresh Milk product
    fresh_milk = EnhancedProduct.objects.filter(name='Fresh Milk', owner=user).first()
    if fresh_milk:
        print(f'Fresh Milk found:')
        print(f'  - ID: {fresh_milk.id}')
        print(f'  - Name: {fresh_milk.name}')
        print(f'  - Price: ${fresh_milk.selling_price}')
        print(f'  - Unit: {fresh_milk.unit_label}')
        print(f'  - Allow fractional: {fresh_milk.allow_fractional_quantities}')
        print(f'  - Active: {fresh_milk.is_active}')
    else:
        print('Fresh Milk product not found')
        products = EnhancedProduct.objects.filter(owner=user, name__icontains='milk')[:5]
        print('Products containing milk:')
        for p in products:
            print(f'  - {p.name}: ${p.selling_price}/{p.unit_label} - Fractional: {p.allow_fractional_quantities}')

    # Also check all liquid products
    print(f'\nAll products with Liter unit:')
    liter_products = EnhancedProduct.objects.filter(owner=user, unit_label='Liter')[:10]
    for p in liter_products:
        print(f'  - {p.name}: ${p.selling_price}/Liter - Fractional: {p.allow_fractional_quantities}')