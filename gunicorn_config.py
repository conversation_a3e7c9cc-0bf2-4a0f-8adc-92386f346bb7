#!/usr/bin/env python3
"""
Gunicorn configuration file with comprehensive logging
"""

import os

# Server socket
bind = "unix:/run/gunicorn/zayyrah.sock"
umask = 0o007

# Worker processes
workers = 3
worker_class = "sync"
worker_connections = 1000
timeout = 30
keepalive = 2
max_requests = 1000
max_requests_jitter = 50

# Logging
# Access log - captures all HTTP requests
accesslog = "/home/<USER>/ChannabPOS/gunicorn_logs.log"

# Error log - captures application errors and Gunicorn messages
errorlog = "/home/<USER>/ChannabPOS/gunicorn_logs.log"

# Log level for error logging
loglevel = "info"

# Access log format - comprehensive request logging with Cloudflare headers
access_log_format = (
    '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s '
    '"%(f)s" "%(a)s" %(D)s %(L)s %(p)s '
    'cf_ray="%({CF-RAY}i)s" '
    'cf_ipcountry="%({CF-IPCOUNTRY}i)s" '
    'cf_visitor="%({CF-VISITOR}i)s" '
    'cf_connecting_ip="%({CF-CONNECTING-IP}i)s" '
    'x_forwarded_for="%({X-FORWARDED-FOR}i)s" '
    'x_real_ip="%({X-REAL-IP}i)s"'
)

# Capture output from workers (includes Django logs)
capture_output = True

# Enable logging of slow requests
timeout = 120

# Preload application for better memory usage
preload_app = True

# Security
limit_request_line = 4094
limit_request_fields = 100
limit_request_field_size = 8190

# Process naming
proc_name = "zayyrah_gunicorn"

# Restart workers after this many requests (helps prevent memory leaks)
max_requests = 1000
max_requests_jitter = 100

# Worker tmp directory
worker_tmp_dir = "/dev/shm"

# User and group
user = "ubuntu"
group = "www-data"

# Daemonize
daemon = False

# PID file
pidfile = "/run/gunicorn/zayyrah.pid"

# Environment variables
raw_env = [
    "DJANGO_SETTINGS_MODULE=zayyrah.settings",
]

# Enable stats for monitoring
statsd_host = None

# Custom access log format explanation:
# %(h)s - remote address
# %(l)s - remote log name
# %(u)s - user name
# %(t)s - date of the request
# %(r)s - status line (method, path, HTTP version)
# %(s)s - status code
# %(b)s - response length
# %(f)s - referer
# %(a)s - user agent
# %(D)s - time to serve request in microseconds
# %(L)s - request time in decimal seconds
# %(p)s - process ID

def when_ready(server):
    """Called just after the server starts"""
    server.log.info("Zayyrah Gunicorn server is ready. PID: %s", os.getpid())

def worker_int(worker):
    """Called when a worker receives the SIGINT or SIGQUIT signal"""
    worker.log.info("Worker received SIGINT or SIGQUIT signal")

def pre_fork(server, worker):
    """Called just before a worker is forked"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def post_fork(server, worker):
    """Called just after a worker has been forked"""
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def worker_abort(worker):
    """Called when a worker receives the SIGABRT signal"""
    worker.log.info("Worker received SIGABRT signal")