# 🚀 COMPLETE POS API GUIDE - Ready for Web Integration

## 🌐 Base Information
**Live URL:** `http://*************:8002/inventory/pos/`
**API Base:** `http://*************:8002/inventory/pos/api/`
**Status:** ✅ Production Ready

---

## 🔐 Authentication
All APIs require user authentication via Django session or API token.

**Login First:**
```bash
curl -X POST "http://*************:8002/api/v1/accounts/login/" \
  -H "Content-Type: application/json" \
  -d '{
    "mobile_number": "***********",
    "password": "Test54321"
  }'
```

**Include in Headers:**
```json
{
  "Content-Type": "application/json",
  "X-CSRFToken": "your_csrf_token",
  "Cookie": "sessionid=your_session_id"
}
```

---

## 📋 1. PRODUCTS API

### GET Products List
**URL:** `GET /inventory/pos/api/products/`

**Parameters:**
```
?category=1&search=milk
```

**Response:**
```json
{
  "success": true,
  "products": [
    {
      "id": 3,
      "name": "Fresh Milk (POS)",
      "sku": "MILK-001",
      "barcode": "**********",
      "price": 150.0,
      "original_price": 150.0,
      "current_stock": 65.0,
      "unit_label": "Liter",
      "category_name": "Dairy",
      "allow_discount": true,
      "max_discount_percent": 10.0,
      "tax_rate": 0.0,
      "image_url": "/media/products/milk.jpg"
    }
  ],
  "total_count": 1
}
```

---

## 🛒 2. TRANSACTION LIFECYCLE

### Step 1: Create Transaction
**URL:** `POST /inventory/pos/api/transactions/`

**Request:**
```json
{
  "customer_id": 24,
  "notes": "Walk-in customer purchase"
}
```

**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": 13,
    "transaction_number": "20250921025522-1-5693",
    "status": "active",
    "total_amount": 0.0,
    "customer_name": "Walk-in Customer"
  }
}
```

### Step 2: Add Items
**URL:** `POST /inventory/pos/api/transactions/{transaction_id}/items/`

**Request:**
```json
{
  "product_id": 3,
  "quantity": 2.5,
  "unit_price": 150.0,
  "discount_percent": 5.0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Added 2.5 Liter of Fresh Milk (POS)",
  "item": {
    "id": 25,
    "product_id": 3,
    "quantity": 2.5,
    "unit_price": 150.0,
    "line_total": 356.25,
    "discount_amount": 18.75
  },
  "transaction_total": 356.25
}
```

### Step 3: Get Transaction Details
**URL:** `GET /inventory/pos/api/transactions/{transaction_id}/`

**Response:**
```json
{
  "success": true,
  "transaction": {
    "id": 13,
    "transaction_number": "20250921025522-1-5693",
    "status": "active",
    "subtotal": 375.0,
    "tax_total": 0.0,
    "discount_total": 18.75,
    "total_amount": 356.25,
    "payment_method": null,
    "payment_status": "pending",
    "amount_paid": 0.0,
    "customer_name": "Walk-in Customer",
    "items": [
      {
        "id": 25,
        "product_id": 3,
        "product_name": "Fresh Milk (POS)",
        "quantity": 2.5,
        "unit_price": 150.0,
        "discount_percent": 5.0,
        "discount_amount": 18.75,
        "line_total": 356.25,
        "tax_amount": 0.0,
        "unit_label": "Liter"
      }
    ],
    "total_items": 2.5,
    "items_count": 1
  }
}
```

### Step 4: Process Payment
**URL:** `POST /inventory/pos/api/transactions/{transaction_id}/payment/`

**Request:**
```json
{
  "payment_method": "cash",
  "amounts": {
    "cash": 356.25,
    "card": 0.0,
    "credit": 0.0
  },
  "customer_payment": 400.0
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "payment_details": {
    "total_amount": 356.25,
    "amount_paid": 356.25,
    "change_due": 43.75,
    "payment_method": "cash",
    "payment_status": "paid"
  },
  "transaction": {
    "id": 13,
    "status": "active",
    "payment_status": "paid"
  }
}
```

### Step 5: Complete Transaction
**URL:** `POST /inventory/pos/api/transactions/{transaction_id}/complete/`

**Request:**
```json
{
  "print_receipt": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Transaction completed successfully",
  "transaction": {
    "id": 13,
    "transaction_number": "20250921025522-1-5693",
    "status": "completed",
    "total_amount": 356.25,
    "payment_status": "paid",
    "completed_at": "2025-09-21T02:55:22.123Z"
  },
  "stock_deductions": [
    {
      "product_id": 3,
      "product_name": "Fresh Milk (POS)",
      "quantity_deducted": 2.5,
      "remaining_stock": 62.5
    }
  ]
}
```

---

## 🛍️ 3. ITEM MANAGEMENT APIs

### Update Item
**URL:** `PUT /inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Request:**
```json
{
  "quantity": 3.0,
  "discount_percent": 10.0
}
```

### Remove Item
**URL:** `DELETE /inventory/pos/api/transactions/{transaction_id}/items/{item_id}/`

**Response:**
```json
{
  "success": true,
  "message": "Item removed successfully",
  "transaction_total": 0.0
}
```

---

## 👥 4. CUSTOMERS API

### Get Customers
**URL:** `GET /inventory/pos/api/customers/`

**Parameters:**
```
?search=walk
```

**Response:**
```json
{
  "success": true,
  "customers": [
    {
      "id": 24,
      "name": "Walk-in Customer",
      "display_name": "Walk-in Customer",
      "mobile_number": null,
      "email": null,
      "credit_limit": 0.0,
      "current_balance": 0.0,
      "is_walk_in": true
    }
  ]
}
```

---

## ✅ 5. VALIDATION APIs

### Validate Item Stock
**URL:** `POST /inventory/pos/api/validate/item/`

**Request:**
```json
{
  "product_id": 3,
  "quantity": 5.0,
  "transaction_id": 13
}
```

**Response:**
```json
{
  "success": true,
  "valid": true,
  "message": "Item can be added",
  "available_stock": 62.5,
  "requested_quantity": 5.0
}
```

### Validate Payment
**URL:** `POST /inventory/pos/api/validate/payment/`

**Request:**
```json
{
  "transaction_id": 13,
  "payment_method": "cash",
  "amounts": {
    "cash": 356.25,
    "card": 0.0,
    "credit": 0.0
  },
  "customer_payment": 400.0
}
```

**Response:**
```json
{
  "success": true,
  "valid": true,
  "validation": {
    "total_required": 356.25,
    "total_provided": 356.25,
    "change_due": 43.75,
    "payment_complete": true
  }
}
```

---

## 📊 6. REPORTS API

### Get Sales Reports
**URL:** `GET /inventory/pos/api/reports/`

**Parameters:**
```
?date_from=2024-12-01&date_to=2024-12-31&report_type=daily
```

**Response:**
```json
{
  "success": true,
  "reports": {
    "summary": {
      "total_sales": 2456.75,
      "total_transactions": 15,
      "average_transaction": 163.78,
      "total_items_sold": 45.5
    },
    "daily_breakdown": [
      {
        "date": "2025-09-21",
        "sales": 356.25,
        "transactions": 1,
        "items": 2.5
      }
    ],
    "top_products": [
      {
        "product_id": 3,
        "product_name": "Fresh Milk (POS)",
        "quantity_sold": 15.5,
        "revenue": 2325.0
      }
    ],
    "payment_methods": {
      "cash": 2100.0,
      "card": 300.0,
      "credit": 56.75
    }
  }
}
```

---

## 💻 7. JAVASCRIPT INTEGRATION EXAMPLE

### Complete POS Flow in JavaScript

```javascript
class POSIntegration {
  constructor(baseUrl = 'http://*************:8002') {
    this.baseUrl = baseUrl;
    this.apiUrl = `${baseUrl}/inventory/pos/api`;
    this.csrfToken = this.getCsrfToken();
  }

  getCsrfToken() {
    const cookieValue = document.cookie.match(/csrftoken=([^;]*)/);
    return cookieValue ? cookieValue[1] : '';
  }

  async makeRequest(endpoint, options = {}) {
    const url = `${this.apiUrl}${endpoint}`;
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': this.csrfToken,
        ...options.headers
      },
      credentials: 'include'
    };

    const response = await fetch(url, { ...defaultOptions, ...options });
    return await response.json();
  }

  // Get all products
  async getProducts(filters = {}) {
    const params = new URLSearchParams(filters);
    return await this.makeRequest(`/products/?${params}`);
  }

  // Create new transaction
  async createTransaction(customerId = null, notes = '') {
    return await this.makeRequest('/transactions/', {
      method: 'POST',
      body: JSON.stringify({
        customer_id: customerId,
        notes: notes
      })
    });
  }

  // Add item to transaction
  async addItem(transactionId, productId, quantity, discountPercent = 0) {
    return await this.makeRequest(`/transactions/${transactionId}/items/`, {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        quantity: quantity,
        discount_percent: discountPercent
      })
    });
  }

  // Get transaction details
  async getTransaction(transactionId) {
    return await this.makeRequest(`/transactions/${transactionId}/`);
  }

  // Process payment
  async processPayment(transactionId, paymentMethod, amounts, customerPayment = 0) {
    return await this.makeRequest(`/transactions/${transactionId}/payment/`, {
      method: 'POST',
      body: JSON.stringify({
        payment_method: paymentMethod,
        amounts: amounts,
        customer_payment: customerPayment
      })
    });
  }

  // Complete transaction
  async completeTransaction(transactionId, printReceipt = false) {
    return await this.makeRequest(`/transactions/${transactionId}/complete/`, {
      method: 'POST',
      body: JSON.stringify({
        print_receipt: printReceipt
      })
    });
  }

  // Validate item before adding
  async validateItem(productId, quantity, transactionId = null) {
    return await this.makeRequest('/validate/item/', {
      method: 'POST',
      body: JSON.stringify({
        product_id: productId,
        quantity: quantity,
        transaction_id: transactionId
      })
    });
  }

  // Get customers
  async getCustomers(search = '') {
    const params = search ? `?search=${encodeURIComponent(search)}` : '';
    return await this.makeRequest(`/customers/${params}`);
  }

  // Get reports
  async getReports(dateFrom = null, dateTo = null, reportType = 'daily') {
    const params = new URLSearchParams();
    if (dateFrom) params.append('date_from', dateFrom);
    if (dateTo) params.append('date_to', dateTo);
    if (reportType) params.append('report_type', reportType);

    return await this.makeRequest(`/reports/?${params}`);
  }
}

// Usage Example
const pos = new POSIntegration();

async function completePOSTransaction() {
  try {
    // 1. Get products
    const products = await pos.getProducts();
    console.log('Products:', products);

    // 2. Create transaction
    const transaction = await pos.createTransaction(24, 'Web API Transaction');
    const transactionId = transaction.transaction.id;
    console.log('Transaction created:', transaction);

    // 3. Add items
    const item = await pos.addItem(transactionId, 3, 2.5, 5.0);
    console.log('Item added:', item);

    // 4. Get updated transaction
    const details = await pos.getTransaction(transactionId);
    console.log('Transaction details:', details);

    // 5. Process payment
    const payment = await pos.processPayment(transactionId, 'cash', {
      cash: details.transaction.total_amount,
      card: 0,
      credit: 0
    }, 400);
    console.log('Payment processed:', payment);

    // 6. Complete transaction
    const completed = await pos.completeTransaction(transactionId, true);
    console.log('Transaction completed:', completed);

    return completed;
  } catch (error) {
    console.error('POS transaction failed:', error);
  }
}

// Run the transaction
completePOSTransaction();
```

---

## 🎯 8. PAYMENT METHODS

### Supported Payment Types:
1. **Cash**: `"cash"`
2. **Card**: `"card"`
3. **Credit**: `"credit"`
4. **Mixed**: `"mixed"`

### Payment Structure:
```json
{
  "payment_method": "mixed",
  "amounts": {
    "cash": 200.0,
    "card": 150.0,
    "credit": 6.25
  },
  "customer_payment": 400.0  // For change calculation
}
```

---

## 🚨 9. ERROR HANDLING

### Common Errors:

**Authentication (401):**
```json
{
  "success": false,
  "error": "Authentication required"
}
```

**Stock Insufficient (400):**
```json
{
  "success": false,
  "error": "Insufficient stock available. Only 2.5 Liter remaining."
}
```

**Product Not Found (404):**
```json
{
  "success": false,
  "error": "Product not found or not available for POS"
}
```

**Validation Error (400):**
```json
{
  "success": false,
  "error": "Invalid request data",
  "details": {
    "quantity": ["This field is required"],
    "product_id": ["Product not found"]
  }
}
```

---

## ✨ 10. KEY FEATURES

### ✅ What Works:
- 🛍️ **Product Management** - Get products with real-time stock
- 🛒 **Transaction Lifecycle** - Create → Add Items → Pay → Complete
- 💳 **Multiple Payment Methods** - Cash, Card, Credit, Mixed
- 👥 **Customer Integration** - Walk-in and registered customers
- 📊 **Real-time Stock Deduction** - Automatic inventory updates
- 🎯 **Flexible Discounts** - Item-level and transaction-level
- 📋 **Comprehensive Reporting** - Sales analytics and insights
- ✅ **Validation APIs** - Stock and payment validation
- 🔄 **Item Management** - Add, update, remove transaction items

### 🎯 Perfect For:
- Web-based POS applications
- Mobile POS apps
- Third-party integrations
- Automated sales processing
- Real-time inventory management

---

## 🌐 FINAL SUMMARY

**Base URL:** `http://*************:8002/inventory/pos/api/`
**Authentication:** Session-based with CSRF tokens
**Format:** JSON requests and responses
**Status:** ✅ Production ready

**Complete API Endpoints:**
1. `GET /products/` - List products
2. `POST /transactions/` - Create transaction
3. `GET /transactions/{id}/` - Get transaction details
4. `POST /transactions/{id}/items/` - Add items
5. `PUT /transactions/{id}/items/{item_id}/` - Update items
6. `DELETE /transactions/{id}/items/{item_id}/` - Remove items
7. `POST /transactions/{id}/payment/` - Process payment
8. `POST /transactions/{id}/complete/` - Complete transaction
9. `GET /customers/` - List customers
10. `POST /validate/item/` - Validate stock
11. `POST /validate/payment/` - Validate payment
12. `GET /reports/` - Sales reports

**🚀 Ready for immediate web integration!**