# 💳 Customer Payment API Endpoints

## Base URL
```
http://zayyrah.com
```

## Authentication
All endpoints require authentication. Include in headers:
```
Authorization: Bearer YOUR_AUTH_TOKEN
Content-Type: application/json
```

---

## 📋 API Endpoints Summary

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/customers/{customer_id}/payments/` | Create new payment |
| GET | `/api/v1/customers/{customer_id}/payments/` | List customer payments |
| GET | `/api/v1/customers/{customer_id}/payments/{payment_id}/` | Get payment details |
| PUT | `/api/v1/customers/{customer_id}/payments/{payment_id}/` | Update payment |
| DELETE | `/api/v1/customers/{customer_id}/payments/{payment_id}/` | Delete payment |

---

## 1. 📝 CREATE PAYMENT

**POST** `/api/v1/customers/{customer_id}/payments/`

### Request Body
```json
{
  "amount": 250.75,
  "payment_type": "card",
  "payment_date": "2025-09-23T18:30:00",
  "note": "Payment for groceries",
  "reference_number": "REF123456"
}
```

### Required Fields
- `amount` (number) - Payment amount (must be > 0)

### Optional Fields
- `payment_type` (string) - Payment method (default: "cash")
- `payment_date` (string) - ISO datetime (default: current time)
- `note` (string) - Payment note (can be empty or null, stored as empty string)
- `reference_number` (string) - Reference number (can be empty or null, stored as empty string)

### Payment Types
- `cash` - Cash
- `card` - Card
- `bank_transfer` - Bank Transfer
- `mobile_money` - Mobile Money
- `cheque` - Cheque
- `other` - Other

### Success Response (201)
```json
{
  "success": true,
  "message": "Payment recorded successfully",
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "John Doe",
    "amount": 250.75,
    "formatted_amount": "Rs. 250.75",
    "payment_type": "card",
    "payment_type_display": "Card",
    "payment_date": "2025-09-23T18:30:00+00:00",
    "note": "Payment for groceries",
    "reference_number": "REF123456",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00"
  }
}
```

### Error Response (400)
```json
{
  "success": false,
  "message": "Invalid amount value"
}
```

---

## 2. 📋 GET PAYMENTS LIST

**GET** `/api/v1/customers/{customer_id}/payments/`

### Query Parameters
- `page` (int) - Page number (default: 1)
- `page_size` (int) - Items per page (default: 20, max: 100)
- `payment_type` (string) - Filter by payment type
- `ordering` (string) - Sort order

### Ordering Options
- `-payment_date` - Newest first (default)
- `payment_date` - Oldest first
- `-amount` - Highest amount first
- `amount` - Lowest amount first

### Example Request
```
GET /api/v1/customers/28/payments/?page=1&page_size=10&ordering=-payment_date
```

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "customer": {
      "id": 28,
      "name": "John Doe",
      "mobile_number": "03001234567"
    },
    "payments": [
      {
        "id": 12,
        "amount": 250.75,
        "formatted_amount": "Rs. 250.75",
        "payment_type": "card",
        "payment_type_display": "Card",
        "payment_date": "2025-09-23T18:30:00+00:00",
        "note": "Payment for groceries",
        "reference_number": "REF123456",
        "created_by": "***********",
        "created_at": "2025-09-23T13:21:39.577900+00:00"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 2,
      "total_payments": 25,
      "page_size": 20,
      "has_next": true,
      "has_previous": false
    },
    "summary": {
      "total_payments_amount": 2500.50,
      "total_payments_count": 25
    }
  }
}
```

---

## 3. 👁️ GET PAYMENT DETAILS

**GET** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

### Success Response (200)
```json
{
  "success": true,
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "John Doe",
    "amount": 250.75,
    "formatted_amount": "Rs. 250.75",
    "payment_type": "card",
    "payment_type_display": "Card",
    "payment_date": "2025-09-23T18:30:00+00:00",
    "note": "Payment for groceries",
    "reference_number": "REF123456",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00",
    "updated_at": "2025-09-23T14:21:39.577900+00:00"
  }
}
```

---

## 4. ✏️ UPDATE PAYMENT

**PUT** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

### Request Body (All fields optional)
```json
{
  "amount": 300.00,
  "payment_type": "bank_transfer",
  "payment_date": "2025-09-23T19:00:00",
  "note": "Updated payment note",
  "reference_number": "NEW_REF789"
}
```

### Success Response (200)
```json
{
  "success": true,
  "message": "Payment updated successfully",
  "data": {
    "id": 12,
    "customer_id": 28,
    "customer_name": "John Doe",
    "amount": 300.00,
    "formatted_amount": "Rs. 300.00",
    "payment_type": "bank_transfer",
    "payment_type_display": "Bank Transfer",
    "payment_date": "2025-09-23T19:00:00+00:00",
    "note": "Updated payment note",
    "reference_number": "NEW_REF789",
    "created_by": "***********",
    "created_at": "2025-09-23T13:21:39.577900+00:00",
    "updated_at": "2025-09-23T14:25:15.123456+00:00"
  }
}
```

---

## 5. 🗑️ DELETE PAYMENT

**DELETE** `/api/v1/customers/{customer_id}/payments/{payment_id}/`

### Success Response (200)
```json
{
  "success": true,
  "message": "Payment of Rs. 250.75 on 2025-09-23 deleted successfully"
}
```

### Error Response (404)
```json
{
  "success": false,
  "message": "Payment not found"
}
```

---

## 🚨 HTTP Status Codes

| Code | Description |
|------|-------------|
| 200 | Success |
| 201 | Created successfully |
| 400 | Bad request (validation error) |
| 401 | Unauthorized (missing/invalid token) |
| 404 | Resource not found |
| 500 | Internal server error |

---

## 📝 cURL Examples

### Create Payment
```bash
curl -X POST "http://zayyrah.com/api/v1/customers/28/payments/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 150.50,
    "payment_type": "cash",
    "note": "Cash payment for order #123"
  }'
```

### Get Payments List
```bash
curl -X GET "http://zayyrah.com/api/v1/customers/28/payments/?page=1&page_size=10" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Payment Details
```bash
curl -X GET "http://zayyrah.com/api/v1/customers/28/payments/12/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Update Payment
```bash
curl -X PUT "http://zayyrah.com/api/v1/customers/28/payments/12/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "amount": 200.00,
    "note": "Updated payment amount"
  }'
```

### Delete Payment
```bash
curl -X DELETE "http://zayyrah.com/api/v1/customers/28/payments/12/" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## 🔧 Validation Rules

- **Amount**: Must be positive number (> 0), supports up to 2 decimal places
- **Payment Type**: Must be one of: cash, card, bank_transfer, mobile_money, cheque, other
- **Payment Date**: ISO 8601 format (YYYY-MM-DDTHH:MM:SS), optional timezone
- **Note**: Text field, optional, can be empty or null (automatically converted to empty string)
- **Reference Number**: Text field, optional, can be empty or null (automatically converted to empty string)

---

## 🛡️ Security Features

- ✅ Authentication required for all endpoints
- ✅ Users can only access their own customer payments
- ✅ CSRF protection enabled
- ✅ Input validation and sanitization
- ✅ SQL injection protection

---

**✨ Payment API is ready for integration!**