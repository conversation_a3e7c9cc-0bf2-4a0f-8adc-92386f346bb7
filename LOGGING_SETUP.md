# Gunicorn & Django API Logging Setup

## Overview
This setup provides comprehensive logging for all API requests, errors, and system events to `/home/<USER>/ChannabPOS/gunicorn_logs.log`.

## Features

### 🎯 **What Gets Logged**
- ✅ All API requests (GET, POST, PUT, DELETE, etc.)
- ✅ Request details: IP address, User-Agent, request body
- ✅ Response details: Status code, duration, response content
- ✅ User information (authenticated user or Anonymous)
- ✅ Error responses with full error details
- ✅ Application exceptions and stack traces
- ✅ Gunicorn access logs with detailed timing
- ✅ System startup and worker process information

### 🔐 **Security Features**
- 🛡️ Sensitive data masking (passwords, tokens, OTP codes)
- 🛡️ IP address tracking for security monitoring
- 🛡️ Request/response size monitoring
- 🛡️ Error level classification (INFO, WARNING, ERROR)

## Configuration Files

### 1. **Gunicorn Configuration** - `/home/<USER>/ChannabPOS/gunicorn_config.py`
```python
# Key settings:
accesslog = "/home/<USER>/ChannabPOS/gunicorn_logs.log"
errorlog = "/home/<USER>/ChannabPOS/gunicorn_logs.log"
loglevel = "info"
capture_output = True
```

### 2. **Systemd Service** - `/etc/systemd/system/gunicorn.service`
```ini
# Logging to file
StandardOutput=append:/home/<USER>/ChannabPOS/gunicorn_logs.log
StandardError=append:/home/<USER>/ChannabPOS/gunicorn_logs.log

# Using config file
ExecStart=/home/<USER>/ChannabPOS/.venv/bin/gunicorn \
    --config /home/<USER>/ChannabPOS/gunicorn_config.py \
    zayyrah.wsgi:application
```

### 3. **Django Settings** - `zayyrah/settings.py`
```python
# Custom logging configuration with multiple handlers
LOGGING = {
    'handlers': {
        'api_file': {
            'filename': '/home/<USER>/ChannabPOS/gunicorn_logs.log',
            'formatter': 'api_format',
        },
        'error_file': {
            'filename': '/home/<USER>/ChannabPOS/gunicorn_logs.log',
            'formatter': 'error_format',
        }
    }
}
```

### 4. **Custom Middleware** - `zayyrah/middleware.py`
- Logs all API requests/responses
- Masks sensitive data
- Tracks request duration
- Captures client IP and User-Agent

## Log Format Examples

### ✅ **Successful Request**
```
[2025-09-19 12:38:11,641] INFO API-REQUEST: POST /api/v1/accounts/login/ | IP: ************* | User: Anonymous | User-Agent: curl/7.81.0 | Body: {"mobile_number": "***********", "password": "***MASKED***"}
[2025-09-19 12:38:12,821] INFO API-REQUEST: RESPONSE POST /api/v1/accounts/login/ | Status: 200 | Duration: 1180.71ms | IP: ************* | User: Anonymous | Response: {"success": true, "message": "Login successful", ...}
```

### ⚠️ **Authentication Error**
```
[2025-09-19 12:38:42,008] INFO API-REQUEST: POST /api/v1/accounts/login/ | IP: ************* | User: Anonymous | User-Agent: curl/7.81.0 | Body: {"mobile_number": "***********", "password": "***MASKED***"}
[2025-09-19 12:38:42,905] WARNING API-REQUEST: RESPONSE POST /api/v1/accounts/login/ | Status: 401 | Duration: 897.01ms | IP: ************* | User: Anonymous | Response: {"success": false, "message": "Invalid credentials", ...}
```

### ❌ **404 Not Found**
```
[2025-09-19 12:38:46,789] INFO API-REQUEST: GET /api/v1/nonexistent/ | IP: ************* | User: Anonymous | User-Agent: curl/7.81.0 | Body: None
[2025-09-19 12:38:47,000] WARNING API-REQUEST: RESPONSE GET /api/v1/nonexistent/ | Status: 404 | Duration: 210.99ms | IP: ************* | User: Anonymous | Response: <!DOCTYPE html>...
```

## Log Management

### **Viewing Logs**
```bash
# View latest logs
tail -f /home/<USER>/ChannabPOS/gunicorn_logs.log

# View last 50 lines
tail -n 50 /home/<USER>/ChannabPOS/gunicorn_logs.log

# Search for specific IP
grep "IP: *************" /home/<USER>/ChannabPOS/gunicorn_logs.log

# Search for errors
grep "ERROR\|WARNING" /home/<USER>/ChannabPOS/gunicorn_logs.log

# Search for specific endpoint
grep "/api/v1/accounts/login/" /home/<USER>/ChannabPOS/gunicorn_logs.log
```

### **Log Rotation**
- Automatic rotation script: `/home/<USER>/ChannabPOS/log_rotation.sh`
- Rotates when file exceeds 100MB
- Creates compressed backups
- Removes backups older than 30 days

```bash
# Run log rotation manually
sudo /home/<USER>/ChannabPOS/log_rotation.sh

# Set up cron job for automatic rotation (daily check)
echo "0 2 * * * /home/<USER>/ChannabPOS/log_rotation.sh" | sudo crontab -
```

## Service Management

### **Restart Services**
```bash
# Restart Gunicorn
sudo systemctl restart gunicorn

# Restart Nginx
sudo systemctl restart nginx

# Check status
sudo systemctl status gunicorn
sudo systemctl status nginx
```

### **View Service Logs**
```bash
# Gunicorn systemd logs
sudo journalctl -u gunicorn -f

# Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

## File Permissions
```bash
# Log file permissions
-rw-rw-r-- ubuntu:www-data /home/<USER>/ChannabPOS/gunicorn_logs.log

# Scripts permissions
-rwxr-xr-x ubuntu:ubuntu /home/<USER>/ChannabPOS/log_rotation.sh
```

## Troubleshooting

### **If Logging Stops Working**
1. Check Gunicorn status: `sudo systemctl status gunicorn`
2. Check file permissions: `ls -la /home/<USER>/ChannabPOS/gunicorn_logs.log`
3. Restart services: `sudo systemctl restart gunicorn`
4. Check systemd logs: `sudo journalctl -u gunicorn -f`

### **Performance Considerations**
- Log file is monitored for size (auto-rotation at 100MB)
- Only API requests are logged (not static files)
- Response content is limited to 1000 chars for successful requests
- Sensitive data is automatically masked

## Security Notes
- 🔒 Passwords, tokens, and OTP codes are automatically masked
- 🔒 IP addresses are logged for security monitoring
- 🔒 Log file is readable only by ubuntu and www-data groups
- 🔒 No sensitive data is exposed in log files

## Integration with Monitoring Tools
The log format is compatible with:
- **ELK Stack** (Elasticsearch, Logstash, Kibana)
- **Grafana** + **Loki**
- **Splunk**
- **Datadog**
- **AWS CloudWatch**

Example Logstash configuration available in `/home/<USER>/ChannabPOS/logstash_config.conf`