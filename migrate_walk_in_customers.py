#!/usr/bin/env python3
"""
Migrate existing walk-in transactions to have customer records
"""

import os
import sys
import django
from decimal import Decimal

# Set up Django environment
sys.path.append('/home/<USER>/ChannabPOS/zayyrah')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'zayyrah.settings')
django.setup()

from django.contrib.auth import get_user_model
from inventory.models import POSTransaction
from customers.models import Customer
from django.db import transaction

def migrate_walk_in_customers():
    print("Migrating Walk-in Customer Transactions")
    print("=" * 50)

    User = get_user_model()

    # Get the test user
    try:
        user = User.objects.get(mobile_number='03478181583')
        print(f"✓ Found user: {user.mobile_number}")
    except User.DoesNotExist:
        print("✗ User not found")
        return

    # Find all transactions without customers
    walk_in_transactions = POSTransaction.objects.filter(
        owner=user,
        customer__isnull=True
    )

    print(f"Found {walk_in_transactions.count()} walk-in transactions without customer records")

    if walk_in_transactions.count() == 0:
        print("✓ No walk-in transactions to migrate")
        return

    # Create or get walk-in customer
    walk_in_customer = None
    try:
        walk_in_customer = Customer.objects.filter(
            owner=user,
            name__icontains='Walk-in'
        ).first()

        if not walk_in_customer:
            walk_in_customer = Customer.objects.create(
                owner=user,
                name='Walk-in Customer',
                mobile_number=None,
                email='',
                notes='Auto-created for walk-in POS transactions',
                opening_balance=Decimal('0.00'),
                credit_limit=Decimal('0.00')
            )
            print(f"✓ Created new walk-in customer: {walk_in_customer.display_name}")
        else:
            print(f"✓ Using existing walk-in customer: {walk_in_customer.display_name}")

    except Exception as e:
        print(f"✗ Error creating walk-in customer: {e}")
        return

    # Migrate transactions
    with transaction.atomic():
        updated_count = 0
        for pos_transaction in walk_in_transactions:
            pos_transaction.customer = walk_in_customer
            pos_transaction.save()
            updated_count += 1
            print(f"  ✓ Updated transaction {pos_transaction.transaction_number}")

        print(f"\n✅ Successfully migrated {updated_count} transactions")

    # Verify migration
    print(f"\nVerification:")
    remaining_walk_ins = POSTransaction.objects.filter(
        owner=user,
        customer__isnull=True
    ).count()
    print(f"  • Remaining transactions without customers: {remaining_walk_ins}")

    total_customers = Customer.objects.filter(owner=user).count()
    print(f"  • Total customers now: {total_customers}")

if __name__ == '__main__':
    migrate_walk_in_customers()