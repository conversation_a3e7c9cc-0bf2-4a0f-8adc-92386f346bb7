# ChannabPOS API Documentation

## Overview
This document provides comprehensive API specifications for the ChannabPOS system. The system consists of three main modules: **Accounts**, **Customers**, and **POS (Point of Sale)**. The backend is fully implemented with Django REST Framework and ready for mobile app integration.

**Base URL**:
- Development: `http://localhost:8000/api/v1/`
- Production: `https://your-domain.com/api/v1/`

## Authentication
All API endpoints use **JWT (JSON Web Token)** authentication. The system provides access and refresh tokens for secure mobile app integration.

### Headers Required
```
Content-Type: application/json
Authorization: Bearer <token>  // For token-based auth
```

---

## 1. ACCOUNTS MODULE

### 1.1 User Registration
**Endpoint**: `POST /api/v1/accounts/register/`

**Description**: Register a new user account (customer or business)

**Request Body**:
```json
{
  "mobile_number": "**********",
  "password": "securepassword123",
  "password_confirm": "securepassword123",
  "account_type": "customer",
  "shop_name": "My Shop Name"
}
```

**Field Requirements**:
- `mobile_number`: 6-15 digits, unique across all users
- `password`: Minimum 8 characters, validated by Django's password validators
- `password_confirm`: Must match password field
- `account_type`: Either "customer" or "business"
- `shop_name`: Required only if account_type is "business"

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Account created successfully",
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "customer",
    "shop_name": "",
    "is_active": true,
    "date_joined": "2024-01-15T10:30:00Z"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Response Error (400)**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "mobile_number": ["This field is required."],
    "shop_name": ["Shop name is required for business accounts."]
  }
}
```

### 1.2 User Login
**Endpoint**: `POST /api/v1/accounts/login/`

**Description**: Authenticate user and get access token

**Request Body**:
```json
{
  "mobile_number": "**********",
  "password": "securepassword"
}
```

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "My Shop",
    "is_active": true,
    "date_joined": "2024-01-15T10:30:00Z"
  },
  "tokens": {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
}
```

**Response Error (401)**:
```json
{
  "success": false,
  "message": "Invalid credentials"
}
```

### 1.3 User Profile
**Endpoint**: `GET /api/v1/accounts/profile/`

**Description**: Get current user profile information

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "My Shop",
    "is_active": true,
    "date_joined": "2024-01-15T10:30:00Z"
  }
}
```

### 1.4 Update Profile
**Endpoint**: `PUT /api/v1/accounts/profile/update/`

**Description**: Update user profile information

**Request Body**:
```json
{
  "shop_name": "Updated Shop Name",
  "account_type": "business"
}
```

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "id": 1,
    "mobile_number": "**********",
    "account_type": "business",
    "shop_name": "Updated Shop Name",
    "is_active": true,
    "date_joined": "2024-01-15T10:30:00Z"
  }
}
```

### 1.5 Token Refresh
**Endpoint**: `POST /api/v1/accounts/token/refresh/`

**Description**: Refresh access token using refresh token

**Request Body**:
```json
{
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response Success (200)**:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

### 1.6 Logout
**Endpoint**: `POST /api/v1/accounts/logout/`

**Description**: Logout user and blacklist refresh token

**Request Body**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

---

## 2. CUSTOMERS MODULE

### 2.1 List Customers
**Endpoint**: `GET /api/v1/customers/`

**Description**: Get paginated list of customers for the authenticated user

**Query Parameters**:
- `page` (integer): Page number (default: 1)
- `page_size` (integer): Items per page (default: 12, max: 100)
- `search` (string): Search by name or mobile number

**Example**: `GET /api/v1/customers/?page=1&page_size=20&search=john`

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "count": 45,
    "next": "http://your-domain.com/api/v1/customers/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "name": "John Doe",
        "mobile_number": "**********",
        "email": "<EMAIL>",
        "notes": "Regular customer",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-20T15:45:00Z"
      },
      {
        "id": 2,
        "name": "Jane Smith",
        "mobile_number": "9876543211",
        "email": "",
        "notes": "",
        "created_at": "2024-01-16T11:30:00Z",
        "updated_at": "2024-01-16T11:30:00Z"
      }
    ]
  }
}
```

### 2.2 Create Customer
**Endpoint**: `POST /api/v1/customers/`

**Description**: Create a new customer

**Request Body**:
```json
{
  "name": "John Doe",
  "mobile_number": "**********",
  "email": "<EMAIL>",
  "notes": "Regular customer"
}
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Customer created successfully",
  "data": {
    "id": 1,
    "name": "John Doe",
    "mobile_number": "**********",
    "email": "<EMAIL>",
    "notes": "Regular customer",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

**Response Error (400)**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "mobile_number": ["Customer with this mobile number already exists."],
    "name": ["Provide at least a mobile number or name."]
  }
}
```

### 2.3 Get Customer Details
**Endpoint**: `GET /api/v1/customers/{id}/`

**Description**: Get details of a specific customer

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "John Doe",
    "mobile_number": "**********",
    "email": "<EMAIL>",
    "notes": "Regular customer",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:45:00Z",
    "total_sales": 15,
    "total_amount": "1250.75",
    "recent_sales": [
      {
        "id": 101,
        "total": "85.50",
        "payment_method": "cash",
        "created_at": "2024-01-20T15:45:00Z"
      }
    ]
  }
}
```

### 2.4 Update Customer
**Endpoint**: `PUT /api/v1/customers/{id}/`

**Description**: Update customer information

**Request Body**:
```json
{
  "name": "John Updated",
  "mobile_number": "**********",
  "email": "<EMAIL>",
  "notes": "VIP customer"
}
```

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Customer updated successfully",
  "data": {
    "id": 1,
    "name": "John Updated",
    "mobile_number": "**********",
    "email": "<EMAIL>",
    "notes": "VIP customer",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-22T09:15:00Z"
  }
}
```

### 2.5 Delete Customer
**Endpoint**: `DELETE /api/v1/customers/{id}/`

**Description**: Delete a customer

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Customer deleted successfully"
}
```

**Response Error (404)**:
```json
{
  "success": false,
  "message": "Customer not found"
}
```

---

## 3. POS MODULE

### 3.1 Product Categories

#### 3.1.1 List Categories
**Endpoint**: `GET /api/v1/pos/categories/`

**Description**: Get list of product categories

**Response Success (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Electronics",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z",
      "products_count": 25
    },
    {
      "id": 2,
      "name": "Clothing",
      "created_at": "2024-01-16T11:30:00Z",
      "updated_at": "2024-01-16T11:30:00Z",
      "products_count": 10
    }
  ]
}
```

#### 3.1.2 Create Category
**Endpoint**: `POST /api/v1/pos/categories/`

**Request Body**:
```json
{
  "name": "Books"
}
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Category created successfully",
  "data": {
    "id": 3,
    "name": "Books",
    "created_at": "2024-01-22T10:30:00Z",
    "updated_at": "2024-01-22T10:30:00Z",
    "products_count": 0
  }
}
```

### 3.2 Products

#### 3.2.1 List Products
**Endpoint**: `GET /api/v1/pos/products/`

**Description**: Get paginated list of products

**Query Parameters**:
- `page` (integer): Page number
- `page_size` (integer): Items per page
- `search` (string): Search by name or SKU
- `category` (integer): Filter by category ID
- `low_stock` (boolean): Filter products with low stock

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "count": 100,
    "next": "http://your-domain.com/api/v1/pos/products/?page=2",
    "previous": null,
    "results": [
      {
        "id": 1,
        "name": "iPhone 15",
        "sku": "IP15-128GB",
        "category": {
          "id": 1,
          "name": "Electronics"
        },
        "purchase_price": "800.00",
        "selling_price": "999.00",
        "stock_quantity": 25,
        "unit": "each",
        "unit_label": "Each",
        "tax_rate": "18.00",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-20T15:45:00Z"
      }
    ]
  }
}
```

#### 3.2.2 Create Product
**Endpoint**: `POST /api/v1/pos/products/`

**Request Body**:
```json
{
  "name": "Samsung Galaxy S24",
  "sku": "SGS24-256GB",
  "category_id": 1,
  "purchase_price": "700.00",
  "selling_price": "899.00",
  "stock_quantity": 15,
  "unit": "each",
  "unit_custom_label": "",
  "tax_rate": "18.00"
}
```

**Available Product Units**:
- `each` - Each (default)
- `kg` - Kilogram
- `g` - Gram
- `l` - Liter
- `ml` - Milliliter
- `m` - Meter
- `cm` - Centimeter
- `box` - Box
- `pack` - Pack
- `custom` - Custom (requires `unit_custom_label`)
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Product created successfully",
  "data": {
    "id": 2,
    "name": "Samsung Galaxy S24",
    "sku": "SGS24-256GB",
    "category": {
      "id": 1,
      "name": "Electronics"
    },
    "purchase_price": "700.00",
    "selling_price": "899.00",
    "stock_quantity": 15,
    "unit": "each",
    "unit_label": "Each",
    "tax_rate": "18.00",
    "created_at": "2024-01-22T10:30:00Z",
    "updated_at": "2024-01-22T10:30:00Z"
  }
}
```

#### 3.2.3 Get Product Details
**Endpoint**: `GET /api/v1/pos/products/{id}/`

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "iPhone 15",
    "sku": "IP15-128GB",
    "category": {
      "id": 1,
      "name": "Electronics"
    },
    "purchase_price": "800.00",
    "selling_price": "999.00",
    "stock_quantity": 25,
    "unit": "each",
    "unit_label": "Each",
    "tax_rate": "18.00",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-20T15:45:00Z",
    "unit_custom_label": "",
    "stock_entries": [
      {
        "id": 1,
        "quantity_added": 50,
        "quantity_remaining": 25,
        "purchase_price": "800.00",
        "added_on": "2024-01-15",
        "expiry_date": null,
        "notes": "Initial stock",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-20T15:45:00Z"
      }
    ],
    "recent_sales": [
      {
        "id": 1,
        "sale_id": 101,
        "quantity": 2,
        "price": "999.00",
        "total": "1998.00",
        "created_at": "2024-01-20T15:45:00Z"
      }
    ]
  }
}
```

#### 3.2.4 Update Product
**Endpoint**: `PUT /api/v1/pos/products/{id}/`

**Request Body**:
```json
{
  "name": "iPhone 15 Pro",
  "selling_price": "1199.00",
  "tax_rate": "18.00"
}
```

**Response Success (200)**:
```json
{
  "success": true,
  "message": "Product updated successfully",
  "data": {
    "id": 1,
    "name": "iPhone 15 Pro",
    "sku": "IP15-128GB",
    "category": {
      "id": 1,
      "name": "Electronics"
    },
    "purchase_price": "800.00",
    "selling_price": "1199.00",
    "stock_quantity": 25,
    "unit": "each",
    "unit_label": "Each",
    "tax_rate": "18.00",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-22T11:30:00Z"
  }
}
```

#### 3.2.5 Add Stock Entry
**Endpoint**: `POST /api/v1/pos/products/{id}/stock/`

**Request Body**:
```json
{
  "quantity_added": 20,
  "purchase_price": "795.00",
  "added_on": "2024-01-22",
  "expiry_date": "2025-12-31",
  "notes": "New shipment"
}
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Stock entry added successfully",
  "data": {
    "id": 2,
    "quantity_added": 20,
    "quantity_remaining": 20,
    "purchase_price": "795.00",
    "added_on": "2024-01-22",
    "expiry_date": "2025-12-31",
    "notes": "New shipment",
    "created_at": "2024-01-22T10:30:00Z"
  }
}
```

**Stock Management Notes**:
- Stock entries support different purchase prices per batch
- FIFO (First In, First Out) allocation when creating sales
- Automatic stock quantity calculation from all stock entries
- Expiry date tracking for perishable items
- Stock remaining is updated automatically when sales are made

### 3.3 Sales

#### 3.3.1 List Sales
**Endpoint**: `GET /api/v1/pos/sales/`

**Description**: Get paginated list of sales

**Query Parameters**:
- `page` (integer): Page number
- `page_size` (integer): Items per page
- `date_from` (date): Filter sales from date (YYYY-MM-DD)
- `date_to` (date): Filter sales to date (YYYY-MM-DD)
- `customer` (integer): Filter by customer ID
- `payment_method` (string): Filter by payment method ('cash', 'credit')

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "count": 150,
    "next": "http://your-domain.com/api/v1/pos/sales/?page=2",
    "previous": null,
    "results": [
      {
        "id": 101,
        "customer": {
          "id": 1,
          "name": "John Doe",
          "mobile_number": "**********"
        },
        "subtotal": "1998.00",
        "tax_total": "359.64",
        "discount_total": "0.00",
        "total": "2357.64",
        "payment_method": "cash",
        "items_count": 2,
        "notes": "",
        "created_at": "2024-01-20T15:45:00Z"
      }
    ]
  }
}
```

#### 3.3.2 Create Sale
**Endpoint**: `POST /api/v1/pos/sales/`

**Description**: Create a new sale transaction

**Request Body**:
```json
{
  "customer_id": 1,
  "payment_method": "cash",
  "discount_total": "50.00",
  "notes": "Customer discount applied",
  "items": [
    {
      "product_id": 1,
      "quantity": 2,
      "price": "999.00"
    },
    {
      "product_id": 2,
      "quantity": 1,
      "price": "899.00"
    }
  ]
}
```

**Response Success (201)**:
```json
{
  "success": true,
  "message": "Sale created successfully",
  "data": {
    "id": 102,
    "customer": {
      "id": 1,
      "name": "John Doe",
      "mobile_number": "**********"
    },
    "subtotal": "2897.00",
    "tax_total": "521.46",
    "discount_total": "50.00",
    "total": "3368.46",
    "payment_method": "cash",
    "notes": "Customer discount applied",
    "items": [
      {
        "id": 201,
        "product": {
          "id": 1,
          "name": "iPhone 15",
          "sku": "IP15-128GB"
        },
        "quantity": 2,
        "price": "999.00",
        "tax_rate": "18.00",
        "line_total": "1998.00"
      },
      {
        "id": 202,
        "product": {
          "id": 2,
          "name": "Samsung Galaxy S24",
          "sku": "SGS24-256GB"
        },
        "quantity": 1,
        "price": "899.00",
        "tax_rate": "18.00",
        "line_total": "899.00"
      }
    ],
    "created_at": "2024-01-22T14:30:00Z"
  }
}
```

**Response Error (400)**:
```json
{
  "success": false,
  "message": "Insufficient stock",
  "errors": {
    "items": ["Not enough stock available for iPhone 15."]
  }
}
```

#### 3.3.3 Get Sale Details
**Endpoint**: `GET /api/v1/pos/sales/{id}/`

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "id": 101,
    "customer": {
      "id": 1,
      "name": "John Doe",
      "mobile_number": "**********",
      "email": "<EMAIL>"
    },
    "subtotal": "1998.00",
    "tax_total": "359.64",
    "discount_total": "0.00",
    "total": "2357.64",
    "payment_method": "cash",
    "notes": "",
    "items": [
      {
        "id": 201,
        "product": {
          "id": 1,
          "name": "iPhone 15",
          "sku": "IP15-128GB",
          "unit_label": "Each"
        },
        "quantity": 2,
        "price": "999.00",
        "tax_rate": "18.00",
        "line_total": "1998.00"
      }
    ],
    "created_at": "2024-01-20T15:45:00Z",
    "updated_at": "2024-01-20T15:45:00Z"
  }
}
```

### 3.4 Dashboard Analytics

#### 3.4.1 Dashboard Summary
**Endpoint**: `GET /api/v1/pos/dashboard/`

**Description**: Get dashboard summary data

**Query Parameters**:
- `period` (string): 'today', 'week', 'month', 'year' (default: 'today')

**Response Success (200)**:
```json
{
  "success": true,
  "data": {
    "period": "today",
    "sales": {
      "total_count": 15,
      "total_amount": "12450.75",
      "average_sale": "830.05"
    },
    "products": {
      "total_count": 125,
      "low_stock_count": 8,
      "out_of_stock_count": 2
    },
    "customers": {
      "total_count": 45,
      "new_today": 3
    },
    "revenue": {
      "cash": "8230.50",
      "credit": "4220.25"
    },
    "top_products": [
      {
        "id": 1,
        "name": "iPhone 15",
        "quantity_sold": 8,
        "revenue": "7992.00"
      },
      {
        "id": 2,
        "name": "Samsung Galaxy S24",
        "quantity_sold": 5,
        "revenue": "4495.00"
      }
    ]
  }
}
```

---

## Error Responses

### Standard Error Format
All API endpoints return errors in the following format:

```json
{
  "success": false,
  "message": "Error description",
  "errors": {
    "field_name": ["Specific error message"],
    "another_field": ["Another error message"]
  }
}
```

### HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (permission denied)
- `404` - Not Found
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

---

## Rate Limiting
- All endpoints are rate-limited to 1000 requests per hour per user
- Rate limit information is returned in response headers:
  - `X-RateLimit-Limit`: Request limit per hour
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Unix timestamp when limit resets

---

## Pagination
List endpoints use page-based pagination:
- `count`: Total number of items
- `next`: URL for next page (null if last page)
- `previous`: URL for previous page (null if first page)
- `results`: Array of items for current page

**Pagination Parameters**:
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 20, max: 100)

---

## Data Validation Rules

### Mobile Number
- Format: 6-15 digits only
- Must be unique per user for customers
- Required for user registration

### Prices
- Format: Decimal with max 10 digits, 2 decimal places
- Must be >= 0.00

### SKU
- Max 64 characters
- Must be unique per user for products
- Optional field

### Tax Rate
- Format: Decimal with max 5 digits, 2 decimal places
- Represents percentage (e.g., 18.00 for 18%)

---

## Implementation Notes for Flutter Team

### 1. Authentication Flow
- Use JWT tokens for all API requests
- Store access and refresh tokens securely
- Implement automatic token refresh when access token expires
- Access tokens expire in 24 hours, refresh tokens in 7 days

### 2. Error Handling
- Always check the `success` field in responses
- Handle validation errors from the `errors` object
- Implement proper error messages for users
- Handle network connectivity issues gracefully

### 3. Data Management
- **Pagination**: Use page-based pagination for list views
- **Search**: Implement debounced search (300ms delay recommended)
- **Offline Support**: Cache critical data for offline functionality
- **Real-time Updates**: Consider implementing periodic data refresh

### 4. Business Logic
- **Stock Management**: Always validate stock before allowing sales
- **FIFO System**: Stock is allocated on First-In-First-Out basis
- **Tax Calculations**: Tax rates are percentages (18.00 = 18%)
- **Currency**: All monetary values are strings to maintain precision

### 5. Date and Time
- All dates are in ISO 8601 format (UTC)
- Convert to local timezone in the app
- Date filters use YYYY-MM-DD format

### 6. Security
- Never store passwords in the app
- Use secure storage for tokens
- Implement proper session management
- Validate all user inputs before sending to API

### 7. Performance
- Implement lazy loading for large lists
- Cache frequently accessed data
- Use pagination for better performance
- Compress images before uploading (when image support is added)

### 8. API Status
✅ **Fully Implemented**: All endpoints documented are live and ready
✅ **Database**: PostgreSQL with proper relationships and constraints
✅ **Authentication**: JWT with refresh token support
✅ **Validation**: Comprehensive input validation
✅ **Stock Management**: FIFO allocation and automatic calculations
✅ **Multi-user**: Proper data isolation per business owner

### 9. Testing
Start the development server:
```bash
cd zayyrah
source ../.venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

Base URL: `http://localhost:8000/api/v1/`

This documentation provides the complete foundation for building the mobile app. The backend is production-ready and fully tested.