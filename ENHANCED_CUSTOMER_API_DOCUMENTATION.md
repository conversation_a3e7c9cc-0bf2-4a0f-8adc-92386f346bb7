# Enhanced Customer List API Documentation

## Overview
The enhanced `/api/v1/customers/` endpoint provides comprehensive customer analytics with time-filtered sales data from both POS transactions and manual sales.

## Features Implemented ✅

### 🕐 **Time Filtering (Default: Today)**
- `today` - Today's sales only
- `yesterday` - Yesterday's sales
- `last_7_days` - Last 7 days
- `last_30_days` - Last 30 days
- `this_week` - Current week (Monday to today)
- `this_month` - Current month
- `last_month` - Previous month
- `this_year` - Current year
- `custom` - Custom date range with start_date & end_date

### 📊 **Ultra-Thinking Sales Analytics**
- **Combined Sales**: POS transactions + Manual sales
- **Period-specific**: Sales data for selected time filter
- **Lifetime comparison**: All-time customer analytics
- **Breakdown**: Separate POS vs Manual sales counts and amounts

### 🎯 **Smart Sorting Options**
- `period_sales_desc` - Highest sales first (default)
- `period_sales_asc` - Lowest sales first
- `transactions_desc` - Most transactions first
- `name_asc` - Alphabetical by name
- `recent` - Recently updated customers

### 📄 **Pagination & Summary**
- Configurable page size
- Period summary analytics
- Customer segmentation (with/without sales)
- Average metrics per customer

## API Endpoint

```
GET /api/v1/customers/
```

### Query Parameters

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `time_filter` | string | `today` | Time period for sales data |
| `sort_by` | string | `period_sales_desc` | Sorting option |
| `page` | integer | `1` | Page number |
| `page_size` | integer | `20` | Items per page |
| `start_date` | string | - | Custom start date (YYYY-MM-DD) |
| `end_date` | string | - | Custom end date (YYYY-MM-DD) |

### Example Requests

```bash
# Today's customer sales (default)
GET /api/v1/customers/

# Last 30 days sorted by highest sales
GET /api/v1/customers/?time_filter=last_30_days&sort_by=period_sales_desc

# Custom date range
GET /api/v1/customers/?time_filter=custom&start_date=2025-09-01&end_date=2025-09-23

# This month with pagination
GET /api/v1/customers/?time_filter=this_month&page=1&page_size=10
```

## Response Format

```json
{
  "success": true,
  "data": {
    "customers": [
      {
        "id": 26,
        "name": "Walk-in Customer (2025-09-22)",
        "mobile_number": null,
        "email": "",
        "created_at": "2025-09-22T17:12:58.311794+00:00",
        "updated_at": "2025-09-22T17:12:58.311809+00:00",

        "period_sales": {
          "total_amount": 1500.01,
          "total_transactions": 7,
          "pos_transactions": {
            "count": 7,
            "amount": 1500.01
          },
          "manual_sales": {
            "count": 0,
            "amount": 0.0
          }
        },

        "lifetime_sales": {
          "total_amount": 0.0,
          "total_transactions": 0
        },

        "credit_info": {
          "credit_limit": 0.0,
          "current_balance": 0.0,
          "available_credit": 0.0
        }
      }
    ],

    "pagination": {
      "current_page": 1,
      "total_pages": 1,
      "total_customers": 2,
      "page_size": 10,
      "has_next": false,
      "has_previous": false
    },

    "period_summary": {
      "time_filter": "today",
      "date_range": {
        "start_date": "2025-09-23",
        "end_date": "2025-09-23"
      },
      "total_customers": 2,
      "customers_with_sales": 1,
      "customers_without_sales": 1,
      "total_sales_amount": 1500.01,
      "total_transactions": 7,
      "avg_sale_per_customer": 750.0,
      "avg_transactions_per_customer": 3.5
    },

    "filters_applied": {
      "time_filter": "today",
      "sort_by": "period_sales_desc",
      "start_date": "2025-09-23",
      "end_date": "2025-09-23"
    }
  }
}
```

## Data Breakdown

### Customer Object Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Customer database ID |
| `name` | string | Customer display name |
| `mobile_number` | string/null | Phone number |
| `email` | string | Email address |
| `created_at` | datetime | Customer creation date |
| `updated_at` | datetime | Last modification date |

### Period Sales Analytics

| Field | Type | Description |
|-------|------|-------------|
| `total_amount` | float | Combined POS + Manual sales amount |
| `total_transactions` | integer | Combined transaction count |
| `pos_transactions.count` | integer | POS transaction count |
| `pos_transactions.amount` | float | POS sales amount |
| `manual_sales.count` | integer | Manual sales count |
| `manual_sales.amount` | float | Manual sales amount |

### Period Summary Analytics

| Field | Type | Description |
|-------|------|-------------|
| `total_customers` | integer | Total customers returned |
| `customers_with_sales` | integer | Customers with sales in period |
| `customers_without_sales` | integer | Customers with no sales |
| `total_sales_amount` | float | Total sales across all customers |
| `total_transactions` | integer | Total transactions across all customers |
| `avg_sale_per_customer` | float | Average sales amount per customer |
| `avg_transactions_per_customer` | float | Average transactions per customer |

## Usage Examples

### Frontend Integration
```javascript
// Load customers for today with highest sales first
const loadCustomers = async (timeFilter = 'today') => {
    const response = await fetch(
        `/api/v1/customers/?time_filter=${timeFilter}&sort_by=period_sales_desc`,
        {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        }
    );

    const data = await response.json();
    if (data.success) {
        const customers = data.data.customers;
        const summary = data.data.period_summary;

        // Display customers and analytics
        displayCustomers(customers);
        displaySummary(summary);
    }
};
```

### Dashboard Analytics
```javascript
// Get period summary for dashboard widgets
const getPeriodSummary = async () => {
    const response = await fetch('/api/v1/customers/?time_filter=today&page_size=1');
    const data = await response.json();

    return {
        totalCustomers: data.data.period_summary.total_customers,
        customersWithSales: data.data.period_summary.customers_with_sales,
        totalSales: data.data.period_summary.total_sales_amount,
        avgPerCustomer: data.data.period_summary.avg_sale_per_customer
    };
};
```

## Key Benefits

1. **Ultra-Thinking Analytics**: Combines multiple sales sources for complete picture
2. **Time Flexibility**: Multiple predefined periods + custom range support
3. **Performance Optimized**: Efficient database queries with pagination
4. **Comprehensive Data**: Period vs lifetime comparisons
5. **Business Intelligence**: Customer segmentation and averages
6. **Real-time**: Live sales data with instant filtering

## Notes

- **Authentication Required**: All requests need valid JWT token
- **Owner Filtering**: Users only see their own customers
- **Timezone Aware**: Dates use Django timezone settings
- **Error Handling**: Comprehensive error responses with details
- **Logging**: Full request/response logging for debugging