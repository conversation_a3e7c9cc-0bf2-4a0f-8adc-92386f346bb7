// Flutter API Integration for Customer Payment CRUD Operations
// Complete implementation with models, services, and UI examples

import 'dart:convert';
import 'package:http/http.dart' as http;

// =====================================================
// 1. PAYMENT MODEL CLASSES
// =====================================================

class CustomerPayment {
  final int id;
  final int customerId;
  final String customerName;
  final double amount;
  final String formattedAmount;
  final String paymentType;
  final String paymentTypeDisplay;
  final DateTime paymentDate;
  final String note;
  final String referenceNumber;
  final String createdBy;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CustomerPayment({
    required this.id,
    required this.customerId,
    required this.customerName,
    required this.amount,
    required this.formattedAmount,
    required this.paymentType,
    required this.paymentTypeDisplay,
    required this.paymentDate,
    required this.note,
    required this.referenceNumber,
    required this.createdBy,
    required this.createdAt,
    this.updatedAt,
  });

  factory CustomerPayment.fromJson(Map<String, dynamic> json) {
    return CustomerPayment(
      id: json['id'],
      customerId: json['customer_id'] ?? 0,
      customerName: json['customer_name'] ?? '',
      amount: (json['amount'] as num).toDouble(),
      formattedAmount: json['formatted_amount'] ?? '',
      paymentType: json['payment_type'] ?? '',
      paymentTypeDisplay: json['payment_type_display'] ?? '',
      paymentDate: DateTime.parse(json['payment_date']),
      note: json['note'] ?? '',
      referenceNumber: json['reference_number'] ?? '',
      createdBy: json['created_by'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
        ? DateTime.parse(json['updated_at'])
        : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'customer_id': customerId,
      'customer_name': customerName,
      'amount': amount,
      'formatted_amount': formattedAmount,
      'payment_type': paymentType,
      'payment_type_display': paymentTypeDisplay,
      'payment_date': paymentDate.toIso8601String(),
      'note': note,
      'reference_number': referenceNumber,
      'created_by': createdBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class CreatePaymentRequest {
  final double amount;
  final String paymentType;
  final DateTime? paymentDate;
  final String? note;
  final String? referenceNumber;

  CreatePaymentRequest({
    required this.amount,
    required this.paymentType,
    this.paymentDate,
    this.note,
    this.referenceNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'amount': amount,
      'payment_type': paymentType,
      if (paymentDate != null) 'payment_date': paymentDate!.toIso8601String(),
      if (note != null && note!.isNotEmpty) 'note': note,
      if (referenceNumber != null && referenceNumber!.isNotEmpty)
        'reference_number': referenceNumber,
    };
  }
}

class PaymentListResponse {
  final bool success;
  final List<CustomerPayment> payments;
  final PaymentSummary summary;
  final PaginationInfo pagination;
  final CustomerInfo customer;

  PaymentListResponse({
    required this.success,
    required this.payments,
    required this.summary,
    required this.pagination,
    required this.customer,
  });

  factory PaymentListResponse.fromJson(Map<String, dynamic> json) {
    final data = json['data'];
    return PaymentListResponse(
      success: json['success'] ?? false,
      payments: (data['payments'] as List)
          .map((payment) => CustomerPayment.fromJson(payment))
          .toList(),
      summary: PaymentSummary.fromJson(data['summary']),
      pagination: PaginationInfo.fromJson(data['pagination']),
      customer: CustomerInfo.fromJson(data['customer']),
    );
  }
}

class PaymentSummary {
  final double totalPaymentsAmount;
  final int totalPaymentsCount;

  PaymentSummary({
    required this.totalPaymentsAmount,
    required this.totalPaymentsCount,
  });

  factory PaymentSummary.fromJson(Map<String, dynamic> json) {
    return PaymentSummary(
      totalPaymentsAmount: (json['total_payments_amount'] as num).toDouble(),
      totalPaymentsCount: json['total_payments_count'] ?? 0,
    );
  }
}

class PaginationInfo {
  final int currentPage;
  final int totalPages;
  final int totalPayments;
  final int pageSize;
  final bool hasNext;
  final bool hasPrevious;

  PaginationInfo({
    required this.currentPage,
    required this.totalPages,
    required this.totalPayments,
    required this.pageSize,
    required this.hasNext,
    required this.hasPrevious,
  });

  factory PaginationInfo.fromJson(Map<String, dynamic> json) {
    return PaginationInfo(
      currentPage: json['current_page'] ?? 1,
      totalPages: json['total_pages'] ?? 1,
      totalPayments: json['total_payments'] ?? 0,
      pageSize: json['page_size'] ?? 20,
      hasNext: json['has_next'] ?? false,
      hasPrevious: json['has_previous'] ?? false,
    );
  }
}

class CustomerInfo {
  final int id;
  final String name;
  final String? mobileNumber;

  CustomerInfo({
    required this.id,
    required this.name,
    this.mobileNumber,
  });

  factory CustomerInfo.fromJson(Map<String, dynamic> json) {
    return CustomerInfo(
      id: json['id'],
      name: json['name'] ?? '',
      mobileNumber: json['mobile_number'],
    );
  }
}

class ApiResponse<T> {
  final bool success;
  final String? message;
  final T? data;
  final String? error;

  ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, T Function(dynamic)? fromJsonT) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null && fromJsonT != null ? fromJsonT(json['data']) : json['data'],
      error: json['error'],
    );
  }
}

// =====================================================
// 2. PAYMENT TYPES ENUM
// =====================================================

enum PaymentType {
  cash('cash', 'Cash'),
  card('card', 'Card'),
  bankTransfer('bank_transfer', 'Bank Transfer'),
  mobileMoney('mobile_money', 'Mobile Money'),
  cheque('cheque', 'Cheque'),
  other('other', 'Other');

  const PaymentType(this.value, this.displayName);
  final String value;
  final String displayName;

  static PaymentType fromString(String value) {
    return PaymentType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PaymentType.cash,
    );
  }
}

// =====================================================
// 3. PAYMENT API SERVICE
// =====================================================

class PaymentApiService {
  final String baseUrl;
  final String? authToken;
  final Map<String, String> _headers;

  PaymentApiService({
    required this.baseUrl,
    this.authToken,
  }) : _headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    if (authToken != null) 'Authorization': 'Bearer $authToken',
  };

  // =====================================================
  // CREATE PAYMENT
  // =====================================================
  Future<ApiResponse<CustomerPayment>> createPayment(
    int customerId,
    CreatePaymentRequest request,
  ) async {
    try {
      final url = Uri.parse('$baseUrl/api/v1/customers/$customerId/payments/');

      print('Creating payment for customer $customerId');
      print('Request: ${jsonEncode(request.toJson())}');

      final response = await http.post(
        url,
        headers: _headers,
        body: jsonEncode(request.toJson()),
      );

      print('Response status: ${response.statusCode}');
      print('Response body: ${response.body}');

      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode == 201) {
        return ApiResponse<CustomerPayment>(
          success: jsonResponse['success'] ?? true,
          message: jsonResponse['message'],
          data: CustomerPayment.fromJson(jsonResponse['data']),
        );
      } else {
        return ApiResponse<CustomerPayment>(
          success: false,
          error: jsonResponse['message'] ?? 'Failed to create payment',
        );
      }
    } catch (e) {
      print('Error creating payment: $e');
      return ApiResponse<CustomerPayment>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // =====================================================
  // GET PAYMENTS LIST
  // =====================================================
  Future<ApiResponse<PaymentListResponse>> getPayments(
    int customerId, {
    int page = 1,
    int pageSize = 20,
    String? paymentType,
    String? sortBy,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'page_size': pageSize.toString(),
        if (paymentType != null) 'payment_type': paymentType,
        if (sortBy != null) 'ordering': sortBy,
      };

      final uri = Uri.parse('$baseUrl/api/v1/customers/$customerId/payments/')
          .replace(queryParameters: queryParams);

      print('Fetching payments for customer $customerId');
      print('URL: $uri');

      final response = await http.get(uri, headers: _headers);

      print('Response status: ${response.statusCode}');

      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return ApiResponse<PaymentListResponse>(
          success: true,
          data: PaymentListResponse.fromJson(jsonResponse),
        );
      } else {
        return ApiResponse<PaymentListResponse>(
          success: false,
          error: jsonResponse['message'] ?? 'Failed to fetch payments',
        );
      }
    } catch (e) {
      print('Error fetching payments: $e');
      return ApiResponse<PaymentListResponse>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // =====================================================
  // GET SINGLE PAYMENT
  // =====================================================
  Future<ApiResponse<CustomerPayment>> getPayment(
    int customerId,
    int paymentId,
  ) async {
    try {
      final url = Uri.parse('$baseUrl/api/v1/customers/$customerId/payments/$paymentId/');

      print('Fetching payment $paymentId for customer $customerId');

      final response = await http.get(url, headers: _headers);

      print('Response status: ${response.statusCode}');

      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return ApiResponse<CustomerPayment>(
          success: jsonResponse['success'] ?? true,
          data: CustomerPayment.fromJson(jsonResponse['data']),
        );
      } else {
        return ApiResponse<CustomerPayment>(
          success: false,
          error: jsonResponse['message'] ?? 'Failed to fetch payment',
        );
      }
    } catch (e) {
      print('Error fetching payment: $e');
      return ApiResponse<CustomerPayment>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // =====================================================
  // UPDATE PAYMENT
  // =====================================================
  Future<ApiResponse<CustomerPayment>> updatePayment(
    int customerId,
    int paymentId,
    CreatePaymentRequest request,
  ) async {
    try {
      final url = Uri.parse('$baseUrl/api/v1/customers/$customerId/payments/$paymentId/');

      print('Updating payment $paymentId for customer $customerId');
      print('Request: ${jsonEncode(request.toJson())}');

      final response = await http.put(
        url,
        headers: _headers,
        body: jsonEncode(request.toJson()),
      );

      print('Response status: ${response.statusCode}');

      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return ApiResponse<CustomerPayment>(
          success: jsonResponse['success'] ?? true,
          message: jsonResponse['message'],
          data: CustomerPayment.fromJson(jsonResponse['data']),
        );
      } else {
        return ApiResponse<CustomerPayment>(
          success: false,
          error: jsonResponse['message'] ?? 'Failed to update payment',
        );
      }
    } catch (e) {
      print('Error updating payment: $e');
      return ApiResponse<CustomerPayment>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }

  // =====================================================
  // DELETE PAYMENT
  // =====================================================
  Future<ApiResponse<void>> deletePayment(
    int customerId,
    int paymentId,
  ) async {
    try {
      final url = Uri.parse('$baseUrl/api/v1/customers/$customerId/payments/$paymentId/');

      print('Deleting payment $paymentId for customer $customerId');

      final response = await http.delete(url, headers: _headers);

      print('Response status: ${response.statusCode}');

      final jsonResponse = jsonDecode(response.body);

      if (response.statusCode == 200) {
        return ApiResponse<void>(
          success: jsonResponse['success'] ?? true,
          message: jsonResponse['message'],
        );
      } else {
        return ApiResponse<void>(
          success: false,
          error: jsonResponse['message'] ?? 'Failed to delete payment',
        );
      }
    } catch (e) {
      print('Error deleting payment: $e');
      return ApiResponse<void>(
        success: false,
        error: 'Network error: ${e.toString()}',
      );
    }
  }
}

// =====================================================
// 4. USAGE EXAMPLES
// =====================================================

class PaymentApiExample {
  late PaymentApiService _paymentService;

  void initializeService() {
    _paymentService = PaymentApiService(
      baseUrl: 'http://zayyrah.com',
      authToken: 'your_auth_token_here', // Replace with actual token
    );
  }

  // Example: Create a new payment
  Future<void> createPaymentExample() async {
    final request = CreatePaymentRequest(
      amount: 250.75,
      paymentType: PaymentType.card.value,
      paymentDate: DateTime.now(),
      note: 'Payment for groceries',
      referenceNumber: 'REF123456',
    );

    final result = await _paymentService.createPayment(28, request);

    if (result.success && result.data != null) {
      print('Payment created successfully!');
      print('Payment ID: ${result.data!.id}');
      print('Amount: ${result.data!.formattedAmount}');
    } else {
      print('Failed to create payment: ${result.error}');
    }
  }

  // Example: Get payments for a customer
  Future<void> getPaymentsExample() async {
    final result = await _paymentService.getPayments(
      28,
      page: 1,
      pageSize: 10,
      sortBy: '-payment_date', // Sort by payment date descending
    );

    if (result.success && result.data != null) {
      final response = result.data!;
      print('Fetched ${response.payments.length} payments');
      print('Total payments: ${response.summary.totalPaymentsCount}');
      print('Total amount: Rs. ${response.summary.totalPaymentsAmount}');

      for (final payment in response.payments) {
        print('- ${payment.formattedAmount} via ${payment.paymentTypeDisplay} on ${payment.paymentDate}');
      }
    } else {
      print('Failed to fetch payments: ${result.error}');
    }
  }

  // Example: Update a payment
  Future<void> updatePaymentExample() async {
    final request = CreatePaymentRequest(
      amount: 300.00,
      paymentType: PaymentType.bankTransfer.value,
      note: 'Updated payment note',
    );

    final result = await _paymentService.updatePayment(28, 12, request);

    if (result.success && result.data != null) {
      print('Payment updated successfully!');
      print('New amount: ${result.data!.formattedAmount}');
    } else {
      print('Failed to update payment: ${result.error}');
    }
  }

  // Example: Delete a payment
  Future<void> deletePaymentExample() async {
    final result = await _paymentService.deletePayment(28, 12);

    if (result.success) {
      print('Payment deleted successfully!');
      print('Message: ${result.message}');
    } else {
      print('Failed to delete payment: ${result.error}');
    }
  }

  // Example: Get single payment details
  Future<void> getPaymentDetailsExample() async {
    final result = await _paymentService.getPayment(28, 12);

    if (result.success && result.data != null) {
      final payment = result.data!;
      print('Payment Details:');
      print('ID: ${payment.id}');
      print('Amount: ${payment.formattedAmount}');
      print('Type: ${payment.paymentTypeDisplay}');
      print('Date: ${payment.paymentDate}');
      print('Note: ${payment.note}');
      print('Reference: ${payment.referenceNumber}');
      print('Created by: ${payment.createdBy}');
    } else {
      print('Failed to fetch payment: ${result.error}');
    }
  }
}

// =====================================================
// 5. FLUTTER UI WIDGETS (Optional)
// =====================================================

/*
// Example Flutter UI widgets for payment management

class PaymentListWidget extends StatefulWidget {
  final int customerId;

  const PaymentListWidget({Key? key, required this.customerId}) : super(key: key);

  @override
  _PaymentListWidgetState createState() => _PaymentListWidgetState();
}

class _PaymentListWidgetState extends State<PaymentListWidget> {
  late PaymentApiService _paymentService;
  List<CustomerPayment> _payments = [];
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _paymentService = PaymentApiService(
      baseUrl: 'http://zayyrah.com',
      authToken: 'your_auth_token', // Get from secure storage
    );
    _loadPayments();
  }

  Future<void> _loadPayments() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final result = await _paymentService.getPayments(widget.customerId);

    setState(() {
      _isLoading = false;
      if (result.success && result.data != null) {
        _payments = result.data!.payments;
      } else {
        _error = result.error;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('Error: $_error'),
            ElevatedButton(
              onPressed: _loadPayments,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadPayments,
      child: ListView.builder(
        itemCount: _payments.length,
        itemBuilder: (context, index) {
          final payment = _payments[index];
          return PaymentListItem(
            payment: payment,
            onTap: () => _showPaymentDetails(payment),
            onEdit: () => _editPayment(payment),
            onDelete: () => _deletePayment(payment),
          );
        },
      ),
    );
  }

  void _showPaymentDetails(CustomerPayment payment) {
    // Show payment details dialog
  }

  void _editPayment(CustomerPayment payment) {
    // Navigate to edit payment screen
  }

  Future<void> _deletePayment(CustomerPayment payment) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Payment'),
        content: Text('Are you sure you want to delete this payment of ${payment.formattedAmount}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await _paymentService.deletePayment(
        widget.customerId,
        payment.id,
      );

      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Payment deleted successfully')),
        );
        _loadPayments(); // Refresh list
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete payment: ${result.error}')),
        );
      }
    }
  }
}

class PaymentListItem extends StatelessWidget {
  final CustomerPayment payment;
  final VoidCallback onTap;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const PaymentListItem({
    Key? key,
    required this.payment,
    required this.onTap,
    required this.onEdit,
    required this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getPaymentTypeColor(payment.paymentType),
          child: Text(
            _getPaymentTypeIcon(payment.paymentType),
            style: const TextStyle(color: Colors.white),
          ),
        ),
        title: Text(
          payment.formattedAmount,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(payment.paymentTypeDisplay),
            Text(
              '${payment.paymentDate.day}/${payment.paymentDate.month}/${payment.paymentDate.year}',
              style: TextStyle(color: Colors.grey[600]),
            ),
            if (payment.note.isNotEmpty)
              Text(
                payment.note,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
          ],
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Text('Edit'),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Text('Delete'),
            ),
          ],
          onSelected: (value) {
            if (value == 'edit') {
              onEdit();
            } else if (value == 'delete') {
              onDelete();
            }
          },
        ),
        onTap: onTap,
      ),
    );
  }

  Color _getPaymentTypeColor(String paymentType) {
    switch (paymentType) {
      case 'cash':
        return Colors.green;
      case 'card':
        return Colors.blue;
      case 'bank_transfer':
        return Colors.purple;
      case 'mobile_money':
        return Colors.orange;
      case 'cheque':
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }

  String _getPaymentTypeIcon(String paymentType) {
    switch (paymentType) {
      case 'cash':
        return '💵';
      case 'card':
        return '💳';
      case 'bank_transfer':
        return '🏦';
      case 'mobile_money':
        return '📱';
      case 'cheque':
        return '📝';
      default:
        return '💰';
    }
  }
}
*/

// =====================================================
// MAIN FUNCTION FOR TESTING
// =====================================================

void main() async {
  // Example usage
  final example = PaymentApiExample();
  example.initializeService();

  print('=== Flutter Payment API Examples ===');

  // Test the API methods
  await example.createPaymentExample();
  await example.getPaymentsExample();
  await example.getPaymentDetailsExample();
  // await example.updatePaymentExample();
  // await example.deletePaymentExample();
}